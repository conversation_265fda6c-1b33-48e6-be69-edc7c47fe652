/*! elementor - v3.31.0 - 27-08-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[304,915],{1616:(e,t,i)=>{var n=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(i(7469));class NestedAccordionTitleKeyboardHandler extends s.default{__construct(){super.__construct(...arguments);const e=arguments.length<=0?void 0:arguments[0];this.toggleTitle=e.toggleTitle}getDefaultSettings(){return{...super.getDefaultSettings(),selectors:{itemTitle:".e-n-accordion-item-title",itemContainer:".e-n-accordion-item > .e-con"},ariaAttributes:{titleStateAttribute:"aria-expanded",activeTitleSelector:'[aria-expanded="true"]'},datasets:{titleIndex:"data-accordion-index"}}}handeTitleLinkEnterOrSpaceEvent(e){this.toggleTitle(e)}handleContentElementEscapeEvents(e){this.getActiveTitleElement().trigger("focus"),this.toggleTitle(e)}handleTitleEscapeKeyEvents(e){const t=e?.currentTarget?.parentElement,i=t?.open;i&&this.toggleTitle(e)}}t.default=NestedAccordionTitleKeyboardHandler},7469:(e,t,i)=>{var n=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i(6281),i(4846),i(7458),i(6211);var s=n(i(7224));class NestedTitleKeyboardHandler extends s.default{__construct(e){super.__construct(e),this.directionNext="next",this.directionPrevious="previous",this.focusableElementSelector='audio, button, canvas, details, iframe, input, select, summary, textarea, video, [accesskey], [contenteditable], [href], [tabindex]:not([tabindex="-1"])'}getWidgetNumber(){return this.$element.find("> .elementor-widget-container > .e-n-tabs, > .e-n-tabs").attr("data-widget-number")}getDefaultSettings(){return{selectors:{itemTitle:`[id*="e-n-tab-title-${this.getWidgetNumber()}"]`,itemContainer:`[id*="e-n-tab-content-${this.getWidgetNumber()}"]`},ariaAttributes:{titleStateAttribute:"aria-selected",activeTitleSelector:'[aria-selected="true"]'},datasets:{titleIndex:"data-tab-index"},keyDirection:{ArrowLeft:elementorFrontendConfig.is_rtl?this.directionNext:this.directionPrevious,ArrowUp:this.directionPrevious,ArrowRight:elementorFrontendConfig.is_rtl?this.directionPrevious:this.directionNext,ArrowDown:this.directionNext}}}getDefaultElements(){const e=this.getSettings("selectors");return{$itemTitles:this.findElement(e.itemTitle),$itemContainers:this.findElement(e.itemContainer),$focusableContainerElements:this.getFocusableElements(this.findElement(e.itemContainer))}}getFocusableElements(e){return e.find(this.focusableElementSelector).not("[disabled], [inert]")}getKeyDirectionValue(e){const t=this.getSettings("keyDirection")[e.key];return this.directionNext===t?1:-1}getTitleIndex(e){const{titleIndex:t}=this.getSettings("datasets");return e.getAttribute(t)}getTitleFilterSelector(e){const{titleIndex:t}=this.getSettings("datasets");return`[${t}="${e}"]`}getActiveTitleElement(){const e=this.getSettings("ariaAttributes").activeTitleSelector;return this.elements.$itemTitles.filter(e)}onInit(){super.onInit(...arguments)}bindEvents(){this.elements.$itemTitles.on(this.getTitleEvents()),this.elements.$focusableContainerElements.on(this.getContentElementEvents())}unbindEvents(){this.elements.$itemTitles.off(this.getTitleEvents()),this.elements.$focusableContainerElements.children().off(this.getContentElementEvents())}getTitleEvents(){return{keydown:this.handleTitleKeyboardNavigation.bind(this)}}getContentElementEvents(){return{keydown:this.handleContentElementKeyboardNavigation.bind(this)}}isDirectionKey(e){return["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(e.key)}isActivationKey(e){return["Enter"," "].includes(e.key)}handleTitleKeyboardNavigation(e){if(this.isDirectionKey(e)){e.preventDefault();const t=parseInt(this.getTitleIndex(e.currentTarget))||1,i=this.elements.$itemTitles.length,n=this.getTitleIndexFocusUpdated(e,t,i);this.changeTitleFocus(n),e.stopPropagation()}else if(this.isActivationKey(e)){if(e.preventDefault(),this.handeTitleLinkEnterOrSpaceEvent(e))return;const t=this.getTitleIndex(e.currentTarget);elementorFrontend.elements.$window.trigger("elementor/nested-elements/activate-by-keyboard",{widgetId:this.getID(),titleIndex:t})}else"Escape"===e.key&&this.handleTitleEscapeKeyEvents(e)}handeTitleLinkEnterOrSpaceEvent(e){const t="a"===e?.currentTarget?.tagName?.toLowerCase();return!elementorFrontend.isEditMode()&&t&&(e?.currentTarget?.click(),e.stopPropagation()),t}getTitleIndexFocusUpdated(e,t,i){let n=0;switch(e.key){case"Home":n=1;break;case"End":n=i;break;default:const s=this.getKeyDirectionValue(e);n=i<t+s?1:0===t+s?i:t+s}return n}changeTitleFocus(e){const t=this.elements.$itemTitles.filter(this.getTitleFilterSelector(e));this.setTitleTabindex(e),t.trigger("focus")}setTitleTabindex(e){this.elements.$itemTitles.attr("tabindex","-1");this.elements.$itemTitles.filter(this.getTitleFilterSelector(e)).attr("tabindex","0")}handleTitleEscapeKeyEvents(){}handleContentElementKeyboardNavigation(e){"Tab"!==e.key||e.shiftKey?"Escape"===e.key&&(e.preventDefault(),e.stopPropagation(),this.handleContentElementEscapeEvents(e)):this.handleContentElementTabEvents(e)}handleContentElementEscapeEvents(){this.getActiveTitleElement().trigger("focus")}handleContentElementTabEvents(){}}t.default=NestedTitleKeyboardHandler},8216:(e,t,i)=>{var n=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i(4846),i(6211),i(9655);var s=n(i(7224)),o=n(i(1616));class NestedAccordion extends s.default{constructor(){super(...arguments),this.animations=new Map}getDefaultSettings(){return{selectors:{accordion:".e-n-accordion",accordionContentContainers:".e-n-accordion > .e-con",accordionItems:".e-n-accordion-item",accordionItemTitles:".e-n-accordion-item-title",accordionItemTitlesText:".e-n-accordion-item-title-text",accordionContent:".e-n-accordion-item > .e-con",directAccordionItems:":scope > .e-n-accordion-item",directAccordionItemTitles:":scope > .e-n-accordion-item > .e-n-accordion-item-title"},default_state:"expanded",attributes:{index:"data-accordion-index",ariaLabelledBy:"aria-labelledby"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$accordion:this.findElement(e.accordion),$contentContainers:this.findElement(e.accordionContentContainers),$accordionItems:this.findElement(e.accordionItems),$accordionTitles:this.findElement(e.accordionItemTitles),$accordionContent:this.findElement(e.accordionContent)}}onInit(){super.onInit(...arguments),this.injectKeyboardHandler()}injectKeyboardHandler(){"nested-accordion.default"===this.getSettings("elementName")&&new o.default({$element:this.$element,toggleTitle:this.clickListener.bind(this)})}linkContainer(e){const{container:t,index:i,targetContainer:n,action:{type:s}}=e.detail,o=t.view.$el;if(t.model.get("id")===this.$element.data("id")){const{$accordionItems:e}=this.getDefaultElements();let t,r;switch(s){case"move":[t,r]=this.move(o,i,n,e);break;case"duplicate":[t,r]=this.duplicate(o,i,n,e)}void 0!==t&&t.appendChild(r),this.updateIndexValues(),this.updateListeners(o),elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/elements/link-data-bindings"))}}move(e,t,i,n){return[n[t],i.view.$el[0]]}duplicate(e,t,i,n){return[n[t+1],i.view.$el[0]]}updateIndexValues(){const{$accordionContent:e,$accordionItems:t}=this.getDefaultElements(),i=this.getSettings(),n=t[0].getAttribute("id").slice(0,-1);t.each((t,s)=>{s.setAttribute("id",`${n}${t}`),s.querySelector(i.selectors.accordionItemTitles).setAttribute(i.attributes.index,t+1),s.querySelector(i.selectors.accordionItemTitles).setAttribute("aria-controls",`${n}${t}`),s.querySelector(i.selectors.accordionItemTitlesText).setAttribute("data-binding-index",t+1),e[t].setAttribute(i.attributes.ariaLabelledBy,`${n}${t}`)})}updateListeners(e){this.elements.$accordionTitles=e.find(this.getSettings("selectors.accordionItemTitles")),this.elements.$accordionItems=e.find(this.getSettings("selectors.accordionItems")),this.elements.$accordionTitles.on("click",this.clickListener.bind(this))}bindEvents(){this.elements.$accordionTitles.on("click",this.clickListener.bind(this)),elementorFrontend.elements.$window.on("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}unbindEvents(){this.elements.$accordionTitles.off()}clickListener(e){e.preventDefault(),this.elements=this.getDefaultElements();const t=this.getSettings(),i=e?.currentTarget?.closest(t.selectors.accordionItems),n=e?.currentTarget?.closest(t.selectors.accordion),s=i.querySelector(t.selectors.accordionItemTitles),o=i.querySelector(t.selectors.accordionContent),{max_items_expended:r}=this.getElementSettings(),a=n.querySelectorAll(t.selectors.directAccordionItems),l=n.querySelectorAll(t.selectors.directAccordionItemTitles);"one"===r&&this.closeAllItems(a,l),i.open?this.closeAccordionItem(i,s):this.prepareOpenAnimation(i,s,o)}animateItem(e,t,i,n){e.style.overflow="hidden";let s=this.animations.get(e);s&&s.cancel(),s=e.animate({height:[t,i]},{duration:this.getAnimationDuration()}),s.onfinish=()=>this.onAnimationFinish(e,n),this.animations.set(e,s),e.querySelector("summary")?.setAttribute("aria-expanded",n)}closeAccordionItem(e,t){const i=`${e.offsetHeight}px`,n=`${t.offsetHeight}px`;this.animateItem(e,i,n,!1)}prepareOpenAnimation(e,t,i){e.style.overflow="hidden",e.style.height=`${e.offsetHeight}px`,e.open=!0,window.requestAnimationFrame(()=>this.openAccordionItem(e,t,i))}openAccordionItem(e,t,i){const n=`${e.offsetHeight}px`,s=`${t.offsetHeight+i.offsetHeight}px`;this.animateItem(e,n,s,!0)}onAnimationFinish(e,t){e.open=t,this.animations.set(e,null),e.style.height=e.style.overflow=""}closeAllItems(e,t){t.forEach((t,i)=>{this.closeAccordionItem(e[i],t)})}getAnimationDuration(){const{size:e,unit:t}=this.getElementSettings("n_accordion_animation_duration");return e*("ms"===t?1:1e3)}}t.default=NestedAccordion}}]);