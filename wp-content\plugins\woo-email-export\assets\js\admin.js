jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize date pickers
    $('.wee-datepicker').datepicker({
        dateFormat: 'mm/dd/yy',
        changeMonth: true,
        changeYear: true,
        maxDate: 0, // Don't allow future dates
        yearRange: '-10:+0'
    });
    
    // Set default dates (last 30 days)
    var today = new Date();
    var thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    $('#start_date').datepicker('setDate', thirtyDaysAgo);
    $('#end_date').datepicker('setDate', today);
    
    // Form validation
    $('#wee-export-form').on('submit', function(e) {
        var isValid = true;
        var errorMessages = [];
        
        // Clear previous errors
        $('.wee-error').removeClass('wee-error');
        $('.wee-error-message').remove();
        
        // Validate start date
        var startDate = $('#start_date').val();
        if (!startDate) {
            $('#start_date').addClass('wee-error');
            $('#start_date').after('<span class="wee-error-message">Start date is required.</span>');
            isValid = false;
        }
        
        // Validate end date
        var endDate = $('#end_date').val();
        if (!endDate) {
            $('#end_date').addClass('wee-error');
            $('#end_date').after('<span class="wee-error-message">End date is required.</span>');
            isValid = false;
        }
        
        // Validate date range
        if (startDate && endDate) {
            var start = new Date(startDate);
            var end = new Date(endDate);
            
            if (start > end) {
                $('#end_date').addClass('wee-error');
                $('#end_date').after('<span class="wee-error-message">End date must be after start date.</span>');
                isValid = false;
            }
        }
        
        // Validate order statuses
        var selectedStatuses = $('#order_statuses').val();
        if (!selectedStatuses || selectedStatuses.length === 0) {
            $('#order_statuses').addClass('wee-error');
            $('#order_statuses').after('<span class="wee-error-message">Please select at least one order status.</span>');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            
            // Scroll to first error
            var firstError = $('.wee-error').first();
            if (firstError.length) {
                $('html, body').animate({
                    scrollTop: firstError.offset().top - 100
                }, 500);
            }
            
            return false;
        }
        
        // Show loading state
        var submitBtn = $('#wee-submit-btn');
        submitBtn.prop('disabled', true);
        submitBtn.val(wee_admin.strings.processing);
        
        // Add loading spinner
        if ($('.wee-loading').length === 0) {
            submitBtn.after('<span class="wee-loading show">⏳</span>');
        } else {
            $('.wee-loading').addClass('show');
        }
    });
    
    // Multi-select enhancement
    $('#order_statuses').on('focus', function() {
        $(this).attr('size', Math.min($(this).find('option').length, 8));
    }).on('blur', function() {
        $(this).removeAttr('size');
    });
    
    // Select all / deselect all functionality
    if ($('#order_statuses option').length > 0) {
        var selectAllHtml = '<div class="wee-select-controls" style="margin-top: 5px;">' +
            '<a href="#" id="wee-select-all">Select All</a> | ' +
            '<a href="#" id="wee-deselect-all">Deselect All</a>' +
            '</div>';
        $('#order_statuses').after(selectAllHtml);
        
        $('#wee-select-all').on('click', function(e) {
            e.preventDefault();
            $('#order_statuses option').prop('selected', true);
        });
        
        $('#wee-deselect-all').on('click', function(e) {
            e.preventDefault();
            $('#order_statuses option').prop('selected', false);
        });
    }
    
    // Auto-dismiss notices after 5 seconds
    setTimeout(function() {
        $('.notice.is-dismissible').fadeOut();
    }, 5000);
    
    // Add helpful tooltips
    if (typeof $.fn.tooltip !== 'undefined') {
        $('[title]').tooltip({
            position: { my: "left+15 center", at: "right center" }
        });
    }
    
    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl/Cmd + Enter to submit form
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
            $('#wee-export-form').submit();
        }
        
        // Escape to clear form
        if (e.keyCode === 27) {
            if (confirm('Clear the form?')) {
                $('#wee-export-form')[0].reset();
                $('.wee-error').removeClass('wee-error');
                $('.wee-error-message').remove();
            }
        }
    });
    
    // Add form reset functionality
    var resetHtml = '<button type="button" id="wee-reset-btn" class="button" style="margin-left: 10px;">Reset Form</button>';
    $('#wee-submit-btn').after(resetHtml);
    
    $('#wee-reset-btn').on('click', function() {
        if (confirm('Are you sure you want to reset the form?')) {
            $('#wee-export-form')[0].reset();
            $('.wee-error').removeClass('wee-error');
            $('.wee-error-message').remove();
            
            // Reset to default dates
            $('#start_date').datepicker('setDate', thirtyDaysAgo);
            $('#end_date').datepicker('setDate', today);
        }
    });
    
    // Add info box with instructions
    var infoHtml = '<div class="wee-info-box">' +
        '<h4>📋 Instructions:</h4>' +
        '<ul>' +
        '<li><strong>Date Range:</strong> Select the start and end dates for orders to include</li>' +
        '<li><strong>Order Status:</strong> Choose one or more order statuses to filter by</li>' +
        '<li><strong>Export:</strong> Click "Export to CSV" to download the email list</li>' +
        '<li><strong>Shortcuts:</strong> Ctrl+Enter to export, Escape to clear form</li>' +
        '</ul>' +
        '</div>';
    
    $('.wrap h1').after(infoHtml);
});
