/*! elementor - v3.31.0 - 27-08-2025 */
(()=>{var e={815:(e,t,r)=>{"use strict";var o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.App<PERSON>anager=void 0;var n=o(r(41594)),i=o(r(39805)),a=o(r(40989)),s=o(r(5417)),l=r(7470);t.AppManager=function(){return(0,a.default)(function AppManager(){(0,i.default)(this,AppManager),this.popover=null,this.onRoute=function(){},this.unbindIframeEvents=function(){}},[{key:"mount",value:function mount(e,t){var r,o,i=this;if(!this.popover){var a=null==e?void 0:e.closest(t.wrapperElement),u=null==a?void 0:a.querySelector(t.reactAnchor);if(u){this.attachEditorEventListeners(),this.popover=(0,l.createRoot)(u);var p=(null===(r=elementor)||void 0===r||null===(o=r.getPreferences)||void 0===o?void 0:o.call(r,"ui_theme"))||"auto",c=elementorCommon.config.isRTL;this.popover.render(n.default.createElement(s.default,{colorScheme:p,isRTL:c,onClose:function onClose(){return i.unmount()}}))}}}},{key:"unmount",value:function unmount(){this.popover&&(this.detachEditorEventListeners(),this.popover.unmount(),this.unbindIframeEvents()),this.popover=null}},{key:"setupIframeEventListeners",value:function setupIframeEventListeners(){var e=this,t=document.getElementById("elementor-preview-iframe");if(t){var r=t.contentWindow.document,o=function handleClick(){return e.unmount()};r.addEventListener("click",o),r.addEventListener("keydown",o),this.unbindIframeEvents=function(){r.removeEventListener("click",o),r.removeEventListener("keydown",o)}}}},{key:"setupRouteListener",value:function setupRouteListener(){var e=this;this.onRoute=function(t,r){"panel/elements/categories"!==r&&"panel/editor/content"!==r||e.unmount()},$e.routes.on("run:after",this.onRoute)}},{key:"attachEditorEventListeners",value:function attachEditorEventListeners(){this.setupIframeEventListeners(),this.setupRouteListener()}},{key:"detachEditorEventListeners",value:function detachEditorEventListeners(){$e.routes.off("run:after",this.onRoute)}}])}()},5417:(e,t,r)=>{"use strict";var o=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(r(41594)),a=r(86956),s=n(r(71737)),l=function App(e){return i.default.createElement(a.DirectionProvider,{rtl:e.isRTL},i.default.createElement(a.LocalizationProvider,null,i.default.createElement(a.ThemeProvider,{colorScheme:e.colorScheme,palette:"unstable"},i.default.createElement(a.Infotip,{content:i.default.createElement(s.default,{doClose:e.onClose}),placement:"right",arrow:!0,open:!0,disableHoverListener:!0,PopperProps:{modifiers:[{name:"offset",options:{offset:[-24,8]}}]}},i.default.createElement("span",null)))))};l.propTypes={colorScheme:o.oneOf(["auto","light","dark"]),isRTL:o.bool,onClose:o.func.isRequired};t.default=l},7470:(e,t,r)=>{"use strict";var o=r(75206);t.createRoot=o.createRoot,t.hydrateRoot=o.hydrateRoot},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var o=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40362:(e,t,r)=>{"use strict";var o=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,n,i,a){if(a!==o){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},40989:(e,t,r)=>{var o=r(45498);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},41594:e=>{"use strict";e.exports=React},45498:(e,t,r)=>{var o=r(10564).default,n=r(11327);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},62688:(e,t,r)=>{e.exports=r(40362)()},71737:(e,t,r)=>{"use strict";var o=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(r(41594)),a=r(86956),s=r(12470),l={image:"https://assets.elementor.com/v4-promotion/v1/images/v4_chip.png",image_alt:(0,s.__)("Elementor V4","elementor"),title:(0,s.__)("Elementor V4","elementor"),description:[(0,s.__)("You’ve got powerful new tools with Editor V4. But, keep in mind that this is an early release, so don’t use it on live sites yet.","elementor")],upgrade_text:(0,s.__)("Learn more","elementor"),upgrade_url:"https://go.elementor.com/wp-dash-opt-in-v4-help-center/"},u=function PopoverCard(e){var t=e.doClose,r=null==l?void 0:l.title,o=null==l?void 0:l.description,n=null==l?void 0:l.image,s=null==l?void 0:l.image_alt,u=null==l?void 0:l.upgrade_text,p=null==l?void 0:l.upgrade_url;return i.default.createElement(a.ClickAwayListener,{disableReactTree:!0,mouseEvent:"onMouseDown",touchEvent:"onTouchStart",onClickAway:t},i.default.createElement(a.Box,{sx:{width:296},"data-testid":"e-popover-card"},i.default.createElement(a.Stack,{direction:"row",alignItems:"center",py:1,px:2},i.default.createElement(a.Typography,{variant:"subtitle2"},r),i.default.createElement(a.CloseButton,{edge:"end",sx:{ml:"auto"},slotProps:{icon:{fontSize:"small"}},onClick:t})),i.default.createElement(a.Image,{src:n,alt:s,sx:{height:150,width:"100%"}}),i.default.createElement(a.Stack,{px:2},1===o.length?i.default.createElement(a.Typography,{variant:"body2",color:"secondary",sx:{pt:1.5,pb:1}},o[0]):i.default.createElement(a.List,{sx:{pl:2}},o.map(function(e,t){return i.default.createElement(a.ListItem,{key:t,sx:{listStyle:"disc",display:"list-item",color:"text.secondary",p:0}},i.default.createElement(a.Typography,{variant:"body2",color:"secondary"},e))}))),i.default.createElement(a.Stack,{pt:1,pb:1.5,px:2},i.default.createElement(a.Button,{variant:"contained",size:"small",color:"accent",onClick:function redirectHandler(){return window.open(p,"_blank"),t()},sx:{ml:"auto"}},u))))};u.propTypes={doClose:o.func};t.default=u},75206:e=>{"use strict";e.exports=ReactDOM},86956:e=>{"use strict";e.exports=elementorV2.ui},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(96784),t=e(__webpack_require__(39805)),r=e(__webpack_require__(40989)),o=__webpack_require__(815);new(function(){return(0,r.default)(function AlphaChipApp(){(0,t.default)(this,AlphaChipApp),this.appManager=new o.AppManager,this.initializeListener()},[{key:"initializeListener",value:function initializeListener(){var e=this;document.addEventListener("alphachip:open",function(t){var r=t.detail.target,o=null==r?void 0:r.find(".elementor-panel-heading-category-chip")[0];o&&e.appManager.mount(o,{wrapperElement:".elementor-panel-category-title",reactAnchor:".e-promotion-react-wrapper"})})}}])}())})()})();