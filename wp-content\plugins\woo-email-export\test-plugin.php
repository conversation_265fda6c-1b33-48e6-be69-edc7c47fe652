<?php
/**
 * Test file for WooCommerce Email Export Plugin
 * 
 * This file can be used to test basic plugin functionality
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test plugin installation and basic functionality
 */
function wee_test_plugin() {
    echo "<h2>WooCommerce Email Export - Plugin Test</h2>";
    
    // Test 1: Check if WooCommerce is active
    echo "<h3>Test 1: WooCommerce Status</h3>";
    if (class_exists('WooCommerce')) {
        echo "✅ WooCommerce is active<br>";
    } else {
        echo "❌ WooCommerce is not active<br>";
        return;
    }
    
    // Test 2: Check plugin constants
    echo "<h3>Test 2: Plugin Constants</h3>";
    if (defined('WEE_PLUGIN_DIR')) {
        echo "✅ WEE_PLUGIN_DIR: " . WEE_PLUGIN_DIR . "<br>";
    } else {
        echo "❌ WEE_PLUGIN_DIR not defined<br>";
    }
    
    if (defined('WEE_PLUGIN_URL')) {
        echo "✅ WEE_PLUGIN_URL: " . WEE_PLUGIN_URL . "<br>";
    } else {
        echo "❌ WEE_PLUGIN_URL not defined<br>";
    }
    
    // Test 3: Check if admin class exists
    echo "<h3>Test 3: Admin Class</h3>";
    if (class_exists('WEE_Admin')) {
        echo "✅ WEE_Admin class exists<br>";
    } else {
        echo "❌ WEE_Admin class not found<br>";
    }
    
    // Test 4: Check file structure
    echo "<h3>Test 4: File Structure</h3>";
    $required_files = [
        WEE_PLUGIN_DIR . 'includes/class-wee-admin.php',
        WEE_PLUGIN_DIR . 'assets/css/admin.css',
        WEE_PLUGIN_DIR . 'assets/js/admin.js',
        WEE_PLUGIN_DIR . 'uninstall.php'
    ];
    
    foreach ($required_files as $file) {
        if (file_exists($file)) {
            echo "✅ " . basename($file) . " exists<br>";
        } else {
            echo "❌ " . basename($file) . " missing<br>";
        }
    }
    
    // Test 5: Check order statuses
    echo "<h3>Test 5: WooCommerce Order Statuses</h3>";
    $statuses = wc_get_order_statuses();
    if (!empty($statuses)) {
        echo "✅ Found " . count($statuses) . " order statuses:<br>";
        foreach ($statuses as $key => $label) {
            echo "- " . $key . ": " . $label . "<br>";
        }
    } else {
        echo "❌ No order statuses found<br>";
    }
    
    // Test 6: Check if we can query orders
    echo "<h3>Test 6: Order Query Test</h3>";
    $orders = wc_get_orders([
        'limit' => 5,
        'return' => 'objects'
    ]);
    
    if (!empty($orders)) {
        echo "✅ Found " . count($orders) . " recent orders<br>";
        foreach ($orders as $order) {
            $email = $order->get_billing_email();
            echo "- Order #" . $order->get_id() . ": " . ($email ? $email : 'No email') . "<br>";
        }
    } else {
        echo "⚠️ No orders found (this is normal for new installations)<br>";
    }
    
    echo "<h3>Test Complete!</h3>";
    echo "<p>If all tests pass, the plugin should work correctly.</p>";
}

// Run test if accessed directly (for debugging)
if (isset($_GET['wee_test']) && current_user_can('manage_options')) {
    wee_test_plugin();
}
