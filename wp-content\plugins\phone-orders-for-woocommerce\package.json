{"name": "phone-orders-vue", "sideEffects": ["*.css", "*.vue"], "private": true, "version": "3.1.0", "description": "Phone orders plugin with vue interface", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "webpack --mode=development", "watch": "webpack --mode=development --watch", "prod": "webpack --mode=production"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/algolplus/phone-orders-for-woocommerce.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/algolplus/phone-orders-for-woocommerce#readme", "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-transform-runtime": "^7.18.6", "@babel/preset-env": "^7.18.6", "@babel/runtime": "^7.18.6", "@vue/compiler-sfc": "^3.1.0", "axios": "0.21.1", "babel-loader": "^8.2.5", "css-loader": "^5.1.1", "css-minimizer-webpack-plugin": "^4.0.0", "less": "^4.1.1", "less-loader": "^8.0.0", "mini-css-extract-plugin": "^1.3.9", "moment-locales-webpack-plugin": "^1.2.0", "numeral": "^2.0.6", "qs": "^6.5.2", "terser-webpack-plugin": "^5.1.1", "vue": "^3.1.0", "vue-loader": "^16.0.0", "vue-resource": "^1.5.1", "vue-spinner": "^1.0.3", "webpack": "^5.73.0", "webpack-cli": "^4.5.0"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.1.1", "@fortawesome/free-regular-svg-icons": "^6.1.1", "@fortawesome/free-solid-svg-icons": "^6.1.1", "@fortawesome/vue-fontawesome": "^3.0.1", "@popperjs/core": "^2.11.6", "@vuepic/vue-datepicker": "^3.3.1", "bootstrap": "^5.1.3", "bootstrap-vue-3": "0.1.13", "lodash": "^4.17.21", "moment": "^2.22.2", "tiny-emitter": "^2.1.0", "vue-multiselect": "^3.0.0-alpha.2", "vue-wp-list-table": "^1.1.0", "vue3-clipboard": "^1.0.0", "vuedraggable": "^4.1.0", "vuex": "^4.0.0"}}