/*! elementor - v3.31.0 - 27-08-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[304],{7469:(e,t,i)=>{var n=i(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i(6281),i(4846),i(7458),i(6211);var s=n(i(7224));class NestedTitleKeyboardHandler extends s.default{__construct(e){super.__construct(e),this.directionNext="next",this.directionPrevious="previous",this.focusableElementSelector='audio, button, canvas, details, iframe, input, select, summary, textarea, video, [accesskey], [contenteditable], [href], [tabindex]:not([tabindex="-1"])'}getWidgetNumber(){return this.$element.find("> .elementor-widget-container > .e-n-tabs, > .e-n-tabs").attr("data-widget-number")}getDefaultSettings(){return{selectors:{itemTitle:`[id*="e-n-tab-title-${this.getWidgetNumber()}"]`,itemContainer:`[id*="e-n-tab-content-${this.getWidgetNumber()}"]`},ariaAttributes:{titleStateAttribute:"aria-selected",activeTitleSelector:'[aria-selected="true"]'},datasets:{titleIndex:"data-tab-index"},keyDirection:{ArrowLeft:elementorFrontendConfig.is_rtl?this.directionNext:this.directionPrevious,ArrowUp:this.directionPrevious,ArrowRight:elementorFrontendConfig.is_rtl?this.directionPrevious:this.directionNext,ArrowDown:this.directionNext}}}getDefaultElements(){const e=this.getSettings("selectors");return{$itemTitles:this.findElement(e.itemTitle),$itemContainers:this.findElement(e.itemContainer),$focusableContainerElements:this.getFocusableElements(this.findElement(e.itemContainer))}}getFocusableElements(e){return e.find(this.focusableElementSelector).not("[disabled], [inert]")}getKeyDirectionValue(e){const t=this.getSettings("keyDirection")[e.key];return this.directionNext===t?1:-1}getTitleIndex(e){const{titleIndex:t}=this.getSettings("datasets");return e.getAttribute(t)}getTitleFilterSelector(e){const{titleIndex:t}=this.getSettings("datasets");return`[${t}="${e}"]`}getActiveTitleElement(){const e=this.getSettings("ariaAttributes").activeTitleSelector;return this.elements.$itemTitles.filter(e)}onInit(){super.onInit(...arguments)}bindEvents(){this.elements.$itemTitles.on(this.getTitleEvents()),this.elements.$focusableContainerElements.on(this.getContentElementEvents())}unbindEvents(){this.elements.$itemTitles.off(this.getTitleEvents()),this.elements.$focusableContainerElements.children().off(this.getContentElementEvents())}getTitleEvents(){return{keydown:this.handleTitleKeyboardNavigation.bind(this)}}getContentElementEvents(){return{keydown:this.handleContentElementKeyboardNavigation.bind(this)}}isDirectionKey(e){return["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(e.key)}isActivationKey(e){return["Enter"," "].includes(e.key)}handleTitleKeyboardNavigation(e){if(this.isDirectionKey(e)){e.preventDefault();const t=parseInt(this.getTitleIndex(e.currentTarget))||1,i=this.elements.$itemTitles.length,n=this.getTitleIndexFocusUpdated(e,t,i);this.changeTitleFocus(n),e.stopPropagation()}else if(this.isActivationKey(e)){if(e.preventDefault(),this.handeTitleLinkEnterOrSpaceEvent(e))return;const t=this.getTitleIndex(e.currentTarget);elementorFrontend.elements.$window.trigger("elementor/nested-elements/activate-by-keyboard",{widgetId:this.getID(),titleIndex:t})}else"Escape"===e.key&&this.handleTitleEscapeKeyEvents(e)}handeTitleLinkEnterOrSpaceEvent(e){const t="a"===e?.currentTarget?.tagName?.toLowerCase();return!elementorFrontend.isEditMode()&&t&&(e?.currentTarget?.click(),e.stopPropagation()),t}getTitleIndexFocusUpdated(e,t,i){let n=0;switch(e.key){case"Home":n=1;break;case"End":n=i;break;default:const s=this.getKeyDirectionValue(e);n=i<t+s?1:0===t+s?i:t+s}return n}changeTitleFocus(e){const t=this.elements.$itemTitles.filter(this.getTitleFilterSelector(e));this.setTitleTabindex(e),t.trigger("focus")}setTitleTabindex(e){this.elements.$itemTitles.attr("tabindex","-1");this.elements.$itemTitles.filter(this.getTitleFilterSelector(e)).attr("tabindex","0")}handleTitleEscapeKeyEvents(){}handleContentElementKeyboardNavigation(e){"Tab"!==e.key||e.shiftKey?"Escape"===e.key&&(e.preventDefault(),e.stopPropagation(),this.handleContentElementEscapeEvents(e)):this.handleContentElementTabEvents(e)}handleContentElementEscapeEvents(){this.getActiveTitleElement().trigger("focus")}handleContentElementTabEvents(){}}t.default=NestedTitleKeyboardHandler}}]);