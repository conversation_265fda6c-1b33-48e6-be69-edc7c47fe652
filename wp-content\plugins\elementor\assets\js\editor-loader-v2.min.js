/*! elementor - v3.31.0 - 27-08-2025 */
(()=>{"use strict";var e;if(window.__elementorEditorV1LoadingPromise=new Promise(function(e){window.addEventListener("elementor/init",function(){e()},{once:!0})}),window.elementor.start(),null===(e=window.elementorV2)||void 0===e||!e.editor)throw new Error('The "@elementor/editor" package was not loaded.');window.elementorV2.editor.start(document.getElementById("elementor-editor-wrapper-v2"))})();