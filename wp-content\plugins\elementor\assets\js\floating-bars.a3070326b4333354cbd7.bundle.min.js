/*! elementor - v3.31.0 - 27-08-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[336],{115:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=class FloatingBarDomHelper{constructor(t){this.$element=t}maybeMoveToTop(){const t=this.$element[0],e=t.querySelector(".e-floating-bars");if(elementorFrontend.isEditMode())e.classList.add("is-sticky");else if(t.dataset.widget_type.startsWith("floating-bars")&&e.classList.contains("has-vertical-position-top")&&!e.classList.contains("is-sticky")){const e=document.getElementById("wpadminbar"),n=t.closest(".elementor");e?e.after(n):document.body.prepend(n)}}}},442:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n(5724),n(4846),n(9655);var i=s(n(7224));class ClickTrackingHandler extends i.default{clicks=[];getDefaultSettings(){return{selectors:{contentWrapper:".e-contact-buttons__content-wrapper",contactButtonCore:".e-contact-buttons__send-button",contentWrapperFloatingBars:".e-floating-bars",floatingBarCTAButton:".e-floating-bars__cta-button",elementorWrapper:'[data-elementor-type="floating-buttons"]'}}}getDefaultElements(){const t=this.getSettings("selectors");return{contentWrapper:this.$element[0].querySelector(t.contentWrapper),contentWrapperFloatingBars:this.$element[0].querySelector(t.contentWrapperFloatingBars)}}bindEvents(){this.elements.contentWrapper&&this.elements.contentWrapper.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contentWrapperFloatingBars&&this.elements.contentWrapperFloatingBars.addEventListener("click",this.onChatButtonTrackClick.bind(this)),window.addEventListener("beforeunload",()=>{this.clicks.length>0&&this.sendClicks()})}onChatButtonTrackClick(t){const e=t.target||t.srcElement,n=this.getSettings("selectors");(e.matches(n.contactButtonCore)||e.closest(n.contactButtonCore)||e.matches(n.floatingBarCTAButton)||e.closest(n.floatingBarCTAButton))&&this.getDocumentIdAndTrack(e,n)}getDocumentIdAndTrack(t,e){const n=t.closest(e.elementorWrapper).dataset.elementorId;this.trackClick(n)}trackClick(t){t&&(this.clicks.push(t),this.clicks.length>=10&&this.sendClicks())}sendClicks(){const t=new FormData;t.append("action","elementor_send_clicks"),t.append("_nonce",elementorFrontendConfig?.nonces?.floatingButtonsClickTracking),this.clicks.forEach(e=>t.append("clicks[]",e)),fetch(elementorFrontendConfig?.urls?.ajaxurl,{method:"POST",body:t}).then(()=>{this.clicks=[]})}}e.default=ClickTrackingHandler},5199:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n(6281),n(4846),n(9655);var i=s(n(7224)),o=s(n(115)),a=s(n(442));class FloatingBarsHandler extends i.default{getDefaultSettings(){return{selectors:{main:".e-floating-bars",closeButton:".e-floating-bars__close-button",ctaButton:".e-floating-bars__cta-button"},constants:{ctaEntranceAnimation:"style_cta_button_animation",ctaEntranceAnimationDelay:"style_cta_button_animation_delay",hasEntranceAnimation:"has-entrance-animation",visible:"visible",isSticky:"is-sticky",hasVerticalPositionTop:"has-vertical-position-top",hasVerticalPositionBottom:"has-vertical-position-bottom",isHidden:"is-hidden",animated:"animated"}}}getDefaultElements(){const t=this.getSettings("selectors");return{main:this.$element[0].querySelector(t.main),mainAll:this.$element[0].querySelectorAll(t.main),closeButton:this.$element[0].querySelector(t.closeButton),ctaButton:this.$element[0].querySelector(t.ctaButton)}}onElementChange(t){["advanced_vertical_position"].includes(t)&&this.initDefaultState()}getResponsiveSetting(t){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),t,"",e)}bindEvents(){this.elements.closeButton&&this.elements.closeButton.addEventListener("click",this.closeFloatingBar.bind(this)),this.elements.ctaButton&&this.elements.ctaButton.addEventListener("animationend",this.handleAnimationEnd.bind(this)),this.elements.main&&window.addEventListener("keyup",this.onDocumentKeyup.bind(this)),this.hasStickyElements()&&window.addEventListener("resize",this.handleStickyElements.bind(this))}isStickyTop(){const{isSticky:t,hasVerticalPositionTop:e}=this.getSettings("constants");return this.elements.main.classList.contains(t)&&this.elements.main.classList.contains(e)}isStickyBottom(){const{isSticky:t,hasVerticalPositionBottom:e}=this.getSettings("constants");return this.elements.main.classList.contains(t)&&this.elements.main.classList.contains(e)}hasStickyElements(){return document.querySelectorAll(".elementor-sticky").length>0}focusOnLoad(){this.elements.main.setAttribute("tabindex","0"),this.elements.main.focus({focusVisible:!0})}applyBodyPadding(){const t=this.elements.main.offsetHeight;document.body.style.paddingTop=`${t}px`}removeBodyPadding(){document.body.style.paddingTop="0"}handleWPAdminBar(){const t=elementorFrontend.elements.$wpAdminBar;t.length&&(this.elements.main.style.top=`${t.height()}px`)}handleStickyElements(){const t=this.elements.main.offsetHeight,e=elementorFrontend.elements.$wpAdminBar,n=document.querySelectorAll(".elementor-sticky:not(.elementor-sticky__spacer)");0!==n.length&&(n.forEach(n=>{const s=n.getAttribute("data-settings"),i=JSON.parse(s)?.sticky,o="0px"===n.style.top||"top"===i,a="0px"===n.style.bottom||"bottom"===i;this.isStickyTop()&&o?e.length?n.style.top=`${t+e.height()}px`:n.style.top=`${t}px`:this.isStickyBottom()&&a&&(n.style.bottom=`${t}px`),elementorFrontend.isEditMode()&&(o?n.style.top=this.isStickyTop()?`${t}px`:"0px":a&&(n.style.bottom=this.isStickyBottom()?`${t}px`:"0px"))}),document.querySelectorAll(".elementor-sticky__spacer").forEach(e=>{const n=e.getAttribute("data-settings"),s=JSON.parse(n)?.sticky,i="0px"===e.style.top||"top"===s;this.isStickyTop()&&i&&(e.style.marginBottom=`${t}px`)}))}closeFloatingBar(){const{isHidden:t}=this.getSettings("constants");elementorFrontend.isEditMode()||(this.elements.main.classList.add(t),this.hasStickyElements()?this.handleStickyElements():this.isStickyTop()&&this.removeBodyPadding())}initEntranceAnimation(){const{animated:t,ctaEntranceAnimation:e,ctaEntranceAnimationDelay:n,hasEntranceAnimation:s}=this.getSettings("constants"),i=this.getResponsiveSetting(e),o=(this.getResponsiveSetting(n)||0)+500;this.elements.ctaButton.classList.add(t),this.elements.ctaButton.classList.add(i),setTimeout(()=>{this.elements.ctaButton.classList.remove(s)},o)}handleAnimationEnd(){this.removeEntranceAnimationClasses(),this.focusOnLoad()}removeEntranceAnimationClasses(){if(!this.elements.ctaButton)return;const{animated:t,ctaEntranceAnimation:e,visible:n}=this.getSettings("constants"),s=this.getResponsiveSetting(e);this.elements.ctaButton.classList.remove(t),this.elements.ctaButton.classList.remove(s),this.elements.ctaButton.classList.add(n)}onDocumentKeyup(t){27===t.keyCode&&this.elements.main&&this.elements.main.contains(document.activeElement)&&this.closeFloatingBar()}initDefaultState(){const{hasEntranceAnimation:t}=this.getSettings("constants");this.isStickyTop()&&this.handleWPAdminBar(),this.hasStickyElements()?this.handleStickyElements():this.isStickyTop()&&this.applyBodyPadding(),!this.elements.main||this.elements.ctaButton.classList.contains(t)||elementorFrontend.isEditMode()||this.focusOnLoad()}setupInnerContainer(){this.elements.main.closest(".e-con-inner").classList.add("e-con-inner--floating-bars"),this.elements.main.closest(".e-con").classList.add("e-con--floating-bars")}onInit(){const{hasEntranceAnimation:t}=this.getSettings("constants");super.onInit(...arguments),this.clickTrackingHandler=new a.default({$element:this.$element});new o.default(this.$element).maybeMoveToTop(),this.elements.ctaButton&&this.elements.ctaButton.classList.contains(t)&&this.initEntranceAnimation(),this.initDefaultState(),this.setupInnerContainer()}}e.default=FloatingBarsHandler}}]);