/*! elementor - v3.31.0 - 27-08-2025 */
/*! For license information please see element-manager-admin.min.js.LICENSE.txt */
(()=>{var r={2214:r=>{"use strict";r.exports=wp.components},7470:(r,l,u)=>{"use strict";var s=u(75206);l.createRoot=s.createRoot,l.hydrateRoot=s.hydrateRoot},9535:(r,l,u)=>{var s=u(89736);function _regenerator(){var l,u,c="function"==typeof Symbol?Symbol:{},p=c.iterator||"@@iterator",m=c.toStringTag||"@@toStringTag";function i(r,c,p,m){var h=c&&c.prototype instanceof Generator?c:Generator,y=Object.create(h.prototype);return s(y,"_invoke",function(r,s,c){var p,m,h,y=0,_=c||[],x=!1,v={p:0,n:0,v:l,a:d,f:d.bind(l,4),d:function d(r,u){return p=r,m=0,h=l,v.n=u,g}};function d(r,s){for(m=r,h=s,u=0;!x&&y&&!c&&u<_.length;u++){var c,p=_[u],b=v.p,w=p[2];r>3?(c=w===s)&&(h=p[(m=p[4])?5:(m=3,3)],p[4]=p[5]=l):p[0]<=b&&((c=r<2&&b<p[1])?(m=0,v.v=s,v.n=p[1]):b<w&&(c=r<3||p[0]>s||s>w)&&(p[4]=r,p[5]=s,v.n=w,m=0))}if(c||r>1)return g;throw x=!0,s}return function(c,_,b){if(y>1)throw TypeError("Generator is already running");for(x&&1===_&&d(_,b),m=_,h=b;(u=m<2?l:h)||!x;){p||(m?m<3?(m>1&&(v.n=-1),d(m,h)):v.n=h:v.v=h);try{if(y=2,p){if(m||(c="next"),u=p[c]){if(!(u=u.call(p,h)))throw TypeError("iterator result is not an object");if(!u.done)return u;h=u.value,m<2&&(m=0)}else 1===m&&(u=p.return)&&u.call(p),m<2&&(h=TypeError("The iterator does not provide a '"+c+"' method"),m=1);p=l}else if((u=(x=v.n<0)?h:r.call(s,v))!==g)break}catch(r){p=l,m=1,h=r}finally{y=1}}return{value:u,done:x}}}(r,p,m),!0),y}var g={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}u=Object.getPrototypeOf;var h=[][p]?u(u([][p]())):(s(u={},p,function(){return this}),u),y=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(h);function f(r){return Object.setPrototypeOf?Object.setPrototypeOf(r,GeneratorFunctionPrototype):(r.__proto__=GeneratorFunctionPrototype,s(r,m,"GeneratorFunction")),r.prototype=Object.create(y),r}return GeneratorFunction.prototype=GeneratorFunctionPrototype,s(y,"constructor",GeneratorFunctionPrototype),s(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",s(GeneratorFunctionPrototype,m,"GeneratorFunction"),s(y),s(y,m,"Generator"),s(y,p,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),(r.exports=_regenerator=function _regenerator(){return{w:i,m:f}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regenerator,r.exports.__esModule=!0,r.exports.default=r.exports},10564:r=>{function _typeof(l){return r.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},r.exports.__esModule=!0,r.exports.default=r.exports,_typeof(l)}r.exports=_typeof,r.exports.__esModule=!0,r.exports.default=r.exports},10906:(r,l,u)=>{var s=u(91819),c=u(20365),p=u(37744),m=u(78687);r.exports=function _toConsumableArray(r){return s(r)||c(r)||p(r)||m()},r.exports.__esModule=!0,r.exports.default=r.exports},11018:r=>{r.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},11327:(r,l,u)=>{var s=u(10564).default;r.exports=function toPrimitive(r,l){if("object"!=s(r)||!r)return r;var u=r[Symbol.toPrimitive];if(void 0!==u){var c=u.call(r,l||"default");if("object"!=s(c))return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===l?String:Number)(r)},r.exports.__esModule=!0,r.exports.default=r.exports},12470:r=>{"use strict";r.exports=wp.i18n},18821:(r,l,u)=>{var s=u(70569),c=u(65474),p=u(37744),m=u(11018);r.exports=function _slicedToArray(r,l){return s(r)||c(r,l)||p(r,l)||m()},r.exports.__esModule=!0,r.exports.default=r.exports},20365:r=>{r.exports=function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)},r.exports.__esModule=!0,r.exports.default=r.exports},33929:(r,l,u)=>{var s=u(67114),c=u(89736);r.exports=function AsyncIterator(r,l){function n(u,c,p,m){try{var g=r[u](c),h=g.value;return h instanceof s?l.resolve(h.v).then(function(r){n("next",r,p,m)},function(r){n("throw",r,p,m)}):l.resolve(h).then(function(r){g.value=r,p(g)},function(r){return n("throw",r,p,m)})}catch(r){m(r)}}var u;this.next||(c(AsyncIterator.prototype),c(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),c(this,"_invoke",function(r,s,c){function f(){return new l(function(l,u){n(r,c,l,u)})}return u=u?u.then(f,f):f()},!0)},r.exports.__esModule=!0,r.exports.default=r.exports},37744:(r,l,u)=>{var s=u(78113);r.exports=function _unsupportedIterableToArray(r,l){if(r){if("string"==typeof r)return s(r,l);var u={}.toString.call(r).slice(8,-1);return"Object"===u&&r.constructor&&(u=r.constructor.name),"Map"===u||"Set"===u?Array.from(r):"Arguments"===u||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(u)?s(r,l):void 0}},r.exports.__esModule=!0,r.exports.default=r.exports},41594:r=>{"use strict";r.exports=React},45498:(r,l,u)=>{var s=u(10564).default,c=u(11327);r.exports=function toPropertyKey(r){var l=c(r,"string");return"symbol"==s(l)?l:l+""},r.exports.__esModule=!0,r.exports.default=r.exports},46313:(r,l,u)=>{var s=u(9535),c=u(33929);r.exports=function _regeneratorAsyncGen(r,l,u,p,m){return new c(s().w(r,l,u,p),m||Promise)},r.exports.__esModule=!0,r.exports.default=r.exports},49905:r=>{"use strict";r.exports=wp.domReady},53051:(r,l,u)=>{var s=u(67114),c=u(9535),p=u(62507),m=u(46313),g=u(33929),h=u(95315),y=u(66961);function _regeneratorRuntime(){"use strict";var l=c(),u=l.m(_regeneratorRuntime),_=(Object.getPrototypeOf?Object.getPrototypeOf(u):u.__proto__).constructor;function n(r){var l="function"==typeof r&&r.constructor;return!!l&&(l===_||"GeneratorFunction"===(l.displayName||l.name))}var x={throw:1,return:2,break:3,continue:3};function a(r){var l,u;return function(s){l||(l={stop:function stop(){return u(s.a,2)},catch:function _catch(){return s.v},abrupt:function abrupt(r,l){return u(s.a,x[r],l)},delegateYield:function delegateYield(r,c,p){return l.resultName=c,u(s.d,y(r),p)},finish:function finish(r){return u(s.f,r)}},u=function t(r,u,c){s.p=l.prev,s.n=l.next;try{return r(u,c)}finally{l.next=s.n}}),l.resultName&&(l[l.resultName]=s.v,l.resultName=void 0),l.sent=s.v,l.next=s.n;try{return r.call(this,l)}finally{s.p=l.prev,s.n=l.next}}}return(r.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(r,u,s,c){return l.w(a(r),u,s,c&&c.reverse())},isGeneratorFunction:n,mark:l.m,awrap:function awrap(r,l){return new s(r,l)},AsyncIterator:g,async:function async(r,l,u,s,c){return(n(l)?m:p)(a(r),l,u,s,c)},keys:h,values:y}},r.exports.__esModule=!0,r.exports.default=r.exports)()}r.exports=_regeneratorRuntime,r.exports.__esModule=!0,r.exports.default=r.exports},53630:(r,l,u)=>{"use strict";var s=u(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.App=void 0;var c=s(u(41594)),p=s(u(61790)),m=s(u(10906)),g=s(u(85707)),h=s(u(58155)),y=s(u(18821)),_=u(91003),x=u(2214),v=u(12470),b=u(77886),w=u(99397),E=u(67748);function ownKeys(r,l){var u=Object.keys(r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(r);l&&(s=s.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),u.push.apply(u,s)}return u}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var u=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(u),!0).forEach(function(l){(0,g.default)(r,l,u[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(u)):ownKeys(Object(u)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(u,l))})}return r}l.App=function App(){var r=(0,_.useState)(!0),l=(0,y.default)(r,2),u=l[0],s=l[1],g=(0,_.useState)(""),S=(0,y.default)(g,2),k=S[0],C=S[1],O=(0,_.useState)([]),j=(0,y.default)(O,2),P=j[0],R=j[1],M=(0,_.useState)([]),A=(0,y.default)(M,2),T=A[0],N=A[1],F=(0,_.useState)([]),I=(0,y.default)(F,2),D=I[0],L=I[1],B=(0,_.useState)([]),U=(0,y.default)(B,2),G=U[0],W=U[1],H=(0,_.useState)({isLoading:!1,data:null}),q=(0,y.default)(H,2),z=q[0],V=q[1],$=(0,_.useState)([]),K=(0,y.default)($,2),X=K[0],Y=K[1],Z=(0,_.useState)("widget"),J=(0,y.default)(Z,2),Q=J[0],ee=J[1],te=(0,_.useState)("asc"),ne=(0,y.default)(te,2),re=ne[0],ae=ne[1],oe=(0,_.useState)(""),le=(0,y.default)(oe,2),ie=le[0],ue=le[1],se=(0,_.useState)("all"),ce=(0,y.default)(se,2),de=ce[0],fe=ce[1],pe=(0,_.useState)({isSaving:!1,isUnsavedChanges:!1}),me=(0,y.default)(pe,2),ge=me[0],he=me[1],ye=(0,_.useState)(!1),_e=(0,y.default)(ye,2),xe=_e[0],ve=_e[1],be=(0,_.useState)(!1),we=(0,y.default)(be,2),Ee=we[0],Se=we[1],ke=(0,_.useState)(null),Ce=(0,y.default)(ke,2),Oe=Ce[0],je=Ce[1],Pe=(0,_.useState)(null),Re=(0,y.default)(Pe,2),Me=Re[0],Ae=Re[1],Te=(0,_.useState)([]),Ne=(0,y.default)(Te,2),Fe=Ne[0],Ie=Ne[1],De=Fe.manager_permissions,Le=Fe.element_manager,Be=function getWidgetUsage(r){return z.data&&z.data.hasOwnProperty(r)?z.data[r]:0},Ue=(0,_.useMemo)(function(){var r=P.filter(function(r){return r.title.toLowerCase().includes(k.toLowerCase())});return""!==ie&&(r=r.filter(function(r){return r.plugin.toLowerCase()===ie.toLowerCase()})),"all"!==de&&(r=r.filter(function(r){return"active"===de?!X.includes(r.name):X.includes(r.name)})),r.sort(function(r,l){var u,s;return"widget"===Q&&(u=r.title,s=l.title),"usage"===Q&&(u=Be(r.name),s=Be(l.name)),u===s?0:"asc"===re?u<s?-1:1:u>s?-1:1}),r},[P,k,Q,re,ie,z,de,X]),Ge=function getSortingIndicatorClasses(r){return Q!==r?"":"asc"===re?"sorted asc":"sorted desc"},We=function onSortingClicked(r){Q===r?ae("asc"===re?"desc":"asc"):(ee(r),ae("asc"))},He=function(){var r=(0,h.default)(p.default.mark(function _callee(){return p.default.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return ve(!1),he(_objectSpread(_objectSpread({},ge),{},{isSaving:!0})),r.next=1,(0,w.saveDisabledWidgets)(X,Me);case 1:he(_objectSpread(_objectSpread({},ge),{},{isSaving:!1,isUnsavedChanges:!1})),Se(!0);case 2:case"end":return r.stop()}},_callee)}));return function onSaveClicked(){return r.apply(this,arguments)}}(),qe=function(){var r=(0,h.default)(p.default.mark(function _callee2(){var r;return p.default.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return V(_objectSpread(_objectSpread({},z),{},{isLoading:!0})),l.next=1,(0,w.getUsageWidgets)();case 1:r=l.sent,V({data:r,isLoading:!1}),ee("usage"),ae("desc");case 2:case"end":return l.stop()}},_callee2)}));return function onScanUsageElementsClicked(){return r.apply(this,arguments)}}(),ze=function UsageTimesColumn(r){var l=r.widgetName;return null!==z.data?c.default.createElement(c.default.Fragment,null,Be(l)," ",(0,v.__)("times","elementor")):z.isLoading?c.default.createElement(x.Spinner,null):c.default.createElement(x.Button,{onClick:qe,size:"small",variant:"secondary"},(0,v.__)("Show","elementor"))};return(0,_.useEffect)(function(){var r=function(){var r=(0,h.default)(p.default.mark(function _callee3(){var r,l,u,c;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return p.next=1,(0,w.getAdminAppData)();case 1:u=p.sent,je(u.notice_data),Y(u.disabled_elements),R(u.widgets),N(u.promotion_widgets),Ie(u.promotion_data),null!==(r=u.additional_data)&&void 0!==r&&r.roles&&W(u.additional_data.roles),null!==(l=u.additional_data)&&void 0!==l&&l.role_restrictions&&Ae(u.additional_data.role_restrictions),(c=u.plugins.map(function(r){return{label:r,value:r}})).unshift({label:(0,v.__)("All Plugins","elementor"),value:""}),L(c),s(!1);case 2:case"end":return p.stop()}},_callee3)}));return function onLoading(){return r.apply(this,arguments)}}();r()},[]),(0,_.useEffect)(function(){u||he(_objectSpread(_objectSpread({},ge),{},{isUnsavedChanges:!0}))},[X,Me]),(0,_.useEffect)(function(){var r=function handleBeforeUnload(r){r.preventDefault(),r.returnValue=""};return ge.isUnsavedChanges?window.addEventListener("beforeunload",r):window.removeEventListener("beforeunload",r),function(){window.removeEventListener("beforeunload",r)}},[ge.isUnsavedChanges]),u?c.default.createElement(x.Flex,{justify:"center",style:{margin:"100px"}},c.default.createElement(x.Spinner,{style:{height:"calc(4px * 20)",width:"calc(4px * 20)"}})):c.default.createElement(c.default.Fragment,null,c.default.createElement("p",{style:{marginBottom:"20px",maxWidth:"800px"}},(0,v.__)("Here's where you can fine-tune Elementor to your workflow. Disable elements you don't use for a cleaner interface, more focused creative experience, and improved performance.","elementor")," ",c.default.createElement("a",{href:"https://go.elementor.com/wp-dash-element-manager/",rel:"noreferrer",target:"_blank"},(0,v.__)("Learn More","elementor"))),!Oe.is_viewed&&c.default.createElement("p",null,c.default.createElement(x.Notice,{onRemove:function onRemove(){(0,w.markNoticeViewed)(Oe.notice_id,Oe.nonce),je(_objectSpread(_objectSpread({},Oe),{},{is_viewed:!0}))},status:"warning"},c.default.createElement("strong",null,(0,v.__)("Before you continue:","elementor"))," ",(0,v.__)("Deactivating widgets here will remove them from both the Elementor Editor and your website, which can cause changes to your overall layout, design and what visitors see.","elementor"))),c.default.createElement(x.Panel,null,c.default.createElement(x.PanelBody,null,c.default.createElement(x.Flex,{style:{position:"sticky",top:"32px",background:"rgb(255, 255, 255)",zIndex:10,padding:"20px 16px",boxShadow:"rgba(0, 0, 0, 0.15) 0 5px 10px 0",margin:"-16px -16px 24px"}},c.default.createElement(x.FlexItem,null,c.default.createElement(x.Flex,{align:"center"},c.default.createElement(x.SearchControl,{label:(0,v.__)("Search widgets","elementor"),value:k,size:"compact",style:{height:"40px",border:"1px solid rgba(30, 30, 30, 0.5)",background:"transparent"},__nextHasNoMarginBottom:!0,onChange:C}),c.default.createElement(x.FlexItem,{style:{maxWidth:"130px"}},c.default.createElement(x.SelectControl,{onChange:ue,size:"__unstable-large",__nextHasNoMarginBottom:!0,options:D})),c.default.createElement(x.FlexItem,{style:{maxWidth:"130px"}},c.default.createElement(x.SelectControl,{onChange:fe,size:"__unstable-large",__nextHasNoMarginBottom:!0,options:[{label:(0,v.__)("All Statuses","elementor"),value:"all"},{label:(0,v.__)("Active","elementor"),value:"active"},{label:(0,v.__)("Inactive","elementor"),value:"inactive"}]})),c.default.createElement("hr",{style:{height:"30px",margin:"0 5px",borderWidth:"0 1px 0 0",borderStyle:"solid",borderColor:"rgba(30, 30, 30, 0.5)"}}),c.default.createElement(x.ButtonGroup,null,c.default.createElement(x.Button,{variant:"secondary",style:{marginInlineEnd:"10px"},disabled:z.isLoading,isBusy:z.isLoading,onClick:qe},(0,v.__)("Scan Element Usage","elementor")),c.default.createElement(x.Button,{variant:"secondary",style:{marginInlineEnd:"10px"},onClick:function deactivateAllUnusedWidgets(){var r=P.filter(function(r){return!z.data.hasOwnProperty(r.name)||X.includes(r.name)});Y(r.map(function(r){return r.name}))},disabled:null===z.data},(0,v.__)("Deactivate Unused Elements","elementor")),c.default.createElement(x.Button,{variant:"secondary",disabled:!X.length,style:{marginInlineEnd:"10px"},onClick:function enableAllWidgets(){Y([])}},(0,v.__)("Enable All","elementor"))))),c.default.createElement(x.FlexItem,null,c.default.createElement(x.Button,{variant:"primary",disabled:ge.isSaving||!ge.isUnsavedChanges,isBusy:ge.isSaving,onClick:function onClick(){ve(!0)}},(0,v.__)("Save Changes","elementor")))),c.default.createElement(x.PanelRow,null,Ue.length?c.default.createElement("table",{className:"wp-list-table widefat fixed striped table-view-list"},c.default.createElement("thead",null,c.default.createElement("tr",null,c.default.createElement("th",{className:"manage-column sortable ".concat(Ge("widget"))},c.default.createElement(x.Button,{href:"#",onClick:function onClick(r){r.preventDefault(),We("widget")}},c.default.createElement("span",null,(0,v.__)("Element","elementor")),c.default.createElement("span",{className:"sorting-indicators"},c.default.createElement("span",{className:"sorting-indicator asc","aria-hidden":"true"}),c.default.createElement("span",{className:"sorting-indicator desc","aria-hidden":"true"})))),c.default.createElement("th",null,(0,v.__)("Status","elementor")),c.default.createElement("th",{className:"manage-column sortable ".concat(Ge("usage"))},c.default.createElement(x.Button,{href:"#",onClick:function onClick(r){r.preventDefault(),We("usage")}},c.default.createElement("span",null,(0,v.__)("Usage","elementor")),c.default.createElement("span",{className:"sorting-indicators"},c.default.createElement("span",{className:"sorting-indicator asc","aria-hidden":"true"}),c.default.createElement("span",{className:"sorting-indicator desc","aria-hidden":"true"})))),c.default.createElement("th",null,(0,v.__)("Plugin","elementor")),c.default.createElement("th",null,c.default.createElement(x.Flex,{justify:"flex-start",gap:0},c.default.createElement(x.FlexItem,null,(0,v.__)("Permission","elementor")),c.default.createElement(x.FlexItem,null,c.default.createElement(x.Tooltip,{placement:"top",delay:100,text:(0,v.__)("Choose which users will have access to each widget.","elementor")},c.default.createElement(x.Button,{icon:"info-outline",iconSize:16}))),null===Me&&c.default.createElement(x.FlexItem,{style:{marginInlineStart:"10px"}},c.default.createElement(b.UpgradeButton,{href:T.length?De.pro.url:De.advanced.url,size:"small",text:T.length?De.pro.text:De.advanced.text})))))),c.default.createElement("tbody",null,Ue.map(function(r){return c.default.createElement("tr",{key:r.name,"data-key-id":r.name},c.default.createElement("td",null,c.default.createElement("i",{style:{marginInlineEnd:"5px",marginInlineStart:"0",display:"inline-block"},className:"".concat(r.icon)})," ",r.title),c.default.createElement("td",null,c.default.createElement(x.ToggleControl,{checked:!X.includes(r.name),__nextHasNoMarginBottom:!0,onChange:function onChange(){X.includes(r.name)?Y(X.filter(function(l){return l!==r.name})):Y([].concat((0,m.default)(X),[r.name]))}})),c.default.createElement("td",null,c.default.createElement(ze,{widgetName:r.name})),c.default.createElement("td",null,r.plugin),c.default.createElement("td",null,null===Me||X.includes(r.name)?c.default.createElement(E.EditButtonDisabled,null):c.default.createElement(E.RolePermissions,{widgetName:r.name,roles:G,widgetsRoleRestrictions:Me,setWidgetsRoleRestrictions:Ae})))}))):c.default.createElement(c.default.Fragment,null,(0,v.__)("No elements found.","elementor"))),T.length>0&&c.default.createElement(c.default.Fragment,null,c.default.createElement(x.PanelRow,null,c.default.createElement(x.Flex,{style:{marginTop:"40px",marginBottom:"20px"}},c.default.createElement(x.FlexItem,null,c.default.createElement("h3",null,(0,v.__)("Elementor Pro Elements","elementor")),c.default.createElement("p",null,(0,v.__)("Unleash the full power of Elementor's features and web creation tools.","elementor"))),c.default.createElement(x.FlexItem,null,c.default.createElement(b.UpgradeButton,{href:Le.url,text:Le.text})))),c.default.createElement(x.PanelRow,null,c.default.createElement("table",{className:"wp-list-table widefat fixed striped table-view-list"},c.default.createElement("thead",null,c.default.createElement("tr",null,c.default.createElement("th",{className:"manage-column"},c.default.createElement("span",null,(0,v.__)("Element","elementor"))),c.default.createElement("th",null,(0,v.__)("Status","elementor")),c.default.createElement("th",null,(0,v.__)("Usage","elementor")),c.default.createElement("th",null,(0,v.__)("Plugin","elementor")),c.default.createElement("th",null,c.default.createElement(x.Flex,{justify:"flex-start"},c.default.createElement(x.FlexItem,null,(0,v.__)("Permission","elementor")),c.default.createElement(x.FlexItem,null,c.default.createElement(x.Tooltip,{placement:"top",delay:100,text:(0,v.__)("Choose which role will have access to a specific widget.","elementor")},c.default.createElement(x.Button,{icon:"info-outline"}))))))),c.default.createElement("tbody",null,T.map(function(r){return c.default.createElement("tr",{key:r.name},c.default.createElement("td",null,c.default.createElement("i",{style:{marginInlineEnd:"5px"},className:"".concat(r.icon)})," ",r.title),c.default.createElement("td",null,c.default.createElement(x.ToggleControl,{__nextHasNoMarginBottom:!0,checked:!1,disabled:!0})),c.default.createElement("td",null),c.default.createElement("td",null,(0,v.__)("Elementor Pro","elementor")),c.default.createElement("td",null,c.default.createElement(E.EditButtonDisabled,null)))}))))))),xe&&c.default.createElement(x.Modal,{title:(0,v.__)("Sure you want to save these changes?","elementor"),size:"small",isDismissible:!1,onRequestClose:function onRequestClose(){ve(!1)}},c.default.createElement("p",{style:{maxWidth:"400px",marginBlockEnd:"30px",marginBlockStart:"0"}},(0,v.__)("Turning widgets off will hide them from the editor panel, and can potentially affect your layout or front-end.","elementor"),c.default.createElement("span",{style:{display:"block",marginTop:"20px"}},(0,v.__)("If you’re adding widgets back in, enjoy them!","elementor"))),c.default.createElement(x.ButtonGroup,{style:{display:"flex",justifyContent:"flex-end",gap:"30px"}},c.default.createElement(x.Button,{variant:"link",onClick:function onClick(){ve(!1)}},(0,v.__)("Cancel","elementor")),c.default.createElement(x.Button,{variant:"primary",onClick:He},(0,v.__)("Save","elementor")))),c.default.createElement("div",{style:{position:"fixed",bottom:"40px",left:"50%",transform:"translateX(-50%)",display:Ee?"block":"none"}},c.default.createElement(x.Snackbar,{isDismissible:!0,status:"success",onRemove:function onRemove(){return Se(!1)}},(0,v.__)("We saved your changes.","elementor"))))}},58155:r=>{function asyncGeneratorStep(r,l,u,s,c,p,m){try{var g=r[p](m),h=g.value}catch(r){return void u(r)}g.done?l(h):Promise.resolve(h).then(s,c)}r.exports=function _asyncToGenerator(r){return function(){var l=this,u=arguments;return new Promise(function(s,c){var p=r.apply(l,u);function _next(r){asyncGeneratorStep(p,s,c,_next,_throw,"next",r)}function _throw(r){asyncGeneratorStep(p,s,c,_next,_throw,"throw",r)}_next(void 0)})}},r.exports.__esModule=!0,r.exports.default=r.exports},61790:(r,l,u)=>{var s=u(53051)();r.exports=s;try{regeneratorRuntime=s}catch(r){"object"==typeof globalThis?globalThis.regeneratorRuntime=s:Function("r","regeneratorRuntime = r")(s)}},62507:(r,l,u)=>{var s=u(46313);r.exports=function _regeneratorAsync(r,l,u,c,p){var m=s(r,l,u,c,p);return m.next().then(function(r){return r.done?r.value:m.next()})},r.exports.__esModule=!0,r.exports.default=r.exports},65474:r=>{r.exports=function _iterableToArrayLimit(r,l){var u=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=u){var s,c,p,m,g=[],h=!0,y=!1;try{if(p=(u=u.call(r)).next,0===l){if(Object(u)!==u)return;h=!1}else for(;!(h=(s=p.call(u)).done)&&(g.push(s.value),g.length!==l);h=!0);}catch(r){y=!0,c=r}finally{try{if(!h&&null!=u.return&&(m=u.return(),Object(m)!==m))return}finally{if(y)throw c}}return g}},r.exports.__esModule=!0,r.exports.default=r.exports},66961:(r,l,u)=>{var s=u(10564).default;r.exports=function _regeneratorValues(r){if(null!=r){var l=r["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],u=0;if(l)return l.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length))return{next:function next(){return r&&u>=r.length&&(r=void 0),{value:r&&r[u++],done:!r}}}}throw new TypeError(s(r)+" is not iterable")},r.exports.__esModule=!0,r.exports.default=r.exports},67114:r=>{r.exports=function _OverloadYield(r,l){this.v=r,this.k=l},r.exports.__esModule=!0,r.exports.default=r.exports},67748:(r,l,u)=>{"use strict";var s=u(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.RolePermissions=l.EditButtonDisabled=void 0;var c=s(u(41594)),p=s(u(85707)),m=u(2214),g=u(12470);function ownKeys(r,l){var u=Object.keys(r);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(r);l&&(s=s.filter(function(l){return Object.getOwnPropertyDescriptor(r,l).enumerable})),u.push.apply(u,s)}return u}function _objectSpread(r){for(var l=1;l<arguments.length;l++){var u=null!=arguments[l]?arguments[l]:{};l%2?ownKeys(Object(u),!0).forEach(function(l){(0,p.default)(r,l,u[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(u)):ownKeys(Object(u)).forEach(function(l){Object.defineProperty(r,l,Object.getOwnPropertyDescriptor(u,l))})}return r}var h=function RolesList(r){var l=r.roles,u=r.widgetRoleRestrictions,s=l.filter(function(r){return!u.includes(r.id)});return s.length?s.length===l.length?c.default.createElement(c.default.Fragment,null,"(",(0,g.__)("All Roles","elementor"),")"):c.default.createElement(c.default.Fragment,null,"(",s.map(function(r){return r.name}).join(", "),")"):c.default.createElement(c.default.Fragment,null,"(",(0,g.__)("Admin","elementor"),")")};l.RolePermissions=function RolePermissions(r){var l=r.roles,u=r.widgetName,s=r.widgetsRoleRestrictions,y=r.setWidgetsRoleRestrictions,_=s[u]||[];return c.default.createElement(c.default.Fragment,null,c.default.createElement(m.Dropdown,{className:"my-container-class-name",contentClassName:"my-dropdown-content-classname",popoverProps:{placement:"bottom-start"},renderToggle:function renderToggle(r){var u=r.isOpen,s=r.onToggle;return c.default.createElement(c.default.Fragment,null,c.default.createElement(m.Button,{variant:"link",onClick:s,"aria-expanded":u,style:{textDecoration:"none"}},(0,g.__)("Edit","elementor"))," ",c.default.createElement("span",{style:{color:"var(--e-a-color-txt-muted)"}},c.default.createElement(h,{roles:l,widgetRoleRestrictions:_})))},renderContent:function renderContent(){var r=l.every(function(r){return!_.includes(r.id)}),g=!r&&l.some(function(r){return!_.includes(r.id)});return c.default.createElement("div",{style:{minWidth:"150px",paddingInline:"10px",paddingBlockStart:"10px"}},c.default.createElement(m.CheckboxControl,{checked:r,indeterminate:g,label:"All",onChange:function onChange(r){y(_objectSpread(_objectSpread({},s),{},r?(0,p.default)({},u,[]):(0,p.default)({},u,l.map(function(r){return r.id}))))}}),l.map(function(r){return c.default.createElement("div",{key:r.id},c.default.createElement(m.CheckboxControl,{checked:!_.includes(r.id),label:r.name,onChange:function onChange(){!function toggleRoleRestrictions(r,l,u,s){var c=u[r]||[];c.includes(l)?c.splice(c.indexOf(l),1):c.push(l),s(_objectSpread(_objectSpread({},u),{},(0,p.default)({},r,c)))}(u,r.id,s,y)}}))}))}}))},l.EditButtonDisabled=function EditButtonDisabled(){return c.default.createElement(c.default.Fragment,null,c.default.createElement(m.Button,{variant:"link",disabled:!0,style:{textDecoration:"none"}},(0,g.__)("Edit","elementor")))}},70569:r=>{r.exports=function _arrayWithHoles(r){if(Array.isArray(r))return r},r.exports.__esModule=!0,r.exports.default=r.exports},75206:r=>{"use strict";r.exports=ReactDOM},77886:(r,l,u)=>{"use strict";var s=u(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.UpgradeButton=void 0;var c=s(u(41594)),p=s(u(78304)),m=u(2214);l.UpgradeButton=function UpgradeButton(r){return c.default.createElement(m.Button,(0,p.default)({},r,{variant:"primary",target:"_blank",rel:"noreferrer",style:{background:"var(--e-a-btn-bg-accent, #93003f)"}}))}},78113:r=>{r.exports=function _arrayLikeToArray(r,l){(null==l||l>r.length)&&(l=r.length);for(var u=0,s=Array(l);u<l;u++)s[u]=r[u];return s},r.exports.__esModule=!0,r.exports.default=r.exports},78304:r=>{function _extends(){return r.exports=_extends=Object.assign?Object.assign.bind():function(r){for(var l=1;l<arguments.length;l++){var u=arguments[l];for(var s in u)({}).hasOwnProperty.call(u,s)&&(r[s]=u[s])}return r},r.exports.__esModule=!0,r.exports.default=r.exports,_extends.apply(null,arguments)}r.exports=_extends,r.exports.__esModule=!0,r.exports.default=r.exports},78687:r=>{r.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},r.exports.__esModule=!0,r.exports.default=r.exports},85707:(r,l,u)=>{var s=u(45498);r.exports=function _defineProperty(r,l,u){return(l=s(l))in r?Object.defineProperty(r,l,{value:u,enumerable:!0,configurable:!0,writable:!0}):r[l]=u,r},r.exports.__esModule=!0,r.exports.default=r.exports},89736:r=>{function _regeneratorDefine(l,u,s,c){var p=Object.defineProperty;try{p({},"",{})}catch(l){p=0}r.exports=_regeneratorDefine=function regeneratorDefine(r,l,u,s){if(l)p?p(r,l,{value:u,enumerable:!s,configurable:!s,writable:!s}):r[l]=u;else{var c=function o(l,u){_regeneratorDefine(r,l,function(r){return this._invoke(l,u,r)})};c("next",0),c("throw",1),c("return",2)}},r.exports.__esModule=!0,r.exports.default=r.exports,_regeneratorDefine(l,u,s,c)}r.exports=_regeneratorDefine,r.exports.__esModule=!0,r.exports.default=r.exports},91003:(r,l,u)=>{"use strict";u.r(l),u.d(l,{Children:()=>s.Children,Component:()=>s.Component,Fragment:()=>s.Fragment,Platform:()=>x,PureComponent:()=>s.PureComponent,RawHTML:()=>RawHTML,StrictMode:()=>s.StrictMode,Suspense:()=>s.Suspense,cloneElement:()=>s.cloneElement,concatChildren:()=>concatChildren,createContext:()=>s.createContext,createElement:()=>s.createElement,createInterpolateElement:()=>create_interpolate_element,createPortal:()=>y.createPortal,createRef:()=>s.createRef,createRoot:()=>_.createRoot,findDOMNode:()=>y.findDOMNode,flushSync:()=>y.flushSync,forwardRef:()=>s.forwardRef,hydrate:()=>y.hydrate,hydrateRoot:()=>_.hydrateRoot,isEmptyElement:()=>isEmptyElement,isValidElement:()=>s.isValidElement,lazy:()=>s.lazy,memo:()=>s.memo,render:()=>y.render,renderToString:()=>N,startTransition:()=>s.startTransition,switchChildrenNodeName:()=>switchChildrenNodeName,unmountComponentAtNode:()=>y.unmountComponentAtNode,useCallback:()=>s.useCallback,useContext:()=>s.useContext,useDebugValue:()=>s.useDebugValue,useDeferredValue:()=>s.useDeferredValue,useEffect:()=>s.useEffect,useId:()=>s.useId,useImperativeHandle:()=>s.useImperativeHandle,useInsertionEffect:()=>s.useInsertionEffect,useLayoutEffect:()=>s.useLayoutEffect,useMemo:()=>s.useMemo,useReducer:()=>s.useReducer,useRef:()=>s.useRef,useState:()=>s.useState,useSyncExternalStore:()=>s.useSyncExternalStore,useTransition:()=>s.useTransition});var s=u(41594);let c,p,m,g;const h=/<(\/)?(\w+)\s*(\/)?>/g;function createFrame(r,l,u,s,c){return{element:r,tokenStart:l,tokenLength:u,prevOffset:s,leadingTextStart:c,children:[]}}const isValidConversionMap=r=>{const l="object"==typeof r,u=l&&Object.values(r);return l&&u.length&&u.every(r=>(0,s.isValidElement)(r))};function proceed(r){const l=function nextToken(){const r=h.exec(c);if(null===r)return["no-more-tokens"];const l=r.index,[u,s,p,m]=r,g=u.length;if(m)return["self-closed",p,l,g];if(s)return["closer",p,l,g];return["opener",p,l,g]}(),[u,y,_,x]=l,v=g.length,b=_>p?p:null;if(!r[y])return addText(),!1;switch(u){case"no-more-tokens":if(0!==v){const{leadingTextStart:r,tokenStart:l}=g.pop();m.push(c.substr(r,l))}return addText(),!1;case"self-closed":return 0===v?(null!==b&&m.push(c.substr(b,_-b)),m.push(r[y]),p=_+x,!0):(addChild(createFrame(r[y],_,x)),p=_+x,!0);case"opener":return g.push(createFrame(r[y],_,x,_+x,b)),p=_+x,!0;case"closer":if(1===v)return function closeOuterElement(r){const{element:l,leadingTextStart:u,prevOffset:p,tokenStart:h,children:y}=g.pop(),_=r?c.substr(p,r-p):c.substr(p);_&&y.push(_);null!==u&&m.push(c.substr(u,h-u));m.push((0,s.cloneElement)(l,null,...y))}(_),p=_+x,!0;const l=g.pop(),u=c.substr(l.prevOffset,_-l.prevOffset);l.children.push(u),l.prevOffset=_+x;const h=createFrame(l.element,l.tokenStart,l.tokenLength,_+x);return h.children=l.children,addChild(h),p=_+x,!0;default:return addText(),!1}}function addText(){const r=c.length-p;0!==r&&m.push(c.substr(p,r))}function addChild(r){const{element:l,tokenStart:u,tokenLength:p,prevOffset:m,children:h}=r,y=g[g.length-1],_=c.substr(y.prevOffset,u-y.prevOffset);_&&y.children.push(_),y.children.push((0,s.cloneElement)(l,null,...h)),y.prevOffset=m||u+p}const create_interpolate_element=(r,l)=>{if(c=r,p=0,m=[],g=[],h.lastIndex=0,!isValidConversionMap(l))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are React Elements");do{}while(proceed(l));return(0,s.createElement)(s.Fragment,null,...m)};function concatChildren(...r){return r.reduce((r,l,u)=>(s.Children.forEach(l,(l,c)=>{l&&"string"!=typeof l&&(l=(0,s.cloneElement)(l,{key:[u,c].join()})),r.push(l)}),r),[])}function switchChildrenNodeName(r,l){return r&&s.Children.map(r,(r,u)=>{if("string"==typeof r?.valueOf())return(0,s.createElement)(l,{key:u},r);const{children:c,...p}=r.props;return(0,s.createElement)(l,{key:u,...p},c)})}var y=u(75206),_=u(7470);const isEmptyElement=r=>"number"!=typeof r&&("string"==typeof r?.valueOf()||Array.isArray(r)?!r.length:!r),x={OS:"web",select:r=>"web"in r?r.web:r.default,isWeb:!0};function isObject(r){return"[object Object]"===Object.prototype.toString.call(r)}var __assign=function(){return __assign=Object.assign||function __assign(r){for(var l,u=1,s=arguments.length;u<s;u++)for(var c in l=arguments[u])Object.prototype.hasOwnProperty.call(l,c)&&(r[c]=l[c]);return r},__assign.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function lowerCase(r){return r.toLowerCase()}var v=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],b=/[^A-Z0-9]+/gi;function replace(r,l,u){return l instanceof RegExp?r.replace(l,u):l.reduce(function(r,l){return r.replace(l,u)},r)}function dotCase(r,l){return void 0===l&&(l={}),function noCase(r,l){void 0===l&&(l={});for(var u=l.splitRegexp,s=void 0===u?v:u,c=l.stripRegexp,p=void 0===c?b:c,m=l.transform,g=void 0===m?lowerCase:m,h=l.delimiter,y=void 0===h?" ":h,_=replace(replace(r,s,"$1\0$2"),p,"\0"),x=0,w=_.length;"\0"===_.charAt(x);)x++;for(;"\0"===_.charAt(w-1);)w--;return _.slice(x,w).split("\0").map(g).join(y)}(r,__assign({delimiter:"."},l))}function paramCase(r,l){return void 0===l&&(l={}),dotCase(r,__assign({delimiter:"-"},l))}const w=/[\u007F-\u009F "'>/="\uFDD0-\uFDEF]/;function escapeAmpersand(r){return r.replace(/&(?!([a-z0-9]+|#[0-9]+|#x[a-f0-9]+);)/gi,"&amp;")}function escapeLessThan(r){return r.replace(/</g,"&lt;")}function escapeAttribute(r){return function __unstableEscapeGreaterThan(r){return r.replace(/>/g,"&gt;")}(function escapeQuotationMark(r){return r.replace(/"/g,"&quot;")}(escapeAmpersand(r)))}function isValidAttributeName(r){return!w.test(r)}function RawHTML({children:r,...l}){let u="";return s.Children.toArray(r).forEach(r=>{"string"==typeof r&&""!==r.trim()&&(u+=r)}),(0,s.createElement)("div",{dangerouslySetInnerHTML:{__html:u},...l})}const{Provider:E,Consumer:S}=(0,s.createContext)(void 0),k=(0,s.forwardRef)(()=>null),C=new Set(["string","boolean","number"]),O=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),j=new Set(["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"]),P=new Set(["autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"]),R=new Set(["animation","animationIterationCount","baselineShift","borderImageOutset","borderImageSlice","borderImageWidth","columnCount","cx","cy","fillOpacity","flexGrow","flexShrink","floodOpacity","fontWeight","gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart","lineHeight","opacity","order","orphans","r","rx","ry","shapeImageThreshold","stopOpacity","strokeDasharray","strokeDashoffset","strokeMiterlimit","strokeOpacity","strokeWidth","tabSize","widows","x","y","zIndex","zoom"]);function hasPrefix(r,l){return l.some(l=>0===r.indexOf(l))}function isInternalAttribute(r){return"key"===r||"children"===r}function getNormalAttributeValue(r,l){return"style"===r?function renderStyle(r){if(!function isPlainObject(r){var l,u;return!1!==isObject(r)&&(void 0===(l=r.constructor)||!1!==isObject(u=l.prototype)&&!1!==u.hasOwnProperty("isPrototypeOf"))}(r))return r;let l;for(const u in r){const s=r[u];if(null==s)continue;l?l+=";":l="";l+=getNormalStylePropertyName(u)+":"+getNormalStylePropertyValue(u,s)}return l}(l):l}const M=["accentHeight","alignmentBaseline","arabicForm","baselineShift","capHeight","clipPath","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","dominantBaseline","enableBackground","fillOpacity","fillRule","floodColor","floodOpacity","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","horizAdvX","horizOriginX","imageRendering","letterSpacing","lightingColor","markerEnd","markerMid","markerStart","overlinePosition","overlineThickness","paintOrder","panose1","pointerEvents","renderingIntent","shapeRendering","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","textAnchor","textDecoration","textRendering","underlinePosition","underlineThickness","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","vHanging","vIdeographic","vMathematical","vectorEffect","vertAdvY","vertOriginX","vertOriginY","wordSpacing","writingMode","xmlnsXlink","xHeight"].reduce((r,l)=>(r[l.toLowerCase()]=l,r),{}),A=["allowReorder","attributeName","attributeType","autoReverse","baseFrequency","baseProfile","calcMode","clipPathUnits","contentScriptType","contentStyleType","diffuseConstant","edgeMode","externalResourcesRequired","filterRes","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","suppressContentEditableWarning","suppressHydrationWarning","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector"].reduce((r,l)=>(r[l.toLowerCase()]=l,r),{}),T=["xlink:actuate","xlink:arcrole","xlink:href","xlink:role","xlink:show","xlink:title","xlink:type","xml:base","xml:lang","xml:space","xmlns:xlink"].reduce((r,l)=>(r[l.replace(":","").toLowerCase()]=l,r),{});function getNormalAttributeName(r){switch(r){case"htmlFor":return"for";case"className":return"class"}const l=r.toLowerCase();return A[l]?A[l]:M[l]?paramCase(M[l]):T[l]?T[l]:l}function getNormalStylePropertyName(r){return r.startsWith("--")?r:hasPrefix(r,["ms","O","Moz","Webkit"])?"-"+paramCase(r):paramCase(r)}function getNormalStylePropertyValue(r,l){return"number"!=typeof l||0===l||R.has(r)?l:l+"px"}function renderElement(r,l,u={}){if(null==r||!1===r)return"";if(Array.isArray(r))return renderChildren(r,l,u);switch(typeof r){case"string":return function escapeHTML(r){return escapeLessThan(escapeAmpersand(r))}(r);case"number":return r.toString()}const{type:c,props:p}=r;switch(c){case s.StrictMode:case s.Fragment:return renderChildren(p.children,l,u);case RawHTML:const{children:r,...c}=p;return renderNativeComponent(Object.keys(c).length?"div":null,{...c,dangerouslySetInnerHTML:{__html:r}},l,u)}switch(typeof c){case"string":return renderNativeComponent(c,p,l,u);case"function":return c.prototype&&"function"==typeof c.prototype.render?function renderComponent(r,l,u,s={}){const c=new r(l,s);"function"==typeof c.getChildContext&&Object.assign(s,c.getChildContext());const p=renderElement(c.render(),u,s);return p}(c,p,l,u):renderElement(c(p,u),l,u)}switch(c&&c.$$typeof){case E.$$typeof:return renderChildren(p.children,p.value,u);case S.$$typeof:return renderElement(p.children(l||c._currentValue),l,u);case k.$$typeof:return renderElement(c.render(p),l,u)}return""}function renderNativeComponent(r,l,u,s={}){let c="";if("textarea"===r&&l.hasOwnProperty("value")){c=renderChildren(l.value,u,s);const{value:r,...p}=l;l=p}else l.dangerouslySetInnerHTML&&"string"==typeof l.dangerouslySetInnerHTML.__html?c=l.dangerouslySetInnerHTML.__html:void 0!==l.children&&(c=renderChildren(l.children,u,s));if(!r)return c;const p=function renderAttributes(r){let l="";for(const u in r){const s=getNormalAttributeName(u);if(!isValidAttributeName(s))continue;let c=getNormalAttributeValue(u,r[u]);if(!C.has(typeof c))continue;if(isInternalAttribute(u))continue;const p=j.has(s);if(p&&!1===c)continue;const m=p||hasPrefix(u,["data-","aria-"])||P.has(s);("boolean"!=typeof c||m)&&(l+=" "+s,p||("string"==typeof c&&(c=escapeAttribute(c)),l+='="'+c+'"'))}return l}(l);return O.has(r)?"<"+r+p+"/>":"<"+r+p+">"+c+"</"+r+">"}function renderChildren(r,l,u={}){let s="";r=Array.isArray(r)?r:[r];for(let c=0;c<r.length;c++){s+=renderElement(r[c],l,u)}return s}const N=renderElement},91819:(r,l,u)=>{var s=u(78113);r.exports=function _arrayWithoutHoles(r){if(Array.isArray(r))return s(r)},r.exports.__esModule=!0,r.exports.default=r.exports},95315:r=>{r.exports=function _regeneratorKeys(r){var l=Object(r),u=[];for(var s in l)u.unshift(s);return function e(){for(;u.length;)if((s=u.pop())in l)return e.value=s,e.done=!1,e;return e.done=!0,e}},r.exports.__esModule=!0,r.exports.default=r.exports},96784:r=>{r.exports=function _interopRequireDefault(r){return r&&r.__esModule?r:{default:r}},r.exports.__esModule=!0,r.exports.default=r.exports},99397:(r,l,u)=>{"use strict";var s=u(96784);Object.defineProperty(l,"__esModule",{value:!0}),l.saveDisabledWidgets=l.markNoticeViewed=l.getUsageWidgets=l.getAdminAppData=void 0;var c=s(u(61790)),p=s(u(58155));l.saveDisabledWidgets=function(){var r=(0,p.default)(c.default.mark(function _callee(r){var l,u,s,p=arguments;return c.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return l=p.length>1&&void 0!==p[1]?p[1]:{},c.prev=1,u={action:"elementor_element_manager_save_disabled_elements",nonce:eElementManagerConfig.nonce,widgets:JSON.stringify(r)},null!==l&&(u.elements_restriction=JSON.stringify(l)),c.next=2,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams(u)});case 2:c.next=4;break;case 3:c.prev=3,s=c.catch(1),console.error(s);case 4:case"end":return c.stop()}},_callee,null,[[1,3]])}));return function saveDisabledWidgets(l){return r.apply(this,arguments)}}(),l.getAdminAppData=function(){var r=(0,p.default)(c.default.mark(function _callee2(){var r,l,u;return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,s.next=1,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"elementor_element_manager_get_admin_app_data",nonce:eElementManagerConfig.nonce})});case 1:return r=s.sent,s.next=2,r.json();case 2:if(!(l=s.sent).success){s.next=3;break}return s.abrupt("return",l.data);case 3:s.next=5;break;case 4:s.prev=4,u=s.catch(0),console.error(u);case 5:case"end":return s.stop()}},_callee2,null,[[0,4]])}));return function getAdminAppData(){return r.apply(this,arguments)}}(),l.getUsageWidgets=function(){var r=(0,p.default)(c.default.mark(function _callee3(){var r,l,u;return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,s.next=1,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"elementor_element_manager_get_widgets_usage",nonce:eElementManagerConfig.nonce})});case 1:return r=s.sent,s.next=2,r.json();case 2:if(!(l=s.sent).success){s.next=3;break}return s.abrupt("return",l.data);case 3:s.next=5;break;case 4:s.prev=4,u=s.catch(0),console.error(u);case 5:case"end":return s.stop()}},_callee3,null,[[0,4]])}));return function getUsageWidgets(){return r.apply(this,arguments)}}(),l.markNoticeViewed=function(){var r=(0,p.default)(c.default.mark(function _callee4(r,l){var u;return c.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,s.next=1,fetch(eElementManagerConfig.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"elementor_set_admin_notice_viewed",notice_id:r,_wpnonce:l})});case 1:s.next=3;break;case 2:s.prev=2,u=s.catch(0),console.error(u);case 3:case"end":return s.stop()}},_callee4,null,[[0,2]])}));return function markNoticeViewed(l,u){return r.apply(this,arguments)}}()}},l={};function __webpack_require__(u){var s=l[u];if(void 0!==s)return s.exports;var c=l[u]={exports:{}};return r[u](c,c.exports,__webpack_require__),c.exports}__webpack_require__.d=(r,l)=>{for(var u in l)__webpack_require__.o(l,u)&&!__webpack_require__.o(r,u)&&Object.defineProperty(r,u,{enumerable:!0,get:l[u]})},__webpack_require__.o=(r,l)=>Object.prototype.hasOwnProperty.call(r,l),__webpack_require__.r=r=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},(()=>{"use strict";var r=__webpack_require__(96784),l=r(__webpack_require__(41594)),u=__webpack_require__(91003),s=r(__webpack_require__(49905)),c=__webpack_require__(53630);(0,s.default)(function(){var r=document.getElementById("elementor-element-manager-wrap");r&&(0,u.render)(l.default.createElement(c.App,null),r)})})()})();