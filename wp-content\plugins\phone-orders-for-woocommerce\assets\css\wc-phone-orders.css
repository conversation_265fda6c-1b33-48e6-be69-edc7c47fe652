@charset "UTF-8";

#woocommerce-order-items .inside {
    display: block !important;
    padding: 0;
}

#woocommerce-order-items .hndle, #woocommerce-order-items .handlediv {
    display: none;
}

#woocommerce-order-items .woocommerce_order_items_wrapper {
    margin: 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items {
    width: 100%;
    background: #fff;
}
#woocommerce-order-items .woocommerce_order_items_wrapper .wc-order-item-variable-attribute {
    display: flex;
    align-items: center;
    gap: .25rem;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items thead th {
    text-align: left;
    padding: 1em;
    font-weight: normal;
    color: #999;
    background: #f8f8f8;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items thead th.sortable {
    cursor: pointer;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items thead th .wc-arrow {
    float: right;
    position: relative;
    margin-right: -1em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td {
    padding: 1.5em 1em 1em;
    text-align: left;
    line-height: 1.5em;
    vertical-align: top;
    border-bottom: 1px solid #f8f8f8;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th textarea, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td textarea {
    width: 100%;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th select, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td select {
    width: 50%;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th input, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th textarea, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td input, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td textarea {
    font-size: 14px;
    padding: 4px;
    color: #555;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th:last-child, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td:last-child {
    padding-right: 2em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody th:first-child, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td:first-child {
    padding-left: 2em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody tr td {
    cursor: pointer;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody tr.selected {
    background: #F5EBF3;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody tr.selected td {
    border-color: #E6CCE1;
    opacity: 0.8;
}

/*#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody tr:last-child td {
    border-bottom: 1px solid #dfdfdf;
}*/

/*#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody tr:first-child td {
    border-top: 8px solid #f8f8f8;
}*/

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tbody#order_line_items tr:first-child td {
    border-top: none;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.thumb {
    text-align: left;
    width: 38px;
    padding-bottom: 1.5em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.thumb .wc-order-item-thumbnail {
    width: 38px;
    height: 38px;
    border: 2px solid #e8e8e8;
    background: #f8f8f8;
    color: #ccc;
    position: relative;
    font-size: 21px;
    display: block;
    text-align: center;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.thumb .wc-order-item-thumbnail:before {
    font-family: 'Dashicons';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    width: 38px;
    line-height: 38px;
    display: block;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.thumb .wc-order-item-thumbnail img {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    position: relative;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.name .wc-order-item-sku, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.name .wc-order-item-variation, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.name .wc-order-item-weight {
    display: block;
    margin-top: .5em;
    font-size: 0.92em !important;
    color: #888;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item {
    min-width: 200px;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .center,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .variation-id {
    text-align: center;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost {
    text-align: right;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost label {
    white-space: nowrap;
    color: #999;
    font-size: 0.833em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost label input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax label input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity label input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost label input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax label input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class label input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost label input {
    display: inline;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost input {
    width: 70px;
    vertical-align: middle;
    text-align: right;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost select,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax select,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity select,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost select,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax select,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class select,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost select {
    width: 85px;
    height: 26px;
    vertical-align: middle;
    font-size: 1em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input {
    display: inline-block;
    background: #fff;
    border: 1px solid #ddd;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
    margin: 1px 0;
    min-width: 80px;
    overflow: hidden;
    line-height: 1em;
    text-align: right;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input {
    width: 100%;
    box-sizing: border-box;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input label {
    font-size: 0.75em;
    padding: 4px 6px 0;
    color: #555;
    display: block;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input input {
    width: 100%;
    box-sizing: border-box;
    border: 0;
    box-shadow: none;
    margin: 0;
    padding: 0 6px 4px;
    color: #555;
    background: transparent;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input input::-webkit-input-placeholder,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input input::-webkit-input-placeholder,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input input::-webkit-input-placeholder,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input input::-webkit-input-placeholder,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input input::-webkit-input-placeholder,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input input::-webkit-input-placeholder,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input input::-webkit-input-placeholder {
    color: #ddd;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input:first-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input:first-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input:first-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input:first-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input:first-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input:first-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input:first-child {
    border-bottom: 1px dashed #ddd;
    background: #fff;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input:first-child label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input:first-child label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input:first-child label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input:first-child label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input:first-child label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input:first-child label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input:first-child label {
    color: #ccc;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .split-input div.input:first-child input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .split-input div.input:first-child input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .split-input div.input:first-child input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .split-input div.input:first-child input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .split-input div.input:first-child input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .split-input div.input:first-child input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .split-input div.input:first-child input {
    color: #ccc;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .view,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .view,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .view,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .view,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .view,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .view,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .view {
    white-space: nowrap;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .edit,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .edit,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .edit,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .edit,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .edit,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .edit,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .edit {
    text-align: right;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost small.times, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost del, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-taxes, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-discount, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax del,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity del,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost del,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax del,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class del,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost del,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-refund-fields {
    font-size: 0.92em !important;
    color: #888;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-taxes, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-refund-fields,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-taxes,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-refund-fields {
    margin: 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-taxes label, #woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-refund-fields label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-taxes label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-refund-fields label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-taxes label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-refund-fields label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-taxes label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-refund-fields label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-taxes label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-refund-fields label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-taxes label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-refund-fields label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-taxes label,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-refund-fields label {
    display: block;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class .wc-order-item-discount,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost .wc-order-item-discount {
    display: block;
    margin-top: .5em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .cost small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_cost small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .line_tax small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .tax_class small.times,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item_cost small.times {
    margin-right: .25em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity {
    text-align: center;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity input {
    text-align: center;
    width: 60px;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .quantity input.fractional-qty {
    width: 80px;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items span.subtotal {
    opacity: 0.5;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.tax_class,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items th.tax_class {
    text-align: left;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .calculated {
    border-color: #ae8ca2;
    border-style: dotted;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta {
    width: 100%;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta {
    margin: .5em 0 0 0;
    font-size: 0.92em !important;
    color: #888;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr th,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr th {
    border: 0;
    padding: 0 4px .5em 0;
    line-height: 1.5em;
    width: 20%;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr td,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr td {
    padding: 0 4px .5em 0;
    border: 0;
    line-height: 1.5em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr td input,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr td input {
    width: 100%;
    margin: 0;
    position: relative;
    border-bottom: 0;
    box-shadow: none;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr td textarea,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr td textarea {
    width: 100%;
    height: 4em;
    margin: 0;
    box-shadow: none;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr td input:focus + textarea,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr td input:focus + textarea {
    border-top-color: #999;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr td p,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr td p {
    margin: 0 0 .5em;
    line-height: 1.5em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.meta tr td p:last-child,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items table.display_meta tr td p:last-child {
    margin: 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .refund_by {
    border-bottom: 1px dotted #999;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.fee .thumb div {
    display: block;
    text-indent: -9999px;
    position: relative;
    height: 1em;
    width: 1em;
    font-size: 1.5em;
    line-height: 1em;
    vertical-align: middle;
    margin: 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.fee .thumb div:before {
    font-family: 'WooCommerce';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    color: #ccc;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.refund .thumb div {
    display: block;
    text-indent: -9999px;
    position: relative;
    height: 1em;
    width: 1em;
    font-size: 1.5em;
    line-height: 1em;
    vertical-align: middle;
    margin: 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.refund .thumb div:before {
    font-family: 'WooCommerce';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    color: #ccc;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.shipping .thumb div {
    display: block;
    text-indent: -9999px;
    position: relative;
    height: 1em;
    width: 1em;
    font-size: 1.5em;
    line-height: 1em;
    vertical-align: middle;
    margin: 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.shipping .thumb div:before {
    font-family: 'WooCommerce';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    color: #ccc;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.shipping .shipping_method_name,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr.shipping .shipping_method {
    width: 100%;
    margin: 0 0 .5em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items th.line_tax {
    white-space: nowrap;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items th.line_tax .delete-order-tax,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.line_tax .delete-order-tax {
    display: block;
    text-indent: -9999px;
    position: relative;
    height: 1em;
    width: 1em;
    float: right;
    font-size: 14px;
    visibility: hidden;
    margin: 3px -18px 0 0;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items th.line_tax .delete-order-tax:before,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.line_tax .delete-order-tax:before {
    font-family: 'Dashicons';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    color: #999;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items th.line_tax .delete-order-tax:hover:before,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.line_tax .delete-order-tax:hover:before {
    color: #a00;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items th.line_tax:hover .delete-order-tax,
#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td.line_tax:hover .delete-order-tax {
    visibility: visible;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items small.refunded {
    display: block;
    color: #a00;
    white-space: nowrap;
    margin-top: .5em;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items small.refunded:before {
    font-family: 'Dashicons';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    position: relative;
    top: auto;
    left: auto;
    margin: -1px 4px 0 0;
    vertical-align: middle;
    line-height: 1em;
}

#woocommerce-order-items .wc-order-edit-line-item {
    padding-left: 0;
}

#woocommerce-order-items .wc-order-edit-line-item-actions {
    width: 44px;
    text-align: right;
    padding-left: 0;
    vertical-align: middle;
}

#woocommerce-order-items .wc-order-edit-line-item-actions a {
    color: #ccc;
    display: inline-block;
    cursor: pointer;
    padding: 0 0 .5em 0;
    margin: 0 0 0 12px;
    vertical-align: middle;
    text-decoration: none;
    text-decoration-color: transparent;
    line-height: 16px;
    width: 16px;
    overflow: hidden;
}

#woocommerce-order-items .wc-order-edit-line-item-actions a:before {
    margin: 0;
    padding: 0;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

#woocommerce-order-items .wc-order-edit-line-item-actions a:hover:before {
    color: #999;
}

#woocommerce-order-items .wc-order-edit-line-item-actions a:first-child {
    margin-left: 0;
}

#woocommerce-order-items .wc-order-edit-line-item-actions .edit-order-item:before {
    font-family: 'Dashicons';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    position: relative;
}

#woocommerce-order-items .wc-order-edit-line-item-actions .delete-order-item:before,
#woocommerce-order-items .wc-order-edit-line-item-actions .delete_refund:before {
    font-family: 'Dashicons';
    speak: none;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    margin: 0;
    text-indent: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    content: "";
    position: relative;
}

#woocommerce-order-items .wc-order-edit-line-item-actions .delete-order-item:hover:before,
#woocommerce-order-items .wc-order-edit-line-item-actions .delete_refund:hover:before {
    color: #a00;
}

#woocommerce-order-items tbody tr .wc-order-edit-line-item-actions {
    visibility: hidden;
}

#woocommerce-order-items tbody tr:hover .wc-order-edit-line-item-actions {
    visibility: visible;
}

#woocommerce-order-items .wc-order-totals .wc-order-edit-line-item-actions {
    width: 1.5em;
    visibility: visible !important;
}

#woocommerce-order-items .wc-order-totals .wc-order-edit-line-item-actions a {
    padding: 0;
}

#poststuff {
    max-width: 1200px;
    margin: 0 auto;
}

.select2-container .select2-selection--single {
    min-height: 32px;
    height: 32px;
    border-radius: 0;
    line-height: 1.5;
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    user-select: none;
    -webkit-user-select: none;
}

#woo-phone-orders .select2-container .select2-selection--single .select2-selection__arrow {
    height: 100%;
}

#woo-phone-orders .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 32px;
}

#woo-phone-orders .select2-results {
    overflow-y: hidden;
}

#search-items-box {
    padding: 12px;
}

#search-customer-box select,
#search-items-box select {
    width: 100%;
}

#woo-phone-orders #woocommerce-order-items .handlediv {
    display: block;
    width: auto;
}

#woo-phone-orders .link-add-custom-item {
    display: block;
    padding-top: 12px;
    text-decoration: none;
    text-decoration-color: transparent;
}

#woo-phone-orders #woocommerce-order-items tbody tr .wc-order-edit-line-item-actions {
    visibility: visible;
    padding: 6px;
}

#order_line_items .line_cost .view {
    padding-top: 6px;
}

#order_line_items .total, #order_line_items .total_with_tax {
    padding: 4px;
}

#order_line_items .item_cost input[type="text"] {
    height: 28px;
}

#order_line_items .item-msg {
    color: red;
    padding: 5px 0;
}

#woo-phone-orders form.custom-customer-form label {
    display: inline-block;
    width: 100px;
    vertical-align: baseline;
}

#woo-phone-orders form.custom-customer-form input {
    width: 300px;
}

#woo-phone-orders form.custom-customer-form > div {
    margin: 4px 0;
}

div#search-customer-box {
    padding: 0 0 2px;
}

#woo-phone-orders .clear-customer, #clear-phone-orders-errors {
    margin-top: 5px;
    text-decoration: none;
    text-decoration-color: transparent;
    display: block;
    font-size: 23px;
    text-align: center;
}

#woo-phone-orders .clear-customer:hover {
    text-decoration: none;
    text-decoration-color: transparent;
}

#woo-phone-orders .edit_address {
    width: 14px;
    height: 0;
    padding: 14px 0 0;
    margin: 0 0 0 6px;
    overflow: hidden;
    position: relative;
    color: #999;
    border: 0;
    float: right;
}

#woo-phone-orders .edit_address::after {
    font-family: Dashicons;
    content: "\f464";
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    vertical-align: top;
    line-height: 14px;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
}

#post-body #woocommerce-order-items .wc-order-totals {
    margin: 0;
    padding: 0;
    text-align: right;
    line-height: 26px;
    width: 100%;
}

#woocommerce-order-items .wc-order-totals .label-total {
    vertical-align: top;
}

#woocommerce-order-items .wc-order-totals .total {
    font-size: 1em !important;
    width: 10em;
    margin: 0 0 0 .5em;
    box-sizing: border-box;
}

#woocommerce-order-items .wc-order-totals .total {
    font-size: 1em !important;
    width: 10em;
    margin: 0 0 0 .5em;
    box-sizing: border-box;
}

#woocommerce-order-items .wc-order-totals .amount {
    font-weight: 700;
}

#woocommerce-order-items .amount {
    white-space: nowrap;
}

#woo-phone-orders h4 {
    font-size: 1em;
}

#woo-phone-orders .order-footer {
    padding: 16px 12px;
}

#woo-phone-orders .order-footer__note textarea {
    width: 100%;
    padding: 10px;
    border-radius: 2px;
}

#woo-phone-orders .order-actions {
    padding: 16px 12px;
    border-top: solid 1px #dfdfdf;
}

#shipping_method label {
    margin: 4px 0 0 4px;
    font-weight: normal;
}

#woo-phone-orders .total-shipping-label {
    display: block;
    color: #777;
    line-height: 12px;
    padding: 0 0 8px 0;
}

#woo-phone-orders .tax-form input[type="radio"],
#woo-phone-orders .tax-form label {
    margin: 0;
}

#woocommerce-order-items .wc-order__actions {
    width: 100%;
}

#woocommerce-order-items .wc-order__actions td {
    padding-bottom: 1em;
}

#woocommerce-order-items .wc-order__actions .description span {
    font-weight: bolder;
}

#woocommerce-order-items a[data-action="remove-tax"] {
    font-weight: normal;
}

#woocommerce-order-items .order-total-line,
#woocommerce-order-items .order-taxes-line,
#woocommerce-order-items .order-discount-line {
    color: #999;
}

#woocommerce-order-items .order-total-line--updated {
    color: #000;
}

#woo-phone-orders .updated.fade {
    opacity: initial;
    -webkit-transition: initial;
    -o-transition: initial;
    transition: initial;
}

#woo-phone-orders label,
.woo-phone-orders_modal label {
    font-weight: normal;
    margin-bottom: initial;
}

#woo-phone-orders input[type=checkbox],
#woo-phone-orders input[type=radio] {
    margin: initial;
}

#woo-phone-orders .discount-type-toggler label,
.woo-phone-orders_modal .discount-type-toggler label {
    width: 40px;
}

#woo-phone-orders .discount-type-toggler input,
.woo-phone-orders_modal .discount-type-toggler input {
    display: none;
}

#woo-phone-orders .disable-on-order {
    position: relative;
}

#woo-phone-orders .order-created .disable-on-order {
    user-select: none;
}

#woo-phone-orders .order-created .disable-on-order:after {
    content: ' ';
    position: absolute;
    top: 1px;
    right: 1px;
    bottom: 1px;
    left: 1px;
    background: rgba(255, 255, 255, 0.7);
    z-index: 1040;
}

#woo-phone-orders .order-created #woocommerce-order-items.disable-on-order:after {
    bottom: 61px;
}

#woo-phone-orders input[type=radio] {
    margin: 0;
}

#pay-modal .payment_box {
    padding: 5px 0;
}

.order-actions #clear-all {
    color: #ffffff;
    background-color: #d9534f;
    border-color: #d43f3a;
}

#phone-orders-errors .phone-orders-error {
    margin: 0;
    padding: 15px;
}

#clear-phone-orders-errors {
    float: none;
}

#edit-address-modal form select {
    height: 34px;
}

.option {
    width: 60%;
}

#phone-orders-app input[type="text"].option,
#phone-orders-app textarea.option,
#phone-orders-app select.option {
    width: 60%;
}

.form-table td:first-child {
    width: 25%;
}

td.total-value {
    width: 20%;
}

#wc-phone-orders-log .tablenav .tablenav-pages a, .tablenav-pages-navspan {
    display: inline;
}

#recalculate {
    margin-bottom: 10px;
}

#phone-orders-app .option_hours {
	width:3em;
}
#phone-orders-app .option_number {
	width:5em;
}

.modal-dialog.modal-xl {
    max-width: 80%;
}

#woo-phone-orders .cpf-data-on-cart,
#woo-phone-orders .cpf-img-on-cart {
    margin-bottom: .3em;
}

#woo-phone-orders .cpf-img-on-cart,
#woo-phone-orders .cpf-data-on-cart {
    display: block;
}

#woo-phone-orders .cpf-img-on-cart small,
#woo-phone-orders .cpf-data-on-cart small {
    font-size: 100%;
}

#phone-orders-app table.form-table {
    table-layout: auto;
}

#phone-orders-app #poststuff {
    max-width: 100%;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items tr td.line_total div {
    text-align: right !important;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items td:last-child {
    text-align: center;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item__wpo-readonly-child-item td:last-child {
    text-align: left;
}

#woocommerce-order-items .woocommerce_order_items_wrapper table.woocommerce_order_items .item__wpo-readonly-child-item .wc-order-item-missing-variation-attribute {
    margin: 5px 0;
}

#woocommerce-order-items .wc-order-edit-line-item-actions {
    display: inline-block;
}

#woo-phone-orders .wpo-search-options-block label {
    margin-bottom: 5px;
}

.show_cart_link_note {
    vertical-align: middle;
    color: red;
}

#phone-orders-app .tab-content {
    display: block;
}

#phone-orders-app .modal.fade.show {
    opacity: 1;
}

#phone-orders-app .modal-backdrop.show {
    opacity: .5;
}

#phone-orders-app .modal.show .modal-dialog {
    transform: translate(0);
}

#phone-orders-app .modal-dialog {
    position: relative;
    width: auto;
    margin: .5rem;
    pointer-events: none;
}

@media (min-width: 576px) {
    #phone-orders-app .modal-dialog {
	max-width: 500px;
	margin: 1.75rem auto;
    }
}

@media (min-width: 992px) {
    #phone-orders-app .modal-lg {
	max-width: 800px;
    }
}

#phone-orders-app .modal-content {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: .3rem;
    outline: 0;
}

@media (min-width: 768px) {
    #phone-orders-app .container {
	max-width: 720px;
    }
}

@media (min-width: 1200px) {
    #phone-orders-app .container {
	max-width: 1140px;
    }
}

#phone-orders-app .container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

#phone-orders-app .modal-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem;
}

#phone-orders-app .modal-header .close {
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
}

#phone-orders-app button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
}

#phone-orders-app .close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
}

#phone-orders-app .option_checkbox_label {
    margin-bottom: 0;
}

#phone-orders-app .modal-dialog.modal-xl {
    max-width: 80%;
}

#phone-orders-app .wpo-add-product-create-woocommerce-product {
    margin-top: 10px;
}

#phone-orders-app .wpo-add-product-create-woocommerce-product label {
    margin-bottom: 0;
}

#phone-orders-app .wc-order-totals .woocommerce-Price-amount {
	color: #999;
}

#phone-orders-app .wc-order-totals .total-value .woocommerce-Price-amount {
    color: #212529;
}

.multiselect .multiselect__input {
    border: none;
}

#phone-orders-app .form-check-inline,
.modal .form-check-inline {
    margin-right: 5px;
}

#phone-orders-app .wc-order__actions .btn + span > .btn,
#phone-orders-app .wc-order__actions span + .btn {
    margin-left: 5px;
}

.btn + .btn {
    margin-left: 5px;
}

#phone-orders-app .multiselect__content::-webkit-scrollbar {
    display: block;
    background-color: darkgrey;
}

#phone-orders-app .multiselect__content::-webkit-scrollbar-thumb {
    border-radius: 12px;
    background-color: grey;
}

#woocommerce-order-items .find-results-full-screen-mode .multiselect__content-wrapper .multiselect__content {
    overflow-y: scroll;
    overflow-x: hidden;
}

#phone-orders-app .option-number-addresses {
    width: 4em;
}

#phone-orders-app .fade {
    opacity: 1;
}

#phone-orders-app .fade:not(.show) {
    opacity: 0;
}

#phone-orders-app .btn.disabled,
#phone-orders-app .btn:disabled,
#phone-orders-app fieldset:disabled .btn {
    pointer-events: all;
    cursor: not-allowed;
    box-shadow: none;
}

.modal.show.modal-static .modal-dialog {
    transform: none;
}

#woocommerce-order-items .find-results-full-screen-mode__search__block .wpo-barcode-mode-alert {
    margin: 0;
}

#phone-orders-app .pro-features > * + a {
    margin-left: 10px;
}
