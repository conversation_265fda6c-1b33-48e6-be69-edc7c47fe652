/*! elementor - v3.31.0 - 27-08-2025 */
/*! For license information please see app.min.js.LICENSE.txt */
(()=>{var c,m,h={205:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(41621)),P=y(h(87861)),C=y(h(85707)),w=y(h(47483));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}function _superPropGet(c,m,h,y){var v=(0,E.default)((0,b.default)(1&y?c.prototype:c),m,h);return 2&y&&"function"==typeof v?function(c){return v.apply(h,c)}:v}var S=m.default=function(c){function Button(){return(0,v.default)(this,Button),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,Button,arguments)}return(0,P.default)(Button,c),(0,g.default)(Button,[{key:"getCssId",value:function getCssId(){return"eps-app-header-btn-"+_superPropGet(Button,"getCssId",this,3)([])}},{key:"getClassName",value:function getClassName(){return this.props.includeHeaderBtnClass?"eps-app__header-btn "+_superPropGet(Button,"getClassName",this,3)([]):_superPropGet(Button,"getClassName",this,3)([])}}])}(w.default);(0,C.default)(S,"defaultProps",Object.assign({},w.default.defaultProps,{hideText:!0,includeHeaderBtnClass:!0}))},496:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=InfoModalText;var g=v(h(41594)),_=h(79397),b=v(h(55725));function InfoModalText(c){return g.default.createElement(b.default,{variant:"sm",className:(0,_.arrayToClassName)(["e-app-import-export-info-modal__text",c.className])},c.children)}InfoModalText.propTypes={className:y.string,children:y.any.isRequired},InfoModalText.defaultProps={className:""}},632:(c,m,h)=>{"use strict";h.r(m),h.d(m,{CancelledError:()=>y.cc,InfiniteQueryObserver:()=>E.z,MutationCache:()=>P.q,MutationObserver:()=>C._,QueriesObserver:()=>b.T,QueryCache:()=>v.$,QueryClient:()=>g.E,QueryObserver:()=>_.$,dehydrate:()=>A.h,focusManager:()=>T.m,hashQueryKey:()=>D.Od,hydrate:()=>A.Q,isCancelledError:()=>y.wm,isError:()=>D.bJ,notifyManager:()=>S.j,onlineManager:()=>N.t,setLogger:()=>w.B});var y=h(81133),v=h(33382),g=h(39387),_=h(74342),b=h(89774),E=h(69046),P=h(41415),C=h(31571),w=h(8118),S=h(25800),T=h(91669),N=h(26434),D=h(6369),A=h(45206),W=h(61533),q={};for(const c in W)["default","CancelledError","QueryCache","QueryClient","QueryObserver","QueriesObserver","InfiniteQueryObserver","MutationCache","MutationObserver","setLogger","notifyManager","focusManager","onlineManager","hashQueryKey","isError","isCancelledError","dehydrate","hydrate"].indexOf(c)<0&&(q[c]=()=>W[c]);h.d(m,q)},2363:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DialogTitle;var g=v(h(41594)),_=v(h(85707)),b=v(h(78304)),E=v(h(85418));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function DialogTitle(c){return g.default.createElement(E.default,(0,b.default)({},c,{className:"eps-dialog__title ".concat(c.className)}))}DialogTitle.propTypes=_objectSpread(_objectSpread({},E.default.propTypes),{},{className:y.string}),DialogTitle.defaultProps=_objectSpread(_objectSpread({},E.default.propTypes),{},{variant:"h3",tag:"h3",className:""})},2526:(c,m,h)=>{"use strict";var y=h(62688),v=h(12470).__,g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ModalTip;var _=g(h(41594)),b=h(79397),E=g(h(85418)),P=g(h(55725));function ModalTip(c){return _.default.createElement("div",{className:(0,b.arrayToClassName)(["eps-modal__tip",c.className])},_.default.createElement(E.default,{variant:"h3",tag:"h3"},c.title),c.description&&_.default.createElement(P.default,{variant:"xs"},c.description))}ModalTip.propTypes={className:y.string,title:y.string,description:y.string},ModalTip.defaultProps={className:"",title:v("Tip","elementor")}},3073:(c,m)=>{"use strict";function _createForOfIteratorHelper(c,m){var h="undefined"!=typeof Symbol&&c[Symbol.iterator]||c["@@iterator"];if(!h){if(Array.isArray(c)||(h=function _unsupportedIterableToArray(c,m){if(c){if("string"==typeof c)return _arrayLikeToArray(c,m);var h={}.toString.call(c).slice(8,-1);return"Object"===h&&c.constructor&&(h=c.constructor.name),"Map"===h||"Set"===h?Array.from(c):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray(c,m):void 0}}(c))||m&&c&&"number"==typeof c.length){h&&(c=h);var y=0,v=function F(){};return{s:v,n:function n(){return y>=c.length?{done:!0}:{done:!1,value:c[y++]}},e:function e(c){throw c},f:v}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var g,_=!0,b=!1;return{s:function s(){h=h.call(c)},n:function n(){var c=h.next();return _=c.done,c},e:function e(c){b=!0,g=c},f:function f(){try{_||null==h.return||h.return()}finally{if(b)throw g}}}}function _arrayLikeToArray(c,m){(null==m||m>c.length)&&(m=c.length);for(var h=0,y=Array(m);h<m;h++)y[h]=c[h];return y}Object.defineProperty(m,"__esModule",{value:!0}),m.appsEventTrackingDispatch=void 0;m.appsEventTrackingDispatch=function appsEventTrackingDispatch(c,m){var h=function objectCreator(c,h){var y,v=_createForOfIteratorHelper(c);try{for(v.s();!(y=v.n()).done;){var g=y.value;m.hasOwnProperty(g)&&null!==m[g]&&(h[g]=m[g])}}catch(c){v.e(c)}finally{v.f()}return h},y=[],v=["layout","site_part","error","document_name","document_type","view_type_clicked","tag","sort_direction","sort_type","action","grid_location","kit_name","page_source","element_position","element","event_type","modal_type","method","status","step","item","category","element_location","search_term","section","site_area"],g={},_={};!function init(){h(v,_),h(y,g);var m=c.split("/");g.placement=m[0],g.event=m[1],Object.keys(_).length&&(g.details=_)}(),$e.run(c,g)}},3416:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Grid;var g=v(h(41594)),_=v(h(10906)),b=h(79397);function Grid(c){var m=["eps-grid",c.className].concat((0,_.default)(function getPropsClasses(c,m){var h=[];for(var y in c)if(m[y]){var v=isValidPropValue(m[y])?m[y]:"";h.push(getBaseClassName()+renderPropValueBrackets(c[y],v))}return h}({direction:"--direction{{ -VALUE }}",justify:"--justify{{ -VALUE }}",alignContent:"--align-content{{ -VALUE }}",alignItems:"--align-items{{ -VALUE }}",container:"-container",item:"-item",noWrap:"-container--no-wrap",wrapReverse:"-container--wrap-reverse",zeroMinWidth:"-item--zero-min-width",spacing:"-container--spacing",xs:"-item-xs{{ -VALUE }}",sm:"-item-sm{{ -VALUE }}",md:"-item-md{{ -VALUE }}",lg:"-item-lg{{ -VALUE }}",xl:"-item-xl{{ -VALUE }}",xxl:"-item-xxl{{ -VALUE }}"},c)));return g.default.createElement("div",{style:function getStyle(){return isValidPropValue(c.spacing)?{"--grid-spacing-gutter":(0,b.pxToRem)(c.spacing)}:{}}(),className:(0,b.arrayToClassName)(m)},c.children)}function renderPropValueBrackets(c,m){var h=c.match(/{{.*?}}/);if(h){var y=m?h[0].replace(/[{ }]/g,"").replace(/value/i,m):"";c=c.replace(h[0],y)}return c}function getBaseClassName(){return"eps-grid"}function isValidPropValue(c){return c&&"boolean"!=typeof c}h(28042),Grid.propTypes={className:y.string,direction:y.oneOf(["row","column","row-reverse","column-reverse"]),justify:y.oneOf(["start","center","end","space-between","space-evenly","space-around","stretch"]),alignContent:y.oneOf(["start","center","end","space-between","stretch"]),alignItems:y.oneOf(["start","center","end","baseline","stretch"]),container:y.bool,item:y.bool,noWrap:y.bool,wrapReverse:y.bool,zeroMinWidth:y.bool,spacing:y.number,xs:y.oneOfType([y.number,y.bool]),sm:y.oneOfType([y.number,y.bool]),md:y.oneOfType([y.number,y.bool]),lg:y.oneOfType([y.number,y.bool]),xl:y.oneOfType([y.number,y.bool]),xxl:y.oneOfType([y.number,y.bool]),children:y.any.isRequired},Grid.defaultProps={className:""}},3600:(c,m,h)=>{"use strict";function getHashPath(){const c=window.location.href,m=c.indexOf("#");return-1===m?"":c.substring(m+1)}h.r(m),h.d(m,{createHashSource:()=>createHashSource});let createHashSource=(c="/")=>({get location(){return{pathname:getHashPath(),search:""}},addEventListener(c,m){"popstate"===c&&window.addEventListener("hashchange",m)},removeEventListener(c,m){"popstate"===c&&window.addEventListener("hashchange",m)},history:{get entries(){return[{pathname:getHashPath(),search:""}]},get index(){return 0},get state(){},pushState(c,m,h){!function pushHashPath(c){window.location.hash="#"+c}(h)},replaceState(c,m,h){!function replaceHashPath(c){const m=window.location.href.indexOf("#");window.location.replace(window.location.href.slice(0,m>=0?m:0)+"#"+c)}(h)}}})},3826:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Select;var g=v(h(41594));function Select(c){return g.default.createElement("select",{multiple:c.multiple,className:c.className,value:c.value,onChange:c.onChange,ref:c.elRef,onClick:function onClick(){var m;return null===(m=c.onClick)||void 0===m?void 0:m.call(c)}},c.options.map(function(c){return c.children?g.default.createElement("optgroup",{label:c.label,key:c.label},c.children.map(function(c){return g.default.createElement("option",{key:c.value,value:c.value},c.label)})):g.default.createElement("option",{key:c.value,value:c.value},c.label)}))}Select.propTypes={className:y.string,onChange:y.func,options:y.array,elRef:y.object,multiple:y.bool,value:y.oneOfType([y.array,y.string]),onClick:y.func},Select.defaultProps={className:"",options:[]}},4380:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardHeader;var g=v(h(41594)),_=h(79397);function CardHeader(c){var m="eps-card__header",h=[m,c.className],y={};return Object.prototype.hasOwnProperty.call(c,"padding")&&(y["--eps-card-header-padding"]=(0,_.pxToRem)(c.padding),h.push(m+"--padding")),g.default.createElement("header",{className:(0,_.arrayToClassName)(h),style:y},c.children)}h(45302),CardHeader.propTypes={className:y.string,padding:y.string,passive:y.bool,active:y.bool,children:y.any.isRequired},CardHeader.defaultProps={className:""}},4815:()=>{},5195:()=>{},5299:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TableRow;var g=v(h(41594)),_=h(79397);function TableRow(c){return g.default.createElement("tr",{className:(0,_.arrayToClassName)(["eps-table__row",c.className])},c.children)}TableRow.propTypes={children:y.any.isRequired,className:y.string}},5853:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var v=y(h(40989)),g=y(h(39805)),_=y(h(85707)),b=y(h(47485)),E=y(h(59504)),P=y(h(71251));function _createForOfIteratorHelper(c,m){var h="undefined"!=typeof Symbol&&c[Symbol.iterator]||c["@@iterator"];if(!h){if(Array.isArray(c)||(h=function _unsupportedIterableToArray(c,m){if(c){if("string"==typeof c)return _arrayLikeToArray(c,m);var h={}.toString.call(c).slice(8,-1);return"Object"===h&&c.constructor&&(h=c.constructor.name),"Map"===h||"Set"===h?Array.from(c):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray(c,m):void 0}}(c))||m&&c&&"number"==typeof c.length){h&&(c=h);var y=0,v=function F(){};return{s:v,n:function n(){return y>=c.length?{done:!0}:{done:!1,value:c[y++]}},e:function e(c){throw c},f:v}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var g,_=!0,b=!1;return{s:function s(){h=h.call(c)},n:function n(){var c=h.next();return _=c.done,c},e:function e(c){b=!0,g=c},f:function f(){try{_||null==h.return||h.return()}finally{if(b)throw g}}}}function _arrayLikeToArray(c,m){(null==m||m>c.length)&&(m=c.length);for(var h=0,y=Array(m);h<m;h++)y[h]=c[h];return y}m.default=(0,v.default)(function ImportExport(){(0,g.default)(this,ImportExport),(0,_.default)(this,"routes",[{path:"/import/*",component:E.default},{path:"/export/*",component:P.default}]);var c,m=_createForOfIteratorHelper(this.routes);try{for(m.s();!(c=m.n()).done;){var h=c.value;b.default.addRoute(h)}}catch(c){m.e(c)}finally{m.f()}})},5912:()=>{},6056:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=HeaderButtons;var _=g(h(41594)),b=g(h(78304)),E=g(h(46361)),P=g(h(205));function HeaderButtons(c){var m=(0,E.default)(),h="";if(c.buttons.length){var v=c.buttons.map(function(c){return _.default.createElement(P.default,(0,b.default)({key:c.id},c))});h=_.default.createElement(_.default.Fragment,null,v)}return _.default.createElement("div",{className:"eps-app__header-buttons"},_.default.createElement(P.default,{text:y("Close","elementor"),icon:"eicon-close",className:"eps-app__close-button",onClick:function actionOnClose(){c.onClose?c.onClose():m.backToDashboard()}}),h)}HeaderButtons.propTypes={buttons:v.arrayOf(v.object),onClose:v.func},HeaderButtons.defaultProps={buttons:[]}},6369:(c,m,h)=>{"use strict";h.d(m,{BH:()=>replaceEqualDeep,Cp:()=>partialMatchKey,F$:()=>hashQueryKeyByOptions,G6:()=>scheduleMicrotask,GR:()=>parseMutationArgs,HN:()=>ensureQueryKeyArray,KK:()=>parseMutationFilterArgs,MK:()=>matchQuery,Od:()=>hashQueryKey,S$:()=>v,Zw:()=>functionalUpdate,_D:()=>replaceAt,bJ:()=>isError,b_:()=>parseFilterArgs,f8:()=>shallowEqualObjects,gn:()=>isValidTimeout,iv:()=>difference,j3:()=>timeUntilStale,jY:()=>getAbortController,lQ:()=>noop,nJ:()=>matchMutation,vh:()=>parseQueryArgs,yy:()=>sleep});var y=h(68102),v="undefined"==typeof window;function noop(){}function functionalUpdate(c,m){return"function"==typeof c?c(m):c}function isValidTimeout(c){return"number"==typeof c&&c>=0&&c!==1/0}function ensureQueryKeyArray(c){return Array.isArray(c)?c:[c]}function difference(c,m){return c.filter(function(c){return-1===m.indexOf(c)})}function replaceAt(c,m,h){var y=c.slice(0);return y[m]=h,y}function timeUntilStale(c,m){return Math.max(c+(m||0)-Date.now(),0)}function parseQueryArgs(c,m,h){return isQueryKey(c)?"function"==typeof m?(0,y.A)({},h,{queryKey:c,queryFn:m}):(0,y.A)({},m,{queryKey:c}):c}function parseMutationArgs(c,m,h){return isQueryKey(c)?"function"==typeof m?(0,y.A)({},h,{mutationKey:c,mutationFn:m}):(0,y.A)({},m,{mutationKey:c}):"function"==typeof c?(0,y.A)({},m,{mutationFn:c}):(0,y.A)({},c)}function parseFilterArgs(c,m,h){return isQueryKey(c)?[(0,y.A)({},m,{queryKey:c}),h]:[c||{},m]}function parseMutationFilterArgs(c,m){return isQueryKey(c)?(0,y.A)({},m,{mutationKey:c}):c}function matchQuery(c,m){var h=c.active,y=c.exact,v=c.fetching,g=c.inactive,_=c.predicate,b=c.queryKey,E=c.stale;if(isQueryKey(b))if(y){if(m.queryHash!==hashQueryKeyByOptions(b,m.options))return!1}else if(!partialMatchKey(m.queryKey,b))return!1;var P=function mapQueryStatusFilter(c,m){return!0===c&&!0===m||null==c&&null==m?"all":!1===c&&!1===m?"none":(null!=c?c:!m)?"active":"inactive"}(h,g);if("none"===P)return!1;if("all"!==P){var C=m.isActive();if("active"===P&&!C)return!1;if("inactive"===P&&C)return!1}return("boolean"!=typeof E||m.isStale()===E)&&(("boolean"!=typeof v||m.isFetching()===v)&&!(_&&!_(m)))}function matchMutation(c,m){var h=c.exact,y=c.fetching,v=c.predicate,g=c.mutationKey;if(isQueryKey(g)){if(!m.options.mutationKey)return!1;if(h){if(hashQueryKey(m.options.mutationKey)!==hashQueryKey(g))return!1}else if(!partialMatchKey(m.options.mutationKey,g))return!1}return("boolean"!=typeof y||"loading"===m.state.status===y)&&!(v&&!v(m))}function hashQueryKeyByOptions(c,m){return((null==m?void 0:m.queryKeyHashFn)||hashQueryKey)(c)}function hashQueryKey(c){return function stableValueHash(c){return JSON.stringify(c,function(c,m){return isPlainObject(m)?Object.keys(m).sort().reduce(function(c,h){return c[h]=m[h],c},{}):m})}(ensureQueryKeyArray(c))}function partialMatchKey(c,m){return partialDeepEqual(ensureQueryKeyArray(c),ensureQueryKeyArray(m))}function partialDeepEqual(c,m){return c===m||typeof c==typeof m&&(!(!c||!m||"object"!=typeof c||"object"!=typeof m)&&!Object.keys(m).some(function(h){return!partialDeepEqual(c[h],m[h])}))}function replaceEqualDeep(c,m){if(c===m)return c;var h=Array.isArray(c)&&Array.isArray(m);if(h||isPlainObject(c)&&isPlainObject(m)){for(var y=h?c.length:Object.keys(c).length,v=h?m:Object.keys(m),g=v.length,_=h?[]:{},b=0,E=0;E<g;E++){var P=h?E:v[E];_[P]=replaceEqualDeep(c[P],m[P]),_[P]===c[P]&&b++}return y===g&&b===y?c:_}return m}function shallowEqualObjects(c,m){if(c&&!m||m&&!c)return!1;for(var h in c)if(c[h]!==m[h])return!1;return!0}function isPlainObject(c){if(!hasObjectPrototype(c))return!1;var m=c.constructor;if(void 0===m)return!0;var h=m.prototype;return!!hasObjectPrototype(h)&&!!h.hasOwnProperty("isPrototypeOf")}function hasObjectPrototype(c){return"[object Object]"===Object.prototype.toString.call(c)}function isQueryKey(c){return"string"==typeof c||Array.isArray(c)}function isError(c){return c instanceof Error}function sleep(c){return new Promise(function(m){setTimeout(m,c)})}function scheduleMicrotask(c){Promise.resolve().then(c).catch(function(c){return setTimeout(function(){throw c})})}function getAbortController(){if("function"==typeof AbortController)return new AbortController}},6622:(c,m,h)=>{"use strict";h.d(m,{PL:()=>infiniteQueryBehavior,RQ:()=>hasPreviousPage,rB:()=>hasNextPage});var y=h(81133),v=h(6369);function infiniteQueryBehavior(){return{onFetch:function onFetch(c){c.fetchFn=function(){var m,h,g,_,b,E,P,C=null==(m=c.fetchOptions)||null==(h=m.meta)?void 0:h.refetchPage,w=null==(g=c.fetchOptions)||null==(_=g.meta)?void 0:_.fetchMore,S=null==w?void 0:w.pageParam,T="forward"===(null==w?void 0:w.direction),N="backward"===(null==w?void 0:w.direction),D=(null==(b=c.state.data)?void 0:b.pages)||[],A=(null==(E=c.state.data)?void 0:E.pageParams)||[],W=(0,v.jY)(),q=null==W?void 0:W.signal,U=A,Q=!1,K=c.options.queryFn||function(){return Promise.reject("Missing queryFn")},H=function buildNewPages(c,m,h,y){return U=y?[m].concat(U):[].concat(U,[m]),y?[h].concat(c):[].concat(c,[h])},G=function fetchPage(m,h,v,g){if(Q)return Promise.reject("Cancelled");if(void 0===v&&!h&&m.length)return Promise.resolve(m);var _={queryKey:c.queryKey,signal:q,pageParam:v,meta:c.meta},b=K(_),E=Promise.resolve(b).then(function(c){return H(m,v,c,g)});(0,y.dd)(b)&&(E.cancel=b.cancel);return E};if(D.length)if(T){var V=void 0!==S,Y=V?S:getNextPageParam(c.options,D);P=G(D,V,Y)}else if(N){var J=void 0!==S,Z=J?S:getPreviousPageParam(c.options,D);P=G(D,J,Z,!0)}else!function(){U=[];var m=void 0===c.options.getNextPageParam,h=!C||!D[0]||C(D[0],0,D);P=h?G([],m,A[0]):Promise.resolve(H([],A[0],D[0]));for(var y=function _loop(h){P=P.then(function(y){if(!C||!D[h]||C(D[h],h,D)){var v=m?A[h]:getNextPageParam(c.options,y);return G(y,m,v)}return Promise.resolve(H(y,A[h],D[h]))})},v=1;v<D.length;v++)y(v)}();else P=G([]);var ee=P.then(function(c){return{pages:c,pageParams:U}});return ee.cancel=function(){Q=!0,null==W||W.abort(),(0,y.dd)(P)&&P.cancel()},ee}}}}function getNextPageParam(c,m){return null==c.getNextPageParam?void 0:c.getNextPageParam(m[m.length-1],m)}function getPreviousPageParam(c,m){return null==c.getPreviousPageParam?void 0:c.getPreviousPageParam(m[0],m)}function hasNextPage(c,m){if(c.getNextPageParam&&Array.isArray(m)){var h=getNextPageParam(c,m);return null!=h&&!1!==h}}function hasPreviousPage(c,m){if(c.getPreviousPageParam&&Array.isArray(m)){var h=getPreviousPageParam(c,m);return null!=h&&!1!==h}}},6634:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=InfoModal,m.infoButtonProps=void 0;var _=g(h(41594)),b=g(h(78304)),E=g(h(61678)),P=g(h(34864)),C=g(h(64485)),w=g(h(496)),S=g(h(29994));h(64632);var T=m.infoButtonProps={id:"info-modal",className:"e-app-export-kit-information__info-icon",icon:"eicon-info-circle",text:y("Kit Info","elementor"),color:"secondary",hideText:!0};function InfoModal(c){var m={className:"e-app-import-export-info-modal",setShow:c.setShow,onOpen:c.onOpen,onClose:c.onClose,referrer:c.referrer};return Object.prototype.hasOwnProperty.call(c,"show")?m.show=c.show:m.toggleButtonProps=T,_.default.createElement(E.default,(0,b.default)({},m,{title:c.title}),c.children)}InfoModal.propTypes={show:v.bool,setShow:v.func,title:v.string,children:v.any.isRequired,onOpen:v.func,onClose:v.func,referrer:v.string},InfoModal.Section=P.default,InfoModal.Heading=C.default,InfoModal.Text=w.default,InfoModal.Tip=S.default},7221:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.PLUGINS_KEYS=void 0,m.default=function usePluginsData(c){return{pluginsData:(0,y.useMemo)(function(){return function getPluginsData(){if(!c)return[];var m=[],h=[];return c.forEach(function(c){switch(c.name){case v.ELEMENTOR:m.unshift(c);break;case v.ELEMENTOR_PRO:m.push(c);break;default:h.push(c)}}),m.concat(h)}()},[c])}};var y=h(41594),v=m.PLUGINS_KEYS=Object.freeze({ELEMENTOR:"Elementor",ELEMENTOR_PRO:"Elementor Pro"})},7229:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardBody;var g=v(h(41594)),_=h(79397);function CardBody(c){var m="eps-card__body",h=[m,c.className],y={};return Object.prototype.hasOwnProperty.call(c,"padding")&&(y["--eps-card-body-padding"]=(0,_.pxToRem)(c.padding),h.push(m+"--padding")),g.default.createElement("main",{className:(0,_.arrayToClassName)(h),style:y},c.children)}h(45302),CardBody.propTypes={className:y.string,padding:y.string,passive:y.bool,active:y.bool,children:y.any.isRequired},CardBody.defaultProps={className:""}},7248:()=>{},7470:(c,m,h)=>{"use strict";var y=h(75206);m.createRoot=y.createRoot,m.hydrateRoot=y.hydrateRoot},8102:(c,m)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;m.default={"import-kit":"/import/process"}},8118:(c,m,h)=>{"use strict";h.d(m,{B:()=>setLogger,t:()=>getLogger});var y=console;function getLogger(){return y}function setLogger(c){y=c}},8555:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DataTable;var g=v(h(41594)),_=h(79397),b=v(h(90878));function DataTable(c){var m=c.className,h=c.onSelect,y=c.initialSelected,v=c.initialDisabled,E=c.headers,P=c.layout,C=c.rows,w=c.selection;return g.default.createElement(b.default,{selection:w,onSelect:h,initialSelected:y,initialDisabled:v,className:(0,_.arrayToClassName)(["e-app-data-table",m])},!!E.length&&g.default.createElement(b.default.Head,null,g.default.createElement(b.default.Row,null,w&&g.default.createElement(b.default.Cell,{tag:"th"},g.default.createElement(b.default.Checkbox,{allSelectedCount:C.length})),E.map(function(c,m){return g.default.createElement(b.default.Cell,{tag:"th",colSpan:P&&P[m],key:m},c)}))),g.default.createElement(b.default.Body,null,C.map(function(c,m){return g.default.createElement(b.default.Row,{key:m},w&&g.default.createElement(b.default.Cell,{tag:"td"},g.default.createElement(b.default.Checkbox,{index:m})),c.map(function(c,m){return g.default.createElement(b.default.Cell,{tag:"td",colSpan:P&&P[m],key:m},c)}))})))}DataTable.propTypes={className:y.string,headers:y.array,rows:y.array,initialDisabled:y.array,initialSelected:y.array,layout:y.array,onSelect:y.func,selection:y.bool,withHeader:y.bool},DataTable.defaultProps={className:"",headers:[],rows:[],initialDisabled:[],initialSelected:[],selection:!1}},8882:(c,m,h)=>{"use strict";h.r(m),h.d(m,{Hydrate:()=>w.C,QueryClientProvider:()=>y.H,QueryErrorResetBoundary:()=>v.U,useHydrate:()=>w.L,useInfiniteQuery:()=>C.q,useIsFetching:()=>g.C,useIsMutating:()=>_.l,useMutation:()=>b.n,useQueries:()=>P.E,useQuery:()=>E.I,useQueryClient:()=>y.j,useQueryErrorResetBoundary:()=>v.h});h(47629),h(95239);var y=h(15292),v=h(24673),g=h(81921),_=h(54880),b=h(52786),E=h(56777),P=h(34009),C=h(53709),w=h(98353),S=h(76783),T={};for(const c in S)["default","QueryClientProvider","useQueryClient","QueryErrorResetBoundary","useQueryErrorResetBoundary","useIsFetching","useIsMutating","useMutation","useQuery","useQueries","useInfiniteQuery","useHydrate","Hydrate"].indexOf(c)<0&&(T[c]=()=>S[c]);h.d(m,T)},9535:(c,m,h)=>{var y=h(89736);function _regenerator(){var m,h,v="function"==typeof Symbol?Symbol:{},g=v.iterator||"@@iterator",_=v.toStringTag||"@@toStringTag";function i(c,v,g,_){var E=v&&v.prototype instanceof Generator?v:Generator,P=Object.create(E.prototype);return y(P,"_invoke",function(c,y,v){var g,_,E,P=0,C=v||[],w=!1,S={p:0,n:0,v:m,a:d,f:d.bind(m,4),d:function d(c,h){return g=c,_=0,E=m,S.n=h,b}};function d(c,y){for(_=c,E=y,h=0;!w&&P&&!v&&h<C.length;h++){var v,g=C[h],T=S.p,N=g[2];c>3?(v=N===y)&&(E=g[(_=g[4])?5:(_=3,3)],g[4]=g[5]=m):g[0]<=T&&((v=c<2&&T<g[1])?(_=0,S.v=y,S.n=g[1]):T<N&&(v=c<3||g[0]>y||y>N)&&(g[4]=c,g[5]=y,S.n=N,_=0))}if(v||c>1)return b;throw w=!0,y}return function(v,C,T){if(P>1)throw TypeError("Generator is already running");for(w&&1===C&&d(C,T),_=C,E=T;(h=_<2?m:E)||!w;){g||(_?_<3?(_>1&&(S.n=-1),d(_,E)):S.n=E:S.v=E);try{if(P=2,g){if(_||(v="next"),h=g[v]){if(!(h=h.call(g,E)))throw TypeError("iterator result is not an object");if(!h.done)return h;E=h.value,_<2&&(_=0)}else 1===_&&(h=g.return)&&h.call(g),_<2&&(E=TypeError("The iterator does not provide a '"+v+"' method"),_=1);g=m}else if((h=(w=S.n<0)?E:c.call(y,S))!==b)break}catch(c){g=m,_=1,E=c}finally{P=1}}return{value:h,done:w}}}(c,g,_),!0),P}var b={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}h=Object.getPrototypeOf;var E=[][g]?h(h([][g]())):(y(h={},g,function(){return this}),h),P=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(E);function f(c){return Object.setPrototypeOf?Object.setPrototypeOf(c,GeneratorFunctionPrototype):(c.__proto__=GeneratorFunctionPrototype,y(c,_,"GeneratorFunction")),c.prototype=Object.create(P),c}return GeneratorFunction.prototype=GeneratorFunctionPrototype,y(P,"constructor",GeneratorFunctionPrototype),y(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",y(GeneratorFunctionPrototype,_,"GeneratorFunction"),y(P),y(P,_,"Generator"),y(P,g,function(){return this}),y(P,"toString",function(){return"[object Generator]"}),(c.exports=_regenerator=function _regenerator(){return{w:i,m:f}},c.exports.__esModule=!0,c.exports.default=c.exports)()}c.exports=_regenerator,c.exports.__esModule=!0,c.exports.default=c.exports},9952:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function Export(){return v.default.createElement(E.QueryClientProvider,{client:S},v.default.createElement(b.ExportContextProvider,null,v.default.createElement(g.LocationProvider,{history:_.default.appHistory},v.default.createElement(g.Router,null,v.default.createElement(P.default,{path:"/",default:!0}),v.default.createElement(C.default,{path:"/process"}),v.default.createElement(w.default,{path:"/complete"})))))};var v=y(h(41594)),g=h(83040),_=y(h(47485)),b=h(72946),E=h(89994),P=y(h(43800)),C=y(h(23857)),w=y(h(18441)),S=new E.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}})},10564:c=>{function _typeof(m){return c.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(c){return typeof c}:function(c){return c&&"function"==typeof Symbol&&c.constructor===Symbol&&c!==Symbol.prototype?"symbol":typeof c},c.exports.__esModule=!0,c.exports.default=c.exports,_typeof(m)}c.exports=_typeof,c.exports.__esModule=!0,c.exports.default=c.exports},10739:c=>{c.exports=function _objectWithoutPropertiesLoose(c,m){if(null==c)return{};var h={};for(var y in c)if({}.hasOwnProperty.call(c,y)){if(-1!==m.indexOf(y))continue;h[y]=c[y]}return h},c.exports.__esModule=!0,c.exports.default=c.exports},10906:(c,m,h)=>{var y=h(91819),v=h(20365),g=h(37744),_=h(78687);c.exports=function _toConsumableArray(c){return y(c)||v(c)||g(c)||_()},c.exports.__esModule=!0,c.exports.default=c.exports},11018:c=>{c.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},c.exports.__esModule=!0,c.exports.default=c.exports},11167:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PluginsToImport;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(53442),P=g(h(19232)),C=g(h(85418)),w=h(28816),S=h(7221);var T=[3,1,1];function PluginsToImport(c){var m=c.plugins;if(null==m||!m.length)return null;var h=(0,b.useContext)(E.ImportContext),v=(0,b.useCallback)(function(c){return h.dispatch({type:"SET_PLUGINS",payload:c})},[]),g=(0,b.useMemo)(function(){return function getPluginsToImport(){var c=m[0],h=c.name,y=c.status;return S.PLUGINS_KEYS.ELEMENTOR_PRO===h&&w.PLUGIN_STATUS_MAP.INACTIVE!==y?m.splice(1):m}()},[m]),_=(0,b.useMemo)(function(){return g.map(function(c,m){return m})},[m]),N=g.length===h.data.plugins.length;return g.length?b.default.createElement("div",{className:"e-app-import-plugins__section"},b.default.createElement(C.default,{variant:"h5",tag:"h3",className:"e-app-import-plugins__section-heading"},y(N?"Plugins to add:":"Missing Required Plugins:","elementor")),b.default.createElement(P.default,{plugins:g,initialSelected:_,onSelect:v,layout:T})):null}PluginsToImport.propTypes={plugins:v.array}},11327:(c,m,h)=>{var y=h(10564).default;c.exports=function toPrimitive(c,m){if("object"!=y(c)||!c)return c;var h=c[Symbol.toPrimitive];if(void 0!==h){var v=h.call(c,m||"default");if("object"!=y(v))return v;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===m?String:Number)(c)},c.exports.__esModule=!0,c.exports.default=c.exports},12456:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.Context=void 0;var v=y(h(41594));m.Context=v.default.createContext()},12470:c=>{"use strict";c.exports=wp.i18n},12505:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Select2;var g=v(h(41594)),_=v(h(85707)),b=v(h(3826));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}h(4815);var E=function getDefaultSettings(){return{allowClear:!0,placeholder:"",dir:elementorCommon.config.isRTL?"rtl":"ltr"}};function Select2(c){var m=g.default.useRef(null);return g.default.useEffect(function(){var h=jQuery(m.current).select2(_objectSpread(_objectSpread(_objectSpread({},E()),c.settings),{},{placeholder:c.placeholder})).on("select2:select select2:unselect",c.onChange);return c.onReady&&c.onReady(h),function(){h.select2("destroy").off("select2:select select2:unselect")}},[c.settings,c.options]),g.default.useEffect(function(){jQuery(m.current).val(c.value).trigger("change")},[c.value]),g.default.createElement(b.default,{multiple:c.multiple,value:c.value,onChange:c.onChange,elRef:m,options:c.options,placeholder:c.placeholder})}Select2.propTypes={value:y.oneOfType([y.array,y.string]),onChange:y.func,onReady:y.func,options:y.array,settings:y.object,multiple:y.bool,placeholder:y.string},Select2.defaultProps={settings:{},options:[],dependencies:[],placeholder:""}},13043:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ImportPluginsFooter;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(53442),P=g(h(91071)),C=g(h(47483)),w=g(h(82372));function ImportPluginsFooter(c){var m=(0,b.useContext)(E.ImportContext),h=(0,w.default)().navigateToMainScreen;return b.default.createElement(P.default,null,b.default.createElement(C.default,{text:y("Previous","elementor"),variant:"contained",onClick:function onClick(){var y;m.dispatch({type:"SET_FILE",payload:null}),null===(y=c.onPreviousClick)||void 0===y||y.call(c),h()}}),b.default.createElement(C.default,{variant:"contained",text:y("Next","elementor"),color:"primary",url:"/import/content",onClick:function onClick(){var m;null===(m=c.onNextClick)||void 0===m||m.call(c)}}))}ImportPluginsFooter.propTypes={onPreviousClick:v.func,onNextClick:v.func}},13411:(c,m,h)=>{"use strict";h.d(m,{Q:()=>y});var y=function(){function Subscribable(){this.listeners=[]}var c=Subscribable.prototype;return c.subscribe=function subscribe(c){var m=this,h=c||function(){};return this.listeners.push(h),this.onSubscribe(),function(){m.listeners=m.listeners.filter(function(c){return c!==h}),m.onUnsubscribe()}},c.hasListeners=function hasListeners(){return this.listeners.length>0},c.onSubscribe=function onSubscribe(){},c.onUnsubscribe=function onUnsubscribe(){},Subscribable}()},13419:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExportProcessing;var g=v(h(41594)),_=v(h(62688)),b=h(86956);function ExportProcessing(c){var m=c.statusText;return g.default.createElement(g.default.Fragment,null,g.default.createElement(b.CircularProgress,{size:20}),g.default.createElement(b.Typography,{variant:"h5",component:"h2"},m),g.default.createElement(b.Typography,{variant:"body1",color:"text.secondary"},y("This usually takes a few moments.","elementor"),g.default.createElement("br",null),y("Don't close this window until the process is finished.","elementor")))}ExportProcessing.propTypes={statusText:_.default.string.isRequired}},14300:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.KIT_SOURCE_MAP=void 0,m.default=function useKit(){var c=(0,P.default)(),m=c.ajaxState,h=c.setAjax,y=c.ajaxActions,_=c.runRequest,D={status:C.INITIAL,data:null},A=(0,E.useState)(D),W=(0,b.default)(A,2),q=W[0],U=W[1],Q=function(){var c=(0,g.default)(v.default.mark(function _callee(c){var m,h,y,g,b,E,P;return v.default.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return m=c.id,h=c.session,y=c.include,g=c.overrideConditions,b=c.referrer,E=c.selectedCustomPostTypes,P={data:{action:S,data:{id:m,session:h,include:y,overrideConditions:g}}},b&&(P.data.data.referrer=b),E&&(P.data.data.selectedCustomPostTypes=E),P.data.data=JSON.stringify(P.data.data),v.abrupt("return",_(P).catch(function(c){var m,h=408===c.status?"timeout":null===(m=c.responseJSON)||void 0===m?void 0:m.data;U(function(c){return _objectSpread(_objectSpread({},c),{},{status:C.ERROR,data:h||{}})})}));case 1:case"end":return v.stop()}},_callee)}));return function initImportProcess(m){return c.apply(this,arguments)}}(),K=function(){var c=(0,g.default)(v.default.mark(function _callee2(c,m){var y,g,E,P,w,S,T,D;return v.default.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:y=!1,g=_createForOfIteratorHelper(m.entries()),v.prev=1,g.s();case 2:if((E=g.n()).done){v.next=7;break}if(P=(0,b.default)(E.value,2),w=P[0],S=P[1],!y){v.next=3;break}return v.abrupt("continue",7);case 3:if((T={data:{action:N,data:{session:c,runner:S}}}).data.data=JSON.stringify(T.data.data),w===m.length-1){v.next=5;break}return v.next=4,_(T).catch(function(c){var m;y=!0;var h=408===c.status?"timeout":null===(m=c.responseJSON)||void 0===m?void 0:m.data;U(function(c){return _objectSpread(_objectSpread({},c),{status:C.ERROR,data:h||{}})})});case 4:v.next=6;break;case 5:h(T);case 6:v.next=2;break;case 7:v.next=9;break;case 8:v.prev=8,D=v.catch(1),g.e(D);case 9:return v.prev=9,g.f(),v.finish(9);case 10:case"end":return v.stop()}},_callee2,null,[[1,8,9,10]])}));return function runImportRunners(m,h){return c.apply(this,arguments)}}(),H=function(){var c=(0,g.default)(v.default.mark(function _callee3(c){var m,h,g,_,b,E,P;return v.default.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return m=c.id,h=c.session,g=c.include,_=c.overrideConditions,b=c.referrer,E=c.selectedCustomPostTypes,y.reset(),v.next=1,Q({id:m,session:h,include:g,overrideConditions:_,referrer:b,selectedCustomPostTypes:E});case 1:if(P=v.sent){v.next=2;break}return v.abrupt("return");case 2:return v.next=3,K(P.data.session,P.data.runners);case 3:case"end":return v.stop()}},_callee3)}));return function importKit(m){return c.apply(this,arguments)}}();return(0,E.useEffect)(function(){if("initial"!==m.status){var c,h,y,v={};if("success"===m.status)if(null!==(c=m.response)&&void 0!==c&&c.file||null!==(h=m.response)&&void 0!==h&&h.kit)v.status=C.EXPORTED;else v.status=null!==(y=m.response)&&void 0!==y&&y.manifest?C.UPLOADED:C.IMPORTED;else"error"===m.status&&(v.status=C.ERROR);v.data=m.response||{},U(function(c){return _objectSpread(_objectSpread({},c),v)})}},[m.status]),{kitState:q,KIT_STATUS_MAP:C,kitActions:{upload:function uploadKit(c){var m=c.kitId,y=c.file,v=c.kitLibraryNonce,g=c.source;h({data:_objectSpread(_objectSpread({action:w,source:void 0===g?"":g},y?{e_import_file:y}:null),{},{kit_id:m},v?{e_kit_library_nonce:v}:{})})},import:H,export:function exportKit(c){var m=c.include,y=c.kitInfo,v=c.plugins,g=c.selectedCustomPostTypes,_=c.screenShotBlob;h({data:{action:T,data:JSON.stringify({include:m,kitInfo:y,plugins:v,selectedCustomPostTypes:g,screenShotBlob:_})}})},reset:function reset(){return y.reset()}}}};var v=y(h(61790)),g=y(h(58155)),_=y(h(85707)),b=y(h(18821)),E=h(41594),P=y(h(73921));function _createForOfIteratorHelper(c,m){var h="undefined"!=typeof Symbol&&c[Symbol.iterator]||c["@@iterator"];if(!h){if(Array.isArray(c)||(h=function _unsupportedIterableToArray(c,m){if(c){if("string"==typeof c)return _arrayLikeToArray(c,m);var h={}.toString.call(c).slice(8,-1);return"Object"===h&&c.constructor&&(h=c.constructor.name),"Map"===h||"Set"===h?Array.from(c):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray(c,m):void 0}}(c))||m&&c&&"number"==typeof c.length){h&&(c=h);var y=0,v=function F(){};return{s:v,n:function n(){return y>=c.length?{done:!0}:{done:!1,value:c[y++]}},e:function e(c){throw c},f:v}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var g,_=!0,b=!1;return{s:function s(){h=h.call(c)},n:function n(){var c=h.next();return _=c.done,c},e:function e(c){b=!0,g=c},f:function f(){try{_||null==h.return||h.return()}finally{if(b)throw g}}}}function _arrayLikeToArray(c,m){(null==m||m>c.length)&&(m=c.length);for(var h=0,y=Array(m);h<m;h++)y[h]=c[h];return y}function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}m.KIT_SOURCE_MAP={CLOUD:"cloud",FILE:"file"};var C=Object.freeze({INITIAL:"initial",UPLOADED:"uploaded",IMPORTED:"imported",EXPORTED:"exported",ERROR:"error"}),w="elementor_upload_kit",S="elementor_import_kit",T="elementor_export_kit",N="elementor_import_kit__runner"},14387:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.DownloadLink=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(87861));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.DownloadLink=function(c){function DownloadLink(){return(0,v.default)(this,DownloadLink),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,DownloadLink,arguments)}return(0,E.default)(DownloadLink,c),(0,g.default)(DownloadLink,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"kits/download-link/{id}"}}])}($e.modules.CommandData)},14495:()=>{},14546:()=>{},14718:(c,m,h)=>{var y=h(29402);c.exports=function _superPropBase(c,m){for(;!{}.hasOwnProperty.call(c,m)&&null!==(c=y(c)););return c},c.exports.__esModule=!0,c.exports.default=c.exports},14888:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Header;var g=v(h(41594)),_=v(h(78304)),b=v(h(3416)),E=v(h(6056)),P=v(h(80791));function Header(c){(0,P.default)({title:c.title});var m="span",h={};return c.titleRedirectRoute&&(m="a",h={href:"#".concat(c.titleRedirectRoute),target:"_self"}),g.default.createElement(b.default,{container:!0,alignItems:"center",justify:"space-between",className:"eps-app__header"},g.default.createElement(m,(0,_.default)({className:"eps-app__logo-title-wrapper"},h),g.default.createElement("i",{className:"eps-app__logo eicon-elementor"}),g.default.createElement("h1",{className:"eps-app__title"},c.title)),g.default.createElement(E.default,{buttons:c.buttons,onClose:c.onClose}))}Header.propTypes={title:y.string,titleRedirectRoute:y.string,buttons:y.arrayOf(y.object),onClose:y.func},Header.defaultProps={buttons:[]}},15104:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=FailedPluginsNotice;var _=g(h(41594)),b=g(h(40587)),E=g(h(47483));function FailedPluginsNotice(c){var m=c.failedPlugins;return _.default.createElement(b.default,{className:"e-app-import-failed-plugins-notice",label:y("Important:","elementor"),color:"warning",button:function getButton(){return _.default.createElement(E.default,{text:y("Learn more","elementor"),variant:"outlined",color:"secondary",size:"sm",target:"_blank",url:"https://go.elementor.com/app-import-plugin-installation-failed/"})}()},y("There are few plugins that we couldn't install:","elementor")+" "+m.map(function(c){return c.name}).join(" | "))}h(74644),FailedPluginsNotice.propTypes={failedPlugins:v.array}},15118:(c,m,h)=>{var y=h(10564).default,v=h(36417);c.exports=function _possibleConstructorReturn(c,m){if(m&&("object"==y(m)||"function"==typeof m))return m;if(void 0!==m)throw new TypeError("Derived constructors may only return object or undefined");return v(c)},c.exports.__esModule=!0,c.exports.default=c.exports},15142:(c,m,h)=>{"use strict";h.r(m),h.d(m,{ServerStyleSheet:()=>Lt,StyleSheetConsumer:()=>jt,StyleSheetContext:()=>St,StyleSheetManager:()=>Ye,ThemeConsumer:()=>It,ThemeContext:()=>Nt,ThemeProvider:()=>ot,__PRIVATE__:()=>Ft,createGlobalStyle:()=>ft,css:()=>lt,default:()=>At,isStyledComponent:()=>se,keyframes:()=>mt,styled:()=>At,useTheme:()=>nt,version:()=>te,withTheme:()=>yt});var __assign=function(){return __assign=Object.assign||function __assign(c){for(var m,h=1,y=arguments.length;h<y;h++)for(var v in m=arguments[h])Object.prototype.hasOwnProperty.call(m,v)&&(c[v]=m[v]);return c},__assign.apply(this,arguments)};Object.create;function __spreadArray(c,m,h){if(h||2===arguments.length)for(var y,v=0,g=m.length;v<g;v++)!y&&v in m||(y||(y=Array.prototype.slice.call(m,0,v)),y[v]=m[v]);return c.concat(y||Array.prototype.slice.call(m))}Object.create;"function"==typeof SuppressedError&&SuppressedError;var y=h(41594),v=h.n(y),g=h(45317),_=h.n(g),b="-ms-",E="-moz-",P="-webkit-",C="comm",w="rule",S="decl",T="@import",N="@keyframes",D="@layer",A=Math.abs,W=String.fromCharCode,q=Object.assign;function trim(c){return c.trim()}function match(c,m){return(c=m.exec(c))?c[0]:c}function replace(c,m,h){return c.replace(m,h)}function indexof(c,m,h){return c.indexOf(m,h)}function Utility_charat(c,m){return 0|c.charCodeAt(m)}function Utility_substr(c,m,h){return c.slice(m,h)}function Utility_strlen(c){return c.length}function Utility_sizeof(c){return c.length}function Utility_append(c,m){return m.push(c),c}function filter(c,m){return c.filter(function(c){return!match(c,m)})}var U=1,Q=1,K=0,H=0,G=0,V="";function node(c,m,h,y,v,g,_,b){return{value:c,root:m,parent:h,type:y,props:v,children:g,line:U,column:Q,length:_,return:"",siblings:b}}function copy(c,m){return q(node("",null,null,"",null,null,0,c.siblings),c,{length:-c.length},m)}function lift(c){for(;c.root;)c=copy(c.root,{children:[c]});Utility_append(c,c.siblings)}function prev(){return G=H>0?Utility_charat(V,--H):0,Q--,10===G&&(Q=1,U--),G}function next(){return G=H<K?Utility_charat(V,H++):0,Q++,10===G&&(Q=1,U++),G}function peek(){return Utility_charat(V,H)}function caret(){return H}function slice(c,m){return Utility_substr(V,c,m)}function token(c){switch(c){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function alloc(c){return U=Q=1,K=Utility_strlen(V=c),H=0,[]}function dealloc(c){return V="",c}function delimit(c){return trim(slice(H-1,delimiter(91===c?c+2:40===c?c+1:c)))}function whitespace(c){for(;(G=peek())&&G<33;)next();return token(c)>2||token(G)>3?"":" "}function escaping(c,m){for(;--m&&next()&&!(G<48||G>102||G>57&&G<65||G>70&&G<97););return slice(c,caret()+(m<6&&32==peek()&&32==next()))}function delimiter(c){for(;next();)switch(G){case c:return H;case 34:case 39:34!==c&&39!==c&&delimiter(G);break;case 40:41===c&&delimiter(c);break;case 92:next()}return H}function commenter(c,m){for(;next()&&c+G!==57&&(c+G!==84||47!==peek()););return"/*"+slice(m,H-1)+"*"+W(47===c?c:next())}function identifier(c){for(;!token(peek());)next();return slice(c,H)}function serialize(c,m){for(var h="",y=0;y<c.length;y++)h+=m(c[y],y,c,m)||"";return h}function stringify(c,m,h,y){switch(c.type){case D:if(c.children.length)break;case T:case S:return c.return=c.return||c.value;case C:return"";case N:return c.return=c.value+"{"+serialize(c.children,y)+"}";case w:if(!Utility_strlen(c.value=c.props.join(",")))return""}return Utility_strlen(h=serialize(c.children,y))?c.return=c.value+"{"+h+"}":""}function prefix(c,m,h){switch(function hash(c,m){return 45^Utility_charat(c,0)?(((m<<2^Utility_charat(c,0))<<2^Utility_charat(c,1))<<2^Utility_charat(c,2))<<2^Utility_charat(c,3):0}(c,m)){case 5103:return P+"print-"+c+c;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return P+c+c;case 4789:return E+c+c;case 5349:case 4246:case 4810:case 6968:case 2756:return P+c+E+c+b+c+c;case 5936:switch(Utility_charat(c,m+11)){case 114:return P+c+b+replace(c,/[svh]\w+-[tblr]{2}/,"tb")+c;case 108:return P+c+b+replace(c,/[svh]\w+-[tblr]{2}/,"tb-rl")+c;case 45:return P+c+b+replace(c,/[svh]\w+-[tblr]{2}/,"lr")+c}case 6828:case 4268:case 2903:return P+c+b+c+c;case 6165:return P+c+b+"flex-"+c+c;case 5187:return P+c+replace(c,/(\w+).+(:[^]+)/,P+"box-$1$2"+b+"flex-$1$2")+c;case 5443:return P+c+b+"flex-item-"+replace(c,/flex-|-self/g,"")+(match(c,/flex-|baseline/)?"":b+"grid-row-"+replace(c,/flex-|-self/g,""))+c;case 4675:return P+c+b+"flex-line-pack"+replace(c,/align-content|flex-|-self/g,"")+c;case 5548:return P+c+b+replace(c,"shrink","negative")+c;case 5292:return P+c+b+replace(c,"basis","preferred-size")+c;case 6060:return P+"box-"+replace(c,"-grow","")+P+c+b+replace(c,"grow","positive")+c;case 4554:return P+replace(c,/([^-])(transform)/g,"$1"+P+"$2")+c;case 6187:return replace(replace(replace(c,/(zoom-|grab)/,P+"$1"),/(image-set)/,P+"$1"),c,"")+c;case 5495:case 3959:return replace(c,/(image-set\([^]*)/,P+"$1$`$1");case 4968:return replace(replace(c,/(.+:)(flex-)?(.*)/,P+"box-pack:$3"+b+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+P+c+c;case 4200:if(!match(c,/flex-|baseline/))return b+"grid-column-align"+Utility_substr(c,m)+c;break;case 2592:case 3360:return b+replace(c,"template-","")+c;case 4384:case 3616:return h&&h.some(function(c,h){return m=h,match(c.props,/grid-\w+-end/)})?~indexof(c+(h=h[m].value),"span",0)?c:b+replace(c,"-start","")+c+b+"grid-row-span:"+(~indexof(h,"span",0)?match(h,/\d+/):+match(h,/\d+/)-+match(c,/\d+/))+";":b+replace(c,"-start","")+c;case 4896:case 4128:return h&&h.some(function(c){return match(c.props,/grid-\w+-start/)})?c:b+replace(replace(c,"-end","-span"),"span ","")+c;case 4095:case 3583:case 4068:case 2532:return replace(c,/(.+)-inline(.+)/,P+"$1$2")+c;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Utility_strlen(c)-1-m>6)switch(Utility_charat(c,m+1)){case 109:if(45!==Utility_charat(c,m+4))break;case 102:return replace(c,/(.+:)(.+)-([^]+)/,"$1"+P+"$2-$3$1"+E+(108==Utility_charat(c,m+3)?"$3":"$2-$3"))+c;case 115:return~indexof(c,"stretch",0)?prefix(replace(c,"stretch","fill-available"),m,h)+c:c}break;case 5152:case 5920:return replace(c,/(.+?):(\d+)(\s*\/\s*(span)?\s*(\d+))?(.*)/,function(m,h,y,v,g,_,E){return b+h+":"+y+E+(v?b+h+"-span:"+(g?_:+_-+y)+E:"")+c});case 4949:if(121===Utility_charat(c,m+6))return replace(c,":",":"+P)+c;break;case 6444:switch(Utility_charat(c,45===Utility_charat(c,14)?18:11)){case 120:return replace(c,/(.+:)([^;\s!]+)(;|(\s+)?!.+)?/,"$1"+P+(45===Utility_charat(c,14)?"inline-":"")+"box$3$1"+P+"$2$3$1"+b+"$2box$3")+c;case 100:return replace(c,":",":"+b)+c}break;case 5719:case 2647:case 2135:case 3927:case 2391:return replace(c,"scroll-","scroll-snap-")+c}return c}function prefixer(c,m,h,y){if(c.length>-1&&!c.return)switch(c.type){case S:return void(c.return=prefix(c.value,c.length,h));case N:return serialize([copy(c,{value:replace(c.value,"@","@"+P)})],y);case w:if(c.length)return function Utility_combine(c,m){return c.map(m).join("")}(h=c.props,function(m){switch(match(m,y=/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":lift(copy(c,{props:[replace(m,/:(read-\w+)/,":"+E+"$1")]})),lift(copy(c,{props:[m]})),q(c,{props:filter(h,y)});break;case"::placeholder":lift(copy(c,{props:[replace(m,/:(plac\w+)/,":"+P+"input-$1")]})),lift(copy(c,{props:[replace(m,/:(plac\w+)/,":"+E+"$1")]})),lift(copy(c,{props:[replace(m,/:(plac\w+)/,b+"input-$1")]})),lift(copy(c,{props:[m]})),q(c,{props:filter(h,y)})}return""})}}function compile(c){return dealloc(parse("",null,null,null,[""],c=alloc(c),0,[0],c))}function parse(c,m,h,y,v,g,_,b,E){for(var P=0,C=0,w=_,S=0,T=0,N=0,D=1,q=1,U=1,Q=0,K="",H=v,G=g,V=y,Y=K;q;)switch(N=Q,Q=next()){case 40:if(108!=N&&58==Utility_charat(Y,w-1)){-1!=indexof(Y+=replace(delimit(Q),"&","&\f"),"&\f",A(P?b[P-1]:0))&&(U=-1);break}case 34:case 39:case 91:Y+=delimit(Q);break;case 9:case 10:case 13:case 32:Y+=whitespace(N);break;case 92:Y+=escaping(caret()-1,7);continue;case 47:switch(peek()){case 42:case 47:Utility_append(comment(commenter(next(),caret()),m,h,E),E);break;default:Y+="/"}break;case 123*D:b[P++]=Utility_strlen(Y)*U;case 125*D:case 59:case 0:switch(Q){case 0:case 125:q=0;case 59+C:-1==U&&(Y=replace(Y,/\f/g,"")),T>0&&Utility_strlen(Y)-w&&Utility_append(T>32?declaration(Y+";",y,h,w-1,E):declaration(replace(Y," ","")+";",y,h,w-2,E),E);break;case 59:Y+=";";default:if(Utility_append(V=ruleset(Y,m,h,P,C,v,b,K,H=[],G=[],w,g),g),123===Q)if(0===C)parse(Y,m,V,V,H,g,w,b,G);else switch(99===S&&110===Utility_charat(Y,3)?100:S){case 100:case 108:case 109:case 115:parse(c,V,V,y&&Utility_append(ruleset(c,V,V,0,0,v,b,K,v,H=[],w,G),G),v,G,w,b,y?H:G);break;default:parse(Y,V,V,V,[""],G,0,b,G)}}P=C=T=0,D=U=1,K=Y="",w=_;break;case 58:w=1+Utility_strlen(Y),T=N;default:if(D<1)if(123==Q)--D;else if(125==Q&&0==D++&&125==prev())continue;switch(Y+=W(Q),Q*D){case 38:U=C>0?1:(Y+="\f",-1);break;case 44:b[P++]=(Utility_strlen(Y)-1)*U,U=1;break;case 64:45===peek()&&(Y+=delimit(next())),S=peek(),C=w=Utility_strlen(K=Y+=identifier(caret())),Q++;break;case 45:45===N&&2==Utility_strlen(Y)&&(D=0)}}return g}function ruleset(c,m,h,y,v,g,_,b,E,P,C,S){for(var T=v-1,N=0===v?g:[""],D=Utility_sizeof(N),W=0,q=0,U=0;W<y;++W)for(var Q=0,K=Utility_substr(c,T+1,T=A(q=_[W])),H=c;Q<D;++Q)(H=trim(q>0?N[Q]+" "+K:replace(K,/&\f/g,N[Q])))&&(E[U++]=H);return node(c,m,h,0===v?w:b,E,P,C,S)}function comment(c,m,h,y){return node(c,m,h,C,W(function Tokenizer_char(){return G}()),Utility_substr(c,2,-2),0,y)}function declaration(c,m,h,y,v){return node(c,m,h,S,Utility_substr(c,0,y),Utility_substr(c,y+1,-1),y,v)}var Y={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},J="undefined"!=typeof process&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",Z="active",ee="data-styled-version",te="6.1.19",ne="/*!sc*/\n",de="undefined"!=typeof window&&"undefined"!=typeof document,fe=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:"undefined"!=typeof process&&void 0!==process.env&&void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)),pe={},me=(new Set,Object.freeze([])),ye=Object.freeze({});function I(c,m,h){return void 0===h&&(h=ye),c.theme!==h.theme&&c.theme||m||h.theme}var ve=new Set(["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","track","u","ul","use","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"]),ge=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,be=/(^-|-$)/g;function R(c){return c.replace(ge,"-").replace(be,"")}var Ee=/(a)(d)/gi,Oe=52,j=function(c){return String.fromCharCode(c+(c>25?39:97))};function x(c){var m,h="";for(m=Math.abs(c);m>Oe;m=m/Oe|0)h=j(m%Oe)+h;return(j(m%Oe)+h).replace(Ee,"$1-$2")}var xe,je=5381,M=function(c,m){for(var h=m.length;h;)c=33*c^m.charCodeAt(--h);return c},z=function(c){return M(je,c)};function $(c){return x(z(c)>>>0)}function B(c){return c.displayName||c.name||"Component"}function L(c){return"string"==typeof c&&!0}var ke="function"==typeof Symbol&&Symbol.for,Te=ke?Symbol.for("react.memo"):60115,Re=ke?Symbol.for("react.forward_ref"):60112,Me={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},De={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Ae={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},We=((xe={})[Re]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},xe[Te]=Ae,xe);function X(c){return("type"in(m=c)&&m.type.$$typeof)===Te?Ae:"$$typeof"in c?We[c.$$typeof]:Me;var m}var Le=Object.defineProperty,Be=Object.getOwnPropertyNames,Qe=Object.getOwnPropertySymbols,Ke=Object.getOwnPropertyDescriptor,ze=Object.getPrototypeOf,$e=Object.prototype;function oe(c,m,h){if("string"!=typeof m){if($e){var y=ze(m);y&&y!==$e&&oe(c,y,h)}var v=Be(m);Qe&&(v=v.concat(Qe(m)));for(var g=X(c),_=X(m),b=0;b<v.length;++b){var E=v[b];if(!(E in De||h&&h[E]||_&&E in _||g&&E in g)){var P=Ke(m,E);try{Le(c,E,P)}catch(c){}}}}return c}function re(c){return"function"==typeof c}function se(c){return"object"==typeof c&&"styledComponentId"in c}function ie(c,m){return c&&m?"".concat(c," ").concat(m):c||m||""}function ae(c,m){if(0===c.length)return"";for(var h=c[0],y=1;y<c.length;y++)h+=m?m+c[y]:c[y];return h}function ce(c){return null!==c&&"object"==typeof c&&c.constructor.name===Object.name&&!("props"in c&&c.$$typeof)}function le(c,m,h){if(void 0===h&&(h=!1),!h&&!ce(c)&&!Array.isArray(c))return m;if(Array.isArray(m))for(var y=0;y<m.length;y++)c[y]=le(c[y],m[y]);else if(ce(m))for(var y in m)c[y]=le(c[y],m[y]);return c}function ue(c,m){Object.defineProperty(c,"toString",{value:m})}function he(c){for(var m=[],h=1;h<arguments.length;h++)m[h-1]=arguments[h];return new Error("An error occurred. See https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#".concat(c," for more information.").concat(m.length>0?" Args: ".concat(m.join(", ")):""))}var et=function(){function e(c){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=c}return e.prototype.indexOfGroup=function(c){for(var m=0,h=0;h<c;h++)m+=this.groupSizes[h];return m},e.prototype.insertRules=function(c,m){if(c>=this.groupSizes.length){for(var h=this.groupSizes,y=h.length,v=y;c>=v;)if((v<<=1)<0)throw he(16,"".concat(c));this.groupSizes=new Uint32Array(v),this.groupSizes.set(h),this.length=v;for(var g=y;g<v;g++)this.groupSizes[g]=0}for(var _=this.indexOfGroup(c+1),b=(g=0,m.length);g<b;g++)this.tag.insertRule(_,m[g])&&(this.groupSizes[c]++,_++)},e.prototype.clearGroup=function(c){if(c<this.length){var m=this.groupSizes[c],h=this.indexOfGroup(c),y=h+m;this.groupSizes[c]=0;for(var v=h;v<y;v++)this.tag.deleteRule(h)}},e.prototype.getGroup=function(c){var m="";if(c>=this.length||0===this.groupSizes[c])return m;for(var h=this.groupSizes[c],y=this.indexOfGroup(c),v=y+h,g=y;g<v;g++)m+="".concat(this.tag.getRule(g)).concat(ne);return m},e}(),tt=new Map,rt=new Map,st=1,Se=function(c){if(tt.has(c))return tt.get(c);for(;rt.has(st);)st++;var m=st++;return tt.set(c,m),rt.set(m,c),m},we=function(c,m){st=m+1,tt.set(c,m),rt.set(m,c)},dt="style[".concat(J,"][").concat(ee,'="').concat(te,'"]'),ht=new RegExp("^".concat(J,'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)')),Ne=function(c,m,h){for(var y,v=h.split(","),g=0,_=v.length;g<_;g++)(y=v[g])&&c.registerName(m,y)},Pe=function(c,m){for(var h,y=(null!==(h=m.textContent)&&void 0!==h?h:"").split(ne),v=[],g=0,_=y.length;g<_;g++){var b=y[g].trim();if(b){var E=b.match(ht);if(E){var P=0|parseInt(E[1],10),C=E[2];0!==P&&(we(C,P),Ne(c,C,E[3]),c.getTag().insertRules(P,v)),v.length=0}else v.push(b)}}},_e=function(c){for(var m=document.querySelectorAll(dt),h=0,y=m.length;h<y;h++){var v=m[h];v&&v.getAttribute(J)!==Z&&(Pe(c,v),v.parentNode&&v.parentNode.removeChild(v))}};function Ce(){return h.nc}var Ie=function(c){var m=document.head,h=c||m,y=document.createElement("style"),v=function(c){var m=Array.from(c.querySelectorAll("style[".concat(J,"]")));return m[m.length-1]}(h),g=void 0!==v?v.nextSibling:null;y.setAttribute(J,Z),y.setAttribute(ee,te);var _=Ce();return _&&y.setAttribute("nonce",_),h.insertBefore(y,g),y},vt=function(){function e(c){this.element=Ie(c),this.element.appendChild(document.createTextNode("")),this.sheet=function(c){if(c.sheet)return c.sheet;for(var m=document.styleSheets,h=0,y=m.length;h<y;h++){var v=m[h];if(v.ownerNode===c)return v}throw he(17)}(this.element),this.length=0}return e.prototype.insertRule=function(c,m){try{return this.sheet.insertRule(m,c),this.length++,!0}catch(c){return!1}},e.prototype.deleteRule=function(c){this.sheet.deleteRule(c),this.length--},e.prototype.getRule=function(c){var m=this.sheet.cssRules[c];return m&&m.cssText?m.cssText:""},e}(),gt=function(){function e(c){this.element=Ie(c),this.nodes=this.element.childNodes,this.length=0}return e.prototype.insertRule=function(c,m){if(c<=this.length&&c>=0){var h=document.createTextNode(m);return this.element.insertBefore(h,this.nodes[c]||null),this.length++,!0}return!1},e.prototype.deleteRule=function(c){this.element.removeChild(this.nodes[c]),this.length--},e.prototype.getRule=function(c){return c<this.length?this.nodes[c].textContent:""},e}(),_t=function(){function e(c){this.rules=[],this.length=0}return e.prototype.insertRule=function(c,m){return c<=this.length&&(this.rules.splice(c,0,m),this.length++,!0)},e.prototype.deleteRule=function(c){this.rules.splice(c,1),this.length--},e.prototype.getRule=function(c){return c<this.length?this.rules[c]:""},e}(),bt=de,Et={isServer:!de,useCSSOMInjection:!fe},Ot=function(){function e(c,m,h){void 0===c&&(c=ye),void 0===m&&(m={});var y=this;this.options=__assign(__assign({},Et),c),this.gs=m,this.names=new Map(h),this.server=!!c.isServer,!this.server&&de&&bt&&(bt=!1,_e(this)),ue(this,function(){return function(c){for(var m=c.getTag(),h=m.length,y="",r=function(h){var v=function(c){return rt.get(c)}(h);if(void 0===v)return"continue";var g=c.names.get(v),_=m.getGroup(h);if(void 0===g||!g.size||0===_.length)return"continue";var b="".concat(J,".g").concat(h,'[id="').concat(v,'"]'),E="";void 0!==g&&g.forEach(function(c){c.length>0&&(E+="".concat(c,","))}),y+="".concat(_).concat(b,'{content:"').concat(E,'"}').concat(ne)},v=0;v<h;v++)r(v);return y}(y)})}return e.registerId=function(c){return Se(c)},e.prototype.rehydrate=function(){!this.server&&de&&_e(this)},e.prototype.reconstructWithOptions=function(c,m){return void 0===m&&(m=!0),new e(__assign(__assign({},this.options),c),this.gs,m&&this.names||void 0)},e.prototype.allocateGSInstance=function(c){return this.gs[c]=(this.gs[c]||0)+1},e.prototype.getTag=function(){return this.tag||(this.tag=(c=function(c){var m=c.useCSSOMInjection,h=c.target;return c.isServer?new _t(h):m?new vt(h):new gt(h)}(this.options),new et(c)));var c},e.prototype.hasNameForId=function(c,m){return this.names.has(c)&&this.names.get(c).has(m)},e.prototype.registerName=function(c,m){if(Se(c),this.names.has(c))this.names.get(c).add(m);else{var h=new Set;h.add(m),this.names.set(c,h)}},e.prototype.insertRules=function(c,m,h){this.registerName(c,m),this.getTag().insertRules(Se(c),h)},e.prototype.clearNames=function(c){this.names.has(c)&&this.names.get(c).clear()},e.prototype.clearRules=function(c){this.getTag().clearGroup(Se(c)),this.clearNames(c)},e.prototype.clearTag=function(){this.tag=void 0},e}(),Pt=/&/g,Ct=/^\s*\/\/.*$/gm;function Ve(c,m){return c.map(function(c){return"rule"===c.type&&(c.value="".concat(m," ").concat(c.value),c.value=c.value.replaceAll(",",",".concat(m," ")),c.props=c.props.map(function(c){return"".concat(m," ").concat(c)})),Array.isArray(c.children)&&"@keyframes"!==c.type&&(c.children=Ve(c.children,m)),c})}function Fe(c){var m,h,y,v=void 0===c?ye:c,g=v.options,_=void 0===g?ye:g,b=v.plugins,E=void 0===b?me:b,l=function(c,y,v){return v.startsWith(h)&&v.endsWith(h)&&v.replaceAll(h,"").length>0?".".concat(m):c},P=E.slice();P.push(function(c){c.type===w&&c.value.includes("&")&&(c.props[0]=c.props[0].replace(Pt,h).replace(y,l))}),_.prefix&&P.push(prefixer),P.push(stringify);var p=function(c,v,g,b){void 0===v&&(v=""),void 0===g&&(g=""),void 0===b&&(b="&"),m=b,h=v,y=new RegExp("\\".concat(h,"\\b"),"g");var E=c.replace(Ct,""),C=compile(g||v?"".concat(g," ").concat(v," { ").concat(E," }"):E);_.namespace&&(C=Ve(C,_.namespace));var w=[];return serialize(C,function middleware(c){var m=Utility_sizeof(c);return function(h,y,v,g){for(var _="",b=0;b<m;b++)_+=c[b](h,y,v,g)||"";return _}}(P.concat(function rulesheet(c){return function(m){m.root||(m=m.return)&&c(m)}}(function(c){return w.push(c)})))),w};return p.hash=E.length?E.reduce(function(c,m){return m.name||he(15),M(c,m.name)},je).toString():"",p}var wt=new Ot,xt=Fe(),St=v().createContext({shouldForwardProp:void 0,styleSheet:wt,stylis:xt}),jt=St.Consumer,kt=v().createContext(void 0);function Ge(){return(0,y.useContext)(St)}function Ye(c){var m=(0,y.useState)(c.stylisPlugins),h=m[0],g=m[1],b=Ge().styleSheet,E=(0,y.useMemo)(function(){var m=b;return c.sheet?m=c.sheet:c.target&&(m=m.reconstructWithOptions({target:c.target},!1)),c.disableCSSOMInjection&&(m=m.reconstructWithOptions({useCSSOMInjection:!1})),m},[c.disableCSSOMInjection,c.sheet,c.target,b]),P=(0,y.useMemo)(function(){return Fe({options:{namespace:c.namespace,prefix:c.enableVendorPrefixes},plugins:h})},[c.enableVendorPrefixes,c.namespace,h]);(0,y.useEffect)(function(){_()(h,c.stylisPlugins)||g(c.stylisPlugins)},[c.stylisPlugins]);var C=(0,y.useMemo)(function(){return{shouldForwardProp:c.shouldForwardProp,styleSheet:E,stylis:P}},[c.shouldForwardProp,E,P]);return v().createElement(St.Provider,{value:C},v().createElement(kt.Provider,{value:P},c.children))}var Tt=function(){function e(c,m){var h=this;this.inject=function(c,m){void 0===m&&(m=xt);var y=h.name+m.hash;c.hasNameForId(h.id,y)||c.insertRules(h.id,y,m(h.rules,y,"@keyframes"))},this.name=c,this.id="sc-keyframes-".concat(c),this.rules=m,ue(this,function(){throw he(12,String(h.name))})}return e.prototype.getName=function(c){return void 0===c&&(c=xt),this.name+c.hash},e}(),qe=function(c){return c>="A"&&c<="Z"};function He(c){for(var m="",h=0;h<c.length;h++){var y=c[h];if(1===h&&"-"===y&&"-"===c[0])return c;qe(y)?m+="-"+y.toLowerCase():m+=y}return m.startsWith("ms-")?"-"+m:m}var Ue=function(c){return null==c||!1===c||""===c},Je=function(c){var m,h,y=[];for(var v in c){var g=c[v];c.hasOwnProperty(v)&&!Ue(g)&&(Array.isArray(g)&&g.isCss||re(g)?y.push("".concat(He(v),":"),g,";"):ce(g)?y.push.apply(y,__spreadArray(__spreadArray(["".concat(v," {")],Je(g),!1),["}"],!1)):y.push("".concat(He(v),": ").concat((m=v,null==(h=g)||"boolean"==typeof h||""===h?"":"number"!=typeof h||0===h||m in Y||m.startsWith("--")?String(h).trim():"".concat(h,"px")),";")))}return y};function Xe(c,m,h,y){return Ue(c)?[]:se(c)?[".".concat(c.styledComponentId)]:re(c)?!re(v=c)||v.prototype&&v.prototype.isReactComponent||!m?[c]:Xe(c(m),m,h,y):c instanceof Tt?h?(c.inject(h,y),[c.getName(y)]):[c]:ce(c)?Je(c):Array.isArray(c)?Array.prototype.concat.apply(me,c.map(function(c){return Xe(c,m,h,y)})):[c.toString()];var v}function Ze(c){for(var m=0;m<c.length;m+=1){var h=c[m];if(re(h)&&!se(h))return!1}return!0}var Rt=z(te),Mt=function(){function e(c,m,h){this.rules=c,this.staticRulesId="",this.isStatic=(void 0===h||h.isStatic)&&Ze(c),this.componentId=m,this.baseHash=M(Rt,m),this.baseStyle=h,Ot.registerId(m)}return e.prototype.generateAndInjectStyles=function(c,m,h){var y=this.baseStyle?this.baseStyle.generateAndInjectStyles(c,m,h):"";if(this.isStatic&&!h.hash)if(this.staticRulesId&&m.hasNameForId(this.componentId,this.staticRulesId))y=ie(y,this.staticRulesId);else{var v=ae(Xe(this.rules,c,m,h)),g=x(M(this.baseHash,v)>>>0);if(!m.hasNameForId(this.componentId,g)){var _=h(v,".".concat(g),void 0,this.componentId);m.insertRules(this.componentId,g,_)}y=ie(y,g),this.staticRulesId=g}else{for(var b=M(this.baseHash,h.hash),E="",P=0;P<this.rules.length;P++){var C=this.rules[P];if("string"==typeof C)E+=C;else if(C){var w=ae(Xe(C,c,m,h));b=M(b,w+P),E+=w}}if(E){var S=x(b>>>0);m.hasNameForId(this.componentId,S)||m.insertRules(this.componentId,S,h(E,".".concat(S),void 0,this.componentId)),y=ie(y,S)}}return y},e}(),Nt=v().createContext(void 0),It=Nt.Consumer;function nt(){var c=(0,y.useContext)(Nt);if(!c)throw he(18);return c}function ot(c){var m=v().useContext(Nt),h=(0,y.useMemo)(function(){return function(c,m){if(!c)throw he(14);if(re(c))return c(m);if(Array.isArray(c)||"object"!=typeof c)throw he(8);return m?__assign(__assign({},m),c):c}(c.theme,m)},[c.theme,m]);return c.children?v().createElement(Nt.Provider,{value:h},c.children):null}var Dt={};new Set;function it(c,m,h){var g=se(c),_=c,b=!L(c),E=m.attrs,P=void 0===E?me:E,C=m.componentId,w=void 0===C?function(c,m){var h="string"!=typeof c?"sc":R(c);Dt[h]=(Dt[h]||0)+1;var y="".concat(h,"-").concat($(te+h+Dt[h]));return m?"".concat(m,"-").concat(y):y}(m.displayName,m.parentComponentId):C,S=m.displayName,T=void 0===S?function(c){return L(c)?"styled.".concat(c):"Styled(".concat(B(c),")")}(c):S,N=m.displayName&&m.componentId?"".concat(R(m.displayName),"-").concat(m.componentId):m.componentId||w,D=g&&_.attrs?_.attrs.concat(P).filter(Boolean):P,A=m.shouldForwardProp;if(g&&_.shouldForwardProp){var W=_.shouldForwardProp;if(m.shouldForwardProp){var q=m.shouldForwardProp;A=function(c,m){return W(c,m)&&q(c,m)}}else A=W}var U=new Mt(h,N,g?_.componentStyle:void 0);function O(c,m){return function(c,m,h){var g=c.attrs,_=c.componentStyle,b=c.defaultProps,E=c.foldedComponentIds,P=c.styledComponentId,C=c.target,w=v().useContext(Nt),S=Ge(),T=c.shouldForwardProp||S.shouldForwardProp,N=I(m,w,b)||ye,D=function(c,m,h){for(var y,v=__assign(__assign({},m),{className:void 0,theme:h}),g=0;g<c.length;g+=1){var _=re(y=c[g])?y(v):y;for(var b in _)v[b]="className"===b?ie(v[b],_[b]):"style"===b?__assign(__assign({},v[b]),_[b]):_[b]}return m.className&&(v.className=ie(v.className,m.className)),v}(g,m,N),A=D.as||C,W={};for(var q in D)void 0===D[q]||"$"===q[0]||"as"===q||"theme"===q&&D.theme===N||("forwardedAs"===q?W.as=D.forwardedAs:T&&!T(q,A)||(W[q]=D[q]));var U=function(c,m){var h=Ge();return c.generateAndInjectStyles(m,h.styleSheet,h.stylis)}(_,D),Q=ie(E,P);return U&&(Q+=" "+U),D.className&&(Q+=" "+D.className),W[L(A)&&!ve.has(A)?"class":"className"]=Q,h&&(W.ref=h),(0,y.createElement)(A,W)}(Q,c,m)}O.displayName=T;var Q=v().forwardRef(O);return Q.attrs=D,Q.componentStyle=U,Q.displayName=T,Q.shouldForwardProp=A,Q.foldedComponentIds=g?ie(_.foldedComponentIds,_.styledComponentId):"",Q.styledComponentId=N,Q.target=g?_.target:c,Object.defineProperty(Q,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(c){this._foldedDefaultProps=g?function(c){for(var m=[],h=1;h<arguments.length;h++)m[h-1]=arguments[h];for(var y=0,v=m;y<v.length;y++)le(c,v[y],!0);return c}({},_.defaultProps,c):c}}),ue(Q,function(){return".".concat(Q.styledComponentId)}),b&&oe(Q,c,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0}),Q}function at(c,m){for(var h=[c[0]],y=0,v=m.length;y<v;y+=1)h.push(m[y],c[y+1]);return h}var ct=function(c){return Object.assign(c,{isCss:!0})};function lt(c){for(var m=[],h=1;h<arguments.length;h++)m[h-1]=arguments[h];if(re(c)||ce(c))return ct(Xe(at(me,__spreadArray([c],m,!0))));var y=c;return 0===m.length&&1===y.length&&"string"==typeof y[0]?Xe(y):ct(Xe(at(y,m)))}function ut(c,m,h){if(void 0===h&&(h=ye),!m)throw he(1,m);var s=function(y){for(var v=[],g=1;g<arguments.length;g++)v[g-1]=arguments[g];return c(m,h,lt.apply(void 0,__spreadArray([y],v,!1)))};return s.attrs=function(y){return ut(c,m,__assign(__assign({},h),{attrs:Array.prototype.concat(h.attrs,y).filter(Boolean)}))},s.withConfig=function(y){return ut(c,m,__assign(__assign({},h),y))},s}var pt=function(c){return ut(it,c)},At=pt;ve.forEach(function(c){At[c]=pt(c)});var Wt=function(){function e(c,m){this.rules=c,this.componentId=m,this.isStatic=Ze(c),Ot.registerId(this.componentId+1)}return e.prototype.createStyles=function(c,m,h,y){var v=y(ae(Xe(this.rules,m,h,y)),""),g=this.componentId+c;h.insertRules(g,g,v)},e.prototype.removeStyles=function(c,m){m.clearRules(this.componentId+c)},e.prototype.renderStyles=function(c,m,h,y){c>2&&Ot.registerId(this.componentId+c),this.removeStyles(c,h),this.createStyles(c,m,h,y)},e}();function ft(c){for(var m=[],h=1;h<arguments.length;h++)m[h-1]=arguments[h];var y=lt.apply(void 0,__spreadArray([c],m,!1)),g="sc-global-".concat($(JSON.stringify(y))),_=new Wt(y,g),l=function(c){var m=Ge(),h=v().useContext(Nt),y=v().useRef(m.styleSheet.allocateGSInstance(g)).current;return m.styleSheet.server&&u(y,c,m.styleSheet,h,m.stylis),v().useLayoutEffect(function(){if(!m.styleSheet.server)return u(y,c,m.styleSheet,h,m.stylis),function(){return _.removeStyles(y,m.styleSheet)}},[y,c,m.styleSheet,h,m.stylis]),null};function u(c,m,h,y,v){if(_.isStatic)_.renderStyles(c,pe,h,v);else{var g=__assign(__assign({},m),{theme:I(m,y,l.defaultProps)});_.renderStyles(c,g,h,v)}}return v().memo(l)}function mt(c){for(var m=[],h=1;h<arguments.length;h++)m[h-1]=arguments[h];var y=ae(lt.apply(void 0,__spreadArray([c],m,!1))),v=$(y);return new Tt(v,y)}function yt(c){var m=v().forwardRef(function(m,h){var y=I(m,v().useContext(Nt),c.defaultProps);return v().createElement(c,__assign({},m,{theme:y,ref:h}))});return m.displayName="WithTheme(".concat(B(c),")"),oe(m,c)}var Lt=function(){function e(){var c=this;this._emitSheetCSS=function(){var m=c.instance.toString();if(!m)return"";var h=Ce(),y=ae([h&&'nonce="'.concat(h,'"'),"".concat(J,'="true"'),"".concat(ee,'="').concat(te,'"')].filter(Boolean)," ");return"<style ".concat(y,">").concat(m,"</style>")},this.getStyleTags=function(){if(c.sealed)throw he(2);return c._emitSheetCSS()},this.getStyleElement=function(){var m;if(c.sealed)throw he(2);var h=c.instance.toString();if(!h)return[];var y=((m={})[J]="",m[ee]=te,m.dangerouslySetInnerHTML={__html:h},m),g=Ce();return g&&(y.nonce=g),[v().createElement("style",__assign({},y,{key:"sc-0-0"}))]},this.seal=function(){c.sealed=!0},this.instance=new Ot({isServer:!0}),this.sealed=!1}return e.prototype.collectStyles=function(c){if(this.sealed)throw he(2);return v().createElement(Ye,{sheet:this.instance},c)},e.prototype.interleaveWithNodeStream=function(c){throw he(3)},e}(),Ft={StyleSheet:Ot,mainSheet:wt};"__sc-".concat(J,"__")},15197:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Conflict;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(53442),P=h(69378),C=g(h(32879)),w=g(h(85418)),S=g(h(55725)),T=g(h(3416)),N=g(h(47483)),D=h(3073);function Conflict(c){var m,h=(0,b.useContext)(E.ImportContext),v=(0,b.useContext)(P.SharedContext),g=null===(m=h.data.uploadedData)||void 0===m?void 0:m.manifest,_=v.data.currentPage,A=function isImportedAssetSelected(c){return h.data.overrideConditions.includes(c)},W=function getAssetClassName(c){var m=["e-app-import-resolver-conflicts__asset"];return c&&m.push("active"),m.join(" ")};return b.default.createElement(T.default,{container:!0,noWrap:!0},b.default.createElement(C.default,{id:c.importedId,type:"main-type",className:"e-app-import-resolver-conflicts__checkbox",onCheck:function onCheck(m){!function eventTracking(c,m){(0,D.appsEventTrackingDispatch)("kit-library/".concat(c),{item:m,page_source:"import",step:_,event_type:"click"})}(m&&m?"check":"uncheck",c.conflictData.template_title)}}),b.default.createElement(T.default,{item:!0},b.default.createElement(w.default,{variant:"h5",tag:"h4",className:"e-app-import-resolver-conflicts__title"},function getConflictTitle(c){var m,h=g.templates[c].doc_type,y=null===(m=elementorAppConfig["import-export"].summaryTitles.templates)||void 0===m?void 0:m[h];return(null==y?void 0:y.single)||h}(c.importedId)),b.default.createElement(T.default,{item:!0},b.default.createElement(S.default,{variant:"sm",tag:"span",className:function getImportedAssetClasses(c){return W(A(c))}(c.importedId)},y("Imported","elementor"),": ",g.templates[c.importedId].title),b.default.createElement(S.default,{style:!0,variant:"sm",tag:"span",className:function getExistingAssetClasses(c){return W(!A(c))}(c.importedId)},y("Existing","elementor"),": ",c.conflictData.template_title," ",function getEditTemplateButton(m,h){return b.default.createElement(N.default,{className:"e-app-import-resolver-conflicts__edit-template",url:m,target:"_blank",icon:"eicon-editor-external-link",text:y("Edit Template","elementor"),hideText:!0,onClick:function onClick(){c.onClick&&c.onClick(h)}})}(c.conflictData.edit_url,c.conflictData.template_title)))))}Conflict.propTypes={importedId:v.number,conflictData:v.object,onClick:v.func}},15292:(c,m,h)=>{"use strict";h.d(m,{H:()=>E,j:()=>b});var y=h(41594),v=h.n(y),g=v().createContext(void 0),_=v().createContext(!1);function getQueryClientContext(c){return c&&"undefined"!=typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=g),window.ReactQueryClientContext):g}var b=function useQueryClient(){var c=v().useContext(getQueryClientContext(v().useContext(_)));if(!c)throw new Error("No QueryClient set, use QueryClientProvider to set one");return c},E=function QueryClientProvider(c){var m=c.client,h=c.contextSharing,y=void 0!==h&&h,g=c.children;v().useEffect(function(){return m.mount(),function(){m.unmount()}},[m]);var b=getQueryClientContext(y);return v().createElement(_.Provider,{value:y},v().createElement(b.Provider,{value:m},g))}},15656:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Dialog;var g=v(h(41594)),_=v(h(54902)),b=v(h(51776)),E=v(h(2363)),P=v(h(59250)),C=v(h(20394)),w=v(h(18861));function Dialog(c){return g.default.createElement(_.default,{onSubmit:c.onSubmit,onClose:c.onClose},g.default.createElement(b.default,null,c.title&&g.default.createElement(E.default,null,c.title),c.text&&g.default.createElement(P.default,null,c.text),c.children),g.default.createElement(C.default,null,g.default.createElement(w.default,{key:"dismiss",text:c.dismissButtonText,onClick:c.dismissButtonOnClick,url:c.dismissButtonUrl,target:c.dismissButtonTarget,tabIndex:"2"}),g.default.createElement(w.default,{key:"approve",text:c.approveButtonText,onClick:c.approveButtonOnClick,url:c.approveButtonUrl,target:c.approveButtonTarget,color:c.approveButtonColor,elRef:c.approveButtonRef,tabIndex:"1"})))}h(91618),Dialog.propTypes={title:y.any,text:y.any,children:y.any,onSubmit:y.func,onClose:y.func,dismissButtonText:y.string.isRequired,dismissButtonOnClick:y.func,dismissButtonUrl:y.string,dismissButtonTarget:y.string,approveButtonText:y.string.isRequired,approveButtonOnClick:y.func,approveButtonUrl:y.string,approveButtonColor:y.string,approveButtonTarget:y.string,approveButtonRef:y.object},Dialog.defaultProps={},Dialog.Wrapper=_.default,Dialog.Content=b.default,Dialog.Title=E.default,Dialog.Text=P.default,Dialog.Actions=C.default,Dialog.Button=w.default},15969:()=>{},16357:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardHeadline;var g=v(h(41594)),_=h(79397);function CardHeadline(c){var m=["eps-card__headline",c.className];return g.default.createElement("h4",{className:(0,_.arrayToClassName)(m)},c.children)}h(45302),CardHeadline.propTypes={className:y.string,children:y.any.isRequired},CardHeadline.defaultProps={className:""}},16686:()=>{},16746:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.Index=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(87861));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.Index=function(c){function Index(){return(0,v.default)(this,Index),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,Index,arguments)}return(0,E.default)(Index,c),(0,g.default)(Index,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"kit-taxonomies/{id}"}}])}($e.modules.CommandData)},17035:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=SiteArea;var g=v(h(41594)),_=v(h(55725)),b=v(h(76547)),E=v(h(54999)),P=h(3073);function SiteArea(c){var m=c.text,h=c.link;return g.default.createElement(E.default,{url:h,color:"secondary",underline:"none",onClick:function onClick(){return function eventTracking(c){var h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";(0,P.appsEventTrackingDispatch)(c,{site_area:m,page_source:"import complete",event_type:h})}("kit-library/open-site-area")}},g.default.createElement(_.default,{className:"e-app-import-export-kit-data__site-area"},m," ",h&&g.default.createElement(b.default,{className:"eicon-editor-external-link"})))}SiteArea.propTypes={text:y.string,link:y.string}},17129:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportInfoModal(c){return g.default.createElement(E.default,(0,_.default)({},c,{title:y("Export a Website Kit","elementor")}),g.default.createElement(E.default.Section,null,g.default.createElement(E.default.Heading,null,y("What’s a Website Kit?","elementor")),g.default.createElement(E.default.Text,null,g.default.createElement(g.default.Fragment,null,y("A Website Kit is a .zip file that contains all the parts of a complete site. It’s an easy way to get a site up and running quickly.","elementor"),g.default.createElement("br",null),g.default.createElement("br",null),g.default.createElement(b.default,{url:"https://go.elementor.com/app-what-are-kits"},y(" Learn more about Website Kits","elementor"))))),g.default.createElement(E.default.Section,null,g.default.createElement(E.default.Heading,null,y("How does exporting work?","elementor")),g.default.createElement(E.default.Text,null,g.default.createElement(g.default.Fragment,null,y("To turn your site into a Website Kit, select the templates, content, settings and plugins you want to include. Once it’s ready, you’ll get a .zip file that you can import to other sites.","elementor"),g.default.createElement("br",null),g.default.createElement("br",null),g.default.createElement(b.default,{url:"https://go.elementor.com/app-export-kit"},y("Learn More","elementor"))))))};var g=v(h(41594)),_=v(h(78304)),b=v(h(54999)),E=v(h(6634))},17556:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExportCompleteHeading;var g=v(h(41594)),_=h(86956),b=v(h(62688));function ExportCompleteHeading(c){var m=c.isCloudExport;return g.default.createElement(_.Box,{"data-testid":"export-complete-heading"},g.default.createElement(_.Typography,{variant:"h4",component:"h2",gutterBottom:!0},y(m?"Your website template is now saved to the library!":"Your .zip file is ready","elementor")),g.default.createElement(_.Typography,{variant:"body2",color:"text.secondary",sx:{mb:3}},m?g.default.createElement(g.default.Fragment,null,y("You can find it in the My Website Templates tab.","elementor")," ",g.default.createElement(_.Link,{href:elementorAppConfig.base_url+"#/kit-library/cloud",sx:{cursor:"pointer"}},y("Take me there","elementor"))):y("Once the download is complete, you can upload it to be used for other sites.","elementor")))}ExportCompleteHeading.propTypes={isCloudExport:b.default.bool.isRequired}},17901:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.ACTION_STATUS_MAP=void 0,m.default=function useInstallPlugins(c){var m=c.plugins,h=void 0===m?[]:m,y=c.bulkMaxItems,v=void 0===y?5:y,_=(0,P.default)(),w=_.response,S=_.pluginsActions,T=(0,E.useState)(!1),N=(0,b.default)(T,2),D=N[0],A=N[1],W=(0,E.useState)(!1),q=(0,b.default)(W,2),U=q[0],Q=q[1],K=(0,E.useState)([]),H=(0,b.default)(K,2),G=H[0],V=H[1],Y=(0,E.useState)([]),J=(0,b.default)(Y,2),Z=J[0],ee=J[1],te=(0,E.useState)(""),ne=(0,b.default)(te,2),de=ne[0],fe=ne[1],pe=(0,E.useState)(null),me=(0,b.default)(pe,2),ye=me[0],ve=me[1],ge=P.PLUGINS_RESPONSE_MAP.ERROR===w.status;return(0,E.useEffect)(function(){if(h.length)if(Z.length===h.length)Q(!0);else if(D){var c=Z.length;ve(h[c])}},[Z,D]),(0,E.useEffect)(function(){ye&&(P.PLUGIN_STATUS_MAP.INACTIVE===ye.status?S.activate:S.install)(ye.plugin)},[ye]),(0,E.useEffect)(function(){if(P.PLUGINS_RESPONSE_MAP.SUCCESS===w.status){var c=w.data;Array.isArray(c)?A(!0):Object.prototype.hasOwnProperty.call(c,"plugin")?P.PLUGIN_STATUS_MAP.ACTIVE===c.status?fe(C.ACTIVATED):P.PLUGIN_STATUS_MAP.INACTIVE===c.status&&fe(C.INSTALLED):fe(C.FAILED)}else P.PLUGINS_RESPONSE_MAP.ERROR===w.status&&fe(C.FAILED)},[w.status]),(0,E.useEffect)(function(){if(de){var c=C.FAILED===de?_objectSpread(_objectSpread({},ye),{},{status:C.FAILED}):w.data;V(function(m){var h=(0,g.default)(m);return h[Z.length]=c,h}),C.ACTIVATED===de||C.FAILED===de?ee(function(m){return[].concat((0,g.default)(m),[c])}):C.INSTALLED===de&&ve(c),fe("")}},[de]),{isDone:U,ready:Z,bulk:(0,E.useMemo)(function(){return function getBulk(){if(G.length>v)return G.slice(G.length-v,G.length);return G}()},[G]),isError:ge}};var g=y(h(10906)),_=y(h(85707)),b=y(h(18821)),E=h(41594),P=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(28816));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}var C=m.ACTION_STATUS_MAP=Object.freeze({ACTIVATED:"activated",INSTALLED:"installed",FAILED:"failed"})},18320:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardImage;var g=v(h(41594));function CardImage(c){var m=g.default.createElement("img",{src:c.src,alt:c.alt,className:"eps-card__image",loading:"lazy"});return g.default.createElement("figure",{className:"eps-card__figure ".concat(c.className)},m,c.children)}h(45302),CardImage.propTypes={className:y.string,src:y.string.isRequired,alt:y.string.isRequired,children:y.any},CardImage.defaultProps={className:""}},18441:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportComplete(){var c=(0,C.useExportContext)(),m=c.data,h=c.isCompleted,v=m.exportedData,g=m.kitInfo,A=(0,_.useRef)(null),W=function downloadFile(c){if(null==c||c.preventDefault(),!A.current){var m=document.createElement("a"),h="elementor-kit",y=(g.title||h).replace(D,"").trim()||h;m.href="data:application/zip;base64,"+v.file,m.download=y+".zip",A.current=m}A.current.click()};(0,_.useEffect)(function(){"cloud"!==g.source&&null!=v&&v.file&&W()},[v,g.source,W]);if(!h)return _.default.createElement(b.Redirect,{to:"/export-customization/",replace:!0});var q="cloud"===g.source,U=_.default.createElement(E.Stack,{direction:"row",spacing:1},q?_.default.createElement(E.Button,{variant:"contained",color:"primary",size:"small",onClick:function onClick(){return window.location.href=elementorAppConfig.base_url+"#/kit-library/cloud"},"data-testid":"view-in-library-button"},y("View in Library","elementor")):_.default.createElement(E.Button,{variant:"contained",color:"primary",size:"small",onClick:function handleDone(){window.top.location=elementorAppConfig.admin_url},"data-testid":"done-button"},y("Done","elementor"))),Q=_.default.createElement(P.PageHeader,{title:y("Export","elementor")});return _.default.createElement(P.BaseLayout,{topBar:_.default.createElement(P.TopBar,null,Q),footer:_.default.createElement(P.Footer,null,U)},_.default.createElement(P.CenteredContent,{hasFooter:!0},_.default.createElement(E.Stack,{spacing:3,alignItems:"center","data-testid":"export-complete-content"},_.default.createElement(S.default,null),_.default.createElement(T.default,{isCloudExport:q}),_.default.createElement(w.default,{kitInfo:g,includes:m.includes}),!q&&_.default.createElement(N.default,{onDownloadClick:W}))))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(83040),E=h(86956),P=h(62100),C=h(72946),w=v(h(82134)),S=v(h(81517)),T=v(h(17556)),N=v(h(67153));var D=/[<>:"/\\|?*]/g},18671:()=>{},18738:()=>{},18791:(c,m,h)=>{"use strict";var y=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;_interopRequireWildcard(h(41594));var v=_interopRequireWildcard(h(75206)),g=h(7470);function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,v=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=y(c)&&"function"!=typeof c)return b;if(g=m?v:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b})(c,m)}m.default={render:function render(c,m){var h;try{var y=(0,g.createRoot)(m);y.render(c),h=function unmountFunction(){y.unmount()}}catch(y){v.render(c,m),h=function unmountFunction(){v.unmountComponentAtNode(m)}}return{unmount:h}}}},18821:(c,m,h)=>{var y=h(70569),v=h(65474),g=h(37744),_=h(11018);c.exports=function _slicedToArray(c,m){return y(c)||v(c,m)||g(c,m)||_()},c.exports.__esModule=!0,c.exports.default=c.exports},18861:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DialogButton;var g=v(h(41594)),_=v(h(85707)),b=v(h(78304)),E=v(h(47483));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function DialogButton(c){return g.default.createElement(E.default,(0,b.default)({},c,{className:"eps-dialog__button ".concat(c.className)}))}DialogButton.propTypes=_objectSpread(_objectSpread({},E.default.propTypes),{},{tabIndex:y.string,type:y.string}),DialogButton.defaultProps=_objectSpread(_objectSpread({},E.default.defaultProps),{},{tabIndex:"0",type:"button"})},19232:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(54069));function PluginsSelection(c){var m=c.plugins,h=c.initialSelected,y=c.initialDisabled,v=c.withHeader,g=c.withStatus,E=c.layout,P=c.onSelect;if(!m.length)return null;var C=(0,_.useMemo)(function(){return m},[m]),w=(0,_.useMemo)(function(){return h},[m]),S=(0,_.useMemo)(function(){return y},[m]);return _.default.createElement(b.default,{plugins:C,initialDisabled:S,initialSelected:w,onSelect:function handleOnSelect(c){if(P){var h=c.map(function(c){return m[c]});P(h)}},withHeader:v,withStatus:g,layout:E})}PluginsSelection.propTypes={initialDisabled:y.array,initialSelected:y.array,layout:y.array,onSelect:y.func,plugins:y.array,selection:y.bool,withHeader:y.bool,withStatus:y.bool},PluginsSelection.defaultProps={initialDisabled:[],initialSelected:[],plugins:[],selection:!0,withHeader:!0,withStatus:!0};m.default=(0,_.memo)(PluginsSelection)},19616:(c,m,h)=>{"use strict";var y=h(12470).__;Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var v=[{type:"content",data:{title:y("Content","elementor"),features:{open:[y("Elementor Pages","elementor"),y("Landing Pages","elementor"),y("Elementor Posts","elementor"),y("WP Pages","elementor"),y("WP Posts","elementor"),y("WP Menus","elementor"),y("Custom Post Types","elementor")]}}},{type:"templates",data:{title:y("Templates","elementor"),features:{open:[y("Saved Templates","elementor"),y("Headers","elementor"),y("Footers","elementor"),y("Archives","elementor"),y("Single Posts","elementor"),y("Single Pages","elementor"),y("Search Results","elementor"),y("404 Error Page","elementor"),y("Popups","elementor"),y("Global widgets","elementor")]}}},{type:"settings",data:{title:y("Settings & configurations","elementor"),features:{open:[y("Global Colors","elementor"),y("Global Fonts","elementor"),y("Theme Style Settings","elementor"),y("Layout Settings","elementor"),y("Lightbox Settings","elementor"),y("Background Settings","elementor"),y("Custom Fonts","elementor"),y("Icons","elementor"),y("Code","elementor")]}}},{type:"plugins",data:{title:y("Plugins","elementor"),features:{open:[y("All plugins are required for this website templates work","elementor")]}}}];m.default=v},19744:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ProcessFailedDialog;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(83040),P=g(h(15656)),C=g(h(41494)),w=g(h(46361)),S=g(h(54999)),T=h(14300),N=g(h(62841)),D=h(69378);var A={general:{title:y("Unable to download the Website Template","elementor"),text:b.default.createElement(b.default.Fragment,null,y("We couldn’t download the Website Template due to technical difficulties on our part. Try again and if the problem persists contact ","elementor"),b.default.createElement(S.default,{url:"https://my.elementor.com/support-center/"},y("Support","elementor")))},"zip-archive-module-missing":{title:y("Couldn’t handle the Website Template","elementor"),text:y("Seems like your server is missing the PHP zip module. Install it on your server or contact your site host for further instructions.","elementor")},"invalid-zip-file":{title:y("Couldn’t use the .zip file","elementor"),text:b.default.createElement(b.default.Fragment,null,y("Seems like there is a problem with the zip’s files. Try installing again and if the problem persists contact ","elementor"),b.default.createElement(S.default,{url:"https://my.elementor.com/support-center/"},y("Support","elementor")))},timeout:{title:y("Unable to download the Website Template","elementor"),text:b.default.createElement(b.default.Fragment,null,y("It took too much time to download your Website Template and we were unable to complete the process. If all the Website Template’s parts don’t appear in ","elementor"),b.default.createElement(S.default,{url:elementorAppConfig.pages_url},y("Pages","elementor")),y(", try again and if the problem persists contact ","elementor"),b.default.createElement(S.default,{url:"https://my.elementor.com/support-center/"},y("Support","elementor")))},"invalid-kit-library-zip-error":{title:y("Unable to download the Website Template","elementor"),text:b.default.createElement(b.default.Fragment,null,y("We couldn’t download the Website Template due to technical difficulty on our part. Try again in a few minutes and if the problem persists contact ","elementor"),b.default.createElement(S.default,{url:"https://my.elementor.com/support-center/"},y("Support","elementor")))},"no-write-permissions":{title:y("Couldn’t access the file","elementor"),text:y("Seems like Elementor isn’t authorized to access relevant files for installing this Website Template. Contact your site host to get permission.","elementor")},"plugin-installation-permissions-error":{title:y("Couldn’t install the Website Template","elementor"),text:y("The Website Template includes plugins you don’t have permission to install. Contact your site admin to change your permissions.","elementor")},"third-party-error":{title:y("Unable to download the Website Template","elementor"),text:y("This is due to a conflict with one or more third-party plugins already active on your site. Try disabling them, and then give the download another go.","elementor")},"domdocument-missing":{title:y("Unable to download the Website Template","elementor"),text:y("This download requires the 'DOMDocument' PHP extension, which we couldn’t detect on your server. Enable this extension, or get in touch with your hosting service for support, and then give the download another go.","elementor")},"insufficient-quota":{title:y("Couldn’t Export the Website Template","elementor"),text:y("The export failed because it will pass the maximum Website Templates you can export.","elementor")},"failed-to-fetch-quota":{title:y("Couldn’t fetch quota","elementor"),text:y("Failed to fetch quota.","elementor")}};function ProcessFailedDialog(c){var m=c.errorType,h=c.onApprove,v=c.onDismiss,g=c.approveButton,_=c.dismissButton,S=c.onModalClose,W=c.onError,q=c.onLearnMore,U=(0,w.default)(),Q=((0,b.useContext)(D.SharedContext).data||{}).returnTo,K=(0,E.useNavigate)(),H=(0,C.default)().getAll(),G=H.referrer,V=H.source,Y="string"==typeof m&&A[m]?m:"general",J=A[Y],Z=J.title,ee=J.text,te=y("Try Again","elementor"),ne="general"===Y&&h,de=function handleOnDismiss(c){var m=T.KIT_SOURCE_MAP.CLOUD===V;Q&&(0,N.default)(Q)||("general"===Y&&v?v():"kit-library"===G?(null==S||S(c),K("/kit-library".concat(m?"/cloud":""))):U.backToDashboard())};return(0,b.useEffect)(function(){null==W||W()},[]),b.default.createElement(P.default,{title:Z,text:ee,approveButtonColor:"link",approveButtonText:ne?te:g,approveButtonOnClick:function handleOnApprove(){ne?h():window.open("https://go.elementor.com/app-import-download-failed","_blank"),null==q||q()},dismissButtonText:_,dismissButtonOnClick:function dismissButtonOnClick(c){return de(c)},onClose:de})}ProcessFailedDialog.propTypes={onApprove:v.func,onDismiss:v.func,errorType:v.string,approveButton:v.string,dismissButton:v.string,onModalClose:v.func,onError:v.func,onLearnMore:v.func},ProcessFailedDialog.defaultProps={errorType:"general",approveButton:y("Learn More","elementor"),dismissButton:y("Close","elementor")}},20364:()=>{},20365:c=>{c.exports=function _iterableToArray(c){if("undefined"!=typeof Symbol&&null!=c[Symbol.iterator]||null!=c["@@iterator"])return Array.from(c)},c.exports.__esModule=!0,c.exports.default=c.exports},20394:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DialogActions;var g=v(h(41594));function DialogActions(c){return g.default.createElement("div",{className:"eps-dialog__buttons"},c.children)}DialogActions.propTypes={children:y.any}},20567:c=>{"use strict";var warning=function(){};c.exports=warning},21364:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TableHead;var g=v(h(41594)),_=h(79397);function TableHead(c){return g.default.createElement("thead",{className:(0,_.arrayToClassName)(["eps-table__head",c.className])},c.children)}TableHead.propTypes={children:y.any.isRequired,className:y.string}},21689:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Box;var g=v(h(41594)),_=h(79397);function Box(c){var m="eps-box",h=[m,c.className],y={};return Object.prototype.hasOwnProperty.call(c,"padding")&&(y["--eps-box-padding"]=(0,_.pxToRem)(c.padding),h.push(m+"--padding")),g.default.createElement("div",{style:y,className:(0,_.arrayToClassName)(h)},c.children)}h(51959),Box.propTypes={className:y.string,padding:y.string,children:y.oneOfType([y.string,y.object,y.arrayOf(y.object)]).isRequired},Box.defaultProps={className:""}},22143:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.XIcon=void 0;var g=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),_=y(h(78304)),b=h(86956);m.XIcon=(0,g.forwardRef)(function(c,m){return g.default.createElement(b.SvgIcon,(0,_.default)({viewBox:"0 0 24 24"},c,{ref:m}),g.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),g.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))})},22322:()=>{},22803:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Loader;var g=v(h(41594)),_=h(79397),b=v(h(76547));function Loader(c){var m="e-app-import-export-loader",h=[m,"eicon-loading eicon-animation-spin"];return c.absoluteCenter&&h.push(m+"--absolute-center"),g.default.createElement(b.default,{className:(0,_.arrayToClassName)(h)})}h(95689),Loader.propTypes={absoluteCenter:y.bool},Loader.defaultProps={absoluteCenter:!1}},23074:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardFooter;var g=v(h(41594)),_=h(79397);function CardFooter(c){var m="eps-card__footer",h=[m,c.className],y={};return Object.prototype.hasOwnProperty.call(c,"padding")&&(y["--eps-card-footer-padding"]=(0,_.pxToRem)(c.padding),h.push(m+"--padding")),g.default.createElement("footer",{className:(0,_.arrayToClassName)(h),style:y},c.children)}h(45302),CardFooter.propTypes={className:y.string,padding:y.string,passive:y.bool,active:y.bool,children:y.object.isRequired},CardFooter.defaultProps={className:""}},23327:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PageHeader;var g=v(h(41594)),_=h(79397),b=v(h(3416)),E=v(h(85418)),P=v(h(55725));function PageHeader(c){var m=["e-app-import-export-page-header",c.className];return g.default.createElement("div",{className:(0,_.arrayToClassName)(m)},g.default.createElement(b.default,{container:!0},g.default.createElement(b.default,{item:!0,className:"e-app-import-export-page-header__content-wrapper"},c.heading&&g.default.createElement(E.default,{variant:"display-3",className:"e-app-import-export-page-header__heading"},c.heading),c.description&&g.default.createElement(P.default,{className:"e-app-import-export-page-header__description"},function handleMultiLine(c){if(Array.isArray(c)){var m=[];return c.forEach(function(c,h){h&&m.push(g.default.createElement("br",{key:h})),m.push(c)}),m}return c}(c.description)))))}h(15969),PageHeader.propTypes={className:y.string,heading:y.string,description:y.oneOfType([y.string,y.array,y.object])},PageHeader.defaultProps={className:""}},23393:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportPluginsActivation(){var c=(0,_.useContext)(E.ImportContext),m=(0,_.useContext)(P.SharedContext),h=(0,b.useNavigate)(),v=(0,D.default)({plugins:c.data.plugins}),g=v.bulk,A=v.ready,W=v.isDone;return(0,_.useEffect)(function(){c.data.plugins.length||h("/import/")},[c.data.plugins]),(0,_.useEffect)(function(){W&&(c.dispatch({type:"SET_IMPORTED_PLUGINS",payload:A}),c.dispatch({type:"SET_PLUGINS_STATE",payload:"success"}),m.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportPluginsActivation.name}))},[W]),(0,_.useEffect)(function(){c.data.importedPlugins.length&&h("/import/process")},[c.data.importedPlugins]),_.default.createElement(C.default,{type:"import"},_.default.createElement("section",{className:"e-app-import-plugins-activation"},_.default.createElement(w.default,{info:y("Activating plugins:","elementor")}),_.default.createElement(T.default,{container:!0,justify:"center"},_.default.createElement(T.default,{item:!0,className:"e-app-import-plugins-activation__installing-plugins"},!(null==g||!g.length)&&_.default.createElement(N.default,null,g.map(function(c){return _.default.createElement(N.default.Item,{className:"e-app-import-plugins-activation__plugin-status-item",key:c.name},_.default.createElement(S.default,{name:c.name,status:c.status}))}))))))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(83040),E=h(53442),P=h(69378),C=v(h(53931)),w=v(h(41994)),S=v(h(79238)),T=v(h(3416)),N=v(h(93279));h(18671);var D=v(h(17901))},23587:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.fetchCloudKitsEligibility=function fetchCloudKitsEligibility(){return _fetchCloudKitsEligibility.apply(this,arguments)};var v=y(h(61790)),g=y(h(58155));function _fetchCloudKitsEligibility(){return(_fetchCloudKitsEligibility=(0,g.default)(v.default.mark(function _callee(){var c,m;return v.default.wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(null===(c=elementorCommon)||void 0===c||null===(c=c.config)||void 0===c||null===(c=c.experimentalFeatures)||void 0===c?void 0:c["cloud-library"]){h.next=1;break}return h.abrupt("return",!1);case 1:return h.next=2,$e.data.get("cloud-kits/eligibility",{},{refresh:!0});case 2:return m=h.sent,h.abrupt("return",null==m?void 0:m.data);case 3:case"end":return h.stop()}},_callee)}))).apply(this,arguments)}},23730:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function KitName(){var c,m=(0,_.useContext)(E.ExportContext),h=(0,_.useState)(null),v=(0,b.default)(h,2),g=v[0],S=v[1],T=function handleChange(c){var h=c.target.value;m.dispatch({type:"SET_KIT_TITLE",payload:h}),N(h)},N=function validateAndShowError(c){var m=function validateKitName(c){return c&&0!==c.trim().length?null:y("Must add a name","elementor")}(c);return S(m),m};return _.default.createElement(C.default,{container:!0,direction:"column"},_.default.createElement(w.default,{tag:"span",variant:"xs"},y("Name","elementor")),_.default.createElement(P.default,{placeholder:y("Type name here...","elementor"),onChange:T,onBlur:T,className:g?"e-app-export-kit-information__field--error":"",title:g||"",value:(null===(c=m.data.kitInfo)||void 0===c?void 0:c.title)||"",autoFocus:!0}),_.default.createElement("div",{className:"e-app-export-kit-information__error-container"},g&&_.default.createElement(w.default,{variant:"xs",className:"e-app-export-kit-information__validation-error"},g)))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(18821)),E=h(81160),P=v(h(79788)),C=v(h(3416)),w=v(h(55725))},23857:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportProcess(){var c=(0,P.useExportContext)(),m=c.data,h=c.dispatch,v=c.isExporting,T=c.isPending,N=m.kitInfo,D=m.includes,A=m.plugins,W=(0,C.useExportKit)({includes:D,kitInfo:N,plugins:A,isExporting:v,dispatch:h}),q=W.status,U=W.STATUS_PROCESSING,Q=W.STATUS_ERROR;if(T)return g.default.createElement(_.Redirect,{to:"/export-customization/",replace:!0});var K=function getStatusText(){return y(q===U?"Setting up your website template...":"Export failed","elementor")},H=g.default.createElement(E.PageHeader,{title:y("Export","elementor")});return g.default.createElement(E.BaseLayout,{topBar:g.default.createElement(E.TopBar,null,H)},g.default.createElement(E.CenteredContent,null,g.default.createElement(b.Stack,{spacing:3,alignItems:"center"},q===U&&g.default.createElement(w.default,{statusText:K()}),q===Q&&g.default.createElement(S.default,{statusText:K()}))))};var g=v(h(41594)),_=h(83040),b=h(86956),E=h(62100),P=h(72946),C=h(73142),w=v(h(13419)),S=v(h(31372))},24017:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Sidebar;var g=v(h(41594));function Sidebar(c){return g.default.createElement("div",{className:"eps-app__sidebar"},c.children)}Sidebar.propTypes={children:y.object}},24079:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.reducer=void 0;var v=y(h(85707)),g=h(56915);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}m.reducer=function reducer(c,m){var h=m.type,y=m.payload;switch(h){case"SET_ID":return _objectSpread(_objectSpread({},c),{},{id:y});case"SET_FILE":return _objectSpread(_objectSpread({},c),{},{file:y});case"SET_KIT_SOURCE":return _objectSpread(_objectSpread({},c),{},{source:y});case"ADD_OVERRIDE_CONDITION":return g.ReducerUtils.updateArray(c,"overrideConditions",y,"add");case"REMOVE_OVERRIDE_CONDITION":return g.ReducerUtils.updateArray(c,"overrideConditions",y,"remove");case"SET_UPLOADED_DATA":return _objectSpread(_objectSpread({},c),{},{uploadedData:y});case"SET_IMPORTED_DATA":return _objectSpread(_objectSpread({},c),{},{importedData:y});case"SET_PLUGINS":return _objectSpread(_objectSpread({},c),{},{plugins:y});case"SET_REQUIRED_PLUGINS":return _objectSpread(_objectSpread({},c),{},{requiredPlugins:y});case"SET_IMPORTED_PLUGINS":return _objectSpread(_objectSpread({},c),{},{importedPlugins:y});case"SET_IS_PRO_INSTALLED_DURING_PROCESS":return _objectSpread(_objectSpread({},c),{},{isProInstalledDuringProcess:y});case"SET_ACTION_TYPE":return _objectSpread(_objectSpread({},c),{},{actionType:y});case"SET_IS_RESOLVED":return _objectSpread(_objectSpread({},c),{},{isResolvedData:y});case"SET_PLUGINS_STATE":return _objectSpread(_objectSpread({},c),{},{pluginsState:y});default:return c}}},24673:(c,m,h)=>{"use strict";h.d(m,{U:()=>b,h:()=>_});var y=h(41594),v=h.n(y);function createValue(){var c=!1;return{clearReset:function clearReset(){c=!1},reset:function reset(){c=!0},isReset:function isReset(){return c}}}var g=v().createContext(createValue()),_=function useQueryErrorResetBoundary(){return v().useContext(g)},b=function QueryErrorResetBoundary(c){var m=c.children,h=v().useMemo(function(){return createValue()},[]);return v().createElement(g.Provider,{value:h},"function"==typeof m?m(h):m)}},24685:(c,m,h)=>{"use strict";var y=h(62688),v=h(12470).__,g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DashboardButton;var _=g(h(41594)),b=g(h(78304)),E=g(h(47483)),P=g(h(46361)),C=h(79397);function DashboardButton(c){var m=(0,P.default)(),h=["e-app-dashboard-button",c.className];return _.default.createElement(E.default,(0,b.default)({},c,{className:(0,C.arrayToClassName)(h),text:c.text,onClick:m.backToDashboard}))}DashboardButton.propTypes={className:y.string,text:y.string},DashboardButton.defaultProps={className:"",variant:"contained",color:"primary",text:v("Back to dashboard","elementor")}},25160:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),Object.defineProperty(m,"DownloadLink",{enumerable:!0,get:function get(){return P.DownloadLink}}),Object.defineProperty(m,"Favorites",{enumerable:!0,get:function get(){return C.Favorites}}),m.Index=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(87861)),P=h(14387),C=h(39701);function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.Index=function(c){function Index(){return(0,v.default)(this,Index),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,Index,arguments)}return(0,E.default)(Index,c),(0,g.default)(Index,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"kits/{id}"}}])}($e.modules.CommandData)},25301:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ProBanner;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(18821)),P=g(h(95801)),C=g(h(69783)),w=g(h(15656));function ProBanner(c){var m=c.onRefresh,h=(0,b.useState)(!1),v=(0,E.default)(h,2),g=v[0],_=v[1],S=function onDialogDismiss(){return _(!1)};return b.default.createElement(b.default.Fragment,null,b.default.createElement(P.default,{heading:y("Install Elementor Pro","elementor"),description:y("Without Elementor Pro, importing components like templates, widgets and popups won't work.","elementor"),button:b.default.createElement(C.default,{onClick:function handleGoPro(){_(!0),function openGoProExternalPage(){window.open("https://go.elementor.com/go-pro-import-export/","_blank")}()}})}),g&&b.default.createElement(w.default,{title:y("Is your Elementor Pro ready?","elementor"),text:y("If you’ve purchased, installed & activated Elementor Pro, we can continue importing all the parts of this site.","elementor"),approveButtonColor:"primary",approveButtonText:y("Yes","elementor"),approveButtonOnClick:function onDialogApprove(){_(!1),m()},dismissButtonText:y("Not yet","elementor"),dismissButtonOnClick:S,onClose:S}))}h(78103),ProBanner.propTypes={status:v.string,onRefresh:v.func},ProBanner.defaultProps={status:""}},25368:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Content;var g=v(h(41594));function Content(c){return g.default.createElement("main",{className:"eps-app__content ".concat(c.className)},c.children)}Content.propTypes={children:y.any,className:y.string},Content.defaultProps={className:""}},25469:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExistingPlugins;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(19232)),P=g(h(85418));var C=[4,1];function ExistingPlugins(c){var m=c.plugins;if(null==m||!m.length)return null;var h=(0,b.useMemo)(function(){return m},[]),v=(0,b.useMemo)(function(){return m.map(function(c,m){return m})},[]);return b.default.createElement("div",{className:"e-app-import-plugins__section"},b.default.createElement(P.default,{variant:"h5",tag:"h3",className:"e-app-import-plugins__section-heading"},y("Plugins you already have:","elementor")),b.default.createElement(E.default,{withHeader:!1,withStatus:!1,plugins:h,initialSelected:v,initialDisabled:v,excludeSelections:v,layout:C}))}ExistingPlugins.propTypes={plugins:v.array}},25800:(c,m,h)=>{"use strict";h.d(m,{j:()=>v});var y=h(6369),v=new(function(){function NotifyManager(){this.queue=[],this.transactions=0,this.notifyFn=function(c){c()},this.batchNotifyFn=function(c){c()}}var c=NotifyManager.prototype;return c.batch=function batch(c){var m;this.transactions++;try{m=c()}finally{this.transactions--,this.transactions||this.flush()}return m},c.schedule=function schedule(c){var m=this;this.transactions?this.queue.push(c):(0,y.G6)(function(){m.notifyFn(c)})},c.batchCalls=function batchCalls(c){var m=this;return function(){for(var h=arguments.length,y=new Array(h),v=0;v<h;v++)y[v]=arguments[v];m.schedule(function(){c.apply(void 0,y)})}},c.flush=function flush(){var c=this,m=this.queue;this.queue=[],m.length&&(0,y.G6)(function(){c.batchNotifyFn(function(){m.forEach(function(m){c.notifyFn(m)})})})},c.setNotifyFunction=function setNotifyFunction(c){this.notifyFn=c},c.setBatchNotifyFunction=function setBatchNotifyFunction(c){this.batchNotifyFn=c},NotifyManager}())},26434:(c,m,h)=>{"use strict";h.d(m,{t:()=>_});var y=h(59994),v=h(13411),g=h(6369),_=new(function(c){function OnlineManager(){var m;return(m=c.call(this)||this).setup=function(c){var m;if(!g.S$&&(null==(m=window)?void 0:m.addEventListener)){var h=function listener(){return c()};return window.addEventListener("online",h,!1),window.addEventListener("offline",h,!1),function(){window.removeEventListener("online",h),window.removeEventListener("offline",h)}}},m}(0,y.A)(OnlineManager,c);var m=OnlineManager.prototype;return m.onSubscribe=function onSubscribe(){this.cleanup||this.setEventListener(this.setup)},m.onUnsubscribe=function onUnsubscribe(){var c;this.hasListeners()||(null==(c=this.cleanup)||c.call(this),this.cleanup=void 0)},m.setEventListener=function setEventListener(c){var m,h=this;this.setup=c,null==(m=this.cleanup)||m.call(this),this.cleanup=c(function(c){"boolean"==typeof c?h.setOnline(c):h.onOnline()})},m.setOnline=function setOnline(c){this.online=c,c&&this.onOnline()},m.onOnline=function onOnline(){this.listeners.forEach(function(c){c()})},m.isOnline=function isOnline(){return"boolean"==typeof this.online?this.online:"undefined"==typeof navigator||void 0===navigator.onLine||navigator.onLine},OnlineManager}(v.Q))},26587:()=>{},28042:()=>{},28101:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function NotFound(){var c={title:y("Not Found","elementor"),className:"eps-app__not-found",content:g.default.createElement("h1",null," ",y("Not Found","elementor")," "),sidebar:g.default.createElement(g.default.Fragment,null)};return g.default.createElement(_.default,c)};var g=v(h(41594)),_=v(h(80226))},28127:(c,m,h)=>{"use strict";var y="__global_unique_id__";c.exports=function(){return h.g[y]=(h.g[y]||0)+1}},28492:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportProcess(){var c=(0,g.useContext)(w.SharedContext),m=(0,g.useContext)(S.ExportContext),h=(0,C.useNavigate)(),y=(0,D.default)(),v=y.kitState,W=y.kitActions,q=y.KIT_STATUS_MAP,U=(0,g.useState)(""),Q=(0,P.default)(U,2),K=Q[0],H=Q[1],G=m.data||{},V=G.plugins,Y=G.exportedData,J=G.kitInfo,Z=G.isExportProcessStarted,ee=(0,A.default)(V).pluginsData,te=function onDialogDismiss(){m.dispatch({type:"SET_DOWNLOAD_URL",payload:""}),h("export")},ne=function generateScreenshot(){return new Promise(function(c){var m=document.createElement("iframe");m.style="visibility: hidden;",m.width="1200",m.height="1000";var h=function messageHandler(y){"kit-screenshot-done"===y.data.name&&(window.removeEventListener("message",h),document.body.removeChild(m),c(y.data.imageUrl||null),window.removeEventListener("message",h))};window.addEventListener("message",h);var y=new URL(window.location.origin);y.searchParams.set("kit_thumbnail","1"),y.searchParams.set("nonce",elementorAppConfig["import-export"].kitPreviewNonce),document.body.appendChild(m),m.src=y.toString()})},de=function(){var h=(0,E.default)(_.default.mark(function _callee(){var h,y,v,g,E,P;return _.default.wrap(function(_){for(;;)switch(_.prev=_.next){case 0:if(y=c.data,v=y.includes,g=y.selectedCustomPostTypes,E={include:[].concat((0,b.default)(v),["plugins"]),kitInfo:J,plugins:ee,selectedCustomPostTypes:g},!(null===(h=elementorCommon)||void 0===h||null===(h=h.config)||void 0===h||null===(h=h.experimentalFeatures)||void 0===h?void 0:h["cloud-library"])||D.KIT_SOURCE_MAP.CLOUD!==m.data.kitInfo.source){_.next=2;break}return _.next=1,ne();case 1:P=_.sent,E.screenShotBlob=P;case 2:W.export(E);case 3:case"end":return _.stop()}},_callee)}));return function exportKit(){return h.apply(this,arguments)}}();return(0,g.useEffect)(function(){Z?de():h("/export")},[]),(0,g.useEffect)(function(){switch(v.status){case q.EXPORTED:m.dispatch({type:"SET_EXPORTED_DATA",payload:v.data});break;case q.ERROR:H(v.data)}},[v.status]),(0,g.useEffect)(function(){Y&&h("export/complete")},[Y]),g.default.createElement(T.default,{type:"export"},g.default.createElement(N.default,{errorType:K,onDialogApprove:te,onDialogDismiss:te}))};var g=_interopRequireWildcard(h(41594)),_=y(h(61790)),b=y(h(10906)),E=y(h(58155)),P=y(h(18821)),C=h(83040),w=h(69378),S=h(81160),T=y(h(53931)),N=y(h(41994)),D=_interopRequireWildcard(h(14300)),A=y(h(30307));function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b})(c,m)}},28816:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.PLUGIN_STATUS_MAP=m.PLUGINS_RESPONSE_MAP=void 0,m.default=function usePlugins(){var c=(0,g.useState)(function(){return P()}),m=(0,v.default)(c,2),h=m[0],y=m[1],C=(0,g.useRef)(!0),w=function fetchRest(c){var m=c.body,v=c.method,g=c.endpoint,b=void 0===g?"":g,P={method:v,headers:{"Content-Type":"application/json; charset=utf-8","X-WP-Nonce":wpApiSettings.nonce,"X-Elementor-Action":"import-plugins"}};return m&&(P.body=JSON.stringify(m)),h.data&&T(),new Promise(function(c,m){fetch(E+b,P).then(function(c){return c.json()}).then(function(m){C.current&&y({status:_.SUCCESS,data:m}),c(m)}).catch(function(c){y({status:_.ERROR,data:c}),m(c)})})},S=function fetchData(c){return w({method:"GET",endpoint:c})},T=function reset(){return y(P())};return(0,g.useEffect)(function(){return S(),function(){C.current=!1}},[]),{response:h,pluginsActions:{fetch:S,install:function install(c){return c=c.split("/")[0],w({method:"POST",body:{slug:c}})},activate:function activate(c){return w({endpoint:c,method:"PUT",body:{status:b.ACTIVE}})},deactivate:function deactivate(c){return w({endpoint:c,method:"PUT",body:{status:b.INACTIVE}})},remove:function remove(c){return w({endpoint:c,method:"DELETE"})},reset:T}}};var v=y(h(18821)),g=h(41594),_=m.PLUGINS_RESPONSE_MAP=Object.freeze({INITIAL:"initial",SUCCESS:"success",ERROR:"error"}),b=m.PLUGIN_STATUS_MAP=Object.freeze({ACTIVE:"active",MULTISITE_ACTIVE:"network-active",INACTIVE:"inactive",NOT_INSTALLED:"Not Installed"}),E=elementorCommon.config.urls.rest+"wp/v2/plugins/",P=function getInitialState(){return{status:_.INITIAL,data:null}}},28929:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Collapse;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(85707)),E=v(h(18821)),P=h(79397),C=h(77879),w=v(h(79514)),S=v(h(62521));function Collapse(c){var m=(0,_.useState)(c.isOpened),h=(0,E.default)(m,2),y=h[0],v=h[1],g="e-app-collapse",w=[g,c.className,(0,b.default)({},g+"--opened",y)];return(0,_.useEffect)(function(){c.isOpened!==y&&v(c.isOpened)},[c.isOpened]),(0,_.useEffect)(function(){c.onChange&&c.onChange(y)},[y]),_.default.createElement(C.CollapseContext.Provider,{value:{toggle:function toggle(){return v(function(c){return!c})}}},_.default.createElement("div",{className:(0,P.arrayToClassName)(w)},c.children))}h(57463),Collapse.propTypes={className:y.string,isOpened:y.bool,onChange:y.func,children:y.oneOfType([y.node,y.arrayOf(y.node)])},Collapse.defaultProps={className:"",isOpened:!1},Collapse.Toggle=w.default,Collapse.Content=S.default},29402:c=>{function _getPrototypeOf(m){return c.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(c){return c.__proto__||Object.getPrototypeOf(c)},c.exports.__esModule=!0,c.exports.default=c.exports,_getPrototypeOf(m)}c.exports=_getPrototypeOf,c.exports.__esModule=!0,c.exports.default=c.exports},29994:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=InfoModalTip;var g=v(h(41594)),_=v(h(78304)),b=h(79397),E=v(h(61678));function InfoModalTip(c){return g.default.createElement(E.default.Tip,(0,_.default)({},c,{className:(0,b.arrayToClassName)(["e-app-import-export-info-modal__tip",c.className])}))}InfoModalTip.propTypes={className:y.string},InfoModalTip.defaultProps={className:""}},30242:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useConnectState(){var c=(0,y.useContext)(v.ConnectStateContext);if(!c)throw new Error("useConnectState must be used within a ConnectStateProvider");return c};var y=h(41594),v=h(76673)},30307:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useExportPluginsData(c){return{pluginsData:(0,y.useMemo)(function(){return function getData(){var m=[];return c.forEach(function(c){var h=c.name,y=c.plugin,v=c.plugin_uri,g=c.version;m.push({name:h,plugin:y,pluginUri:v,version:g})}),m}()},[c])}};var y=h(41594)},31138:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function KitInfo(){var c=(0,b.useExportContext)(),m=c.data,h=c.dispatch,v={templateName:m.kitInfo.title||"",description:m.kitInfo.description||""},E=v.templateName,P=v.description;return g.default.createElement(_.Box,{sx:{mb:3,border:1,borderRadius:1,borderColor:"action.focus",p:2.5}},g.default.createElement(_.Typography,{variant:"caption",component:"label",color:"text.secondary"},y("Website template name","elementor")," *"),g.default.createElement(_.Input,{fullWidth:!0,required:!0,value:E,onChange:function onChange(c){return h({type:"SET_KIT_TITLE",payload:c.target.value||""})},placeholder:y("Type name here...","elementor"),inputProps:{maxLength:75},sx:{mb:2}}),g.default.createElement(_.Typography,{variant:"caption",component:"label",color:"text.secondary"},y("Description (Optional)","elementor")),g.default.createElement(_.Input,{fullWidth:!0,multiline:!0,value:P,onChange:function onChange(c){return h({type:"SET_KIT_DESCRIPTION",payload:c.target.value||""})},placeholder:y("Type description here...","elementor")}))};var g=v(h(41594)),_=h(86956),b=h(72946)},31372:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExportError;var g=v(h(41594)),_=h(86956),b=v(h(62688)),E=h(59957),P="https://go.elementor.com/app-import-download-failed";function ExportError(c){var m=c.statusText;return g.default.createElement(g.default.Fragment,null,g.default.createElement(_.Box,{sx:{width:60,height:60,borderRadius:"50%",backgroundColor:"error.main",display:"flex",alignItems:"center",justifyContent:"center",color:"white"}},g.default.createElement(E.XIcon,{sx:{fontSize:"24px"}})),g.default.createElement(_.Typography,{variant:"h5",component:"h2"},m),g.default.createElement(_.Typography,{variant:"body1",color:"text.secondary"},y("We couldn't complete the export. Please try again, and if the problem persists, check our help guide for troubleshooting steps.","elementor")),g.default.createElement(_.Stack,{direction:"row",spacing:2},g.default.createElement(_.Button,{variant:"contained",onClick:function handleTryAgain(){window.location.href=elementorAppConfig.base_url+"#/export-customization/"},"data-testid":"export-error-try-again-button"},y("Try Again","elementor")),g.default.createElement(_.Button,{variant:"outlined",onClick:function handleLearnMore(){window.open(P,"_blank")},"data-testid":"export-error-learn-more-button"},y("Learn More","elementor"))))}ExportError.propTypes={statusText:b.default.string.isRequired}},31481:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportContent(){var c=(0,_.useContext)(b.SharedContext),m=(0,_.useContext)(E.ImportContext),h=c.data,v=h.referrer,g=h.includes,D=h.currentPage,A=m.data,W=A.plugins,q=A.requiredPlugins,U=A.uploadedData,Q=A.file,K=A.isProInstalledDuringProcess,H=(0,N.default)().navigateToMainScreen,G=function handleResetProcess(){return m.dispatch({type:"SET_FILE",payload:null})},V=function eventTracking(c){"kit-library"===v&&(0,P.appsEventTrackingDispatch)(c,{page_source:"import",step:D,event_type:"click"})};return(0,_.useEffect)(function(){c.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportContent.name})},[]),(0,_.useEffect)(function(){Q||H()},[Q]),_.default.createElement(C.default,{type:"import",footer:function getFooter(){return _.default.createElement(T.default,{hasPlugins:!!W.length,hasConflicts:!!(g.includes("templates")&&null!=U&&U.conflicts&&Object.keys(U.conflicts).length),isImportAllowed:!(!W.length&&!g.length),onResetProcess:G,onPreviousClick:function onPreviousClick(){return V("kit-library/go-back")},onImportClick:function onImportClick(){return V("kit-library/approve-import")}})}()},_.default.createElement("section",{className:"e-app-import-content"},_.default.createElement(w.default,{heading:y("Select which parts you want to apply","elementor"),description:y("All items are already selected by default. Uncheck the ones you don't want.","elementor")}),_.default.createElement(S.default,{manifest:null==U?void 0:U.manifest,hasPro:K,hasPlugins:!!q.length,isAllRequiredPluginsSelected:q.length===W.length,onResetProcess:G})))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(69378),E=h(53442),P=h(3073),C=v(h(53931)),w=v(h(23327)),S=v(h(87026)),T=v(h(79784)),N=v(h(82372));h(5195)},31571:(c,m,h)=>{"use strict";h.d(m,{_:()=>b});var y=h(68102),v=h(59994),g=h(85869),_=h(25800),b=function(c){function MutationObserver(m,h){var y;return(y=c.call(this)||this).client=m,y.setOptions(h),y.bindMethods(),y.updateResult(),y}(0,v.A)(MutationObserver,c);var m=MutationObserver.prototype;return m.bindMethods=function bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},m.setOptions=function setOptions(c){this.options=this.client.defaultMutationOptions(c)},m.onUnsubscribe=function onUnsubscribe(){var c;this.listeners.length||(null==(c=this.currentMutation)||c.removeObserver(this))},m.onMutationUpdate=function onMutationUpdate(c){this.updateResult();var m={listeners:!0};"success"===c.type?m.onSuccess=!0:"error"===c.type&&(m.onError=!0),this.notify(m)},m.getCurrentResult=function getCurrentResult(){return this.currentResult},m.reset=function reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},m.mutate=function mutate(c,m){return this.mutateOptions=m,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,(0,y.A)({},this.options,{variables:void 0!==c?c:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},m.updateResult=function updateResult(){var c=this.currentMutation?this.currentMutation.state:(0,g.$)(),m=(0,y.A)({},c,{isLoading:"loading"===c.status,isSuccess:"success"===c.status,isError:"error"===c.status,isIdle:"idle"===c.status,mutate:this.mutate,reset:this.reset});this.currentResult=m},m.notify=function notify(c){var m=this;_.j.batch(function(){m.mutateOptions&&(c.onSuccess?(null==m.mutateOptions.onSuccess||m.mutateOptions.onSuccess(m.currentResult.data,m.currentResult.variables,m.currentResult.context),null==m.mutateOptions.onSettled||m.mutateOptions.onSettled(m.currentResult.data,null,m.currentResult.variables,m.currentResult.context)):c.onError&&(null==m.mutateOptions.onError||m.mutateOptions.onError(m.currentResult.error,m.currentResult.variables,m.currentResult.context),null==m.mutateOptions.onSettled||m.mutateOptions.onSettled(void 0,m.currentResult.error,m.currentResult.variables,m.currentResult.context))),c.listeners&&m.listeners.forEach(function(c){c(m.currentResult)})})},MutationObserver}(h(13411).Q)},31659:(c,m,h)=>{"use strict";m.__esModule=!0;var y=h(41594),v=(_interopRequireDefault(y),_interopRequireDefault(h(62688))),g=_interopRequireDefault(h(28127));_interopRequireDefault(h(20567));function _interopRequireDefault(c){return c&&c.__esModule?c:{default:c}}function _classCallCheck(c,m){if(!(c instanceof m))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(c,m){if(!c)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!m||"object"!=typeof m&&"function"!=typeof m?c:m}function _inherits(c,m){if("function"!=typeof m&&null!==m)throw new TypeError("Super expression must either be null or a function, not "+typeof m);c.prototype=Object.create(m&&m.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(c,m):c.__proto__=m)}var _=**********;m.default=function createReactContext(c,m){var h,b,E="__create-react-context-"+(0,g.default)()+"__",P=function(c){function Provider(){var m,h;_classCallCheck(this,Provider);for(var y=arguments.length,v=Array(y),g=0;g<y;g++)v[g]=arguments[g];return m=h=_possibleConstructorReturn(this,c.call.apply(c,[this].concat(v))),h.emitter=function createEventEmitter(c){var m=[];return{on:function on(c){m.push(c)},off:function off(c){m=m.filter(function(m){return m!==c})},get:function get(){return c},set:function set(h,y){c=h,m.forEach(function(m){return m(c,y)})}}}(h.props.value),_possibleConstructorReturn(h,m)}return _inherits(Provider,c),Provider.prototype.getChildContext=function getChildContext(){var c;return(c={})[E]=this.emitter,c},Provider.prototype.componentWillReceiveProps=function componentWillReceiveProps(c){if(this.props.value!==c.value){var h=this.props.value,y=c.value,v=void 0;!function objectIs(c,m){return c===m?0!==c||1/c==1/m:c!=c&&m!=m}(h,y)?(v="function"==typeof m?m(h,y):_,0!==(v|=0)&&this.emitter.set(c.value,v)):v=0}},Provider.prototype.render=function render(){return this.props.children},Provider}(y.Component);P.childContextTypes=((h={})[E]=v.default.object.isRequired,h);var C=function(m){function Consumer(){var c,h;_classCallCheck(this,Consumer);for(var y=arguments.length,v=Array(y),g=0;g<y;g++)v[g]=arguments[g];return c=h=_possibleConstructorReturn(this,m.call.apply(m,[this].concat(v))),h.state={value:h.getValue()},h.onUpdate=function(c,m){0!==((0|h.observedBits)&m)&&h.setState({value:h.getValue()})},_possibleConstructorReturn(h,c)}return _inherits(Consumer,m),Consumer.prototype.componentWillReceiveProps=function componentWillReceiveProps(c){var m=c.observedBits;this.observedBits=null==m?_:m},Consumer.prototype.componentDidMount=function componentDidMount(){this.context[E]&&this.context[E].on(this.onUpdate);var c=this.props.observedBits;this.observedBits=null==c?_:c},Consumer.prototype.componentWillUnmount=function componentWillUnmount(){this.context[E]&&this.context[E].off(this.onUpdate)},Consumer.prototype.getValue=function getValue(){return this.context[E]?this.context[E].get():c},Consumer.prototype.render=function render(){return function onlyChild(c){return Array.isArray(c)?c[0]:c}(this.props.children)(this.state.value)},Consumer}(y.Component);return C.contextTypes=((b={})[E]=v.default.object,b),{Provider:P,Consumer:C}},c.exports=m.default},31846:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TemplatesFeatures;var g=v(h(41594)),_=v(h(63895));function TemplatesFeatures(c){var m,h=null===(m=c.features.locked)||void 0===m?void 0:m.length;return g.default.createElement(g.default.Fragment,null,function getOpenFeatures(){var m;return null===(m=c.features.open)||void 0===m?void 0:m.join(", ")}(),function getLockedFeatures(){if(h)return g.default.createElement(_.default,{tag:"span",offset:19,show:c.showTooltip,title:c.features.tooltip,disabled:!c.isLocked,className:c.isLocked?"e-app-export-templates-features__locked":""},", "+c.features.locked.join(", "))}())}h(94010),TemplatesFeatures.propTypes={features:y.object,isLocked:y.bool,showTooltip:y.bool},TemplatesFeatures.defaultProps={showTooltip:!1}},32091:c=>{"use strict";c.exports=function(c,m,h,y,v,g,_,b){if(!c){var E;if(void 0===m)E=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var P=[h,y,v,g,_,b],C=0;(E=new Error(m.replace(/%s/g,function(){return P[C++]}))).name="Invariant Violation"}throw E.framesToPop=1,E}}},32879:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ConflictCheckbox;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(53442),E=v(h(47579));function ConflictCheckbox(c){var m=(0,_.useContext)(b.ImportContext);return(0,_.useEffect)(function(){m.data.overrideConditions.length||m.dispatch({type:"ADD_OVERRIDE_CONDITION",payload:c.id})},[]),_.default.createElement(E.default,{checked:function isSelected(){return m.data.overrideConditions.includes(c.id)}(),onChange:function updateOverrideCondition(h){var y=h.target.checked,v=y?"ADD_OVERRIDE_CONDITION":"REMOVE_OVERRIDE_CONDITION";c.onCheck&&c.onCheck(y),m.dispatch({type:v,payload:c.id})},className:c.className})}ConflictCheckbox.propTypes={className:y.string,id:y.number.isRequired,onCheck:y.func},ConflictCheckbox.defaultProps={className:""}},33382:(c,m,h)=>{"use strict";h.d(m,{$:()=>C});var y=h(59994),v=h(6369),g=h(68102),_=h(25800),b=h(8118),E=h(81133),P=function(){function Query(c){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=c.defaultOptions,this.setOptions(c.options),this.observers=[],this.cache=c.cache,this.queryKey=c.queryKey,this.queryHash=c.queryHash,this.initialState=c.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=c.meta,this.scheduleGc()}var c=Query.prototype;return c.setOptions=function setOptions(c){var m;this.options=(0,g.A)({},this.defaultOptions,c),this.meta=null==c?void 0:c.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(m=this.options.cacheTime)?m:3e5)},c.setDefaultOptions=function setDefaultOptions(c){this.defaultOptions=c},c.scheduleGc=function scheduleGc(){var c=this;this.clearGcTimeout(),(0,v.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){c.optionalRemove()},this.cacheTime))},c.clearGcTimeout=function clearGcTimeout(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},c.optionalRemove=function optionalRemove(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},c.setData=function setData(c,m){var h,y,g=this.state.data,_=(0,v.Zw)(c,g);return(null==(h=(y=this.options).isDataEqual)?void 0:h.call(y,g,_))?_=g:!1!==this.options.structuralSharing&&(_=(0,v.BH)(g,_)),this.dispatch({data:_,type:"success",dataUpdatedAt:null==m?void 0:m.updatedAt}),_},c.setState=function setState(c,m){this.dispatch({type:"setState",state:c,setStateOptions:m})},c.cancel=function cancel(c){var m,h=this.promise;return null==(m=this.retryer)||m.cancel(c),h?h.then(v.lQ).catch(v.lQ):Promise.resolve()},c.destroy=function destroy(){this.clearGcTimeout(),this.cancel({silent:!0})},c.reset=function reset(){this.destroy(),this.setState(this.initialState)},c.isActive=function isActive(){return this.observers.some(function(c){return!1!==c.options.enabled})},c.isFetching=function isFetching(){return this.state.isFetching},c.isStale=function isStale(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(c){return c.getCurrentResult().isStale})},c.isStaleByTime=function isStaleByTime(c){return void 0===c&&(c=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,v.j3)(this.state.dataUpdatedAt,c)},c.onFocus=function onFocus(){var c,m=this.observers.find(function(c){return c.shouldFetchOnWindowFocus()});m&&m.refetch(),null==(c=this.retryer)||c.continue()},c.onOnline=function onOnline(){var c,m=this.observers.find(function(c){return c.shouldFetchOnReconnect()});m&&m.refetch(),null==(c=this.retryer)||c.continue()},c.addObserver=function addObserver(c){-1===this.observers.indexOf(c)&&(this.observers.push(c),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:c}))},c.removeObserver=function removeObserver(c){-1!==this.observers.indexOf(c)&&(this.observers=this.observers.filter(function(m){return m!==c}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:c}))},c.getObserversCount=function getObserversCount(){return this.observers.length},c.invalidate=function invalidate(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},c.fetch=function fetch(c,m){var h,y,g,_=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==m?void 0:m.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var P;return null==(P=this.retryer)||P.continueRetry(),this.promise}if(c&&this.setOptions(c),!this.options.queryFn){var C=this.observers.find(function(c){return c.options.queryFn});C&&this.setOptions(C.options)}var w=(0,v.HN)(this.queryKey),S=(0,v.jY)(),T={queryKey:w,pageParam:void 0,meta:this.meta};Object.defineProperty(T,"signal",{enumerable:!0,get:function get(){if(S)return _.abortSignalConsumed=!0,S.signal}});var N,D,A={fetchOptions:m,options:this.options,queryKey:w,state:this.state,fetchFn:function fetchFn(){return _.options.queryFn?(_.abortSignalConsumed=!1,_.options.queryFn(T)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(h=this.options.behavior)?void 0:h.onFetch)&&(null==(N=this.options.behavior)||N.onFetch(A));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(y=A.fetchOptions)?void 0:y.meta))||this.dispatch({type:"fetch",meta:null==(D=A.fetchOptions)?void 0:D.meta});return this.retryer=new E.eJ({fn:A.fetchFn,abort:null==S||null==(g=S.abort)?void 0:g.bind(S),onSuccess:function onSuccess(c){_.setData(c),null==_.cache.config.onSuccess||_.cache.config.onSuccess(c,_),0===_.cacheTime&&_.optionalRemove()},onError:function onError(c){(0,E.wm)(c)&&c.silent||_.dispatch({type:"error",error:c}),(0,E.wm)(c)||(null==_.cache.config.onError||_.cache.config.onError(c,_),(0,b.t)().error(c)),0===_.cacheTime&&_.optionalRemove()},onFail:function onFail(){_.dispatch({type:"failed"})},onPause:function onPause(){_.dispatch({type:"pause"})},onContinue:function onContinue(){_.dispatch({type:"continue"})},retry:A.options.retry,retryDelay:A.options.retryDelay}),this.promise=this.retryer.promise,this.promise},c.dispatch=function dispatch(c){var m=this;this.state=this.reducer(this.state,c),_.j.batch(function(){m.observers.forEach(function(m){m.onQueryUpdate(c)}),m.cache.notify({query:m,type:"queryUpdated",action:c})})},c.getDefaultState=function getDefaultState(c){var m="function"==typeof c.initialData?c.initialData():c.initialData,h=void 0!==c.initialData?"function"==typeof c.initialDataUpdatedAt?c.initialDataUpdatedAt():c.initialDataUpdatedAt:0,y=void 0!==m;return{data:m,dataUpdateCount:0,dataUpdatedAt:y?null!=h?h:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:y?"success":"idle"}},c.reducer=function reducer(c,m){var h,y;switch(m.type){case"failed":return(0,g.A)({},c,{fetchFailureCount:c.fetchFailureCount+1});case"pause":return(0,g.A)({},c,{isPaused:!0});case"continue":return(0,g.A)({},c,{isPaused:!1});case"fetch":return(0,g.A)({},c,{fetchFailureCount:0,fetchMeta:null!=(h=m.meta)?h:null,isFetching:!0,isPaused:!1},!c.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,g.A)({},c,{data:m.data,dataUpdateCount:c.dataUpdateCount+1,dataUpdatedAt:null!=(y=m.dataUpdatedAt)?y:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var v=m.error;return(0,E.wm)(v)&&v.revert&&this.revertState?(0,g.A)({},this.revertState):(0,g.A)({},c,{error:v,errorUpdateCount:c.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:c.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,g.A)({},c,{isInvalidated:!0});case"setState":return(0,g.A)({},c,m.state);default:return c}},Query}(),C=function(c){function QueryCache(m){var h;return(h=c.call(this)||this).config=m||{},h.queries=[],h.queriesMap={},h}(0,y.A)(QueryCache,c);var m=QueryCache.prototype;return m.build=function build(c,m,h){var y,g=m.queryKey,_=null!=(y=m.queryHash)?y:(0,v.F$)(g,m),b=this.get(_);return b||(b=new P({cache:this,queryKey:g,queryHash:_,options:c.defaultQueryOptions(m),state:h,defaultOptions:c.getQueryDefaults(g),meta:m.meta}),this.add(b)),b},m.add=function add(c){this.queriesMap[c.queryHash]||(this.queriesMap[c.queryHash]=c,this.queries.push(c),this.notify({type:"queryAdded",query:c}))},m.remove=function remove(c){var m=this.queriesMap[c.queryHash];m&&(c.destroy(),this.queries=this.queries.filter(function(m){return m!==c}),m===c&&delete this.queriesMap[c.queryHash],this.notify({type:"queryRemoved",query:c}))},m.clear=function clear(){var c=this;_.j.batch(function(){c.queries.forEach(function(m){c.remove(m)})})},m.get=function get(c){return this.queriesMap[c]},m.getAll=function getAll(){return this.queries},m.find=function find(c,m){var h=(0,v.b_)(c,m)[0];return void 0===h.exact&&(h.exact=!0),this.queries.find(function(c){return(0,v.MK)(h,c)})},m.findAll=function findAll(c,m){var h=(0,v.b_)(c,m)[0];return Object.keys(h).length>0?this.queries.filter(function(c){return(0,v.MK)(h,c)}):this.queries},m.notify=function notify(c){var m=this;_.j.batch(function(){m.listeners.forEach(function(m){m(c)})})},m.onFocus=function onFocus(){var c=this;_.j.batch(function(){c.queries.forEach(function(c){c.onFocus()})})},m.onOnline=function onOnline(){var c=this;_.j.batch(function(){c.queries.forEach(function(c){c.onOnline()})})},QueryCache}(h(13411).Q)},33704:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function KitDescription(){var c=(0,_.useContext)(b.ExportContext);return _.default.createElement(P.default,{container:!0,direction:"column"},_.default.createElement(C.default,{tag:"span",variant:"xs"},y("Description (Optional)","elementor")),_.default.createElement(E.default,{placeholder:y("Type description here...","elementor"),onChange:function onChange(m){c.dispatch({type:"SET_KIT_DESCRIPTION",payload:m.target.value})}}))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(81160),E=v(h(79788)),P=v(h(3416)),C=v(h(55725))},33791:()=>{},33929:(c,m,h)=>{var y=h(67114),v=h(89736);c.exports=function AsyncIterator(c,m){function n(h,v,g,_){try{var b=c[h](v),E=b.value;return E instanceof y?m.resolve(E.v).then(function(c){n("next",c,g,_)},function(c){n("throw",c,g,_)}):m.resolve(E).then(function(c){b.value=c,g(b)},function(c){return n("throw",c,g,_)})}catch(c){_(c)}}var h;this.next||(v(AsyncIterator.prototype),v(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),v(this,"_invoke",function(c,y,v){function f(){return new m(function(m,h){n(c,v,m,h)})}return h=h?h.then(f,f):f()},!0)},c.exports.__esModule=!0,c.exports.default=c.exports},34009:(c,m,h)=>{"use strict";h.d(m,{E:()=>useQueries});var y=h(41594),v=h.n(y),g=h(25800),_=h(89774),b=h(15292);function useQueries(c){var m=v().useRef(!1),h=v().useState(0)[1],E=(0,b.j)(),P=(0,y.useMemo)(function(){return c.map(function(c){var m=E.defaultQueryObserverOptions(c);return m.optimisticResults=!0,m})},[c,E]),C=v().useState(function(){return new _.T(E,P)})[0],w=C.getOptimisticResult(P);return v().useEffect(function(){m.current=!0;var c=C.subscribe(g.j.batchCalls(function(){m.current&&h(function(c){return c+1})}));return function(){m.current=!1,c()}},[C]),v().useEffect(function(){C.setQueries(P,{listeners:!1})},[P,C]),w}},34864:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=InfoModalSection;var g=v(h(41594)),_=h(79397),b=v(h(61678));function InfoModalSection(c){return g.default.createElement(b.default.Section,{className:(0,_.arrayToClassName)(["e-app-import-export-info-modal__section",c.className])},c.children)}InfoModalSection.propTypes={className:y.string,children:y.any},InfoModalSection.defaultProps={className:""}},35013:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.reducer=void 0;var v=y(h(85707));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}m.reducer=function reducer(c,m){var h=m.type,y=m.payload;switch(h){case"SET_DOWNLOAD_URL":return _objectSpread(_objectSpread({},c),{},{downloadUrl:y});case"SET_EXPORTED_DATA":return _objectSpread(_objectSpread({},c),{},{exportedData:y});case"SET_PLUGINS":return _objectSpread(_objectSpread({},c),{},{plugins:y});case"SET_IS_EXPORT_PROCESS_STARTED":return _objectSpread(_objectSpread({},c),{},{isExportProcessStarted:y});case"SET_KIT_TITLE":return _objectSpread(_objectSpread({},c),{},{kitInfo:_objectSpread(_objectSpread({},c.kitInfo),{},{title:y})});case"SET_KIT_DESCRIPTION":return _objectSpread(_objectSpread({},c),{},{kitInfo:_objectSpread(_objectSpread({},c.kitInfo),{},{description:y})});case"SET_KIT_SAVE_SOURCE":return _objectSpread(_objectSpread({},c),{},{kitInfo:_objectSpread(_objectSpread({},c.kitInfo),{},{source:y})});default:return c}}},35676:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=v(h(41594)),_=v(h(4380)),b=v(h(7229)),E=v(h(18320)),P=v(h(70097)),C=v(h(23074)),w=v(h(16357)),S=v(h(62992));h(45302);var T=g.default.forwardRef(function(c,m){return g.default.createElement("article",{className:"eps-card ".concat(c.className),ref:m},c.children)});T.propTypes={type:y.string,className:y.string,children:y.any},T.defaultProps={className:""},T.displayName="Card",T.Header=_.default,T.Body=b.default,T.Image=E.default,T.Overlay=P.default,T.Footer=C.default,T.Headline=w.default,T.Divider=S.default;m.default=T},36417:c=>{c.exports=function _assertThisInitialized(c){if(void 0===c)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c},c.exports.__esModule=!0,c.exports.default=c.exports},36484:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),Object.defineProperty(m,"Eligibility",{enumerable:!0,get:function get(){return P.Eligibility}}),m.Index=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(87861)),P=h(44221);function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.Index=function(c){function Index(){return(0,v.default)(this,Index),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,Index,arguments)}return(0,E.default)(Index,c),(0,g.default)(Index,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"cloud-kits/{id}"}}])}($e.modules.CommandData)},36561:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function Index(){var c,m=new URLSearchParams(window.location.search),h=Object.fromEntries(m.entries()),y=_.default[h.action]||(null===(c=elementorAppConfig.menu_url.split("#"))||void 0===c?void 0:c[1]);return v.default.createElement(g.Redirect,{to:y||"/not-found",noThrow:!0})};var v=y(h(41594)),g=h(83040),_=y(h(8102))},36625:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=y(h(39805)),_=y(h(40989)),b=y(h(15118)),E=y(h(29402)),P=y(h(87861)),C=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(36484));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.default=function(c){function Component(){return(0,g.default)(this,Component),function _callSuper(c,m,h){return m=(0,E.default)(m),(0,b.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,E.default)(c).constructor):m.apply(c,h))}(this,Component,arguments)}return(0,P.default)(Component,c),(0,_.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"cloud-kits"}},{key:"defaultData",value:function defaultData(){return this.importCommands(C)}}])}($e.modules.ComponentBase)},36808:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ConnectProNotice(){return g.default.createElement(_.default,{className:"e-app-import-connect-pro-notice",label:y("Tip:","elementor"),color:"info",button:function getButton(){return g.default.createElement(b.default,{text:y("Let’s do it","elementor"),variant:"outlined",color:"secondary",size:"sm",target:"_blank",url:elementorAppConfig.admin_url+"admin.php?page=elementor-license"})}()},y("Make sure your Elementor Pro account is connected","elementor"))};var g=v(h(41594)),_=v(h(40587)),b=v(h(47483));h(40616)},37628:(c,m)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.cptObjectToOptionsArray=void 0;m.cptObjectToOptionsArray=function cptObjectToOptionsArray(c){var m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"label",h=[];return c&&m&&Object.keys(c).forEach(function(y){return h.push({label:c[y][m],value:y})}),h}},37744:(c,m,h)=>{var y=h(78113);c.exports=function _unsupportedIterableToArray(c,m){if(c){if("string"==typeof c)return y(c,m);var h={}.toString.call(c).slice(8,-1);return"Object"===h&&c.constructor&&(h=c.constructor.name),"Map"===h||"Set"===h?Array.from(c):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?y(c,m):void 0}},c.exports.__esModule=!0,c.exports.default=c.exports},37880:(c,m,h)=>{"use strict";var y=h(12470).__;Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var v=[{type:"templates",data:{title:y("Templates","elementor"),features:{open:[y("Saved Templates","elementor")],locked:[y("Headers","elementor"),y("Footers","elementor"),y("Archives","elementor"),y("Single Posts","elementor"),y("Single Pages","elementor"),y("Search Results","elementor"),y("404 Error Page","elementor"),y("Popups","elementor"),y("Global widgets","elementor")],tooltip:y("To import or export these components, you’ll need Elementor Pro.","elementor")}}},{type:"content",data:{title:y("Content","elementor"),features:{open:[y("Elementor Pages","elementor"),y("Landing Pages","elementor"),y("Elementor Posts","elementor"),y("WP Pages","elementor"),y("WP Posts","elementor"),y("WP Menus","elementor"),y("Custom Post Types","elementor")]}}},{type:"settings",data:{title:y("Site Settings","elementor"),features:{open:[y("Global Colors","elementor"),y("Global Fonts","elementor"),y("Theme Style settings","elementor"),y("Layout Settings","elementor"),y("Lightbox Settings","elementor"),y("Background Settings","elementor")]}}}];m.default=v},38761:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function App(){var c=(0,g.useContext)(S.AppContext).state.isDarkMode,m={config:{variants:{light:!c,dark:c}}};return _.default.appHistory=(0,b.createHistory)((0,E.createHashSource)()),g.default.createElement(w.default,null,g.default.createElement(b.LocationProvider,{history:_.default.appHistory},g.default.createElement(T.ThemeProvider,{theme:m},g.default.createElement(N,{fallback:null},g.default.createElement(b.Router,null,_.default.getRoutes(),g.default.createElement(C.default,{path:"/"}),g.default.createElement(P.default,{default:!0}))))))};var g=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),_=y(h(47485)),b=h(83040),E=h(3600),P=y(h(28101)),C=y(h(36561)),w=y(h(65949));h(87581);var S=h(64095),T=h(15142);var N=g.default.Suspense},38832:(c,m,h)=>{"use strict";var y=h(62688),v=h(12470).__,g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ElementorLoading;var _=g(h(41594));function ElementorLoading(c){return _.default.createElement("div",{className:"elementor-loading"},_.default.createElement("div",{className:"elementor-loader-wrapper"},_.default.createElement("div",{className:"elementor-loader"},_.default.createElement("div",{className:"elementor-loader-boxes"},_.default.createElement("div",{className:"elementor-loader-box"}),_.default.createElement("div",{className:"elementor-loader-box"}),_.default.createElement("div",{className:"elementor-loader-box"}),_.default.createElement("div",{className:"elementor-loader-box"}))),_.default.createElement("div",{className:"elementor-loading-title"},c.loadingText)))}ElementorLoading.propTypes={loadingText:y.string},ElementorLoading.defaultProps={loadingText:v("Loading","elementor")}},39387:(c,m,h)=>{"use strict";h.d(m,{E:()=>w});var y=h(68102),v=h(6369),g=h(33382),_=h(41415),b=h(91669),E=h(26434),P=h(25800),C=h(6622),w=function(){function QueryClient(c){void 0===c&&(c={}),this.queryCache=c.queryCache||new g.$,this.mutationCache=c.mutationCache||new _.q,this.defaultOptions=c.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var c=QueryClient.prototype;return c.mount=function mount(){var c=this;this.unsubscribeFocus=b.m.subscribe(function(){b.m.isFocused()&&E.t.isOnline()&&(c.mutationCache.onFocus(),c.queryCache.onFocus())}),this.unsubscribeOnline=E.t.subscribe(function(){b.m.isFocused()&&E.t.isOnline()&&(c.mutationCache.onOnline(),c.queryCache.onOnline())})},c.unmount=function unmount(){var c,m;null==(c=this.unsubscribeFocus)||c.call(this),null==(m=this.unsubscribeOnline)||m.call(this)},c.isFetching=function isFetching(c,m){var h=(0,v.b_)(c,m)[0];return h.fetching=!0,this.queryCache.findAll(h).length},c.isMutating=function isMutating(c){return this.mutationCache.findAll((0,y.A)({},c,{fetching:!0})).length},c.getQueryData=function getQueryData(c,m){var h;return null==(h=this.queryCache.find(c,m))?void 0:h.state.data},c.getQueriesData=function getQueriesData(c){return this.getQueryCache().findAll(c).map(function(c){return[c.queryKey,c.state.data]})},c.setQueryData=function setQueryData(c,m,h){var y=(0,v.vh)(c),g=this.defaultQueryOptions(y);return this.queryCache.build(this,g).setData(m,h)},c.setQueriesData=function setQueriesData(c,m,h){var y=this;return P.j.batch(function(){return y.getQueryCache().findAll(c).map(function(c){var v=c.queryKey;return[v,y.setQueryData(v,m,h)]})})},c.getQueryState=function getQueryState(c,m){var h;return null==(h=this.queryCache.find(c,m))?void 0:h.state},c.removeQueries=function removeQueries(c,m){var h=(0,v.b_)(c,m)[0],y=this.queryCache;P.j.batch(function(){y.findAll(h).forEach(function(c){y.remove(c)})})},c.resetQueries=function resetQueries(c,m,h){var g=this,_=(0,v.b_)(c,m,h),b=_[0],E=_[1],C=this.queryCache,w=(0,y.A)({},b,{active:!0});return P.j.batch(function(){return C.findAll(b).forEach(function(c){c.reset()}),g.refetchQueries(w,E)})},c.cancelQueries=function cancelQueries(c,m,h){var y=this,g=(0,v.b_)(c,m,h),_=g[0],b=g[1],E=void 0===b?{}:b;void 0===E.revert&&(E.revert=!0);var C=P.j.batch(function(){return y.queryCache.findAll(_).map(function(c){return c.cancel(E)})});return Promise.all(C).then(v.lQ).catch(v.lQ)},c.invalidateQueries=function invalidateQueries(c,m,h){var g,_,b,E=this,C=(0,v.b_)(c,m,h),w=C[0],S=C[1],T=(0,y.A)({},w,{active:null==(g=null!=(_=w.refetchActive)?_:w.active)||g,inactive:null!=(b=w.refetchInactive)&&b});return P.j.batch(function(){return E.queryCache.findAll(w).forEach(function(c){c.invalidate()}),E.refetchQueries(T,S)})},c.refetchQueries=function refetchQueries(c,m,h){var g=this,_=(0,v.b_)(c,m,h),b=_[0],E=_[1],C=P.j.batch(function(){return g.queryCache.findAll(b).map(function(c){return c.fetch(void 0,(0,y.A)({},E,{meta:{refetchPage:null==b?void 0:b.refetchPage}}))})}),w=Promise.all(C).then(v.lQ);return(null==E?void 0:E.throwOnError)||(w=w.catch(v.lQ)),w},c.fetchQuery=function fetchQuery(c,m,h){var y=(0,v.vh)(c,m,h),g=this.defaultQueryOptions(y);void 0===g.retry&&(g.retry=!1);var _=this.queryCache.build(this,g);return _.isStaleByTime(g.staleTime)?_.fetch(g):Promise.resolve(_.state.data)},c.prefetchQuery=function prefetchQuery(c,m,h){return this.fetchQuery(c,m,h).then(v.lQ).catch(v.lQ)},c.fetchInfiniteQuery=function fetchInfiniteQuery(c,m,h){var y=(0,v.vh)(c,m,h);return y.behavior=(0,C.PL)(),this.fetchQuery(y)},c.prefetchInfiniteQuery=function prefetchInfiniteQuery(c,m,h){return this.fetchInfiniteQuery(c,m,h).then(v.lQ).catch(v.lQ)},c.cancelMutations=function cancelMutations(){var c=this,m=P.j.batch(function(){return c.mutationCache.getAll().map(function(c){return c.cancel()})});return Promise.all(m).then(v.lQ).catch(v.lQ)},c.resumePausedMutations=function resumePausedMutations(){return this.getMutationCache().resumePausedMutations()},c.executeMutation=function executeMutation(c){return this.mutationCache.build(this,c).execute()},c.getQueryCache=function getQueryCache(){return this.queryCache},c.getMutationCache=function getMutationCache(){return this.mutationCache},c.getDefaultOptions=function getDefaultOptions(){return this.defaultOptions},c.setDefaultOptions=function setDefaultOptions(c){this.defaultOptions=c},c.setQueryDefaults=function setQueryDefaults(c,m){var h=this.queryDefaults.find(function(m){return(0,v.Od)(c)===(0,v.Od)(m.queryKey)});h?h.defaultOptions=m:this.queryDefaults.push({queryKey:c,defaultOptions:m})},c.getQueryDefaults=function getQueryDefaults(c){var m;return c?null==(m=this.queryDefaults.find(function(m){return(0,v.Cp)(c,m.queryKey)}))?void 0:m.defaultOptions:void 0},c.setMutationDefaults=function setMutationDefaults(c,m){var h=this.mutationDefaults.find(function(m){return(0,v.Od)(c)===(0,v.Od)(m.mutationKey)});h?h.defaultOptions=m:this.mutationDefaults.push({mutationKey:c,defaultOptions:m})},c.getMutationDefaults=function getMutationDefaults(c){var m;return c?null==(m=this.mutationDefaults.find(function(m){return(0,v.Cp)(c,m.mutationKey)}))?void 0:m.defaultOptions:void 0},c.defaultQueryOptions=function defaultQueryOptions(c){if(null==c?void 0:c._defaulted)return c;var m=(0,y.A)({},this.defaultOptions.queries,this.getQueryDefaults(null==c?void 0:c.queryKey),c,{_defaulted:!0});return!m.queryHash&&m.queryKey&&(m.queryHash=(0,v.F$)(m.queryKey,m)),m},c.defaultQueryObserverOptions=function defaultQueryObserverOptions(c){return this.defaultQueryOptions(c)},c.defaultMutationOptions=function defaultMutationOptions(c){return(null==c?void 0:c._defaulted)?c:(0,y.A)({},this.defaultOptions.mutations,this.getMutationDefaults(null==c?void 0:c.mutationKey),c,{_defaulted:!0})},c.clear=function clear(){this.queryCache.clear(),this.mutationCache.clear()},QueryClient}()},39701:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.Favorites=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(87861));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.Favorites=function(c){function Favorites(){return(0,v.default)(this,Favorites),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,Favorites,arguments)}return(0,E.default)(Favorites,c),(0,g.default)(Favorites,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"kits/favorites/{id}"}}])}($e.modules.CommandData)},39805:c=>{c.exports=function _classCallCheck(c,m){if(!(c instanceof m))throw new TypeError("Cannot call a class as a function")},c.exports.__esModule=!0,c.exports.default=c.exports},39970:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DropZone;var _=g(h(41594)),b=g(h(78304)),E=h(79397),P=g(h(98718)),C=g(h(59824)),w=g(h(76547)),S=g(h(85418)),T=g(h(55725));function DropZone(c){var m=["e-app-drop-zone",c.className],h={onDrop:function onDrop(m){if(!c.isLoading){var h=m.dataTransfer.files[0];h&&(0,E.isOneOf)(h.type,c.filetypes)?c.onFileSelect(h,m,"drop"):c.onError({id:"file_not_allowed",message:y("This file type is not allowed","elementor")})}}};return _.default.createElement("section",{className:(0,E.arrayToClassName)(m)},_.default.createElement(C.default,(0,b.default)({},h,{isLoading:c.isLoading}),c.icon&&_.default.createElement(w.default,{className:"e-app-drop-zone__icon ".concat(c.icon)}),c.heading&&_.default.createElement(S.default,{variant:"display-3"},c.heading),c.text&&_.default.createElement(T.default,{variant:"xl",className:"e-app-drop-zone__text"},c.text),c.secondaryText&&_.default.createElement(T.default,{variant:"xl",className:"e-app-drop-zone__secondary-text"},c.secondaryText),c.showButton&&_.default.createElement(P.default,{isLoading:c.isLoading,type:c.type,onButtonClick:c.onButtonClick,onFileSelect:c.onFileSelect,onWpMediaSelect:function onWpMediaSelect(m){return c.onWpMediaSelect(m)},onError:function onError(m){return c.onError(m)},text:c.buttonText,filetypes:c.filetypes,variant:c.buttonVariant,color:c.buttonColor,onFileChoose:c.onFileChoose}),c.description&&_.default.createElement(T.default,{variant:"xl",className:"e-app-drop-zone__description"},c.description)))}h(22322),DropZone.propTypes={className:v.string,children:v.any,type:v.string,onFileSelect:v.func.isRequired,onWpMediaSelect:v.func,heading:v.string,text:v.string,secondaryText:v.string,buttonText:v.string,buttonVariant:v.string,buttonColor:v.string,icon:v.string,showButton:v.bool,showIcon:v.bool,isLoading:v.bool,filetypes:v.array.isRequired,onError:v.func,description:v.string,onButtonClick:v.func,onFileChoose:v.func},DropZone.defaultProps={className:"",type:"file-explorer",icon:"eicon-library-upload",showButton:!0,showIcon:!0,onError:function onError(){}}},40362:(c,m,h)=>{"use strict";var y=h(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,c.exports=function(){function shim(c,m,h,v,g,_){if(_!==y){var b=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw b.name="Invariant Violation",b}}function getShim(){return shim}shim.isRequired=shim;var c={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return c.PropTypes=c,c}},40453:(c,m,h)=>{var y=h(10739);c.exports=function _objectWithoutProperties(c,m){if(null==c)return{};var h,v,g=y(c,m);if(Object.getOwnPropertySymbols){var _=Object.getOwnPropertySymbols(c);for(v=0;v<_.length;v++)h=_[v],-1===m.indexOf(h)&&{}.propertyIsEnumerable.call(c,h)&&(g[h]=c[h])}return g},c.exports.__esModule=!0,c.exports.default=c.exports},40587:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Notice;var g=v(h(41594)),_=h(79397),b=v(h(55725)),E=v(h(76547)),P=v(h(3416));h(26587);var C={danger:"eicon-warning",info:"eicon-info-circle-o",warning:"eicon-warning"};function Notice(c){var m="eps-notice",h=[m,c.className];return c.color&&h.push(m+"-semantic",m+"--"+c.color),g.default.createElement(P.default,{className:(0,_.arrayToClassName)(h),container:!0,noWrap:!0,alignItems:"center",justify:"space-between"},g.default.createElement(P.default,{item:!0,container:!0,alignItems:"start",noWrap:!0},c.withIcon&&c.color&&g.default.createElement(E.default,{className:(0,_.arrayToClassName)(["eps-notice__icon",C[c.color]])}),g.default.createElement(b.default,{variant:"xs",className:"eps-notice__text"},c.label&&g.default.createElement("strong",null,c.label+" "),c.children)),c.button&&g.default.createElement(P.default,{item:!0,container:!0,justify:"end",className:m+"__button-container"},c.button))}Notice.propTypes={className:y.string,color:y.string,label:y.string,children:y.any.isRequired,icon:y.string,withIcon:y.bool,button:y.object},Notice.defaultProps={className:"",withIcon:!0,button:null}},40616:()=>{},40858:c=>{"use strict";c.exports=elementorAppPackages.siteEditor},40989:(c,m,h)=>{var y=h(45498);function _defineProperties(c,m){for(var h=0;h<m.length;h++){var v=m[h];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(c,y(v.key),v)}}c.exports=function _createClass(c,m,h){return m&&_defineProperties(c.prototype,m),h&&_defineProperties(c,h),Object.defineProperty(c,"prototype",{writable:!1}),c},c.exports.__esModule=!0,c.exports.default=c.exports},41415:(c,m,h)=>{"use strict";h.d(m,{q:()=>b});var y=h(59994),v=h(25800),g=h(85869),_=h(6369),b=function(c){function MutationCache(m){var h;return(h=c.call(this)||this).config=m||{},h.mutations=[],h.mutationId=0,h}(0,y.A)(MutationCache,c);var m=MutationCache.prototype;return m.build=function build(c,m,h){var y=new g.s({mutationCache:this,mutationId:++this.mutationId,options:c.defaultMutationOptions(m),state:h,defaultOptions:m.mutationKey?c.getMutationDefaults(m.mutationKey):void 0,meta:m.meta});return this.add(y),y},m.add=function add(c){this.mutations.push(c),this.notify(c)},m.remove=function remove(c){this.mutations=this.mutations.filter(function(m){return m!==c}),c.cancel(),this.notify(c)},m.clear=function clear(){var c=this;v.j.batch(function(){c.mutations.forEach(function(m){c.remove(m)})})},m.getAll=function getAll(){return this.mutations},m.find=function find(c){return void 0===c.exact&&(c.exact=!0),this.mutations.find(function(m){return(0,_.nJ)(c,m)})},m.findAll=function findAll(c){return this.mutations.filter(function(m){return(0,_.nJ)(c,m)})},m.notify=function notify(c){var m=this;v.j.batch(function(){m.listeners.forEach(function(m){m(c)})})},m.onFocus=function onFocus(){this.resumePausedMutations()},m.onOnline=function onOnline(){this.resumePausedMutations()},m.resumePausedMutations=function resumePausedMutations(){var c=this.mutations.filter(function(c){return c.state.isPaused});return v.j.batch(function(){return c.reduce(function(c,m){return c.then(function(){return m.continue().catch(_.lQ)})},Promise.resolve())})},MutationCache}(h(13411).Q)},41494:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useQueryParams(){var c,m=new URLSearchParams(window.location.search),h=Object.fromEntries(m.entries()),y=null===(c=location.hash.match(/\?(.+)/))||void 0===c?void 0:c[1],v={};y&&y.split("&").forEach(function(c){var m=c.split("="),h=(0,g.default)(m,2),y=h[0],_=h[1];v[y]=_});var _=_objectSpread(_objectSpread({},h),v);return{getAll:function getAll(){return _}}};var v=y(h(85707)),g=y(h(18821));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}},41594:c=>{"use strict";c.exports=React},41621:(c,m,h)=>{var y=h(14718);function _get(){return c.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(c,m,h){var v=y(c,m);if(v){var g=Object.getOwnPropertyDescriptor(v,m);return g.get?g.get.call(arguments.length<3?c:h):g.value}},c.exports.__esModule=!0,c.exports.default=c.exports,_get.apply(null,arguments)}c.exports=_get,c.exports.__esModule=!0,c.exports.default=c.exports},41994:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=FileProcess;var _=g(h(41594)),b=h(79397),E=g(h(19744)),P=g(h(77755));function FileProcess(c){return _.default.createElement(P.default,{className:(0,b.arrayToClassName)(["e-app-import-export-file-process",c.className]),icon:"eicon-loading eicon-animation-spin",heading:y("Setting up your website template...","elementor"),description:_.default.createElement(_.default.Fragment,null,y("This usually takes a few moments.","elementor"),_.default.createElement("br",null),y("Don't close this window until the process is finished.","elementor")),info:c.info},!!c.errorType&&_.default.createElement(E.default,{onApprove:c.onDialogApprove,onDismiss:c.onDialogDismiss,errorType:c.errorType}))}FileProcess.propTypes={className:v.string,onDialogApprove:v.func,onDialogDismiss:v.func,errorType:v.string,info:v.string},FileProcess.defaultProps={className:""}},42145:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportResolver(){var c,m=(0,b.useContext)(P.SharedContext),h=(0,b.useContext)(C.ImportContext),v=(0,E.useNavigate)(),g=(null===(c=h.data)||void 0===c||null===(c=c.uploadedData)||void 0===c?void 0:c.conflicts)||{},H=m.data||{},G=H.referrer,V=H.currentPage,Y=function eventTracking(c){var m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;"kit-library"===G&&(0,K.appsEventTrackingDispatch)(c,{site_part:m,page_source:"import",step:V,event_type:"click"})};return(0,b.useEffect)(function(){h.data.uploadedData||v("import"),m.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportResolver.name})},[]),b.default.createElement(w.default,{type:"import",footer:function getFooter(){return b.default.createElement(N.default,null,b.default.createElement(q.default,{text:y("Previous","elementor"),variant:"contained",onClick:function onClick(){Y("kit-library/go-back"),v("import/content")}}),b.default.createElement(q.default,{text:y("Next","elementor"),variant:"contained",color:"primary",onClick:function onClick(){Y("kit-library/approve-selection");var c=h.data.plugins.length?"import/plugins-activation":"import/process";h.dispatch({type:"SET_IS_RESOLVED",payload:!0}),v(c)}}))}()},b.default.createElement("section",{className:"e-app-import-resolver"},b.default.createElement(S.default,{heading:y("Import a Website Kit to your site","elementor"),description:[b.default.createElement(b.default.Fragment,{key:"description-first-line"},y("Parts of this kit overlap with your site’s templates, design and settings. The items you leave checked on this list will replace your current design.","elementor")," ",function getLearnMoreLink(){return b.default.createElement(W.default,{url:"https://go.elementor.com/app-what-are-kits",italic:!0,onClick:function onClick(){return Y("kit-library/seek-more-info")}},y("Learn More","elementor"))}())]}),function isHomePageOverride(){if(m.data.includes.includes("content")){var c,y=(null===(c=h.data)||void 0===c||null===(c=c.uploadedData)||void 0===c||null===(c=c.manifest.content)||void 0===c?void 0:c.page)||{};return Object.entries(y).find(function(c){return c[1].show_on_front})}return!1}()&&b.default.createElement(A.default,{className:"e-app-import-resolver__notice",label:y("Note:","elementor"),color:"warning"},y("Your site's homepage will be determined by the kit. You can change this later.","elementor")),b.default.createElement(D.default,{isOpened:!0},b.default.createElement(D.default.Header,{toggle:!1},b.default.createElement(D.default.Headline,null,y("Select the items you want to keep and apply:","elementor"))),b.default.createElement(D.default.Body,{padding:"20"},b.default.createElement(U.default,{className:"e-app-import-resolver-conflicts__container"},b.default.createElement(Q.default,{separated:!0,className:"e-app-import-resolver-conflicts"},Object.entries(g).map(function(c,m){var h=(0,_.default)(c,2),y=h[0],v=h[1];return b.default.createElement(Q.default.Item,{padding:"20",key:m,className:"e-app-import-resolver-conflicts__item"},b.default.createElement(T.default,{importedId:parseInt(y),conflictData:v[0],onClick:function onClick(c){return Y("kit-library/check-item",c)}}))})))))))};var _=v(h(18821)),b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(83040),P=h(69378),C=h(53442),w=v(h(53931)),S=v(h(23327)),T=v(h(15197)),N=v(h(91071)),D=v(h(49902)),A=v(h(40587)),W=v(h(54999)),q=v(h(47483)),U=v(h(21689)),Q=v(h(93279)),K=h(3073);h(97295)},43800:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportKit(){var c=g.default.createElement(P.default,null),m=g.default.createElement(b.PageHeader,{title:y("Export","elementor")});return g.default.createElement(b.BaseLayout,{topBar:g.default.createElement(b.TopBar,null,m),footer:g.default.createElement(b.Footer,null,c)},g.default.createElement(_.Box,{sx:{p:3,mb:2,maxWidth:"1075px",mx:"auto"}},g.default.createElement(E.default,null),g.default.createElement(w.default,null),g.default.createElement(C.default,null)))};var g=v(h(41594)),_=h(86956),b=h(62100),E=v(h(51160)),P=v(h(73148)),C=v(h(98177)),w=v(h(31138))},44221:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.Eligibility=void 0;var v=y(h(39805)),g=y(h(40989)),_=y(h(15118)),b=y(h(29402)),E=y(h(87861));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.Eligibility=function(c){function Eligibility(){return(0,v.default)(this,Eligibility),function _callSuper(c,m,h){return m=(0,b.default)(m),(0,_.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,b.default)(c).constructor):m.apply(c,h))}(this,Eligibility,arguments)}return(0,E.default)(Eligibility,c),(0,g.default)(Eligibility,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"cloud-kits/eligibility"}}])}($e.modules.CommandData)},44663:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useImportedKitData(){return{getTemplates:function getTemplates(c,m){var h={};for(var y in null==m||null===(v=m.templates)||void 0===v?void 0:v.succeed){var v;h[y]=c[y]}return h},getContent:function getContent(c,m){var h={};for(var y in null==m?void 0:m.content)for(var v in h[y]={},null===(g=m.content[y])||void 0===g?void 0:g.succeed){var g;h[y][v]=c[y][v]}return h},getWPContent:function getWPContent(c,m){var h={};for(var y in null==m?void 0:m["wp-content"]){var v,g=null===(v=m["wp-content"][y])||void 0===v?void 0:v.succeed;h[y]=g?Object.keys(g):[]}return h},getPlugins:function getPlugins(c){var m={activePlugins:[],failedPlugins:[]};return c.forEach(function(c){var h=y.PLUGIN_STATUS_MAP.ACTIVE===c.status?"activePlugins":"failedPlugins";m[h].push(c)}),m}}};var y=h(28816)},45206:(c,m,h)=>{"use strict";h.d(m,{Q:()=>hydrate,h:()=>dehydrate});var y=h(68102);function defaultShouldDehydrateMutation(c){return c.state.isPaused}function defaultShouldDehydrateQuery(c){return"success"===c.state.status}function dehydrate(c,m){var h,y,v=[],g=[];if(!1!==(null==(h=m=m||{})?void 0:h.dehydrateMutations)){var _=m.shouldDehydrateMutation||defaultShouldDehydrateMutation;c.getMutationCache().getAll().forEach(function(c){_(c)&&v.push(function dehydrateMutation(c){return{mutationKey:c.options.mutationKey,state:c.state}}(c))})}if(!1!==(null==(y=m)?void 0:y.dehydrateQueries)){var b=m.shouldDehydrateQuery||defaultShouldDehydrateQuery;c.getQueryCache().getAll().forEach(function(c){b(c)&&g.push(function dehydrateQuery(c){return{state:c.state,queryKey:c.queryKey,queryHash:c.queryHash}}(c))})}return{mutations:v,queries:g}}function hydrate(c,m,h){if("object"==typeof m&&null!==m){var v=c.getMutationCache(),g=c.getQueryCache(),_=m.mutations||[],b=m.queries||[];_.forEach(function(m){var g;v.build(c,(0,y.A)({},null==h||null==(g=h.defaultOptions)?void 0:g.mutations,{mutationKey:m.mutationKey}),m.state)}),b.forEach(function(m){var v,_=g.get(m.queryHash);_?_.state.dataUpdatedAt<m.state.dataUpdatedAt&&_.setState(m.state):g.build(c,(0,y.A)({},null==h||null==(v=h.defaultOptions)?void 0:v.queries,{queryKey:m.queryKey,queryHash:m.queryHash}),m.state)})}}},45302:()=>{},45317:c=>{c.exports=function shallowEqual(c,m,h,y){var v=h?h.call(y,c,m):void 0;if(void 0!==v)return!!v;if(c===m)return!0;if("object"!=typeof c||!c||"object"!=typeof m||!m)return!1;var g=Object.keys(c),_=Object.keys(m);if(g.length!==_.length)return!1;for(var b=Object.prototype.hasOwnProperty.bind(m),E=0;E<g.length;E++){var P=g[E];if(!b(P))return!1;var C=c[P],w=m[P];if(!1===(v=h?h.call(y,C,w,P):void 0)||void 0===v&&C!==w)return!1}return!0}},45498:(c,m,h)=>{var y=h(10564).default,v=h(11327);c.exports=function toPropertyKey(c){var m=v(c,"string");return"symbol"==y(m)?m:m+""},c.exports.__esModule=!0,c.exports.default=c.exports},45735:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ListItem;var g=v(h(41594)),_=h(79397);function ListItem(c){var m,h="eps-list__item",y=[h,c.className];return Object.prototype.hasOwnProperty.call(c,"padding")&&(m={"--eps-list-item-padding":(0,_.pxToRem)(c.padding)},y.push(h+"--padding")),g.default.createElement("li",{style:m,className:(0,_.arrayToClassName)(y)},c.children)}ListItem.propTypes={className:y.string,padding:y.string,children:y.any.isRequired},ListItem.defaultProps={className:""}},46313:(c,m,h)=>{var y=h(9535),v=h(33929);c.exports=function _regeneratorAsyncGen(c,m,h,g,_){return new v(y().w(c,m,h,g),_||Promise)},c.exports.__esModule=!0,c.exports.default=c.exports},46361:(c,m)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useAction(){return{backToDashboard:function backToDashboard(){window.top===window?window.top.location=elementorAppConfig.admin_url:window.top.$e.run("app/close")},backToReferrer:function backToReferrer(){window.top===window?window.top.location=elementorAppConfig.return_url.includes(elementorAppConfig.login_url)?elementorAppConfig.admin_url:elementorAppConfig.return_url:window.top.$e.run("app/close")}}}},46373:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useImportPluginsData(c,m){var h=(0,g.useMemo)(function(){return function getClassifiedPlugins(){var h={missing:[],existing:[],minVersionMissing:[],proData:null},y=(0,_.arrayToObjectByKey)(m,"name");return c.forEach(function(c){var m=y[c.name],v=b.PLUGIN_STATUS_MAP.ACTIVE===(null==m?void 0:m.status)?P:E,g=m||_objectSpread(_objectSpread({},c),{},{status:b.PLUGIN_STATUS_MAP.NOT_INSTALLED});m&&!function getIsMinVersionExist(c,m){return c.localeCompare(m)>-1}(m.version,c.version)&&h.minVersionMissing.push(c),C===g.name&&(h.proData=g),h[v].push(g)}),h}()},[c,m]);return{importPluginsData:c.length&&m.length?h:null}};var v=y(h(85707)),g=h(41594),_=h(79397),b=h(28816);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}var E="missing",P="existing",C="Elementor Pro"},46543:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.reducer=void 0;var v=y(h(85707)),g=h(56915);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}m.reducer=function reducer(c,m){var h=m.type,y=m.payload;switch(h){case"ADD_INCLUDE":return g.ReducerUtils.updateArray(c,"includes",y,"add");case"REMOVE_INCLUDE":return g.ReducerUtils.updateArray(c,"includes",y,"remove");case"SET_REFERRER":return _objectSpread(_objectSpread({},c),{},{referrer:y});case"SET_INCLUDES":return _objectSpread(_objectSpread({},c),{},{includes:y});case"SET_CPT":return _objectSpread(_objectSpread({},c),{},{customPostTypes:y});case"SET_SELECTED_CPT":return _objectSpread(_objectSpread({},c),{},{selectedCustomPostTypes:y});case"SET_CURRENT_PAGE_NAME":return _objectSpread(_objectSpread({},c),{},{currentPage:y});case"SET_RETURN_TO":return _objectSpread(_objectSpread({},c),{},{returnTo:y});default:return c}}},46916:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PageHeader;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(18821)),E=v(h(78304)),P=h(86956),C=v(h(62688)),w=h(59957);var S=(0,P.styled)(function ElementorLogo(c){return _.default.createElement(P.SvgIcon,(0,E.default)({viewBox:"0 0 32 32"},c),_.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.69648 24.8891C0.938383 22.2579 0 19.1645 0 16C0 11.7566 1.68571 7.68687 4.68629 4.68629C7.68687 1.68571 11.7566 0 16 0C19.1645 0 22.2579 0.938383 24.8891 2.69648C27.5203 4.45459 29.5711 6.95344 30.7821 9.87706C31.9931 12.8007 32.3099 16.0177 31.6926 19.1214C31.0752 22.2251 29.5514 25.0761 27.3137 27.3137C25.0761 29.5514 22.2251 31.0752 19.1214 31.6926C16.0177 32.3099 12.8007 31.9931 9.87706 30.7821C6.95344 29.5711 4.45459 27.5203 2.69648 24.8891ZM12.0006 9.33281H9.33437V22.6665H12.0006V9.33281ZM22.6657 9.33281H14.6669V11.9991H22.6657V9.33281ZM22.6657 14.6654H14.6669V17.3316H22.6657V14.6654ZM22.6657 20.0003H14.6669V22.6665H22.6657V20.0003Z"}))})(function(c){var m=c.theme;return{width:m.spacing(4),height:m.spacing(4),"& path":{fill:m.palette.text.primary}}});function PageHeader(c){var m=c.title,h=void 0===m?y("Export","elementor"):m,v=(0,_.useState)(!1),g=(0,b.default)(v,2),E=g[0],C=g[1],T=function handleHelpModalClose(){C(!1)};return _.default.createElement(_.default.Fragment,null,_.default.createElement(P.Stack,{direction:"row",spacing:2,alignItems:"center"},_.default.createElement(S,{sx:{mr:1}}),_.default.createElement(P.Typography,{variant:"h6",component:"h1",color:"text.primary",sx:{fontWeight:600}},h)),_.default.createElement(P.Stack,{direction:"row",spacing:1,alignItems:"center"},_.default.createElement(P.IconButton,{onClick:function handleHelpClick(){C(!0)},"aria-label":y("Help","elementor")},_.default.createElement(P.Box,{component:"i",className:"eicon-info-circle"})),_.default.createElement(P.IconButton,{onClick:function handleClose(){window.top.location=elementorAppConfig.admin_url+"admin.php?page=elementor-tools"},"aria-label":y("Close","elementor")},_.default.createElement(w.XIcon,null))),_.default.createElement(P.Dialog,{open:E,onClose:T,maxWidth:"sm"},_.default.createElement(P.DialogTitle,null,y("Export a Website Template","elementor")),_.default.createElement(P.DialogContent,null,_.default.createElement(P.Box,{sx:{mb:3}},_.default.createElement(P.Typography,{variant:"h6",gutterBottom:!0},y("What's a Website Template?","elementor")),_.default.createElement(P.Typography,{variant:"body2",color:"text.secondary",paragraph:!0},y("A Website Template is a .zip file that contains all the parts of a complete site. It's an easy way to get a site up and running quickly.","elementor")),_.default.createElement(P.Link,{href:"https://go.elementor.com/app-what-are-kits",target:"_blank",variant:"body2"},y("Learn more about Website Templates","elementor"))),_.default.createElement(P.Box,null,_.default.createElement(P.Typography,{variant:"h6",gutterBottom:!0},y("How does exporting work?","elementor")),_.default.createElement(P.Typography,{variant:"body2",color:"text.secondary",paragraph:!0},y("To turn your site into a Website Template, select the templates, content, settings and plugins you want to include. Once it's ready, you'll get a .zip file that you can import to other sites.","elementor")),_.default.createElement(P.Link,{href:"https://go.elementor.com/app-export-kit",target:"_blank",variant:"body2"},y("Learn More","elementor")))),_.default.createElement(P.DialogActions,null,_.default.createElement(P.Button,{onClick:T},y("Close","elementor")))))}PageHeader.propTypes={title:C.default.string}},47483:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=v(h(41594)),_=v(h(78304)),b=v(h(39805)),E=v(h(40989)),P=v(h(15118)),C=v(h(29402)),w=v(h(87861)),S=v(h(85707)),T=h(83040),N=v(h(47485)),D=v(h(76547));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}var A=m.default=function(c){function Button(){return(0,b.default)(this,Button),function _callSuper(c,m,h){return m=(0,C.default)(m),(0,P.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,C.default)(c).constructor):m.apply(c,h))}(this,Button,arguments)}return(0,w.default)(Button,c),(0,E.default)(Button,[{key:"getCssId",value:function getCssId(){return this.props.id}},{key:"getClassName",value:function getClassName(){var c="eps-button";return[c,this.props.className].concat(this.getStylePropsClasses(c)).filter(function(c){return""!==c}).join(" ")}},{key:"getStylePropsClasses",value:function getStylePropsClasses(c){var m=this,h=[];return["color","size","variant"].forEach(function(y){var v=m.props[y];v&&h.push(c+"--"+v)}),h}},{key:"getIcon",value:function getIcon(){if(this.props.icon){var c=this.props.tooltip||this.props.text,m=g.default.createElement(D.default,{className:this.props.icon,"aria-hidden":"true",title:c}),h="";return this.props.hideText&&(h=g.default.createElement("span",{className:"sr-only"},c)),g.default.createElement(g.default.Fragment,null,m,h)}return""}},{key:"getText",value:function getText(){return this.props.hideText?"":g.default.createElement("span",null,this.props.text)}},{key:"render",value:function render(){var c={},m=this.getCssId(),h=this.getClassName();m&&(c.id=m),h&&(c.className=h),this.props.onClick&&(c.onClick=this.props.onClick),this.props.rel&&(c.rel=this.props.rel),this.props.elRef&&(c.ref=this.props.elRef);var y=g.default.createElement(g.default.Fragment,null,this.getIcon(),this.getText());return this.props.url?0===this.props.url.indexOf("http")?g.default.createElement("a",(0,_.default)({href:this.props.url,target:this.props.target},c),y):(c.getProps=function(m){return m.isCurrent&&(c.className+=" active"),{className:c.className}},g.default.createElement(T.LocationProvider,{history:N.default.appHistory},g.default.createElement(T.Link,(0,_.default)({to:this.props.url},c),y))):g.default.createElement("div",c,y)}}])}(g.default.Component);(0,S.default)(A,"propTypes",{text:y.string.isRequired,hideText:y.bool,icon:y.string,tooltip:y.string,id:y.string,className:y.string,url:y.string,onClick:y.func,variant:y.oneOf(["contained","underlined","outlined",""]),color:y.oneOf(["primary","secondary","cta","link","disabled"]),size:y.oneOf(["sm","md","lg"]),target:y.string,rel:y.string,elRef:y.object}),(0,S.default)(A,"defaultProps",{id:"",className:"",variant:"",target:"_parent"})},47485:c=>{"use strict";c.exports=elementorAppPackages.router},47579:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Checkbox;var g=v(h(41594)),_=h(79397);function Checkbox(c){var m=c.className,h=c.checked,y=c.rounded,v=c.indeterminate,b=c.error,E=c.disabled,P=c.onChange,C=c.id,w="eps-checkbox",S=[w,m];return y&&S.push(w+"--rounded"),v&&S.push(w+"--indeterminate"),b&&S.push(w+"--error"),g.default.createElement("input",{className:(0,_.arrayToClassName)(S),type:"checkbox",checked:h,disabled:E,onChange:P,id:C})}h(33791),Checkbox.propTypes={className:y.string,checked:y.bool,disabled:y.bool,indeterminate:y.bool,rounded:y.bool,error:y.bool,onChange:y.func,id:y.string},Checkbox.defaultProps={className:"",checked:null,disabled:!1,indeterminate:!1,error:!1,onChange:function onChange(){}}},47629:(c,m,h)=>{"use strict";var y=h(25800),v=h(75206),g=h.n(v)().unstable_batchedUpdates;y.j.setBatchNotifyFunction(g)},47819:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TableCell;var g=v(h(41594)),_=h(79397);function TableCell(c){var m=function Element(){return g.default.createElement(c.tag,{className:(0,_.arrayToClassName)(["eps-table__cell",c.className]),colSpan:c.colSpan||null},c.children)};return g.default.createElement(m,null)}TableCell.propTypes={children:y.any,className:y.string,colSpan:y.oneOfType([y.number,y.string]),tag:y.oneOf(["td","th"]).isRequired}},49194:()=>{},49477:(c,m,h)=>{"use strict";m.__esModule=!0;var y=_interopRequireDefault(h(41594)),v=_interopRequireDefault(h(31659));function _interopRequireDefault(c){return c&&c.__esModule?c:{default:c}}m.default=y.default.createContext||v.default,c.exports=m.default},49902:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Panel;var g=v(h(41594)),_=h(79397),b=v(h(35676)),E=v(h(28929)),P=v(h(58206)),C=v(h(95799)),w=v(h(78179));function Panel(c){return g.default.createElement(E.default,{isOpened:c.isOpened},g.default.createElement(b.default,{className:(0,_.arrayToClassName)(["eps-panel",c.className])},c.children))}h(83768),Panel.propTypes={className:y.string,isOpened:y.bool,children:y.any.isRequired},Panel.defaultProps={className:"",isOpened:!1},Panel.Header=P.default,Panel.Headline=C.default,Panel.Body=w.default},51160:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportIntro(){return g.default.createElement(_.Box,{sx:{mb:4}},g.default.createElement(_.Typography,{variant:"h4",component:"h2",gutterBottom:!0},y("Export a Website template?","elementor")),g.default.createElement(_.Typography,{variant:"body1",color:"text.secondary"},y("Choose which Elementor components - templates, content and site settings - to include in your website templates file. By default, all of your components will be exported.","elementor")," ",g.default.createElement(_.Link,{href:"https://go.elementor.com/app-what-are-kits",target:"_blank",rel:"noopener noreferrer"},y("Learn more","elementor"))))};var g=v(h(41594)),_=h(86956)},51776:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DialogContent;var g=v(h(41594));function DialogContent(c){return g.default.createElement("div",{className:"eps-dialog__content"},c.children)}DialogContent.propTypes={children:y.any}},51959:()=>{},52786:(c,m,h)=>{"use strict";h.d(m,{n:()=>useMutation});var y=h(68102),v=h(41594),g=h.n(v),_=h(25800),b=h(6369),E=h(31571),P=h(15292),C=h(70963);function useMutation(c,m,h){var v=g().useRef(!1),w=g().useState(0)[1],S=(0,b.GR)(c,m,h),T=(0,P.j)(),N=g().useRef();N.current?N.current.setOptions(S):N.current=new E._(T,S);var D=N.current.getCurrentResult();g().useEffect(function(){v.current=!0;var c=N.current.subscribe(_.j.batchCalls(function(){v.current&&w(function(c){return c+1})}));return function(){v.current=!1,c()}},[]);var A=g().useCallback(function(c,m){N.current.mutate(c,m).catch(b.lQ)},[]);if(D.error&&(0,C.G)(void 0,N.current.options.useErrorBoundary,[D.error]))throw D.error;return(0,y.A)({},D,{mutate:A,mutateAsync:D.mutate})}},52923:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportKit(){var c=(0,_.useContext)(w.SharedContext),m=(0,_.useContext)(S.ImportContext),h=(0,E.useNavigate)(),v=(0,V.default)(),g=v.kitState,Y=v.kitActions,J=v.KIT_STATUS_MAP,Z=(0,_.useState)(""),ee=(0,b.default)(Z,2),te=ee[0],ne=ee[1],de=(0,_.useState)(!1),fe=(0,b.default)(de,2),pe=fe[0],me=fe[1],ye=c.data,ve=ye.referrer,ge=ye.currentPage,be=function eventTracking(c){var m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"click",y=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,v=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,g=arguments.length>5?arguments[5]:void 0;if("kit-library"===ve){var _=null;g&&(_="drop"===g?"drag-drop":"browse");var b=null;m&&"eps-button eps-dialog__button"===m.currentTarget.className.trim()?b="close button":m&&"eps-button eps-dialog__close-button"===m.currentTarget.className.trim()&&(b="x"),(0,K.appsEventTrackingDispatch)(c,{element:b,page_source:"import",event_type:h,step:ge,error:"general"===y?"unknown":y,modal_type:v,method:_})}},Ee=(0,P.useConfirmAction)({doNotShowAgainKey:"upload_json_warning_generic_message",action:function action(c,h){me(!0),m.dispatch({type:"SET_FILE",payload:c}),be("kit-library/file-upload",null,"feedback",null,null,h.type)}}),Oe=Ee.runAction,xe=Ee.dialog,je=Ee.checkbox,ke=(0,C.default)().getAll(),Te=ke.source,Re=ke.kit_id,Me=[m.data.source,Te].includes(V.KIT_SOURCE_MAP.CLOUD);return(0,_.useEffect)(function(){c.dispatch({type:"SET_INCLUDES",payload:[]}),m.dispatch({type:"SET_KIT_SOURCE",payload:Te}),c.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportKit.name})},[]),(0,_.useEffect)(function(){m.data.file&&Y.upload({file:m.data.file})},[m.data.file]),(0,_.useEffect)(function(){J.UPLOADED===g.status?(m.dispatch({type:"SET_UPLOADED_DATA",payload:g.data}),Me&&g.data.uploaded_kit&&m.dispatch({type:"SET_FILE",payload:g.data.uploaded_kit})):"error"===g.status&&ne(g.data)},[g.status]),(0,_.useEffect)(function(){if(m.data.uploadedData&&m.data.file){var c=m.data.uploadedData.manifest.plugins?"/import/plugins":"/import/content";h(c)}},[m.data.uploadedData]),(0,_.useEffect)(function(){V.KIT_SOURCE_MAP.CLOUD===Te&&Re&&Y.upload({source:Te,kitId:Re})},[Te,Re]),_.default.createElement(T.default,{type:"import"},_.default.createElement("section",{className:"e-app-import"},Me?_.default.createElement(Q.default,null):_.default.createElement(_.default.Fragment,null,"kit-library"===ve&&_.default.createElement(U.default,{className:"e-app-import__back-to-library",icon:"eicon-chevron-left",text:y("Back to Website Templates","elementor"),url:pe?"":"/kit-library".concat(Me?"/cloud":"")}),_.default.createElement(N.default,{heading:y("Import a Website Template","elementor"),description:_.default.createElement(_.default.Fragment,null,y("Upload a .zip file with style, site settings, content, etc. Then, we’ll apply them to your site.","elementor")," ",function getLearnMoreLink(){return _.default.createElement(A.default,{url:"https://go.elementor.com/app-what-are-kits",key:"learn-more-link",italic:!0,onClick:function onClick(){return be("kit-library/seek-more-info",null,"click")}},y("Learn More","elementor"))}())}),_.default.createElement(W.default,{label:y("Heads up!","elementor"),color:"warning",className:"e-app-import__notice"},y("Before applying a new template, we recommend backing up your site so you can roll back any undesired changes.","elementor")),_.default.createElement(q.default,{className:"e-app-import__drop-zone",heading:y("Choose a file to import","elementor"),text:y("Drag & drop the .zip file with your website template","elementor"),secondaryText:"Or",filetypes:["zip"],onFileChoose:function onFileChoose(){return be("kit-library/choose-file")},onFileSelect:Oe,onError:function onError(){return ne("general")},isLoading:pe,buttonText:y("Import from files")}),xe.isOpen&&_.default.createElement(G.default,{title:y("Warning: JSON or ZIP files may be unsafe","elementor"),text:y("Uploading JSON or ZIP files from unknown sources can be harmful and put your site at risk. For maximum safety, upload only JSON or ZIP files from trusted sources.","elementor"),approveButtonColor:"link",approveButtonText:y("Continue","elementor"),approveButtonOnClick:xe.approve,dismissButtonText:y("Cancel","elementor"),dismissButtonOnClick:xe.dismiss,onClose:xe.dismiss},_.default.createElement("label",{htmlFor:"do-not-show-upload-json-warning-again",style:{display:"flex",alignItems:"center",gap:"5px"}},_.default.createElement(H.default,{id:"do-not-show-upload-json-warning-again",type:"checkbox",value:je.isChecked,onChange:function onChange(c){return je.setIsChecked(!!c.target.checked)}}),y("Do not show this message again","elementor")))),te&&_.default.createElement(D.default,{errorType:te,onApprove:function resetImportProcess(){m.dispatch({type:"SET_FILE",payload:null}),ne(null),me(!1),Y.reset()},onModalClose:function onModalClose(c){return be("kit-library/modal-close",c,"load",null,"error")},onError:function onError(){return be("kit-library/modal-open",null,"load",te,"error")},onLearnMore:function onLearnMore(){return be("kit-library/seek-more-info",null,"click",null,"error")}})))};var _=_interopRequireWildcard(h(41594)),b=v(h(18821)),E=h(83040),P=h(68276),C=v(h(41494)),w=h(69378),S=h(53442),T=v(h(53931)),N=v(h(23327)),D=v(h(19744)),A=v(h(54999)),W=v(h(40587)),q=v(h(39970)),U=v(h(47483)),Q=v(h(38832)),K=h(3073),H=v(h(47579)),G=v(h(15656)),V=_interopRequireWildcard(h(14300));function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b})(c,m)}h(80317)},53051:(c,m,h)=>{var y=h(67114),v=h(9535),g=h(62507),_=h(46313),b=h(33929),E=h(95315),P=h(66961);function _regeneratorRuntime(){"use strict";var m=v(),h=m.m(_regeneratorRuntime),C=(Object.getPrototypeOf?Object.getPrototypeOf(h):h.__proto__).constructor;function n(c){var m="function"==typeof c&&c.constructor;return!!m&&(m===C||"GeneratorFunction"===(m.displayName||m.name))}var w={throw:1,return:2,break:3,continue:3};function a(c){var m,h;return function(y){m||(m={stop:function stop(){return h(y.a,2)},catch:function _catch(){return y.v},abrupt:function abrupt(c,m){return h(y.a,w[c],m)},delegateYield:function delegateYield(c,v,g){return m.resultName=v,h(y.d,P(c),g)},finish:function finish(c){return h(y.f,c)}},h=function t(c,h,v){y.p=m.prev,y.n=m.next;try{return c(h,v)}finally{m.next=y.n}}),m.resultName&&(m[m.resultName]=y.v,m.resultName=void 0),m.sent=y.v,m.next=y.n;try{return c.call(this,m)}finally{y.p=m.prev,y.n=m.next}}}return(c.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(c,h,y,v){return m.w(a(c),h,y,v&&v.reverse())},isGeneratorFunction:n,mark:m.m,awrap:function awrap(c,m){return new y(c,m)},AsyncIterator:b,async:function async(c,m,h,y,v){return(n(m)?_:g)(a(c),m,h,y,v)},keys:E,values:P}},c.exports.__esModule=!0,c.exports.default=c.exports)()}c.exports=_regeneratorRuntime,c.exports.__esModule=!0,c.exports.default=c.exports},53441:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=UnfilteredFilesDialog;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(18821)),P=g(h(15656)),C=g(h(73921));function UnfilteredFilesDialog(c){var m=c.show,h=c.setShow,v=c.onReady,g=c.onCancel,_=c.onDismiss,w=c.onLoad,S=c.onEnable,T=c.onClose,N=(0,C.default)(),D=N.ajaxState,A=N.setAjax,W=(0,b.useState)(!1),q=(0,E.default)(W,2),U=q[0],Q=q[1],K=(0,b.useState)(!1),H=(0,E.default)(K,2),G=H[0],V=H[1];return(0,b.useEffect)(function(){U&&(h(!1),A({data:{action:"elementor_ajax",actions:JSON.stringify({enable_unfiltered_files_upload:{action:"enable_unfiltered_files_upload"}})}}),S&&S())},[U]),(0,b.useEffect)(function(){switch(D.status){case"success":v();break;case"error":V(!0),h(!0)}},[D]),(0,b.useEffect)(function(){m&&w&&w()},[m]),m?b.default.createElement(b.default.Fragment,null,G?b.default.createElement(P.default,{title:y("Something went wrong.","elementor"),text:c.errorModalText,approveButtonColor:"link",approveButtonText:y("Continue","elementor"),approveButtonOnClick:v,dismissButtonText:y("Go Back","elementor"),dismissButtonOnClick:g,onClose:g}):b.default.createElement(P.default,{title:y("First, enable unfiltered file uploads.","elementor"),text:c.confirmModalText,approveButtonColor:"link",approveButtonText:y("Enable","elementor"),approveButtonOnClick:function approveButtonOnClick(){return Q(!0)},dismissButtonText:y("Skip","elementor"),dismissButtonOnClick:_||v,onClose:T||_||v})):null}UnfilteredFilesDialog.propTypes={show:v.bool,setShow:v.func.isRequired,onReady:v.func.isRequired,onCancel:v.func.isRequired,onDismiss:v.func,confirmModalText:v.string.isRequired,errorModalText:v.string.isRequired,onLoad:v.func,onEnable:v.func,onClose:v.func},UnfilteredFilesDialog.defaultProps={show:!1,onReady:function onReady(){},onCancel:function onCancel(){}}},53442:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.ImportContext=void 0,m.default=ImportContextProvider;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(18821)),E=h(24079);var P=m.ImportContext=_.default.createContext();function ImportContextProvider(c){var m=(0,_.useReducer)(E.reducer,{id:null,file:null,uploadedData:null,importedData:null,source:"",plugins:[],requiredPlugins:[],importedPlugins:[],overrideConditions:[],isProInstalledDuringProcess:!1,actionType:null,isResolvedData:!1,pluginsState:""}),h=(0,b.default)(m,2),y=h[0],v=h[1];return _.default.createElement(P.Provider,{value:{data:y,dispatch:v}},c.children)}ImportContextProvider.propTypes={children:y.object.isRequired}},53709:(c,m,h)=>{"use strict";h.d(m,{q:()=>useInfiniteQuery});var y=h(69046),v=h(6369),g=h(86830);function useInfiniteQuery(c,m,h){var _=(0,v.vh)(c,m,h);return(0,g.t)(_,y.z)}},53931:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Layout;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(10906)),P=g(h(85707)),C=g(h(18821)),w=g(h(80226)),S=g(h(76182)),T=h(6634),N=g(h(71308)),D=g(h(17129)),A=h(69378),W=h(3073),q=g(h(41494)),U=g(h(62841));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,P.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function Layout(c){var m=(0,b.useState)(!1),h=(0,C.default)(m,2),v=h[0],g=h[1],_=(0,q.default)().getAll().referrer,P=(0,b.useContext)(A.SharedContext),Q=P.data,K=Q.currentPage,H=Q.returnTo,G=function eventTracking(c){var m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,y=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"click",v=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;("kit-library"===P.data.referrer||_)&&(0,W.appsEventTrackingDispatch)(c,{element:h,page_source:"import",event_type:y,step:K,element_position:m,modal_type:v})},V={title:"import"===c.type?y("Import","elementor"):y("Export","elementor"),headerButtons:[function getInfoButtonProps(){return _objectSpread(_objectSpread({},T.infoButtonProps),{},{onClick:function onClick(){G("kit-library/seek-more-info","app_header"),g(!0)}})}()].concat((0,E.default)(c.headerButtons)),content:function getContent(){var m={show:v,setShow:g};return("kit-library"===P.data.referrer||_)&&(m=_objectSpread(_objectSpread({referrer:_},m),{},{onOpen:function onOpen(){return G("kit-library/modal-open",null,null,"load","info")},onClose:function onClose(c){return function onModalClose(c,m){var h=c.target.classList.contains("eps-modal__overlay")?"overlay":"x";G(m,h,null,"info")}(c,"kit-library/modal-close")}})),b.default.createElement(S.default,null,c.children,"import"===c.type?b.default.createElement(N.default,m):b.default.createElement(D.default,m))}(),footer:c.footer,onClose:function onClose(){return function onClose(){G("kit-library/close","app_header",null,"click"),H&&(0,U.default)(H)||("kit-library"===P.data.referrer||"kit-library"===_?window.top.location=elementorAppConfig.admin_url+"admin.php?page=elementor-app#/kit-library":window.top.location=elementorAppConfig.admin_url+"admin.php?page=elementor-tools#tab-import-export-kit")}()}},Y="#tab-import-export-kit";return!_&&-1===elementorAppConfig.return_url.indexOf(Y)&&elementorAppConfig.return_url.includes("page=elementor-tools")&&(elementorAppConfig.return_url+=Y),(0,b.useEffect)(function(){_&&P.dispatch({type:"SET_REFERRER",payload:_})},[_]),b.default.createElement(w.default,V)}Layout.propTypes={type:v.oneOf(["import","export"]),headerButtons:v.arrayOf(v.object),children:v.object.isRequired,footer:v.object},Layout.defaultProps={headerButtons:[]}},54069:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(8555)),P=g(h(55725)),C=g(h(54999)),w=g(h(76547));function PluginsTable(c){var m=c.plugins,h=c.layout,v=c.withHeader,g=c.withStatus,_=c.onSelect,S=c.initialSelected,T=c.initialDisabled,N=function CellText(c){return b.default.createElement(P.default,{className:"e-app-import-export-plugins-table__cell-content"},c.text)},D=function CellLink(c){return b.default.createElement(C.default,{url:c.url,underline:"none"},"".concat(y("Version")," ").concat(c.text)," ",b.default.createElement(w.default,{className:"eicon-editor-external-link"}))},A=m.map(function(c){var m=c.name,h=c.status,y=c.version,v=c.plugin_uri,_=[b.default.createElement(N,{text:m,key:m}),b.default.createElement(D,{text:y,url:v,key:m})];return g&&_.splice(1,0,b.default.createElement(N,{text:h,key:m})),_});return b.default.createElement(E.default,{selection:!0,headers:function getHeaders(){if(!v)return[];var c=["Plugin Name","Version"];return g&&c.splice(1,0,"Status"),c}(),rows:A,onSelect:_,initialSelected:S,initialDisabled:T,layout:h,className:"e-app-import-export-plugins-table"})}h(73157),PluginsTable.propTypes={onSelect:v.func,initialDisabled:v.array,initialSelected:v.array,plugins:v.array,withHeader:v.bool,withStatus:v.bool,layout:v.array},PluginsTable.defaultProps={initialDisabled:[],initialSelected:[],plugins:[],withHeader:!0,withStatus:!0};m.default=(0,b.memo)(PluginsTable)},54525:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=BaseLayout;var v=y(h(41594)),g=y(h(78304)),_=y(h(85707)),b=y(h(40453)),E=h(86956),P=y(h(62688)),C=["children","topBar","footer","sx"];function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function BaseLayout(c){var m,h,y=c.children,_=c.topBar,P=c.footer,w=c.sx,S=void 0===w?{}:w,T=(0,b.default)(c,C),N=(null===(m=elementorAppConfig["import-export-customization"])||void 0===m?void 0:m.uiTheme)||"auto",D="dark"===N||"auto"===N&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",A=(null===(h=elementorCommon)||void 0===h||null===(h=h.config)||void 0===h?void 0:h.isRTL)||!1;return v.default.createElement(E.DirectionProvider,{rtl:A},v.default.createElement(E.ThemeProvider,{colorScheme:D},v.default.createElement(E.Box,(0,g.default)({sx:_objectSpread({height:"100vh",display:"flex",flexDirection:"column",overflow:"hidden"},S)},T),v.default.createElement(E.Box,{sx:{position:"sticky",top:0}},_),v.default.createElement(E.Box,{component:"main",sx:{flex:1,overflow:"auto",display:"flex",flexDirection:"column"}},y),v.default.createElement(E.Box,{sx:{position:"sticky",bottom:0}},P))))}BaseLayout.propTypes={children:P.default.node.isRequired,topBar:P.default.node,footer:P.default.node,sx:P.default.object}},54880:(c,m,h)=>{"use strict";h.d(m,{l:()=>useIsMutating});var y=h(41594),v=h.n(y),g=h(25800),_=h(6369),b=h(15292);function useIsMutating(c,m){var h=v().useRef(!1),y=(0,_.KK)(c,m),E=(0,b.j)(),P=v().useState(E.isMutating(y)),C=P[0],w=P[1],S=v().useRef(y);S.current=y;var T=v().useRef(C);return T.current=C,v().useEffect(function(){h.current=!0;var c=E.getMutationCache().subscribe(g.j.batchCalls(function(){if(h.current){var c=E.isMutating(S.current);T.current!==c&&w(c)}}));return function(){h.current=!1,c()}},[E]),C}},54902:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DialogWrapper;var _=g(h(41594)),b=g(h(47483));function DialogWrapper(c){var m="div";return c.onSubmit&&(m="form"),_.default.createElement("section",{className:"eps-modal__overlay"},_.default.createElement(m,{className:"eps-modal eps-dialog",onSubmit:c.onSubmit},c.onClose&&_.default.createElement(b.default,{onClick:c.onClose,text:y("Close","elementor"),hideText:!0,icon:"eicon-close",className:"eps-dialog__close-button"}),c.children))}DialogWrapper.propTypes={onClose:v.func,onSubmit:v.func,children:v.any}},54999:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=InlineLink;var g=v(h(41594)),_=h(83040),b=v(h(47485)),E=h(79397);function InlineLink(c){var m="eps-inline-link",h=[m,"".concat(m,"--color-").concat(c.color),"none"!==c.underline?"".concat(m,"--underline-").concat(c.underline):"",c.italic?"".concat(m,"--italic"):"",c.className],y=(0,E.arrayToClassName)(h);return c.url?c.url.includes("http")?function getExternalLink(){return g.default.createElement("a",{href:c.url,target:c.target,rel:c.rel,className:y,onClick:c.onClick},c.children)}():function getRouterLink(){return g.default.createElement(_.LocationProvider,{history:b.default.appHistory},g.default.createElement(_.Link,{to:c.url,className:y},c.children))}():function getActionLink(){return g.default.createElement("button",{className:y,onClick:c.onClick},c.children)}()}h(72701),InlineLink.propTypes={className:y.string,children:y.any,url:y.string,target:y.string,rel:y.string,text:y.string,color:y.oneOf(["primary","secondary","cta","link","disabled"]),underline:y.oneOf(["none","hover","always"]),italic:y.bool,onClick:y.func},InlineLink.defaultProps={className:"",color:"link",underline:"always",target:"_blank",rel:"noopener noreferrer"}},55198:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Footer;var g=v(h(41594));function Footer(c){return g.default.createElement("footer",{className:"eps-app__footer"},c.children)}Footer.propTypes={children:y.object}},55725:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Text;var g=v(h(41594)),_=h(79397);function Text(c){var m=[c.className],h=c.variant&&"md"!==c.variant?"-"+c.variant:"";m.push("eps-text"+h);var y=function Element(){return g.default.createElement(c.tag,{className:(0,_.arrayToClassName)(m)},c.children)};return g.default.createElement(y,null)}Text.propTypes={className:y.string,variant:y.oneOf(["xl","lg","md","sm","xs","xxs"]),tag:y.string,children:y.any.isRequired},Text.defaultProps={className:"",tag:"p"}},56367:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var _=_interopRequireWildcard(h(41594)),b=v(h(19232)),E=v(h(22803)),P=_interopRequireWildcard(h(28816)),C=_interopRequireWildcard(h(7221));function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b})(c,m)}var w=[3,1],S=[0];function ExportPluginsSelection(c){var m=c.onSelect,h=(0,P.default)().response,y=(0,C.default)(h.data).pluginsData.filter(function(c){var m=c.status;return P.PLUGIN_STATUS_MAP.ACTIVE===m||P.PLUGIN_STATUS_MAP.MULTISITE_ACTIVE===m});return h.data?_.default.createElement(b.default,{plugins:y,initialSelected:function getInitialSelected(){var c=[0];return y.length>1&&C.PLUGINS_KEYS.ELEMENTOR_PRO===y[1].name&&c.push(1),c}(),initialDisabled:S,layout:w,withStatus:!1,onSelect:m}):_.default.createElement(E.default,{absoluteCenter:!0})}ExportPluginsSelection.propTypes={onSelect:y.func.isRequired};m.default=(0,_.memo)(ExportPluginsSelection)},56441:c=>{"use strict";c.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},56757:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=WizardFooter;var g=v(h(41594)),_=v(h(78304)),b=h(79397),E=v(h(3416));function WizardFooter(c){var m="e-app-wizard-footer",h=[m,c.className];return c.separator&&h.push(m+"__separator"),g.default.createElement(E.default,(0,_.default)({container:!0},c,{className:(0,b.arrayToClassName)(h)}),c.children)}h(79281),WizardFooter.propTypes={className:y.string,justify:y.any,separator:y.any,children:y.oneOfType([y.string,y.object,y.arrayOf(y.object)]).isRequired},WizardFooter.defaultProps={className:""}},56777:(c,m,h)=>{"use strict";h.d(m,{I:()=>useQuery});var y=h(74342),v=h(6369),g=h(86830);function useQuery(c,m,h){var _=(0,v.vh)(c,m,h);return(0,g.t)(_,y.$)}},56915:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.ReducerUtils=void 0;var v=y(h(10906)),g=y(h(85707)),_=y(h(39805)),b=y(h(40989));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,g.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}m.ReducerUtils=function(){return(0,b.default)(function ReducerUtils(){(0,_.default)(this,ReducerUtils)},null,[{key:"updateArray",value:function updateArray(c,m,h,y){return"add"===y?c[m].includes(h)?c:_objectSpread(_objectSpread({},c),{},(0,g.default)({},m,[].concat((0,v.default)(c[m]),[h]))):"remove"===y?_objectSpread(_objectSpread({},c),{},(0,g.default)({},m,c[m].filter(function(c){return c!==h}))):c}}])}()},57401:c=>{"use strict";c.exports=elementorAppPackages.appUi},57463:()=>{},57804:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ImportCompleteFooter;var _=g(h(41594)),b=g(h(91071)),E=g(h(47483)),P=g(h(46361)),C=h(3073);function ImportCompleteFooter(c){var m=c.seeItLiveUrl,h=c.referrer,v=(0,P.default)(),g=function eventTracking(c){var m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";"kit-library"===h&&(0,C.appsEventTrackingDispatch)(c,{page_source:"kit is live",element_location:"app_wizard_footer",event_type:m})};return _.default.createElement(b.default,null,m&&_.default.createElement(E.default,{text:y("See It Live","elementor"),variant:"contained",onClick:function onClick(){g("kit-library/see-it-live"),window.open(m,"_blank")}}),_.default.createElement(E.default,{text:y("Got It","elementor"),variant:"contained",color:"primary",onClick:function onClick(){g("kit-library/close"),v.backToDashboard()}}))}ImportCompleteFooter.propTypes={seeItLiveUrl:v.string,referrer:v.string}},58068:()=>{},58155:c=>{function asyncGeneratorStep(c,m,h,y,v,g,_){try{var b=c[g](_),E=b.value}catch(c){return void h(c)}b.done?m(E):Promise.resolve(E).then(y,v)}c.exports=function _asyncToGenerator(c){return function(){var m=this,h=arguments;return new Promise(function(y,v){var g=c.apply(m,h);function _next(c){asyncGeneratorStep(g,y,v,_next,_throw,"next",c)}function _throw(c){asyncGeneratorStep(g,y,v,_next,_throw,"throw",c)}_next(void 0)})}},c.exports.__esModule=!0,c.exports.default=c.exports},58206:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PanelHeader;var g=v(h(41594)),_=h(79397),b=v(h(35676)),E=v(h(28929));function PanelHeader(c){return g.default.createElement(E.default.Toggle,{active:c.toggle,showIcon:c.showIcon},g.default.createElement(b.default.Header,{padding:"20",className:(0,_.arrayToClassName)(["eps-panel__header",c.className])},c.children))}PanelHeader.propTypes={className:y.string,padding:y.string,toggle:y.bool,showIcon:y.bool,children:y.any.isRequired},PanelHeader.defaultProps={className:"",padding:"20",toggle:!0,showIcon:!0}},59250:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DialogText;var v=y(h(41594)),g=y(h(85707)),_=y(h(78304)),b=y(h(55725));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,g.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function DialogText(c){return v.default.createElement(b.default,(0,_.default)({variant:"xs"},c,{className:"eps-dialog__text ".concat(c.className)}))}DialogText.propTypes=_objectSpread({},b.default.propTypes),DialogText.defaultProps=_objectSpread(_objectSpread({},b.default.defaultProps),{},{tag:"p",variant:"sm"})},59297:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportComplete(){var c=(0,_.useContext)(E.SharedContext),m=(0,_.useContext)(P.ImportContext),h=(0,b.useNavigate)(),v=m.data||{},g=v.importedPlugins,U=v.uploadedData,Q=v.importedData,K=v.isProInstalledDuringProcess,H=(c.data||{}).referrer,G=(0,q.default)(),V=G.getTemplates,Y=G.getContent,J=G.getWPContent,Z=(0,G.getPlugins)(g),ee=Z.activePlugins,te=Z.failedPlugins,ne=(null==Q?void 0:Q.configData)||{},de=ne.elementorHomePageUrl,fe=ne.recentlyEditedElementorPageUrl,pe=de||fe||null,me=function eventTracking(c,m){var h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"click",y=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;"kit-library"===H&&(0,W.appsEventTrackingDispatch)(c,{page_source:m,event_type:h,element_location:y})},ye=(0,_.useMemo)(function(){return function getKitData(){if(!U||!Q)return{};var m=U.manifest;return{templates:V(m.templates,Q),content:Y(m.content,Q),"wp-content":J(m["wp-content"],Q),"site-settings":c.data.includes.includes("settings")?m["site-settings"]:{},plugins:ee,configData:Q.configData}}()},[]);return(0,_.useEffect)(function(){U||h("/import"),U&&me("kit-library/kit-is-live-load","kit is live","load"),c.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportComplete.name})},[]),_.default.createElement(C.default,{type:"import",footer:_.default.createElement(A.default,{seeItLiveUrl:pe,referrer:H})},_.default.createElement(w.default,{image:elementorAppConfig.assets_url+"images/kit-is-live.svg",heading:y("We applied your template and your site is online!","elementor"),description:y("You've imported and applied the following to your site:","elementor"),notice:_.default.createElement(_.default.Fragment,null,_.default.createElement(T.default,{url:"https://go.elementor.com/app-what-are-kits",italic:!0,onClick:function onClick(){return me("kit-library/seek-more-info","kit is live","click","app_header")}},y("Click here","elementor"))," ",y("to learn more about building your site with Elementor Website Templates","elementor"))},!!te.length&&_.default.createElement(N.default,{failedPlugins:te}),K&&_.default.createElement(D.default,null),_.default.createElement(S.default,{data:ye})))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(83040),E=h(69378),P=h(53442),C=v(h(53931)),w=v(h(77755)),S=v(h(81920)),T=v(h(54999)),N=v(h(15104)),D=v(h(36808)),A=v(h(57804)),W=h(3073),q=v(h(44663))},59504:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function Import(){return v.default.createElement(g.default,null,v.default.createElement(_.default,null,v.default.createElement(b.LocationProvider,{history:E.default.appHistory},v.default.createElement(b.Router,null,v.default.createElement(N.default,{path:"complete"}),v.default.createElement(T.default,{path:"process"}),v.default.createElement(w.default,{path:"resolver"}),v.default.createElement(C.default,{path:"content"}),v.default.createElement(D.default,{path:"plugins"}),v.default.createElement(S.default,{path:"plugins-activation"}),v.default.createElement(P.default,{default:!0})))))};var v=y(h(41594)),g=y(h(69378)),_=y(h(53442)),b=h(83040),E=y(h(47485)),P=y(h(52923)),C=y(h(31481)),w=y(h(42145)),S=y(h(23393)),T=y(h(64297)),N=y(h(59297)),D=y(h(61547))},59824:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=DragDrop;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(78304)),E=v(h(18821)),P=h(79397);function DragDrop(c){var m=(0,_.useState)(!1),h=(0,E.default)(m,2),y=h[0],v=h[1],g=function onDragDropActions(c){c.preventDefault(),c.stopPropagation()},C={onDrop:function onDrop(m){g(m),v(!1),c.onDrop&&c.onDrop(m)},onDragOver:function onDragOver(m){g(m),v(!0),c.onDragOver&&c.onDragOver(m)},onDragLeave:function onDragLeave(m){g(m),v(!1),c.onDragLeave&&c.onDragLeave(m)}};return _.default.createElement("div",(0,b.default)({},C,{className:function getClassName(){var m="e-app-drag-drop",h=[m,c.className];return y&&!c.isLoading&&h.push(m+"--drag-over"),(0,P.arrayToClassName)(h)}()}),c.children)}h(49194),DragDrop.propTypes={className:y.string,children:y.any,onDrop:y.func,onDragLeave:y.func,onDragOver:y.func,isLoading:y.bool},DragDrop.defaultProps={className:""}},59957:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),Object.defineProperty(m,"XIcon",{enumerable:!0,get:function get(){return y.XIcon}});var y=h(22143)},59994:(c,m,h)=>{"use strict";function _setPrototypeOf(c,m){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(c,m){return c.__proto__=m,c},_setPrototypeOf(c,m)}function _inheritsLoose(c,m){c.prototype=Object.create(m.prototype),c.prototype.constructor=c,_setPrototypeOf(c,m)}h.d(m,{A:()=>_inheritsLoose})},60603:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useKitData(c){var m=function getLabel(m,h,y){var v,g=((null==c||null===(v=c.configData)||void 0===v?void 0:v.summaryTitles)||elementorAppConfig["import-export"].summaryTitles)[m][h];return null!=g&&g.single?y?y+" "+(y>1?g.plural:g.single):"":g},h=function getTemplates(){var h={};for(var y in null==c?void 0:c.templates){var v=c.templates[y].doc_type;h[v]||(h[v]=0),h[v]++}return Object.entries(h).map(function(c){var h=(0,g.default)(c,2),y=h[0],v=h[1];return m("templates",y,v)}).filter(function(c){return c})},y=function getSiteSettings(){var h=(null==c?void 0:c["site-settings"])||{};return Object.values(h).map(function(c){return m("site-settings",c)})},v=function getContent(){var h=(null==c?void 0:c.content)||{},y=(null==c?void 0:c["wp-content"])||{},v=_objectSpread({},h);for(var _ in v)v[_]=Object.keys(v[_]).concat(y[_]||[]);return v=_objectSpread(_objectSpread({},y),v),Object.entries(v).map(function(c){var h=(0,g.default)(c,2),y=h[0],v=h[1];return m("content",y,v.length)}).filter(function(c){return c})},b=function getPlugins(){return null!=c&&c.plugins?c.plugins.map(function(c){return c.name}):[]};return(0,_.useMemo)(function(){return{templates:h(),siteSettings:y(),content:v(),plugins:b()}},[c])};var v=y(h(85707)),g=y(h(18821)),_=h(41594);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}},61533:()=>{},61547:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportPlugins(){var c,m=(0,_.useContext)(E.ImportContext),h=(0,_.useContext)(P.SharedContext),v=(0,b.useNavigate)(),g=(null===(c=m.data.uploadedData)||void 0===c||null===(c=c.manifest)||void 0===c?void 0:c.plugins)||[],G=(0,U.default)(),V=G.response,Y=G.pluginsActions,J=(0,Q.default)(V.data).pluginsData,Z=(0,K.default)(g,J).importPluginsData,ee=Z||{},te=ee.missing,ne=ee.existing,de=ee.minVersionMissing,fe=ee.proData,pe=h.data||{},me=pe.referrer,ye=pe.currentPage,ve=function eventTracking(c){"kit-library"===me&&(0,H.appsEventTrackingDispatch)(c,{page_source:"import",step:ye,event_type:"click"})};return(0,_.useEffect)(function(){g.length||v("import/content"),h.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportPlugins.name})},[]),(0,_.useEffect)(function(){Z&&!m.data.requiredPlugins.length&&(function handleRequiredPlugins(){te.length&&m.dispatch({type:"SET_REQUIRED_PLUGINS",payload:te})}(),function handleProInstallationStatus(){fe&&!elementorAppConfig.hasPro&&m.dispatch({type:"SET_IS_PRO_INSTALLED_DURING_PROCESS",payload:!0})}())},[Z]),_.default.createElement(C.default,{type:"import",footer:_.default.createElement(D.default,{onPreviousClick:function onPreviousClick(){return ve("kit-library/go-back")},onNextClick:function onNextClick(){return ve("kit-library/approve-selection")}})},_.default.createElement("section",{className:"e-app-import-plugins"},!Z&&_.default.createElement(A.default,{absoluteCenter:!0}),_.default.createElement(w.default,{heading:y("Select which plugins to include","elementor"),description:y("All items are already selected by default. Uncheck the ones you don't want.","elementor")}),!(null==de||!de.length)&&_.default.createElement(W.default,{label:y(" Recommended:","elementor"),className:"e-app-import-plugins__versions-notice",color:"warning"},y("Head over to Updates and make sure that your plugins are updated to the latest version.","elementor")," ",_.default.createElement(q.default,{url:elementorAppConfig.admin_url+"update-core.php"},y("Take me there","elementor"))),U.PLUGIN_STATUS_MAP.NOT_INSTALLED===(null==fe?void 0:fe.status)&&_.default.createElement(N.default,{onRefresh:function handleRefresh(){m.dispatch({type:"SET_REQUIRED_PLUGINS",payload:[]}),Y.fetch()}}),_.default.createElement(S.default,{plugins:te}),_.default.createElement(T.default,{plugins:ne})))};var _=_interopRequireWildcard(h(41594)),b=h(83040),E=h(53442),P=h(69378),C=v(h(53931)),w=v(h(23327)),S=v(h(11167)),T=v(h(25469)),N=v(h(25301)),D=v(h(13043)),A=v(h(22803)),W=v(h(40587)),q=v(h(54999)),U=_interopRequireWildcard(h(28816)),Q=v(h(7221)),K=v(h(46373)),H=h(3073);function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b})(c,m)}h(70165)},61678:(c,m,h)=>{"use strict";var y=h(62688),v=h(12470).__,g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.Modal=void 0,m.default=ModalProvider;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(78304)),P=g(h(85707)),C=g(h(18821)),w=h(79397),S=g(h(47483)),T=g(h(3416)),N=g(h(76547)),D=g(h(55725)),A=g(h(63980)),W=g(h(2526));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,P.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function ModalProvider(c){var m=(0,b.useState)(c.show),h=(0,C.default)(m,2),y=h[0],v=h[1],g=function showModal(){v(!0),c.setShow&&c.setShow(!0)},_=_objectSpread(_objectSpread({},c),{},{show:y,hideModal:function hideModal(){v(!1),c.setShow&&c.setShow(!1)},showModal:g});return(0,b.useEffect)(function(){v(c.show)},[c.show]),b.default.createElement(b.default.Fragment,null,c.toggleButtonProps&&b.default.createElement(S.default,(0,E.default)({},c.toggleButtonProps,{onClick:g})),b.default.createElement(q,_,c.children))}h(97088),ModalProvider.propTypes={children:y.node.isRequired,toggleButtonProps:y.object,title:y.string,icon:y.string,show:y.bool,setShow:y.func,onOpen:y.func,onClose:y.func},ModalProvider.defaultProps={show:!1},ModalProvider.Section=A.default,ModalProvider.Tip=W.default;var q=m.Modal=function Modal(c){var m=(0,b.useRef)(null),h=(0,b.useRef)(null),y=function closeModal(y){var v=m.current,g=h.current,_=g&&g.contains(y.target);v&&v.contains(y.target)&&!_||(c.hideModal(),c.onClose&&c.onClose(y))};return(0,b.useEffect)(function(){var m;c.show&&(document.addEventListener("mousedown",y,!1),null===(m=c.onOpen)||void 0===m||m.call(c));return function(){return document.removeEventListener("mousedown",y,!1)}},[c.show]),c.show?b.default.createElement("div",{className:"eps-modal__overlay",onClick:y},b.default.createElement("div",{className:(0,w.arrayToClassName)(["eps-modal",c.className]),ref:m},b.default.createElement(T.default,{container:!0,className:"eps-modal__header",justify:"space-between",alignItems:"center"},b.default.createElement(T.default,{item:!0},b.default.createElement(N.default,{className:"eps-modal__icon ".concat(c.icon)}),b.default.createElement(D.default,{className:"title",tag:"span"},c.title)),b.default.createElement(T.default,{item:!0},b.default.createElement("div",{className:"eps-modal__close-wrapper",ref:h},b.default.createElement(S.default,{text:v("Close","elementor"),hideText:!0,icon:"eicon-close",onClick:c.closeModal})))),b.default.createElement("div",{className:"eps-modal__body"},c.children))):null};q.propTypes={className:y.string,children:y.any.isRequired,title:y.string.isRequired,icon:y.string,show:y.bool,setShow:y.func,hideModal:y.func,showModal:y.func,closeModal:y.func,onOpen:y.func,onClose:y.func},q.defaultProps={className:""}},61790:(c,m,h)=>{var y=h(53051)();c.exports=y;try{regeneratorRuntime=y}catch(c){"object"==typeof globalThis?globalThis.regeneratorRuntime=y:Function("r","regeneratorRuntime = r")(y)}},62100:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0});var y=h(81399);Object.keys(y).forEach(function(c){"default"!==c&&"__esModule"!==c&&(c in m&&m[c]===y[c]||Object.defineProperty(m,c,{enumerable:!0,get:function get(){return y[c]}}))});var v=h(59957);Object.keys(v).forEach(function(c){"default"!==c&&"__esModule"!==c&&(c in m&&m[c]===v[c]||Object.defineProperty(m,c,{enumerable:!0,get:function get(){return v[c]}}))})},62507:(c,m,h)=>{var y=h(46313);c.exports=function _regeneratorAsync(c,m,h,v,g){var _=y(c,m,h,v,g);return _.next().then(function(c){return c.done?c.value:_.next()})},c.exports.__esModule=!0,c.exports.default=c.exports},62521:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CollapseContent;var g=v(h(41594));function CollapseContent(c){return g.default.createElement("div",{className:"e-app-collapse-content"},c.children)}CollapseContent.propTypes={className:y.string,children:y.any},CollapseContent.defaultProps={className:""}},62688:(c,m,h)=>{c.exports=h(40362)()},62841:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function safeRedirect(c){try{var m=decodeURIComponent(c);if((0,v.default)(m))return window.location.href=m,!0}catch(c){return!1}};var v=y(h(94823))},62992:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardDivider;var g=v(h(41594)),_=h(79397);function CardDivider(c){var m=["eps-card__divider",c.className];return g.default.createElement("hr",{className:(0,_.arrayToClassName)(m)})}h(45302),CardDivider.propTypes={className:y.string},CardDivider.defaultProps={className:""}},63523:()=>{},63895:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Tooltip;var _=v(h(18821)),b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(79397);function Tooltip(c){var m=["e-app-tooltip",c.className],h=(0,b.useRef)(null),y=(0,b.useRef)(!1),v=Object.prototype.hasOwnProperty.call(c,"show"),g=(0,b.useState)(!1),P=(0,_.default)(g,2),C=P[0],w=P[1],S=(0,b.useState)(!1),T=(0,_.default)(S,2),N=T[0],D=T[1],A={trigger:v?"manual":"hover",gravity:{top:"s",right:"w",down:"n",left:"e"}[c.direction],offset:c.offset,title:function title(){return c.title}},W=function setTipsy(){var c=jQuery(h.current);if(c.tipsy(A),v){var m=N?"show":"hide";c.tipsy(m)}};return(0,b.useEffect)(function(){return c.disabled||(y.current=!1,import("".concat(elementorCommon.config.urls.assets,"lib/tipsy/tipsy.min.js?ver=1.0.0")).then(function(){y.current||(C?W():w(!0))})),function(){if(!c.disabled){y.current=!0;var m=document.querySelectorAll(".tipsy");if(!m.length)return;m[m.length-1].remove()}}},[c.disabled]),(0,b.useEffect)(function(){C&&W()},[C,N]),(0,b.useEffect)(function(){c.disabled||c.show===N||D(c.show)},[c.show]),b.default.createElement(c.tag,{className:(0,E.arrayToClassName)(m),ref:h},c.children)}Tooltip.propTypes={className:y.string,offset:y.number,show:y.bool,direction:y.oneOf(["top","right","left","down"]),tag:y.string.isRequired,title:y.string.isRequired,disabled:y.bool,children:y.any},Tooltip.defaultProps={className:"",offset:10,direction:"top",disabled:!1}},63980:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ModalSection;var g=v(h(41594)),_=h(79397);function ModalSection(c){return g.default.createElement("section",{className:(0,_.arrayToClassName)(["eps-modal__section",c.className])},c.children)}ModalSection.propTypes={className:y.string,children:y.any},ModalSection.defaultProps={className:""}},64095:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.AppContext=void 0,m.default=AppProvider;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(18821));var E=m.AppContext=_.default.createContext();function AppProvider(c){var m={isDarkMode:document.body.classList.contains("eps-theme-dark")},h=(0,_.useState)(m),y=(0,b.default)(h,2),v=y[0],g=y[1];return _.default.createElement(E.Provider,{value:{state:v,setState:g}},c.children)}AppProvider.propTypes={children:y.object.isRequired}},64297:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportProcess(){var c=(0,_.useContext)(P.SharedContext),m=(0,_.useContext)(C.ImportContext),h=(0,E.useNavigate)(),v=(0,_.useState)(""),g=(0,b.default)(v,2),Q=g[0],K=g[1],H=(0,_.useState)(!1),G=(0,b.default)(H,2),V=G[0],Y=G[1],J=(0,_.useState)(!1),Z=(0,b.default)(J,2),ee=Z[0],te=Z[1],ne=(0,_.useState)([]),de=(0,b.default)(ne,2),fe=de[0],pe=de[1],me=(0,q.useImportKitLibraryApplyAllPlugins)(fe),ye=(0,A.default)(),ve=ye.kitState,ge=ye.kitActions,be=ye.KIT_STATUS_MAP,Ee=(0,D.default)().getAll(),Oe=Ee.id,xe=Ee.referrer,je=Ee.file_url,ke=Ee.action_type,Te=Ee.nonce,Re=Ee.return_to,Me=c.data||{},De=Me.includes,Ae=Me.selectedCustomPostTypes,We=Me.currentPage,Le=Me.returnTo,Be=m.data||{},Qe=Be.file,Ke=Be.uploadedData,ze=Be.importedData,$e=Be.overrideConditions,et=Be.isResolvedData,tt=(0,_.useMemo)(function(){return De.some(function(c){return["templates","content"].includes(c)})},[De]),rt=(0,W.default)().navigateToMainScreen,st=function importKit(){elementorAppConfig["import-export"].isUnfilteredFilesEnabled||!tt?te(!0):Y(!0)},dt=function onCancelProcess(){m.dispatch({type:"SET_FILE",payload:null}),rt()},ht=function onReady(){Y(!1),te(!0)},vt=function eventTracking(m){var h=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"click";"kit-library"===c.data.referrer&&(0,N.appsEventTrackingDispatch)(m,{page_source:"import",step:We,modal_type:"unfiltered_file",event_type:h})};return(0,_.useEffect)(function(){xe&&c.dispatch({type:"SET_REFERRER",payload:xe}),ke&&m.dispatch({type:"SET_ACTION_TYPE",payload:ke}),Re&&c.dispatch({type:"SET_RETURN_TO",payload:Re}),je&&!Qe?function uploadKit(){var c=decodeURIComponent(je);m.dispatch({type:"SET_ID",payload:Oe}),m.dispatch({type:"SET_FILE",payload:c}),ge.upload({kitId:Oe,file:c,kitLibraryNonce:Te})}():Ke?st():h("import"),c.dispatch({type:"SET_CURRENT_PAGE_NAME",payload:ImportProcess.name})},[]),(0,_.useEffect)(function(){if(ee){var c=m.data,h=A.KIT_SOURCE_MAP.CLOUD===c.source,y=h?c.file.id:c.id,v=h?c.source:xe;ge.import({id:y,session:Ke.session,include:De,overrideConditions:$e,referrer:v,selectedCustomPostTypes:Ae})}},[ee]),(0,_.useEffect)(function(){if(be.INITIAL!==ve.status)switch(ve.status){case be.IMPORTED:m.dispatch({type:"SET_IMPORTED_DATA",payload:ve.data});break;case be.UPLOADED:m.dispatch({type:"SET_UPLOADED_DATA",payload:ve.data});break;case be.ERROR:K(ve.data)}},[ve.status]),(0,_.useEffect)(function(){if(be.INITIAL!==ve.status||et&&"apply-all"===m.data.actionType)if(ze){if(Le&&(0,U.default)(Le))return;h("/import/complete")}else if("apply-all"===m.data.actionType){var y,v;(null!==(y=ve.data)&&void 0!==y&&null!==(y=y.manifest)&&void 0!==y&&y.plugins||null!==(v=m.data.uploadedData)&&void 0!==v&&v.manifest.plugins)&&m.dispatch({type:"SET_PLUGINS_STATE",payload:"have"}),Ke.conflicts&&Object.keys(Ke.conflicts).length&&!et?h("/import/resolver"):(ge.reset(),"have"===m.data.pluginsState&&function applyAllImportPlugins(){var c,h=(null===(c=ve.data)||void 0===c||null===(c=c.manifest)||void 0===c?void 0:c.plugins)||m.data.uploadedData.manifest.plugins;pe(h)}(),""!==m.data.pluginsState&&"success"!==m.data.pluginsState||(!function applyAllSetCpt(){var h,y,v=(null===(h=ve.data)||void 0===h?void 0:h.manifest["custom-post-type-title"])||(null===(y=m.data)||void 0===y||null===(y=y.uploadedData)||void 0===y?void 0:y.manifest["custom-post-type-title"]);if(v){var g=Object.keys(v);c.dispatch({type:"SET_SELECTED_CPT",payload:g})}}(),st()))}else h("/import/plugins")},[Ke,ze,m.data.pluginsState,Le]),(0,_.useEffect)(function(){(null==me?void 0:me.length)>0&&(m.dispatch({type:"SET_PLUGINS",payload:me}),h("import/plugins-activation"))},[me]),_.default.createElement(w.default,{type:"import"},_.default.createElement("section",null,_.default.createElement(S.default,{info:Ke&&y("Importing your content, templates and site settings","elementor"),errorType:Q,onDialogApprove:dt,onDialogDismiss:dt}),_.default.createElement(T.default,{show:V,setShow:Y,confirmModalText:y("This allows Elementor to scan your SVGs for malicious content. Otherwise, you can skip any SVGs in this import.","elementor"),errorModalText:y("Nothing to worry about, just continue without importing SVGs or go back and start the import again.","elementor"),onReady:function onReady(){return ht()},onCancel:function onCancel(){Y(!1),dt()},onLoad:function onLoad(){return vt("kit-library/modal-load","load")},onClose:function onClose(){vt("kit-library/close"),ht()},onDismiss:function onDismiss(){ht(),vt("kit-library/skip")},onEnable:function onEnable(){return vt("kit-library/enable")}})))};var _=_interopRequireWildcard(h(41594)),b=v(h(18821)),E=h(83040),P=h(69378),C=h(53442),w=v(h(53931)),S=v(h(41994)),T=v(h(53441)),N=h(3073),D=v(h(41494)),A=_interopRequireWildcard(h(14300)),W=v(h(82372)),q=h(72380),U=v(h(62841));function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b})(c,m)}},64485:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=InfoModalHeading;var g=v(h(41594)),_=h(79397),b=v(h(85418));function InfoModalHeading(c){return g.default.createElement(b.default,{variant:"h3",tag:"h2",className:(0,_.arrayToClassName)(["e-app-import-export-info-modal__heading",c.className])},c.children)}InfoModalHeading.propTypes={className:y.string,children:y.oneOfType([y.string,y.object,y.arrayOf(y.object)]).isRequired},InfoModalHeading.defaultProps={className:""}},64632:()=>{},65474:c=>{c.exports=function _iterableToArrayLimit(c,m){var h=null==c?null:"undefined"!=typeof Symbol&&c[Symbol.iterator]||c["@@iterator"];if(null!=h){var y,v,g,_,b=[],E=!0,P=!1;try{if(g=(h=h.call(c)).next,0===m){if(Object(h)!==h)return;E=!1}else for(;!(E=(y=g.call(h)).done)&&(b.push(y.value),b.length!==m);E=!0);}catch(c){P=!0,v=c}finally{try{if(!E&&null!=h.return&&(_=h.return(),Object(_)!==_))return}finally{if(P)throw v}}return b}},c.exports.__esModule=!0,c.exports.default=c.exports},65731:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CenteredContent;var v=y(h(41594)),g=h(86956),_=y(h(62688)),b=120,E=180;function CenteredContent(c){var m=c.children,h=c.hasFooter,y=void 0!==h&&h,_=c.offsetHeight,P=c.maxWidth,C=void 0===P?"600px":P,w=_||(y?E:b);return v.default.createElement(g.Box,{sx:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"calc(100vh - ".concat(w,"px)"),p:3}},v.default.createElement(g.Box,{sx:{maxWidth:C,textAlign:"center",width:"100%"}},m))}CenteredContent.propTypes={children:_.default.node.isRequired,hasFooter:_.default.bool,offsetHeight:_.default.number,maxWidth:_.default.string}},65949:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var _=g(h(41594)),b=g(h(39805)),E=g(h(40989)),P=g(h(15118)),C=g(h(29402)),w=g(h(87861)),S=g(h(85707)),T=g(h(15656));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}var N=m.default=function(c){function ErrorBoundary(c){var m;return(0,b.default)(this,ErrorBoundary),(m=function _callSuper(c,m,h){return m=(0,C.default)(m),(0,P.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,C.default)(c).constructor):m.apply(c,h))}(this,ErrorBoundary,[c])).state={hasError:null},m}return(0,w.default)(ErrorBoundary,c),(0,E.default)(ErrorBoundary,[{key:"goBack",value:function goBack(){window.top!==window.self&&window.top.$e.run("app/close"),window.location=elementorAppConfig.return_url}},{key:"render",value:function render(){return this.state.hasError?_.default.createElement(T.default,{title:this.props.title,text:this.props.text,approveButtonUrl:this.props.learnMoreUrl,approveButtonColor:"link",approveButtonTarget:"_blank",approveButtonText:y("Learn More","elementor"),dismissButtonText:y("Go Back","elementor"),dismissButtonOnClick:this.goBack}):this.props.children}}],[{key:"getDerivedStateFromError",value:function getDerivedStateFromError(){return{hasError:!0}}}])}(_.default.Component);(0,S.default)(N,"propTypes",{children:v.any,title:v.string,text:v.string,learnMoreUrl:v.string}),(0,S.default)(N,"defaultProps",{title:y("App could not be loaded","elementor"),text:y("We’re sorry, but something went wrong. Click on ‘Learn more’ and follow each of the steps to quickly solve it.","elementor"),learnMoreUrl:"https://go.elementor.com/app-general-load-issue/"})},66961:(c,m,h)=>{var y=h(10564).default;c.exports=function _regeneratorValues(c){if(null!=c){var m=c["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],h=0;if(m)return m.call(c);if("function"==typeof c.next)return c;if(!isNaN(c.length))return{next:function next(){return c&&h>=c.length&&(c=void 0),{value:c&&c[h++],done:!c}}}}throw new TypeError(y(c)+" is not iterable")},c.exports.__esModule=!0,c.exports.default=c.exports},67114:c=>{c.exports=function _OverloadYield(c,m){this.v=c,this.k=m},c.exports.__esModule=!0,c.exports.default=c.exports},67153:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExportCompleteDownloadLink;var g=v(h(41594)),_=h(86956),b=v(h(62688));function ExportCompleteDownloadLink(c){var m=c.onDownloadClick;return g.default.createElement(_.Typography,{variant:"body2",color:"text.secondary","data-testid":"export-complete-download-link"},y("Is the automatic download not starting?","elementor")," ",g.default.createElement(_.Link,{href:"#",onClick:m,sx:{cursor:"pointer",textDecoration:"underline"}},y("Download manually","elementor")),". ")}ExportCompleteDownloadLink.propTypes={onDownloadClick:b.default.func.isRequired}},68102:(c,m,h)=>{"use strict";function _extends(){return _extends=Object.assign?Object.assign.bind():function(c){for(var m=1;m<arguments.length;m++){var h=arguments[m];for(var y in h)({}).hasOwnProperty.call(h,y)&&(c[y]=h[y])}return c},_extends.apply(null,arguments)}h.d(m,{A:()=>_extends})},68276:c=>{"use strict";c.exports=elementorAppPackages.hooks},68534:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportKit(){var c=(0,_.useContext)(b.ExportContext),m=(0,_.useContext)(E.SharedContext);return(0,_.useEffect)(function(){var h;c.dispatch({type:"SET_IS_EXPORT_PROCESS_STARTED",payload:!0}),m.dispatch({type:"SET_CPT",payload:(0,P.cptObjectToOptionsArray)(null===(h=elementorAppConfig["import-export"].summaryTitles.content)||void 0===h?void 0:h.customPostTypes,"plural")})},[]),_.default.createElement(C.default,{type:"export",footer:function getFooter(){return _.default.createElement(N.default,null,_.default.createElement(A.default,{variant:"contained",text:y("Next","elementor"),color:c.data.kitInfo.title?"primary":"disabled",url:c.data.kitInfo.title?"/export/plugins":""}))}()},_.default.createElement("section",{className:"e-app-export-kit"},_.default.createElement(w.default,{heading:y("Select which items to export","elementor"),description:[y("You can export the content, site settings, and templates as a Website Template to be reused in the future.","elementor"),_.default.createElement(_.default.Fragment,{key:"description-secondary-line"},y("Uncheck the items you don't want to include.","elementor")," ",function getLearnMoreLink(){return _.default.createElement(D.default,{url:"https://go.elementor.com/app-what-are-kits",italic:!0},y("Learn More","elementor"))}())]}),_.default.createElement(W.default,{container:!0,direction:"column",className:"e-app-export-kit__content"},_.default.createElement(T.default,null),_.default.createElement(S.default,{contentData:q.default}))))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(81160),E=h(69378),P=h(37628),C=v(h(53931)),w=v(h(23327)),S=v(h(76492)),T=v(h(84245)),N=v(h(91071)),D=v(h(54999)),A=v(h(47483)),W=v(h(3416)),q=v(h(37880));h(20364)},69046:(c,m,h)=>{"use strict";h.d(m,{z:()=>b});var y=h(68102),v=h(59994),g=h(74342),_=h(6622),b=function(c){function InfiniteQueryObserver(m,h){return c.call(this,m,h)||this}(0,v.A)(InfiniteQueryObserver,c);var m=InfiniteQueryObserver.prototype;return m.bindMethods=function bindMethods(){c.prototype.bindMethods.call(this),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)},m.setOptions=function setOptions(m,h){c.prototype.setOptions.call(this,(0,y.A)({},m,{behavior:(0,_.PL)()}),h)},m.getOptimisticResult=function getOptimisticResult(m){return m.behavior=(0,_.PL)(),c.prototype.getOptimisticResult.call(this,m)},m.fetchNextPage=function fetchNextPage(c){var m;return this.fetch({cancelRefetch:null==(m=null==c?void 0:c.cancelRefetch)||m,throwOnError:null==c?void 0:c.throwOnError,meta:{fetchMore:{direction:"forward",pageParam:null==c?void 0:c.pageParam}}})},m.fetchPreviousPage=function fetchPreviousPage(c){var m;return this.fetch({cancelRefetch:null==(m=null==c?void 0:c.cancelRefetch)||m,throwOnError:null==c?void 0:c.throwOnError,meta:{fetchMore:{direction:"backward",pageParam:null==c?void 0:c.pageParam}}})},m.createResult=function createResult(m,h){var v,g,b,E,P,C,w=m.state,S=c.prototype.createResult.call(this,m,h);return(0,y.A)({},S,{fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,_.rB)(h,null==(v=w.data)?void 0:v.pages),hasPreviousPage:(0,_.RQ)(h,null==(g=w.data)?void 0:g.pages),isFetchingNextPage:w.isFetching&&"forward"===(null==(b=w.fetchMeta)||null==(E=b.fetchMore)?void 0:E.direction),isFetchingPreviousPage:w.isFetching&&"backward"===(null==(P=w.fetchMeta)||null==(C=P.fetchMore)?void 0:C.direction)})},InfiniteQueryObserver}(g.$)},69378:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.SharedContext=void 0,m.default=SharedContextProvider;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(18821)),E=h(46543),P=v(h(37880));var C=m.SharedContext=_.default.createContext();function SharedContextProvider(c){var m={includes:P.default.map(function(c){return c.type}),referrer:null,customPostTypes:[],selectedCustomPostTypes:null,currentPage:null,returnTo:null},h=(0,_.useReducer)(E.reducer,m),y=(0,b.default)(h,2),v=y[0],g=y[1];return _.default.createElement(C.Provider,{value:{data:v,dispatch:g}},c.children)}SharedContextProvider.propTypes={children:y.object.isRequired}},69783:(c,m,h)=>{"use strict";var y=h(62688),v=h(12470).__,g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=GoProButton;var _=g(h(41594)),b=g(h(78304)),E=g(h(47483)),P=h(79397);function GoProButton(c){var m=["e-app-go-pro-button",c.className];return _.default.createElement(E.default,(0,b.default)({},c,{className:(0,P.arrayToClassName)(m),text:c.text}))}GoProButton.propTypes={className:y.string,text:y.string},GoProButton.defaultProps={className:"",variant:"outlined",size:"sm",color:"cta",target:"_blank",rel:"noopener noreferrer",text:v("Upgrade Now","elementor")}},70097:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CardOverlay;var g=v(h(41594));function CardOverlay(c){return g.default.createElement("div",{className:"eps-card__image-overlay ".concat(c.className)},c.children)}h(45302),CardOverlay.propTypes={className:y.string,children:y.object.isRequired},CardOverlay.defaultProps={className:""}},70165:()=>{},70569:c=>{c.exports=function _arrayWithHoles(c){if(Array.isArray(c))return c},c.exports.__esModule=!0,c.exports.default=c.exports},70963:(c,m,h)=>{"use strict";function shouldThrowError(c,m,h){return"function"==typeof m?m.apply(void 0,h):"boolean"==typeof m?m:!!c}h.d(m,{G:()=>shouldThrowError})},71251:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function Export(){return v.default.createElement(E.QueryClientProvider,{client:D},v.default.createElement(g.default,null,v.default.createElement(b.ConnectStateProvider,null,v.default.createElement(_.default,null,v.default.createElement(P.LocationProvider,{history:C.default.appHistory},v.default.createElement(P.Router,null,v.default.createElement(S.default,{path:"complete"}),v.default.createElement(T.default,{path:"plugins"}),v.default.createElement(N.default,{path:"process"}),v.default.createElement(w.default,{default:!0})))))))};var v=y(h(41594)),g=y(h(69378)),_=y(h(81160)),b=h(76673),E=h(89994),P=h(83040),C=y(h(47485)),w=y(h(68534)),S=y(h(71930)),T=y(h(80622)),N=y(h(28492)),D=new E.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}})},71308:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ImportInfoModal(c){var m=function eventTracking(c){return(0,P.appsEventTrackingDispatch)("kit-library/seek-more-info",{page_source:"import",modal_type:"info",event_type:"click",element:c})};return g.default.createElement(E.default,(0,_.default)({},c,{title:y("Import a Website Template","elementor")}),g.default.createElement(E.default.Section,null,g.default.createElement(E.default.Heading,null,y("What’s a Website Template?","elementor")),g.default.createElement(E.default.Text,null,g.default.createElement(g.default.Fragment,null,y("A Website Template is a .zip file that contains all the parts of a complete site. It’s an easy way to get a site up and running quickly.","elementor"),g.default.createElement("br",null),g.default.createElement("br",null),g.default.createElement(b.default,{url:"https://go.elementor.com/app-what-are-kits",onClick:function onClick(){return m("Learn more about website templates")}},y(" Learn more about Website Templates","elementor"))))),g.default.createElement(E.default.Section,null,g.default.createElement(E.default.Heading,null,y("How does importing work?","elementor")),g.default.createElement(E.default.Text,null,g.default.createElement(g.default.Fragment,null,y("Start by uploading the file and selecting the parts and plugins you want to apply. If there are any overlaps between the kit and your current design, you’ll be able to choose which imported parts you want to apply or ignore. Once the file is ready, the kit will be applied to your site and you’ll be able to see it live.","elementor"),g.default.createElement("br",null),g.default.createElement("br",null),g.default.createElement(b.default,{url:"https://go.elementor.com/app-import-kit",onClick:function onClick(){return m("learn more")}},y("Learn More","elementor"))))))};var g=v(h(41594)),_=v(h(78304)),b=v(h(54999)),E=v(h(6634)),P=h(3073)},71900:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Footer;var v=y(h(41594)),g=y(h(78304)),_=y(h(85707)),b=y(h(40453)),E=h(86956),P=y(h(62688)),C=["children","sx"];function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function Footer(c){var m=c.children,h=c.sx,y=void 0===h?{}:h,_=(0,b.default)(c,C);return v.default.createElement(E.Box,(0,g.default)({component:"footer",sx:_objectSpread({mt:"auto",py:2,px:3,borderTop:1,borderColor:"divider",display:"flex",justifyContent:"flex-end",alignItems:"center"},y)},_),m)}Footer.propTypes={children:P.default.node,sx:P.default.object}},71930:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportComplete(){var c,m=(0,_.useContext)(E.ExportContext),h=P.KIT_SOURCE_MAP.CLOUD===m.data.kitInfo.source,v=(0,b.useNavigate)(),g=(0,_.useRef)(null),q=function downloadFile(){if(!g.current){var c,h=document.createElement("a"),y="elementor-kit",v=((null===(c=m.data.kitInfo)||void 0===c?void 0:c.title)||y).replace(W,"").trim()||y;h.href="data:text/plain;base64,"+m.data.exportedData.file,h.download=v+".zip",g.current=h}g.current.click()};(0,_.useEffect)(function(){if(!m.data.exportedData)return v("/export");h||q()},[m.data.downloadUrl,h]);var U=(0,_.useMemo)(function(){return y(h?"Your website template is now saved to the library!":"Your .zip file is ready","elementor")},[h]),Q=(0,_.useMemo)(function(){return h?_.default.createElement(_.default.Fragment,null,y("You can find it in the My Website Templates tab.","elementor")," ",function getTakeMeThereLink(){return _.default.createElement(N.default,{url:"/kit-library/cloud",italic:!0},y("Take me there","elementor"))}()):y("Once the download is complete, you can upload it to be used for other sites.","elementor")},[h]);return _.default.createElement(C.default,{type:"export",footer:function getFooter(){return _.default.createElement(w.default,null,h?_.default.createElement(D.default,{text:y("Open library","elementor"),variant:"contained",color:"primary",url:"/kit-library/cloud"}):_.default.createElement(A.default,{text:y("Done","elementor")}))}()},_.default.createElement(S.default,{image:elementorAppConfig.assets_url+"images/go-pro.svg",heading:U,description:Q,notice:function getNotice(){return h?_.default.createElement(_.default.Fragment,null,y("Build sites faster with Website Templates.","elementor")," ",function getShowMeHowLink(){return _.default.createElement(N.default,{url:"https://go.elementor.com/app-what-are-kits",italic:!0},y("Show me how","elementor"))}()):_.default.createElement(_.default.Fragment,null,y("Is the automatic download not starting?","elementor")," ",function getDownloadLink(){return _.default.createElement(N.default,{onClick:q,italic:!0},y("Download manually","elementor"))}())}()},_.default.createElement(T.default,{data:null===(c=m.data)||void 0===c||null===(c=c.exportedData)||void 0===c?void 0:c.manifest})))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(83040),E=h(81160),P=h(14300),C=v(h(53931)),w=v(h(91071)),S=v(h(77755)),T=v(h(81920)),N=v(h(54999)),D=v(h(47483)),A=v(h(24685));h(91976);var W=/[<>:"/\\|?*]/g},72380:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.useImportKitLibraryApplyAllPlugins=function useImportKitLibraryApplyAllPlugins(c){var m=(0,g.useState)(),h=(0,v.default)(m,2),y=h[0],P=h[1],C=(0,_.default)().response,w=(0,b.default)(C.data).pluginsData,S=((0,E.default)(c,w).importPluginsData||{}).missing;return(0,g.useEffect)(function(){c&&!c.length||P(S)},[c,S]),y};var v=y(h(18821)),g=h(41594),_=y(h(28816)),b=y(h(7221)),E=y(h(46373))},72696:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var v=y(h(85707)),g=y(h(39805)),_=y(h(40989)),b=y(h(15118)),E=y(h(29402)),P=y(h(87861));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,v.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.default=function(c){function EComponent(){return(0,g.default)(this,EComponent),function _callSuper(c,m,h){return m=(0,E.default)(m),(0,b.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,E.default)(c).constructor):m.apply(c,h))}(this,EComponent,arguments)}return(0,P.default)(EComponent,c),(0,_.default)(EComponent,[{key:"getNamespace",value:function getNamespace(){return"kit-library"}},{key:"defaultCommands",value:function defaultCommands(){var c=["apply-kit","approve-import","approve-selection","back-to-library","browse","change-sort-direction","change-sort-type","change-sort-value","check","check-item","check-out-kit","checking-a-checkbox","check-kits-on-theme-forest","checkbox-filtration","collapse","choose-file","choose-site-parts-to-import","clear-filter","close","drop","enable","expand","file-upload","filter","filter-selection","favorite-icon","go-back","go-back-to-view-kits","kit-free-search","kit-is-live-load","kit-import","logo","mark-as-favorite","modal-close","modal-load","modal-open","modal-error","open-site-area","refetch","responsive-controls","see-it-live","seek-more-info","sidebar-tag-filter","skip","select-organizing-category","top-bar-change-view","uncheck","unchecking-a-checkbox","view-demo-page","view-demo-part","view-overview-page","cloud-import","cloud-delete"].reduce(function(c,m){return _objectSpread(_objectSpread({},c),{},(0,v.default)({},m,function(){}))},{});return _objectSpread({},c)}}])}($e.modules.ComponentBase)},72701:()=>{},72946:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.ExportContext=m.EXPORT_STATUS=void 0,m.ExportContextProvider=ExportContextProvider,m.useExportContext=function useExportContext(){var c=(0,g.useContext)(C);if(!c)throw new Error("useExportContext must be used within an ExportContextProvider");return c};var g=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),_=y(h(18821)),b=y(h(10906)),E=y(h(85707)),P=y(h(62688));function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,E.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}var C=m.ExportContext=(0,g.createContext)(),w=m.EXPORT_STATUS={PENDING:"PENDING",EXPORTING:"EXPORTING",COMPLETED:"COMPLETED"},S={downloadUrl:"",exportedData:null,exportStatus:w.PENDING,plugins:[],includes:["content","templates","settings","plugins"],kitInfo:{title:null,description:null,source:null}};function exportReducer(c,m){var h=m.type,y=m.payload;switch(h){case"SET_DOWNLOAD_URL":return _objectSpread(_objectSpread({},c),{},{downloadUrl:y});case"SET_EXPORTED_DATA":return _objectSpread(_objectSpread({},c),{},{exportedData:y});case"SET_PLUGINS":return _objectSpread(_objectSpread({},c),{},{plugins:y});case"SET_EXPORT_STATUS":return _objectSpread(_objectSpread({},c),{},{exportStatus:y});case"SET_KIT_TITLE":return _objectSpread(_objectSpread({},c),{},{kitInfo:_objectSpread(_objectSpread({},c.kitInfo),{},{title:y})});case"SET_KIT_DESCRIPTION":return _objectSpread(_objectSpread({},c),{},{kitInfo:_objectSpread(_objectSpread({},c.kitInfo),{},{description:y})});case"SET_KIT_SAVE_SOURCE":return _objectSpread(_objectSpread({},c),{},{kitInfo:_objectSpread(_objectSpread({},c.kitInfo),{},{source:y})});case"ADD_INCLUDE":return _objectSpread(_objectSpread({},c),{},{includes:c.includes.includes(y)?c.includes:[].concat((0,b.default)(c.includes),[y])});case"REMOVE_INCLUDE":return _objectSpread(_objectSpread({},c),{},{includes:c.includes.filter(function(c){return c!==y})});default:return c}}function ExportContextProvider(c){var m,h=c.children,y=(0,g.useReducer)(exportReducer,S),v=(0,_.default)(y,2),b=v[0],E={data:b,dispatch:v[1],isTemplateNameValid:((null===(m=b.kitInfo.title)||void 0===m?void 0:m.trim())||"").length>0,isExporting:b.exportStatus===w.EXPORTING,isCompleted:b.exportStatus===w.COMPLETED,isPending:b.exportStatus===w.PENDING};return g.default.createElement(C.Provider,{value:E},h)}ExportContextProvider.propTypes={children:P.default.node.isRequired}},73139:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=y(h(39805)),_=y(h(40989)),b=y(h(15118)),E=y(h(29402)),P=y(h(87861)),C=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(16746));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.default=function(c){function Component(){return(0,g.default)(this,Component),function _callSuper(c,m,h){return m=(0,E.default)(m),(0,b.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,E.default)(c).constructor):m.apply(c,h))}(this,Component,arguments)}return(0,P.default)(Component,c),(0,_.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"kit-taxonomies"}},{key:"defaultData",value:function defaultData(){return this.importCommands(C)}}])}($e.modules.ComponentBase)},73142:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.useExportKit=void 0;var v=y(h(61790)),g=y(h(58155)),_=y(h(18821)),b=h(41594),E=h(82580),P=h(72946),C="processing",w="error";m.useExportKit=function useExportKit(c){var m=c.includes,h=c.kitInfo,y=c.plugins,S=c.isExporting,T=c.dispatch,N=(0,b.useState)(C),D=(0,_.default)(N,2),A=D[0],W=D[1],q=(0,b.useCallback)((0,g.default)(v.default.mark(function _callee(){var c,g,_,b,S,N,D,A,q,U,Q,K,H,G,V,Y,J,Z,ee;return v.default.wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(v.prev=0,W(C),S={include:m,kitInfo:{title:(null===(c=h.title)||void 0===c?void 0:c.trim())||null,description:(null===(g=h.description)||void 0===g?void 0:g.trim())||null,source:h.source},plugins:y||[],selectedCustomPostTypes:[]},N=null===(_=elementorCommon)||void 0===_||null===(_=_.config)||void 0===_||null===(_=_.experimentalFeatures)||void 0===_?void 0:_["cloud-library"],D="cloud"===h.source,!N||!D){v.next=2;break}return v.next=1,(0,E.generateScreenshot)();case 1:A=v.sent,S.screenShotBlob=A;case 2:return q=elementorAppConfig["import-export-customization"].restApiBaseUrl,U="".concat(q,"/export"),v.next=3,fetch(U,{method:"POST",headers:{"Content-Type":"application/json","X-WP-Nonce":(null===(b=window.wpApiSettings)||void 0===b?void 0:b.nonce)||""},body:JSON.stringify(S)});case 3:return Q=v.sent,v.next=4,Q.json();case 4:if(K=v.sent,Q.ok){v.next=5;break}throw V=(null==K||null===(H=K.data)||void 0===H?void 0:H.message)||"HTTP error! with the following code: ".concat(null==K||null===(G=K.data)||void 0===G?void 0:G.code),new Error(V);case 5:if(Y="file"===h.source&&K.data&&K.data.file,J="cloud"===h.source&&K.data&&K.data.kit,!Y){v.next=6;break}Z={file:K.data.file,manifest:K.data.manifest},T({type:"SET_EXPORTED_DATA",payload:Z}),v.next=8;break;case 6:if(!J){v.next=7;break}ee={kit:K.data.kit},T({type:"SET_EXPORTED_DATA",payload:ee}),v.next=8;break;case 7:throw new Error("Invalid response format from server");case 8:T({type:"SET_EXPORT_STATUS",payload:P.EXPORT_STATUS.COMPLETED}),window.location.href=elementorAppConfig.base_url+"#/export-customization/complete",v.next=10;break;case 9:v.prev=9,v.catch(0),W(w);case 10:case"end":return v.stop()}},_callee,null,[[0,9]])})),[m,h,y,T]);return(0,b.useEffect)(function(){S&&q()},[S,q]),{status:A,STATUS_PROCESSING:C,STATUS_ERROR:w}}},73148:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportKitFooter(){var c=(0,_.useRef)(),m=(0,P.default)(),h=m.isConnected,v=m.isConnecting,g=m.setConnecting,w=m.handleConnectSuccess,S=m.handleConnectError,T=(0,C.useExportContext)(),N=T.dispatch,D=T.isTemplateNameValid,A=(0,E.default)({enabled:h}),W=A.data,q=A.isLoading,U=A.refetch,Q=(null==W?void 0:W.is_eligible)||!1;(0,_.useEffect)(function(){c.current&&jQuery(c.current).elementorConnect({popup:{width:600,height:700},success:function success(){w(),g(!0),U()},error:function error(){S()}})},[w,S,g,U]),(0,_.useEffect)(function(){v&&!q&&(Q?(N({type:"SET_KIT_SAVE_SOURCE",payload:"cloud"}),N({type:"SET_EXPORT_STATUS",payload:C.EXPORT_STATUS.EXPORTING}),window.location.href=elementorAppConfig.base_url+"#/export-customization/process"):window.location.href=elementorAppConfig.base_url+"#/kit-library/cloud")},[v,q,Q,N]),(0,_.useEffect)(function(){v&&!q&&g(!1)},[v,q,g]);var K=function handleUpgradeClick(){window.location.href=elementorAppConfig.base_url+"#/kit-library/cloud"},H=function handleUploadClick(){N({type:"SET_KIT_SAVE_SOURCE",payload:"cloud"}),N({type:"SET_EXPORT_STATUS",payload:C.EXPORT_STATUS.EXPORTING}),window.location.href=elementorAppConfig.base_url+"#/export-customization/process"};return _.default.createElement(b.Stack,{direction:"row",spacing:1},function renderSaveToLibraryButton(){var m;return h?v||q?_.default.createElement(b.Button,{variant:"outlined",color:"secondary",size:"small",disabled:!0,startIcon:_.default.createElement(b.CircularProgress,{size:16}),"data-testid":"export-kit-footer-save-to-library-button"},y("Save to library","elementor")):Q?_.default.createElement(b.Button,{variant:"outlined",color:"secondary",size:"small",disabled:!D,onClick:H,"data-testid":"export-kit-footer-save-to-library-button"},y("Save to library","elementor")):_.default.createElement(b.Button,{variant:"outlined",color:"secondary",size:"small",disabled:!D,onClick:K,"data-testid":"export-kit-footer-save-to-library-button"},y("Save to library","elementor")):_.default.createElement(b.Button,{ref:c,variant:"outlined",color:"secondary",size:"small",disabled:!D,href:(null===(m=elementorAppConfig)||void 0===m||null===(m=m["cloud-library"])||void 0===m||null===(m=m.library_connect_url)||void 0===m?void 0:m.replace(/&#038;/g,"&"))||"#","data-testid":"export-kit-footer-save-to-library-button"},y("Save to library","elementor"))}(),_.default.createElement(b.Button,{variant:"contained",color:"primary",size:"small",disabled:!D,onClick:function handleExportAsZip(){N({type:"SET_KIT_SAVE_SOURCE",payload:"file"}),N({type:"SET_EXPORT_STATUS",payload:C.EXPORT_STATUS.EXPORTING}),window.location.href=elementorAppConfig.base_url+"#/export-customization/process"},"data-testid":"export-kit-footer-export-zip-button"},y("Export as .zip","elementor")))};var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(86956),E=v(h(94026)),P=v(h(84644)),C=h(72946)},73157:()=>{},73587:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TableBody;var g=v(h(41594)),_=h(79397);function TableBody(c){return g.default.createElement("tbody",{className:(0,_.arrayToClassName)(["eps-table__body",c.className])},c.children)}TableBody.propTypes={children:y.any.isRequired,className:y.string}},73802:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function CptSelectBox(){var c=(0,b.useContext)(E.SharedContext),m=(c.data||[]).customPostTypes,h=(0,b.useState)([]),v=(0,_.default)(h,2),g=v[0],S=v[1];(0,b.useEffect)(function(){S(T(m))},[m]),(0,b.useEffect)(function(){c.dispatch({type:"SET_SELECTED_CPT",payload:g})},[g]);var T=function arrayValueIterator(c){return c.map(function(c){return c.value})};return b.default.createElement(b.default.Fragment,null,b.default.createElement(C.default,{variant:"sm",tag:"p",className:"e-app-export-kit-content__description"},y("Custom Post Type","elementor")),m.length>0?b.default.createElement(P.default,{multiple:!0,settings:{width:"100%"},options:m,onChange:function onChange(c){return function selectedCpt(c){S(T(Array.from(c)))}(c.target.selectedOptions)},value:g,placeholder:y("Click to select custom post types","elementor")}):b.default.createElement(w.default,{variant:"outlined",placeholder:y("No custom post types in your site...","elementor"),className:"e-app-export-kit-content__disabled"}),b.default.createElement(C.default,{variant:"sm",tag:"span",className:"e-app-export-kit-content__small-notice"},y("Add the custom posts types to export. The latest 20 items from each type will be included.","elementor")))};var _=v(h(18821)),b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(69378),P=v(h(12505)),C=v(h(55725)),w=v(h(79788))},73921:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useAjax(){var c=(0,E.useState)(null),m=(0,b.default)(c,2),h=m[0],y=m[1],g="initial",P={status:g,isComplete:!1,response:null},C=(0,E.useState)(P),w=(0,b.default)(C,2),S=w[0],T=w[1],N={reset:function reset(){return T(g)}},D=function(){var c=(0,_.default)(v.default.mark(function _callee(c){return v.default.wrap(function(m){for(;;)switch(m.prev=m.next){case 0:return m.abrupt("return",new Promise(function(m,h){var y=new FormData;if(c.data){for(var v in c.data)y.append(v,c.data[v]);c.data.nonce||y.append("_nonce",elementorCommon.config.ajax.nonce)}var g=_objectSpread(_objectSpread({type:"post",url:elementorCommon.config.ajax.url,headers:{},cache:!1,contentType:!1,processData:!1},c),{},{data:y,success:function success(c){m(c)},error:function error(c){h(c)}});jQuery.ajax(g)}));case 1:case"end":return m.stop()}},_callee)}));return function runRequest(m){return c.apply(this,arguments)}}();return(0,E.useEffect)(function(){h&&D(h).then(function(c){var m=c.success?"success":"error";T(function(h){return _objectSpread(_objectSpread({},h),{},{status:m,response:null==c?void 0:c.data})})}).catch(function(c){var m,h=408===c.status?"timeout":null===(m=c.responseJSON)||void 0===m?void 0:m.data;T(function(c){return _objectSpread(_objectSpread({},c),{},{status:"error",response:h})})}).finally(function(){T(function(c){return _objectSpread(_objectSpread({},c),{},{isComplete:!0})})})},[h]),{ajax:h,setAjax:y,ajaxState:S,ajaxActions:N,runRequest:D}};var v=y(h(61790)),g=y(h(85707)),_=y(h(58155)),b=y(h(18821)),E=h(41594);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,g.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}},74077:()=>{},74342:(c,m,h)=>{"use strict";h.d(m,{$:()=>w});var y=h(68102),v=h(59994),g=h(6369),_=h(25800),b=h(91669),E=h(13411),P=h(8118),C=h(81133),w=function(c){function QueryObserver(m,h){var y;return(y=c.call(this)||this).client=m,y.options=h,y.trackedProps=[],y.selectError=null,y.bindMethods(),y.setOptions(h),y}(0,v.A)(QueryObserver,c);var m=QueryObserver.prototype;return m.bindMethods=function bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},m.onSubscribe=function onSubscribe(){1===this.listeners.length&&(this.currentQuery.addObserver(this),shouldFetchOnMount(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},m.onUnsubscribe=function onUnsubscribe(){this.listeners.length||this.destroy()},m.shouldFetchOnReconnect=function shouldFetchOnReconnect(){return shouldFetchOn(this.currentQuery,this.options,this.options.refetchOnReconnect)},m.shouldFetchOnWindowFocus=function shouldFetchOnWindowFocus(){return shouldFetchOn(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},m.destroy=function destroy(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},m.setOptions=function setOptions(c,m){var h=this.options,y=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(c),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=h.queryKey),this.updateQuery();var v=this.hasListeners();v&&shouldFetchOptionally(this.currentQuery,y,this.options,h)&&this.executeFetch(),this.updateResult(m),!v||this.currentQuery===y&&this.options.enabled===h.enabled&&this.options.staleTime===h.staleTime||this.updateStaleTimeout();var g=this.computeRefetchInterval();!v||this.currentQuery===y&&this.options.enabled===h.enabled&&g===this.currentRefetchInterval||this.updateRefetchInterval(g)},m.getOptimisticResult=function getOptimisticResult(c){var m=this.client.defaultQueryObserverOptions(c),h=this.client.getQueryCache().build(this.client,m);return this.createResult(h,m)},m.getCurrentResult=function getCurrentResult(){return this.currentResult},m.trackResult=function trackResult(c,m){var h=this,y={},v=function trackProp(c){h.trackedProps.includes(c)||h.trackedProps.push(c)};return Object.keys(c).forEach(function(m){Object.defineProperty(y,m,{configurable:!1,enumerable:!0,get:function get(){return v(m),c[m]}})}),(m.useErrorBoundary||m.suspense)&&v("error"),y},m.getNextResult=function getNextResult(c){var m=this;return new Promise(function(h,y){var v=m.subscribe(function(m){m.isFetching||(v(),m.isError&&(null==c?void 0:c.throwOnError)?y(m.error):h(m))})})},m.getCurrentQuery=function getCurrentQuery(){return this.currentQuery},m.remove=function remove(){this.client.getQueryCache().remove(this.currentQuery)},m.refetch=function refetch(c){return this.fetch((0,y.A)({},c,{meta:{refetchPage:null==c?void 0:c.refetchPage}}))},m.fetchOptimistic=function fetchOptimistic(c){var m=this,h=this.client.defaultQueryObserverOptions(c),y=this.client.getQueryCache().build(this.client,h);return y.fetch().then(function(){return m.createResult(y,h)})},m.fetch=function fetch(c){var m=this;return this.executeFetch(c).then(function(){return m.updateResult(),m.currentResult})},m.executeFetch=function executeFetch(c){this.updateQuery();var m=this.currentQuery.fetch(this.options,c);return(null==c?void 0:c.throwOnError)||(m=m.catch(g.lQ)),m},m.updateStaleTimeout=function updateStaleTimeout(){var c=this;if(this.clearStaleTimeout(),!g.S$&&!this.currentResult.isStale&&(0,g.gn)(this.options.staleTime)){var m=(0,g.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout(function(){c.currentResult.isStale||c.updateResult()},m)}},m.computeRefetchInterval=function computeRefetchInterval(){var c;return"function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(c=this.options.refetchInterval)&&c},m.updateRefetchInterval=function updateRefetchInterval(c){var m=this;this.clearRefetchInterval(),this.currentRefetchInterval=c,!g.S$&&!1!==this.options.enabled&&(0,g.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval(function(){(m.options.refetchIntervalInBackground||b.m.isFocused())&&m.executeFetch()},this.currentRefetchInterval))},m.updateTimers=function updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},m.clearTimers=function clearTimers(){this.clearStaleTimeout(),this.clearRefetchInterval()},m.clearStaleTimeout=function clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},m.clearRefetchInterval=function clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},m.createResult=function createResult(c,m){var h,y=this.currentQuery,v=this.options,_=this.currentResult,b=this.currentResultState,E=this.currentResultOptions,C=c!==y,w=C?c.state:this.currentQueryInitialState,S=C?this.currentResult:this.previousQueryResult,T=c.state,N=T.dataUpdatedAt,D=T.error,A=T.errorUpdatedAt,W=T.isFetching,q=T.status,U=!1,Q=!1;if(m.optimisticResults){var K=this.hasListeners(),H=!K&&shouldFetchOnMount(c,m),G=K&&shouldFetchOptionally(c,y,m,v);(H||G)&&(W=!0,N||(q="loading"))}if(m.keepPreviousData&&!T.dataUpdateCount&&(null==S?void 0:S.isSuccess)&&"error"!==q)h=S.data,N=S.dataUpdatedAt,q=S.status,U=!0;else if(m.select&&void 0!==T.data)if(_&&T.data===(null==b?void 0:b.data)&&m.select===this.selectFn)h=this.selectResult;else try{this.selectFn=m.select,h=m.select(T.data),!1!==m.structuralSharing&&(h=(0,g.BH)(null==_?void 0:_.data,h)),this.selectResult=h,this.selectError=null}catch(c){(0,P.t)().error(c),this.selectError=c}else h=T.data;if(void 0!==m.placeholderData&&void 0===h&&("loading"===q||"idle"===q)){var V;if((null==_?void 0:_.isPlaceholderData)&&m.placeholderData===(null==E?void 0:E.placeholderData))V=_.data;else if(V="function"==typeof m.placeholderData?m.placeholderData():m.placeholderData,m.select&&void 0!==V)try{V=m.select(V),!1!==m.structuralSharing&&(V=(0,g.BH)(null==_?void 0:_.data,V)),this.selectError=null}catch(c){(0,P.t)().error(c),this.selectError=c}void 0!==V&&(q="success",h=V,Q=!0)}return this.selectError&&(D=this.selectError,h=this.selectResult,A=Date.now(),q="error"),{status:q,isLoading:"loading"===q,isSuccess:"success"===q,isError:"error"===q,isIdle:"idle"===q,data:h,dataUpdatedAt:N,error:D,errorUpdatedAt:A,failureCount:T.fetchFailureCount,errorUpdateCount:T.errorUpdateCount,isFetched:T.dataUpdateCount>0||T.errorUpdateCount>0,isFetchedAfterMount:T.dataUpdateCount>w.dataUpdateCount||T.errorUpdateCount>w.errorUpdateCount,isFetching:W,isRefetching:W&&"loading"!==q,isLoadingError:"error"===q&&0===T.dataUpdatedAt,isPlaceholderData:Q,isPreviousData:U,isRefetchError:"error"===q&&0!==T.dataUpdatedAt,isStale:isStale(c,m),refetch:this.refetch,remove:this.remove}},m.shouldNotifyListeners=function shouldNotifyListeners(c,m){if(!m)return!0;var h=this.options,y=h.notifyOnChangeProps,v=h.notifyOnChangePropsExclusions;if(!y&&!v)return!0;if("tracked"===y&&!this.trackedProps.length)return!0;var g="tracked"===y?this.trackedProps:y;return Object.keys(c).some(function(h){var y=h,_=c[y]!==m[y],b=null==g?void 0:g.some(function(c){return c===h}),E=null==v?void 0:v.some(function(c){return c===h});return _&&!E&&(!g||b)})},m.updateResult=function updateResult(c){var m=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,g.f8)(this.currentResult,m)){var h={cache:!0};!1!==(null==c?void 0:c.listeners)&&this.shouldNotifyListeners(this.currentResult,m)&&(h.listeners=!0),this.notify((0,y.A)({},h,c))}},m.updateQuery=function updateQuery(){var c=this.client.getQueryCache().build(this.client,this.options);if(c!==this.currentQuery){var m=this.currentQuery;this.currentQuery=c,this.currentQueryInitialState=c.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==m||m.removeObserver(this),c.addObserver(this))}},m.onQueryUpdate=function onQueryUpdate(c){var m={};"success"===c.type?m.onSuccess=!0:"error"!==c.type||(0,C.wm)(c.error)||(m.onError=!0),this.updateResult(m),this.hasListeners()&&this.updateTimers()},m.notify=function notify(c){var m=this;_.j.batch(function(){c.onSuccess?(null==m.options.onSuccess||m.options.onSuccess(m.currentResult.data),null==m.options.onSettled||m.options.onSettled(m.currentResult.data,null)):c.onError&&(null==m.options.onError||m.options.onError(m.currentResult.error),null==m.options.onSettled||m.options.onSettled(void 0,m.currentResult.error)),c.listeners&&m.listeners.forEach(function(c){c(m.currentResult)}),c.cache&&m.client.getQueryCache().notify({query:m.currentQuery,type:"observerResultsUpdated"})})},QueryObserver}(E.Q);function shouldFetchOnMount(c,m){return function shouldLoadOnMount(c,m){return!(!1===m.enabled||c.state.dataUpdatedAt||"error"===c.state.status&&!1===m.retryOnMount)}(c,m)||c.state.dataUpdatedAt>0&&shouldFetchOn(c,m,m.refetchOnMount)}function shouldFetchOn(c,m,h){if(!1!==m.enabled){var y="function"==typeof h?h(c):h;return"always"===y||!1!==y&&isStale(c,m)}return!1}function shouldFetchOptionally(c,m,h,y){return!1!==h.enabled&&(c!==m||!1===y.enabled)&&(!h.suspense||"error"!==c.state.status)&&isStale(c,h)}function isStale(c,m){return c.isStaleByTime(m.staleTime)}},74644:()=>{},75001:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var v=y(h(40989)),g=y(h(39805)),_=y(h(85707)),b=y(h(47485)),E=y(h(93696));function _createForOfIteratorHelper(c,m){var h="undefined"!=typeof Symbol&&c[Symbol.iterator]||c["@@iterator"];if(!h){if(Array.isArray(c)||(h=function _unsupportedIterableToArray(c,m){if(c){if("string"==typeof c)return _arrayLikeToArray(c,m);var h={}.toString.call(c).slice(8,-1);return"Object"===h&&c.constructor&&(h=c.constructor.name),"Map"===h||"Set"===h?Array.from(c):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray(c,m):void 0}}(c))||m&&c&&"number"==typeof c.length){h&&(c=h);var y=0,v=function F(){};return{s:v,n:function n(){return y>=c.length?{done:!0}:{done:!1,value:c[y++]}},e:function e(c){throw c},f:v}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var g,_=!0,b=!1;return{s:function s(){h=h.call(c)},n:function n(){var c=h.next();return _=c.done,c},e:function e(c){b=!0,g=c},f:function f(){try{_||null==h.return||h.return()}finally{if(b)throw g}}}}function _arrayLikeToArray(c,m){(null==m||m>c.length)&&(m=c.length);for(var h=0,y=Array(m);h<m;h++)y[h]=c[h];return y}m.default=(0,v.default)(function ImportExportCustomization(){(0,g.default)(this,ImportExportCustomization),(0,_.default)(this,"routes",[{path:"/export-customization/*",component:E.default}]);var c,m=_createForOfIteratorHelper(this.routes);try{for(m.s();!(c=m.n()).done;){var h=c.value;b.default.addRoute(h)}}catch(c){m.e(c)}finally{m.f()}})},75206:c=>{"use strict";c.exports=ReactDOM},76182:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ContentLayout;var g=v(h(41594));function ContentLayout(c){return g.default.createElement("div",{className:"e-app-import-export-content-layout"},g.default.createElement("div",{className:"e-app-import-export-content-layout__container"},c.children))}h(5912),ContentLayout.propTypes={children:y.any.isRequired}},76492:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=KitContent;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(85707)),E=v(h(18821)),P=v(h(31846)),C=v(h(79092)),w=v(h(73802)),S=v(h(69783)),T=v(h(21689)),N=v(h(93279)),D=v(h(85418)),A=v(h(55725)),W=v(h(3416)),q=h(3073),U=h(69378);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,b.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function KitContent(c){var m=c.contentData,h=c.hasPro,y=(0,_.useState)({}),v=(0,E.default)(y,2),g=v[0],Q=v[1],K=(0,_.useContext)(U.SharedContext).data,H=K.referrer,G=K.currentPage,V=h||elementorAppConfig.hasPro,Y=function setContainerHoverState(c,m){Q(function(h){return _objectSpread(_objectSpread({},h),{},(0,b.default)({},c,m))})};return m.length?_.default.createElement(T.default,null,_.default.createElement(N.default,{separated:!0,className:"e-app-export-kit-content"},m.map(function(c,m){var h,y=c.type,v=c.data,b=(null===(h=v.features)||void 0===h?void 0:h.locked)&&!V;return _.default.createElement(N.default.Item,{padding:"20",key:y,className:"e-app-export-kit-content__item"},_.default.createElement("div",{onMouseEnter:function onMouseEnter(){return b&&Y(m,!0)},onMouseLeave:function onMouseLeave(){return b&&Y(m,!1)}},_.default.createElement(W.default,{container:!0,noWrap:!0},_.default.createElement(C.default,{type:y,className:"e-app-export-kit-content__checkbox",onCheck:function onCheck(c,m){!function eventTracking(c,m){if("kit-library"===H){var h=c.target.checked&&c.target.checked?"check":"uncheck";(0,q.appsEventTrackingDispatch)("kit-library/".concat(h),{page_source:"import",step:G,event_type:"click",site_part:m})}}(c,m)}}),_.default.createElement(W.default,{item:!0,container:!0},_.default.createElement(D.default,{variant:"h4",tag:"h3",className:"e-app-export-kit-content__title"},v.title),_.default.createElement(W.default,{item:!0,container:!0,direction:b?"row":"column",alignItems:"baseline"},_.default.createElement(A.default,{variant:"sm",tag:"p",className:"e-app-export-kit-content__description"},v.description||function getTemplateFeatures(c,m){if(c)return _.default.createElement(P.default,{features:c,isLocked:!V,showTooltip:g[m]})}(v.features,m)),"content"===y&&_.default.createElement(w.default,null),b&&_.default.createElement(S.default,{className:"e-app-export-kit-content__go-pro-button",url:"https://go.elementor.com/go-pro-import-export"}))))))}))):null}h(18738),KitContent.propTypes={className:y.string,contentData:y.array.isRequired,hasPro:y.bool},KitContent.defaultProps={className:""}},76547:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Icon;var g=v(h(41594));function Icon(c){return g.default.createElement("i",{className:"eps-icon ".concat(c.className)})}Icon.propTypes={className:y.string.isRequired},Icon.defaultProps={className:""}},76673:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.ConnectStateContext=void 0,m.ConnectStateProvider=ConnectStateProvider;var g=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),_=y(h(18821)),b=y(h(62688));var E=m.ConnectStateContext=(0,g.createContext)();function ConnectStateProvider(c){var m=c.children,h=(0,g.useState)(elementorCommon.config.library_connect.is_connected),y=(0,_.default)(h,2),v=y[0],b=y[1],P=(0,g.useState)(!1),C=(0,_.default)(P,2),w=C[0],S=C[1],T=(0,g.useCallback)(function(c){S(!0),b(!0),elementorCommon.config.library_connect.is_connected=!0,c&&c()},[]),N=(0,g.useCallback)(function(c){b(!1),S(!1),elementorCommon.config.library_connect.is_connected=!1,c&&c()},[]),D={isConnected:v,isConnecting:w,setConnecting:(0,g.useCallback)(function(c){S(c)},[]),handleConnectSuccess:T,handleConnectError:N};return g.default.createElement(E.Provider,{value:D},m)}ConnectStateProvider.propTypes={children:b.default.node.isRequired}},76783:()=>{},77755:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=WizardStep;var g=v(h(41594)),_=h(79397),b=v(h(3416)),E=v(h(76547)),P=v(h(85418)),C=v(h(55725));function WizardStep(c){var m=["e-app-import-export-wizard-step",c.className];return g.default.createElement(b.default,{className:(0,_.arrayToClassName)(m),justify:"center",container:!0},g.default.createElement(b.default,{item:!0},(c.image||c.icon)&&g.default.createElement(b.default,{className:"e-app-import-export-wizard-step__media-container",justify:"center",alignItems:"end",container:!0},c.image&&g.default.createElement("img",{className:"e-app-import-export-wizard-step__image",src:c.image}),c.icon&&g.default.createElement(E.default,{className:"e-app-import-export-wizard-step__icon ".concat(c.icon)})),c.heading&&g.default.createElement(P.default,{variant:"display-3",className:"e-app-import-export-wizard-step__heading"},c.heading),c.description&&g.default.createElement(C.default,{variant:"xl",className:"e-app-import-export-wizard-step__description"},c.description),c.info&&g.default.createElement(C.default,{variant:"xl",className:"e-app-import-export-wizard-step__info"},c.info),c.children&&g.default.createElement(b.default,{item:!0,className:"e-app-import-export-wizard-step__content"},c.children),c.notice&&g.default.createElement(C.default,{variant:"xs",className:"e-app-import-export-wizard-step__notice"},c.notice)))}h(74077),WizardStep.propTypes={className:y.string,image:y.string,icon:y.string,heading:y.string,description:y.oneOfType([y.string,y.object]),info:y.oneOfType([y.string,y.object]),notice:y.oneOfType([y.string,y.object]),children:y.any},WizardStep.defaultProps={className:""}},77879:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.CollapseContext=void 0;var v=y(h(41594));m.CollapseContext=v.default.createContext()},78103:()=>{},78113:c=>{c.exports=function _arrayLikeToArray(c,m){(null==m||m>c.length)&&(m=c.length);for(var h=0,y=Array(m);h<m;h++)y[h]=c[h];return y},c.exports.__esModule=!0,c.exports.default=c.exports},78179:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PanelBody;var g=v(h(41594)),_=h(79397),b=v(h(35676)),E=v(h(28929));function PanelBody(c){return g.default.createElement(E.default.Content,null,g.default.createElement(b.default.Body,{padding:c.padding,className:(0,_.arrayToClassName)(["eps-panel__body",c.className])},c.children))}PanelBody.propTypes={className:y.string,padding:y.string,children:y.any.isRequired},PanelBody.defaultProps={className:"",padding:"0"}},78304:c=>{function _extends(){return c.exports=_extends=Object.assign?Object.assign.bind():function(c){for(var m=1;m<arguments.length;m++){var h=arguments[m];for(var y in h)({}).hasOwnProperty.call(h,y)&&(c[y]=h[y])}return c},c.exports.__esModule=!0,c.exports.default=c.exports,_extends.apply(null,arguments)}c.exports=_extends,c.exports.__esModule=!0,c.exports.default=c.exports},78687:c=>{c.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},c.exports.__esModule=!0,c.exports.default=c.exports},79092:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=KitContentCheckbox;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=h(69378),E=v(h(47579));function KitContentCheckbox(c){var m=(0,_.useContext)(b.SharedContext),h=function isSelected(){return m.data.includes.includes(c.type)},y=function setIncludes(h){var y,v=h.target.checked?"ADD_INCLUDE":"REMOVE_INCLUDE";null===(y=c.onCheck)||void 0===y||y.call(c,h,c.type),m.dispatch({type:v,payload:c.type})};return(0,_.useEffect)(function(){m.data.includes.length||m.dispatch({type:"ADD_INCLUDE",payload:c.type})},[]),(0,_.useMemo)(function(){return _.default.createElement(E.default,{checked:h(),onChange:y,className:c.className})},[m.data.includes])}KitContentCheckbox.propTypes={className:y.string,type:y.string.isRequired},KitContentCheckbox.defaultProps={className:""}},79238:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PluginStatusItem;var g=v(h(41594)),_=v(h(3416)),b=v(h(47579)),E=v(h(55725)),P=h(28816),C=P.PLUGIN_STATUS_MAP.ACTIVE,w=P.PLUGIN_STATUS_MAP.INACTIVE,S=P.PLUGIN_STATUS_MAP.NOT_INSTALLED;function PluginStatusItem(c){var m=c.name,h=c.status;return S===h?null:(w===h?h="installed":C===h&&(h="activated"),g.default.createElement(_.default,{container:!0,alignItems:"center",key:m},g.default.createElement(b.default,{rounded:!0,checked:!0,error:"failed"===h||null,onChange:function onChange(){}}),g.default.createElement(E.default,{tag:"span",variant:"xs",className:"e-app-import-plugins-activation__plugin-name"},m+" "+h)))}PluginStatusItem.propTypes={name:y.string.isRequired,status:y.string.isRequired}},79281:()=>{},79397:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.stringToRemValues=m.rgbToHex=m.pxToRem=m.isOneOf=m.arrayToObjectByKey=m.arrayToClassName=void 0;var v=y(h(10564)),g=m.pxToRem=function pxToRem(c){if(c)return"string"!=typeof c&&(c=c.toString()),c.split(" ").map(function(c){return"".concat(.0625*c,"rem")}).join(" ")};m.arrayToClassName=function arrayToClassName(c,m){return c.filter(function(c){return"object"===(0,v.default)(c)?Object.entries(c)[0][1]:c}).map(function(c){var h="object"===(0,v.default)(c)?Object.entries(c)[0][0]:c;return m?m(h):h}).join(" ")},m.stringToRemValues=function stringToRemValues(c){return c.split(" ").map(function(c){return g(c)}).join(" ")},m.rgbToHex=function rgbToHex(c,m,h){return"#"+[c,m,h].map(function(c){var m=c.toString(16);return 1===m.length?"0"+m:m}).join("")},m.isOneOf=function isOneOf(c,m){return m.some(function(m){return c.includes(m)})},m.arrayToObjectByKey=function arrayToObjectByKey(c,m){var h={};return c.forEach(function(c){return h[c[m]]=c}),h}},79514:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=CollapseToggle;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(85707)),E=h(79397),P=h(77879);function CollapseToggle(c){var m=(0,_.useContext)(P.CollapseContext),h={"--e-app-collapse-toggle-icon-spacing":(0,E.pxToRem)(c.iconSpacing)},y="e-app-collapse-toggle",v=[y,(0,b.default)({},y+"--active",c.active)],g={style:h,className:(0,E.arrayToClassName)(v)};return c.active&&(g.onClick=function(){return m.toggle()}),_.default.createElement("div",g,c.children,c.active&&c.showIcon&&_.default.createElement("i",{className:"eicon-caret-down e-app-collapse-toggle__icon"}))}CollapseToggle.propTypes={className:y.string,iconSpacing:y.number,showIcon:y.bool,active:y.bool,children:y.any},CollapseToggle.defaultProps={className:"",iconSpacing:20,showIcon:!0,active:!0}},79784:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ImportContentFooter;var _=g(h(41594)),b=h(83040),E=g(h(91071)),P=g(h(47483));function ImportContentFooter(c){var m=c.hasPlugins,h=c.hasConflicts,v=c.isImportAllowed,g=c.onResetProcess,C=c.onPreviousClick,w=c.onImportClick,S=(0,b.useNavigate)();return _.default.createElement(E.default,null,_.default.createElement(P.default,{text:y("Previous","elementor"),variant:"contained",onClick:function onClick(){null==C||C(),m?S("import/plugins/"):g()}}),_.default.createElement(P.default,{variant:"contained",text:y("Import","elementor"),color:v?"primary":"disabled",onClick:function onClick(){return null==w||w(),v&&S(function getNextPageUrl(){return h?"import/resolver":m?"import/plugins-activation":"import/process"}())}}))}ImportContentFooter.propTypes={hasPlugins:v.bool,hasConflicts:v.bool,isImportAllowed:v.bool,onResetProcess:v.func.isRequired,onPreviousClick:v.func,onImportClick:v.func}},79788:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TextField;var g=v(h(41594)),_=v(h(78304)),b=v(h(85707)),E=h(79397);function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,b.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function TextField(c){var m="eps-text-field",h=[m,c.className,(0,b.default)({},m+"--outlined","outlined"===c.variant),(0,b.default)({},m+"--standard","standard"===c.variant)],y=_objectSpread(_objectSpread({},c),{},{className:(0,E.arrayToClassName)(h)});return y.multiline?(delete y.multiline,g.default.createElement("textarea",y)):g.default.createElement("input",(0,_.default)({},y,{type:"text"}))}h(80724),TextField.propTypes={className:y.string,multiline:y.bool,variant:y.oneOf(["standard","outlined"]),children:y.string},TextField.defaultProps={className:"",variant:"standard"}},80226:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Page;var g=v(h(41594)),_=v(h(14888)),b=v(h(24017)),E=v(h(25368)),P=v(h(55198));function Page(c){return g.default.createElement("div",{className:"eps-app__lightbox ".concat(c.className)},g.default.createElement("div",{className:"eps-app"},g.default.createElement(_.default,{title:c.title,buttons:c.headerButtons,titleRedirectRoute:c.titleRedirectRoute,onClose:c.onClose}),g.default.createElement("div",{className:"eps-app__main"},function AppSidebar(){if(c.sidebar)return g.default.createElement(b.default,null,c.sidebar)}(),g.default.createElement(E.default,null,c.content)),function AppFooter(){if(c.footer)return g.default.createElement(P.default,null,c.footer)}()))}Page.propTypes={title:y.string,titleRedirectRoute:y.string,className:y.string,headerButtons:y.arrayOf(y.object),sidebar:y.object,content:y.object.isRequired,footer:y.object,onClose:y.func},Page.defaultProps={className:""}},80317:()=>{},80622:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportPlugins(){var c=(0,b.useContext)(P.SharedContext),m=(0,b.useContext)(C.ExportContext),h=(0,E.useNavigate)(),v=(0,b.useState)(!1),g=(0,_.default)(v,2),A=g[0],W=g[1],q=m.data||[],U=q.plugins,Q=q.isExportProcessStarted,K=!!c.data.includes.length,H=(0,b.useCallback)(function(c){return m.dispatch({type:"SET_PLUGINS",payload:c})},[]);return(0,b.useEffect)(function(){Q||h("/export")},[]),(0,b.useEffect)(function(){if(K&&U.length)W(!0);else{var c=U.length>1;W(c)}},[U]),b.default.createElement(w.default,{type:"export",footer:b.default.createElement(D.default,{isKitReady:A})},b.default.createElement("section",{className:"e-app-export-plugins"},b.default.createElement(S.default,{heading:y("Select which plugins to export","elementor"),description:[y("Your Website Template may not work as expected if key plugins are missing.","elementor"),b.default.createElement(b.default.Fragment,{key:"description-secondary-line"},y("By default, we’ll include everything in your file. Uncheck the items you don't want.","elementor")," ",function getLearnMoreLink(){return b.default.createElement(T.default,{url:"https://go.elementor.com/app-what-are-kits",italic:!0},y("Learn More","elementor"))}())]}),b.default.createElement(N.default,{onSelect:H})))};var _=v(h(18821)),b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),E=h(83040),P=h(69378),C=h(81160),w=v(h(53931)),S=v(h(23327)),T=v(h(54999)),N=v(h(56367)),D=v(h(91829));h(58068)},80724:()=>{},80791:(c,m,h)=>{"use strict";var y=h(12470).__;Object.defineProperty(m,"__esModule",{value:!0}),m.default=function usePageTitle(c){var m=c.title,h=c.prefix;(0,v.useEffect)(function(){h||(h=y("Elementor","elementor")),document.title="".concat(h," | ").concat(m)},[m,h])};var v=h(41594)},81069:(c,m,h)=>{"use strict";var y=h(96784),v=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=y(h(39805)),_=y(h(40989)),b=y(h(15118)),E=y(h(29402)),P=y(h(87861)),C=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var g,_,b={__proto__:null,default:c};if(null===c||"object"!=v(c)&&"function"!=typeof c)return b;if(g=m?y:h){if(g.has(c))return g.get(c);g.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(g=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?g(b,E,_):b[E]=c[E]);return b}(c,m)}(h(25160));function _isNativeReflectConstruct(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(c){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!c})()}m.default=function(c){function Component(){return(0,g.default)(this,Component),function _callSuper(c,m,h){return m=(0,E.default)(m),(0,b.default)(c,_isNativeReflectConstruct()?Reflect.construct(m,h||[],(0,E.default)(c).constructor):m.apply(c,h))}(this,Component,arguments)}return(0,P.default)(Component,c),(0,_.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"kits"}},{key:"defaultData",value:function defaultData(){return this.importCommands(C)}}])}($e.modules.ComponentBase)},81133:(c,m,h)=>{"use strict";h.d(m,{cc:()=>_,dd:()=>isCancelable,eJ:()=>b,wm:()=>isCancelledError});var y=h(91669),v=h(26434),g=h(6369);function defaultRetryDelay(c){return Math.min(1e3*Math.pow(2,c),3e4)}function isCancelable(c){return"function"==typeof(null==c?void 0:c.cancel)}var _=function CancelledError(c){this.revert=null==c?void 0:c.revert,this.silent=null==c?void 0:c.silent};function isCancelledError(c){return c instanceof _}var b=function Retryer(c){var m,h,b,E,P=this,C=!1;this.abort=c.abort,this.cancel=function(c){return null==m?void 0:m(c)},this.cancelRetry=function(){C=!0},this.continueRetry=function(){C=!1},this.continue=function(){return null==h?void 0:h()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(c,m){b=c,E=m});var w=function resolve(m){P.isResolved||(P.isResolved=!0,null==c.onSuccess||c.onSuccess(m),null==h||h(),b(m))},S=function reject(m){P.isResolved||(P.isResolved=!0,null==c.onError||c.onError(m),null==h||h(),E(m))};!function run(){if(!P.isResolved){var b;try{b=c.fn()}catch(c){b=Promise.reject(c)}m=function cancelFn(c){if(!P.isResolved&&(S(new _(c)),null==P.abort||P.abort(),isCancelable(b)))try{b.cancel()}catch(c){}},P.isTransportCancelable=isCancelable(b),Promise.resolve(b).then(w).catch(function(m){var _,b;if(!P.isResolved){var E=null!=(_=c.retry)?_:3,w=null!=(b=c.retryDelay)?b:defaultRetryDelay,T="function"==typeof w?w(P.failureCount,m):w,N=!0===E||"number"==typeof E&&P.failureCount<E||"function"==typeof E&&E(P.failureCount,m);!C&&N?(P.failureCount++,null==c.onFail||c.onFail(P.failureCount,m),(0,g.yy)(T).then(function(){if(!y.m.isFocused()||!v.t.isOnline())return function pause(){return new Promise(function(m){h=m,P.isPaused=!0,null==c.onPause||c.onPause()}).then(function(){h=void 0,P.isPaused=!1,null==c.onContinue||c.onContinue()})}()}).then(function(){C?S(m):run()})):S(m)}})}}()}},81160:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.ExportContext=void 0,m.default=ExportContextProvider;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(18821)),E=h(35013);var P=m.ExportContext=_.default.createContext();function ExportContextProvider(c){var m=(0,_.useReducer)(E.reducer,{downloadUrl:"",exportedData:null,isExportProcessStarted:!1,plugins:[],kitInfo:{title:null,description:null,source:null}}),h=(0,b.default)(m,2),y=h[0],v=h[1];return _.default.createElement(P.Provider,{value:{data:y,dispatch:v}},c.children)}ExportContextProvider.propTypes={children:y.object.isRequired}},81399:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),Object.defineProperty(m,"BaseLayout",{enumerable:!0,get:function get(){return v.default}}),Object.defineProperty(m,"CenteredContent",{enumerable:!0,get:function get(){return g.default}}),Object.defineProperty(m,"Footer",{enumerable:!0,get:function get(){return _.default}}),Object.defineProperty(m,"PageHeader",{enumerable:!0,get:function get(){return b.default}}),Object.defineProperty(m,"TopBar",{enumerable:!0,get:function get(){return E.default}});var v=y(h(54525)),g=y(h(65731)),_=y(h(71900)),b=y(h(46916)),E=y(h(98092))},81517:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function ExportCompleteIcon(){return v.default.createElement(g.Box,{sx:{mb:2},"data-testid":"export-complete-icon"},v.default.createElement("img",{src:elementorAppConfig.assets_url+"images/go-pro.svg",alt:"",style:{width:"80px",height:"80px"}}))};var v=y(h(41594)),g=h(86956)},81920:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(17035)),P=g(h(97769)),C=g(h(8555)),w=g(h(60603));h(16686);var S=elementorAppConfig.hasPro?"#/site-editor":"#/site-editor/promotion";function KitData(c){var m=c.data,h=(0,w.default)(m),v=h.templates,g=h.siteSettings,_=h.content,T=h.plugins,N=(null==m?void 0:m.configData)||elementorAppConfig["import-export"],D=N.elementorHomePageUrl,A=N.recentlyEditedElementorPageUrl,W=D||A,q=[y("Site Area","elementor"),y("Included","elementor")],U=[{siteArea:y("Elementor Templates","elementor"),link:elementorAppConfig.base_url+S,included:v},{siteArea:y("Site Settings","elementor"),link:W?W+"#e:run:panel/global/open":"",included:g},{siteArea:y("Content","elementor"),link:elementorAppConfig.admin_url+"edit.php?post_type=page",included:_},{siteArea:y("Plugins","elementor"),link:elementorAppConfig.admin_url+"plugins.php",included:T}].map(function(c){var m=c.siteArea,h=c.included,y=c.link;if(h.length)return[b.default.createElement(E.default,{key:m,text:m,link:y}),b.default.createElement(P.default,{key:h,data:h})]}).filter(function(c){return c});return U.length?b.default.createElement(C.default,{className:"e-app-import-export-kit-data",headers:q,rows:U,layout:[1,3]}):null}KitData.propTypes={data:v.object};m.default=(0,b.memo)(KitData)},81921:(c,m,h)=>{"use strict";h.d(m,{C:()=>useIsFetching});var y=h(41594),v=h.n(y),g=h(25800),_=h(6369),b=h(15292),E=function checkIsFetching(c,m,h,y){var v=c.isFetching(m);h!==v&&y(v)};function useIsFetching(c,m){var h=v().useRef(!1),y=(0,b.j)(),P=(0,_.b_)(c,m)[0],C=v().useState(y.isFetching(P)),w=C[0],S=C[1],T=v().useRef(P);T.current=P;var N=v().useRef(w);return N.current=w,v().useEffect(function(){h.current=!0,E(y,T.current,N.current,S);var c=y.getQueryCache().subscribe(g.j.batchCalls(function(){h.current&&E(y,T.current,N.current,S)}));return function(){h.current=!1,c()}},[y]),w}},82134:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExportCompleteSummary;var g=v(h(41594)),_=h(86956),b=v(h(62688));function ExportCompleteSummary(c){var m=c.kitInfo,h=c.includes;return g.default.createElement(_.Box,{sx:{width:"100%",border:1,borderRadius:1,borderColor:"action.focus",p:2.5},"data-testid":"export-complete-summary"},g.default.createElement(_.Typography,{variant:"h6",component:"h3",gutterBottom:!0},m.title),m.description&&g.default.createElement(_.Typography,{variant:"body2",color:"text.secondary",sx:{mb:2}},m.description),g.default.createElement(_.Typography,{variant:"caption",color:"text.secondary",sx:{display:"block",mb:1}},y("Exported items:","elementor")),g.default.createElement(_.Typography,{variant:"body2"},h.map(function(c){return{content:y("Content","elementor"),templates:y("Templates","elementor"),settings:y("Settings & configurations","elementor"),plugins:y("Plugins","elementor")}[c]||c}).join(", ")))}ExportCompleteSummary.propTypes={kitInfo:b.default.shape({title:b.default.string,description:b.default.string}).isRequired,includes:b.default.arrayOf(b.default.string).isRequired}},82346:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TableCheckbox;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(10906)),E=h(12456),P=h(79397),C=v(h(47579));function TableCheckbox(c){var m=(0,_.useContext)(E.Context)||{},h=m.selected,y=m.disabled,v=m.setSelected,g=Object.prototype.hasOwnProperty.call(c,"allSelectedCount"),w=h.length===c.allSelectedCount,S=!!g&&!(!(h.length-y.length)||w),T=g?w:h.includes(c.index),N=g?null:y.includes(c.index);return _.default.createElement(C.default,{checked:T,indeterminate:S,onChange:function onChange(){return g?function onSelectAll(){v(function(){return w||S?y.length?(0,b.default)(y):[]:Array(c.allSelectedCount).fill(!0).map(function(c,m){return m})})}():function onSelectRow(){v(function(m){var h=(0,b.default)(m),y=h.indexOf(c.index);return y>-1?h.splice(y,1):h.push(c.index),h})}()},disabled:N,className:(0,P.arrayToClassName)(["eps-table__checkbox",c.className])})}TableCheckbox.propTypes={className:y.string,index:y.number,initialChecked:y.bool,allSelectedCount:y.number}},82372:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useImportActions(){var c=(0,v.useContext)(_.SharedContext),m=(0,g.useNavigate)(),h=(0,b.default)().backToDashboard,y="kit-library"===c.data.referrer;return{navigateToMainScreen:function navigateToMainScreen(){m(y?"/kit-library":"/import")},closeApp:function closeApp(){y?m("/kit-library"):h()}}};var v=h(41594),g=h(83040),_=h(69378),b=y(h(46361))},82580:(c,m)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.generateScreenshot=void 0;m.generateScreenshot=function generateScreenshot(){return new Promise(function(c){var m=document.createElement("iframe");m.style="visibility: hidden;",m.width="1200",m.height="1000";var h=function messageHandler(y){"kit-screenshot-done"===y.data.name&&(window.removeEventListener("message",h),document.body.removeChild(m),c(y.data.imageUrl||null))};window.addEventListener("message",h);var y=new URL(window.location.origin);y.searchParams.set("kit_thumbnail","1"),y.searchParams.set("nonce",elementorAppConfig["import-export-customization"].kitPreviewNonce),document.body.appendChild(m),m.src=y.toString()})}},83040:(c,m,h)=>{"use strict";h.r(m),h.d(m,{Link:()=>Re,Location:()=>fe,LocationProvider:()=>pe,Match:()=>Le,Redirect:()=>We,Router:()=>ve,ServerLocation:()=>me,createHistory:()=>V,createMemorySource:()=>Y,globalHistory:()=>Z,isRedirect:()=>Me,matchPath:()=>w,navigate:()=>ee,redirectTo:()=>De,useLocation:()=>Be,useMatch:()=>ze,useNavigate:()=>Qe,useParams:()=>Ke});var y=h(41594),v=h.n(y),g=h(32091),_=h.n(g),b=h(49477),E=h.n(b);function componentWillMount(){var c=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=c&&this.setState(c)}function componentWillReceiveProps(c){this.setState(function updater(m){var h=this.constructor.getDerivedStateFromProps(c,m);return null!=h?h:null}.bind(this))}function componentWillUpdate(c,m){try{var h=this.props,y=this.state;this.props=c,this.state=m,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(h,y)}finally{this.props=h,this.state=y}}componentWillMount.__suppressDeprecationWarning=!0,componentWillReceiveProps.__suppressDeprecationWarning=!0,componentWillUpdate.__suppressDeprecationWarning=!0;var P=function startsWith(c,m){return c.substr(0,m.length)===m},C=function pick(c,m){for(var h=void 0,y=void 0,v=m.split("?")[0],g=U(v),b=""===g[0],E=q(c),P=0,C=E.length;P<C;P++){var w=!1,S=E[P].route;if(S.default)y={route:S,params:{},uri:m};else{for(var T=U(S.path),D={},W=Math.max(g.length,T.length),Q=0;Q<W;Q++){var H=T[Q],G=g[Q];if(A(H)){D[H.slice(1)||"*"]=g.slice(Q).map(decodeURIComponent).join("/");break}if(void 0===G){w=!0;break}var V=N.exec(H);if(V&&!b){-1===K.indexOf(V[1])||_()(!1);var Y=decodeURIComponent(G);D[V[1]]=Y}else if(H!==G){w=!0;break}}if(!w){h={route:S,params:D,uri:"/"+g.slice(0,Q).join("/")};break}}}return h||y||null},w=function match(c,m){return C([{path:c}],m)},S=function resolve(c,m){if(P(c,"/"))return c;var h=c.split("?"),y=h[0],v=h[1],g=m.split("?")[0],_=U(y),b=U(g);if(""===_[0])return Q(g,v);if(!P(_[0],".")){var E=b.concat(_).join("/");return Q(("/"===g?"":"/")+E,v)}for(var C=b.concat(_),w=[],S=0,T=C.length;S<T;S++){var N=C[S];".."===N?w.pop():"."!==N&&w.push(N)}return Q("/"+w.join("/"),v)},T=function insertParams(c,m){var h=c.split("?"),y=h[0],v=h[1],g=void 0===v?"":v,_="/"+U(y).map(function(c){var h=N.exec(c);return h?m[h[1]]:c}).join("/"),b=m.location,E=(b=void 0===b?{}:b).search,P=(void 0===E?"":E).split("?")[1]||"";return _=Q(_,g,P)},N=/^:(.+)/,D=function isDynamic(c){return N.test(c)},A=function isSplat(c){return c&&"*"===c[0]},W=function rankRoute(c,m){return{route:c,score:c.default?0:U(c.path).reduce(function(c,m){return c+=4,!function isRootSegment(c){return""===c}(m)?D(m)?c+=2:A(m)?c-=5:c+=3:c+=1,c},0),index:m}},q=function rankRoutes(c){return c.map(W).sort(function(c,m){return c.score<m.score?1:c.score>m.score?-1:c.index-m.index})},U=function segmentize(c){return c.replace(/(^\/+|\/+$)/g,"").split("/")},Q=function addQuery(c){for(var m=arguments.length,h=Array(m>1?m-1:0),y=1;y<m;y++)h[y-1]=arguments[y];return c+((h=h.filter(function(c){return c&&c.length>0}))&&h.length>0?"?"+h.join("&"):"")},K=["uri","path"],H=Object.assign||function(c){for(var m=1;m<arguments.length;m++){var h=arguments[m];for(var y in h)Object.prototype.hasOwnProperty.call(h,y)&&(c[y]=h[y])}return c},G=function getLocation(c){var m=c.location,h=m.search,y=m.hash,v=m.href,g=m.origin,_=m.protocol,b=m.host,E=m.hostname,P=m.port,C=c.location.pathname;!C&&v&&J&&(C=new URL(v).pathname);return{pathname:encodeURI(decodeURI(C)),search:h,hash:y,href:v,origin:g,protocol:_,host:b,hostname:E,port:P,state:c.history.state,key:c.history.state&&c.history.state.key||"initial"}},V=function createHistory(c,m){var h=[],y=G(c),v=!1,g=function resolveTransition(){};return{get location(){return y},get transitioning(){return v},_onTransitionComplete:function _onTransitionComplete(){v=!1,g()},listen:function listen(m){h.push(m);var v=function popstateListener(){y=G(c),m({location:y,action:"POP"})};return c.addEventListener("popstate",v),function(){c.removeEventListener("popstate",v),h=h.filter(function(c){return c!==m})}},navigate:function navigate(m){var _=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},b=_.state,E=_.replace,P=void 0!==E&&E;if("number"==typeof m)c.history.go(m);else{b=H({},b,{key:Date.now()+""});try{v||P?c.history.replaceState(b,null,m):c.history.pushState(b,null,m)}catch(h){c.location[P?"replace":"assign"](m)}}y=G(c),v=!0;var C=new Promise(function(c){return g=c});return h.forEach(function(c){return c({location:y,action:"PUSH"})}),C}}},Y=function createMemorySource(){var c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",m=c.indexOf("?"),h={pathname:m>-1?c.substr(0,m):c,search:m>-1?c.substr(m):""},y=0,v=[h],g=[null];return{get location(){return v[y]},addEventListener:function addEventListener(c,m){},removeEventListener:function removeEventListener(c,m){},history:{get entries(){return v},get index(){return y},get state(){return g[y]},pushState:function pushState(c,m,h){var _=h.split("?"),b=_[0],E=_[1],P=void 0===E?"":E;y++,v.push({pathname:b,search:P.length?"?"+P:P}),g.push(c)},replaceState:function replaceState(c,m,h){var _=h.split("?"),b=_[0],E=_[1],P=void 0===E?"":E;v[y]={pathname:b,search:P},g[y]=c},go:function go(c){var m=y+c;m<0||m>g.length-1||(y=m)}}}},J=!("undefined"==typeof window||!window.document||!window.document.createElement),Z=V(function getSource(){return J?window:Y()}()),ee=Z.navigate,te=Object.assign||function(c){for(var m=1;m<arguments.length;m++){var h=arguments[m];for(var y in h)Object.prototype.hasOwnProperty.call(h,y)&&(c[y]=h[y])}return c};function _objectWithoutProperties(c,m){var h={};for(var y in c)m.indexOf(y)>=0||Object.prototype.hasOwnProperty.call(c,y)&&(h[y]=c[y]);return h}function _classCallCheck(c,m){if(!(c instanceof m))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(c,m){if(!c)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!m||"object"!=typeof m&&"function"!=typeof m?c:m}function _inherits(c,m){if("function"!=typeof m&&null!==m)throw new TypeError("Super expression must either be null or a function, not "+typeof m);c.prototype=Object.create(m&&m.prototype,{constructor:{value:c,enumerable:!1,writable:!0,configurable:!0}}),m&&(Object.setPrototypeOf?Object.setPrototypeOf(c,m):c.__proto__=m)}var ne=function createNamedContext(c,m){var h=E()(m);return h.displayName=c,h},de=ne("Location"),fe=function Location(c){var m=c.children;return v().createElement(de.Consumer,null,function(c){return c?m(c):v().createElement(pe,null,m)})},pe=function(c){function LocationProvider(){var m,h;_classCallCheck(this,LocationProvider);for(var y=arguments.length,v=Array(y),g=0;g<y;g++)v[g]=arguments[g];return m=h=_possibleConstructorReturn(this,c.call.apply(c,[this].concat(v))),h.state={context:h.getContext(),refs:{unlisten:null}},_possibleConstructorReturn(h,m)}return _inherits(LocationProvider,c),LocationProvider.prototype.getContext=function getContext(){var c=this.props.history;return{navigate:c.navigate,location:c.location}},LocationProvider.prototype.componentDidCatch=function componentDidCatch(c,m){if(!Me(c))throw c;(0,this.props.history.navigate)(c.uri,{replace:!0})},LocationProvider.prototype.componentDidUpdate=function componentDidUpdate(c,m){m.context.location!==this.state.context.location&&this.props.history._onTransitionComplete()},LocationProvider.prototype.componentDidMount=function componentDidMount(){var c=this,m=this.state.refs,h=this.props.history;h._onTransitionComplete(),m.unlisten=h.listen(function(){Promise.resolve().then(function(){requestAnimationFrame(function(){c.unmounted||c.setState(function(){return{context:c.getContext()}})})})})},LocationProvider.prototype.componentWillUnmount=function componentWillUnmount(){var c=this.state.refs;this.unmounted=!0,c.unlisten()},LocationProvider.prototype.render=function render(){var c=this.state.context,m=this.props.children;return v().createElement(de.Provider,{value:c},"function"==typeof m?m(c):m||null)},LocationProvider}(v().Component);pe.defaultProps={history:Z};var me=function ServerLocation(c){var m=c.url,h=c.children,y=m.indexOf("?"),g=void 0,_="";return y>-1?(g=m.substring(0,y),_=m.substring(y)):g=m,v().createElement(de.Provider,{value:{location:{pathname:g,search:_,hash:""},navigate:function navigate(){throw new Error("You can't call navigate on the server.")}}},h)},ye=ne("Base",{baseuri:"/",basepath:"/"}),ve=function Router(c){return v().createElement(ye.Consumer,null,function(m){return v().createElement(fe,null,function(h){return v().createElement(ge,te({},m,h,c))})})},ge=function(c){function RouterImpl(){return _classCallCheck(this,RouterImpl),_possibleConstructorReturn(this,c.apply(this,arguments))}return _inherits(RouterImpl,c),RouterImpl.prototype.render=function render(){var c=this.props,m=c.location,h=c.navigate,y=c.basepath,g=c.primary,_=c.children,b=(c.baseuri,c.component),E=void 0===b?"div":b,P=_objectWithoutProperties(c,["location","navigate","basepath","primary","children","baseuri","component"]),w=v().Children.toArray(_).reduce(function(c,m){var h=et(y)(m);return c.concat(h)},[]),T=m.pathname,N=C(w,T);if(N){var D=N.params,A=N.uri,W=N.route,q=N.route.value;y=W.default?y:W.path.replace(/\*$/,"");var U=te({},D,{uri:A,location:m,navigate:function navigate(c,m){return h(S(c,A),m)}}),Q=v().cloneElement(q,U,q.props.children?v().createElement(ve,{location:m,primary:g},q.props.children):void 0),K=g?Ee:E,H=g?te({uri:A,location:m,component:E},P):P;return v().createElement(ye.Provider,{value:{baseuri:A,basepath:y}},v().createElement(K,H,Q))}return null},RouterImpl}(v().PureComponent);ge.defaultProps={primary:!0};var be=ne("Focus"),Ee=function FocusHandler(c){var m=c.uri,h=c.location,y=c.component,g=_objectWithoutProperties(c,["uri","location","component"]);return v().createElement(be.Consumer,null,function(c){return v().createElement(je,te({},g,{component:y,requestFocus:c,uri:m,location:h}))})},Oe=!0,xe=0,je=function(c){function FocusHandlerImpl(){var m,h;_classCallCheck(this,FocusHandlerImpl);for(var y=arguments.length,v=Array(y),g=0;g<y;g++)v[g]=arguments[g];return m=h=_possibleConstructorReturn(this,c.call.apply(c,[this].concat(v))),h.state={},h.requestFocus=function(c){!h.state.shouldFocus&&c&&c.focus()},_possibleConstructorReturn(h,m)}return _inherits(FocusHandlerImpl,c),FocusHandlerImpl.getDerivedStateFromProps=function getDerivedStateFromProps(c,m){if(null==m.uri)return te({shouldFocus:!0},c);var h=c.uri!==m.uri,y=m.location.pathname!==c.location.pathname&&c.location.pathname===c.uri;return te({shouldFocus:h||y},c)},FocusHandlerImpl.prototype.componentDidMount=function componentDidMount(){xe++,this.focus()},FocusHandlerImpl.prototype.componentWillUnmount=function componentWillUnmount(){0===--xe&&(Oe=!0)},FocusHandlerImpl.prototype.componentDidUpdate=function componentDidUpdate(c,m){c.location!==this.props.location&&this.state.shouldFocus&&this.focus()},FocusHandlerImpl.prototype.focus=function focus(){var c=this.props.requestFocus;c?c(this.node):Oe?Oe=!1:this.node&&(this.node.contains(document.activeElement)||this.node.focus())},FocusHandlerImpl.prototype.render=function render(){var c=this,m=this.props,h=(m.children,m.style),y=(m.requestFocus,m.component),g=void 0===y?"div":y,_=(m.uri,m.location,_objectWithoutProperties(m,["children","style","requestFocus","component","uri","location"]));return v().createElement(g,te({style:te({outline:"none"},h),tabIndex:"-1",ref:function ref(m){return c.node=m}},_),v().createElement(be.Provider,{value:this.requestFocus},this.props.children))},FocusHandlerImpl}(v().Component);!function polyfill(c){var m=c.prototype;if(!m||!m.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof c.getDerivedStateFromProps&&"function"!=typeof m.getSnapshotBeforeUpdate)return c;var h=null,y=null,v=null;if("function"==typeof m.componentWillMount?h="componentWillMount":"function"==typeof m.UNSAFE_componentWillMount&&(h="UNSAFE_componentWillMount"),"function"==typeof m.componentWillReceiveProps?y="componentWillReceiveProps":"function"==typeof m.UNSAFE_componentWillReceiveProps&&(y="UNSAFE_componentWillReceiveProps"),"function"==typeof m.componentWillUpdate?v="componentWillUpdate":"function"==typeof m.UNSAFE_componentWillUpdate&&(v="UNSAFE_componentWillUpdate"),null!==h||null!==y||null!==v){var g=c.displayName||c.name,_="function"==typeof c.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+g+" uses "+_+" but also contains the following legacy lifecycles:"+(null!==h?"\n  "+h:"")+(null!==y?"\n  "+y:"")+(null!==v?"\n  "+v:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof c.getDerivedStateFromProps&&(m.componentWillMount=componentWillMount,m.componentWillReceiveProps=componentWillReceiveProps),"function"==typeof m.getSnapshotBeforeUpdate){if("function"!=typeof m.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");m.componentWillUpdate=componentWillUpdate;var b=m.componentDidUpdate;m.componentDidUpdate=function componentDidUpdatePolyfill(c,m,h){var y=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:h;b.call(this,c,m,y)}}return c}(je);var ke=function k(){},Te=v().forwardRef;void 0===Te&&(Te=function forwardRef(c){return c});var Re=Te(function(c,m){var h=c.innerRef,y=_objectWithoutProperties(c,["innerRef"]);return v().createElement(ye.Consumer,null,function(c){c.basepath;var g=c.baseuri;return v().createElement(fe,null,function(c){var _=c.location,b=c.navigate,E=y.to,C=y.state,w=y.replace,T=y.getProps,N=void 0===T?ke:T,D=_objectWithoutProperties(y,["to","state","replace","getProps"]),A=S(E,g),W=encodeURI(A),q=_.pathname===W,U=P(_.pathname,W);return v().createElement("a",te({ref:m||h,"aria-current":q?"page":void 0},D,N({isCurrent:q,isPartiallyCurrent:U,href:A,location:_}),{href:A,onClick:function onClick(c){if(D.onClick&&D.onClick(c),tt(c)){c.preventDefault();var m=w;if("boolean"!=typeof w&&q){var h=te({},_.state),y=(h.key,_objectWithoutProperties(h,["key"]));m=function shallowCompare(c,m){var h=Object.keys(c);return h.length===Object.keys(m).length&&h.every(function(h){return m.hasOwnProperty(h)&&c[h]===m[h]})}(te({},C),y)}b(A,{state:C,replace:m})}}}))})})});function RedirectRequest(c){this.uri=c}Re.displayName="Link";var Me=function isRedirect(c){return c instanceof RedirectRequest},De=function redirectTo(c){throw new RedirectRequest(c)},Ae=function(c){function RedirectImpl(){return _classCallCheck(this,RedirectImpl),_possibleConstructorReturn(this,c.apply(this,arguments))}return _inherits(RedirectImpl,c),RedirectImpl.prototype.componentDidMount=function componentDidMount(){var c=this.props,m=c.navigate,h=c.to,y=(c.from,c.replace),v=void 0===y||y,g=c.state,_=(c.noThrow,c.baseuri),b=_objectWithoutProperties(c,["navigate","to","from","replace","state","noThrow","baseuri"]);Promise.resolve().then(function(){var c=S(h,_);m(T(c,b),{replace:v,state:g})})},RedirectImpl.prototype.render=function render(){var c=this.props,m=(c.navigate,c.to),h=(c.from,c.replace,c.state,c.noThrow),y=c.baseuri,v=_objectWithoutProperties(c,["navigate","to","from","replace","state","noThrow","baseuri"]),g=S(m,y);return h||De(T(g,v)),null},RedirectImpl}(v().Component),We=function Redirect(c){return v().createElement(ye.Consumer,null,function(m){var h=m.baseuri;return v().createElement(fe,null,function(m){return v().createElement(Ae,te({},m,{baseuri:h},c))})})},Le=function Match(c){var m=c.path,h=c.children;return v().createElement(ye.Consumer,null,function(c){var y=c.baseuri;return v().createElement(fe,null,function(c){var v=c.navigate,g=c.location,_=S(m,y),b=w(_,g.pathname);return h({navigate:v,location:g,match:b?te({},b.params,{uri:b.uri,path:m}):null})})})},Be=function useLocation(){var c=(0,y.useContext)(de);if(!c)throw new Error("useLocation hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");return c.location},Qe=function useNavigate(){var c=(0,y.useContext)(de);if(!c)throw new Error("useNavigate hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");return c.navigate},Ke=function useParams(){var c=(0,y.useContext)(ye);if(!c)throw new Error("useParams hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");var m=Be(),h=w(c.basepath,m.pathname);return h?h.params:null},ze=function useMatch(c){if(!c)throw new Error("useMatch(path: string) requires an argument of a string to match against");var m=(0,y.useContext)(ye);if(!m)throw new Error("useMatch hook was used but a LocationContext.Provider was not found in the parent tree. Make sure this is used in a component that is a child of Router");var h=Be(),v=S(c,m.baseuri),g=w(v,h.pathname);return g?te({},g.params,{uri:g.uri,path:c}):null},$e=function stripSlashes(c){return c.replace(/(^\/+|\/+$)/g,"")},et=function createRoute(c){return function(m){if(!m)return null;if(m.type===v().Fragment&&m.props.children)return v().Children.map(m.props.children,createRoute(c));if(m.props.path||m.props.default||m.type===We||_()(!1),m.type!==We||m.props.from&&m.props.to||_()(!1),m.type!==We||function validateRedirect(c,m){var h=function filter(c){return D(c)};return U(c).filter(h).sort().join("/")===U(m).filter(h).sort().join("/")}(m.props.from,m.props.to)||_()(!1),m.props.default)return{value:m,default:!0};var h=m.type===We?m.props.from:m.props.path,y="/"===h?c:$e(c)+"/"+$e(h);return{value:m,default:m.props.default,path:m.props.children?$e(y)+"/*":y}}},tt=function shouldNavigate(c){return!c.defaultPrevented&&0===c.button&&!(c.metaKey||c.altKey||c.ctrlKey||c.shiftKey)}},83768:()=>{},84245:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function KitInformation(){return v.default.createElement(v.default.Fragment,null,v.default.createElement(E.default,null,v.default.createElement(P.default,{padding:"24 40",separated:!0,className:"e-app-export-kit-information"},v.default.createElement(P.default.Item,null,v.default.createElement(b.default,{container:!0,noWrap:!0,direction:"column",className:"e-app-export-kit-information__content"},v.default.createElement(b.default,{item:!0,container:!0},v.default.createElement(g.default,null)),v.default.createElement(b.default,{item:!0,container:!0},v.default.createElement(_.default,null)))))))};var v=y(h(41594)),g=y(h(23730)),_=y(h(33704)),b=y(h(3416)),E=y(h(21689)),P=y(h(93279))},84644:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function useConnectState(){var c=(0,g.useState)(elementorCommon.config.library_connect.is_connected),m=(0,v.default)(c,2),h=m[0],y=m[1],_=(0,g.useState)(!1),b=(0,v.default)(_,2),E=b[0],P=b[1],C=(0,g.useCallback)(function(c){P(!0),y(!0),elementorCommon.config.library_connect.is_connected=!0,c&&c()},[]),w=(0,g.useCallback)(function(c){y(!1),P(!1),elementorCommon.config.library_connect.is_connected=!1,c&&c()},[]),S=(0,g.useCallback)(function(c){P(c)},[]);return{isConnected:h,isConnecting:E,setConnecting:S,handleConnectSuccess:C,handleConnectError:w}};var v=y(h(18821)),g=h(41594)},84686:(c,m,h)=>{"use strict";var y=h(41594),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=v(h(40989)),_=v(h(39805)),b=v(h(47485));m.default=(0,g.default)(function Onboarding(){(0,_.default)(this,Onboarding),b.default.addRoute({path:"/onboarding/*",component:y.lazy(function(){return h.e(1352).then(h.bind(h,55723))})})})},85418:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Heading;var g=v(h(41594)),_=h(79397);function Heading(c){var m=[c.className];c.variant&&m.push("eps-"+c.variant);var h=function Element(){return g.default.createElement(c.tag,{className:(0,_.arrayToClassName)(m)},c.children)};return g.default.createElement(h,null)}Heading.propTypes={className:y.string,children:y.oneOfType([y.string,y.object,y.arrayOf(y.object)]).isRequired,tag:y.oneOf(["h1","h2","h3","h4","h5","h6"]),variant:y.oneOf(["display-1","display-2","display-3","display-4","h1","h2","h3","h4","h5","h6"]).isRequired},Heading.defaultProps={className:"",tag:"h1"}},85707:(c,m,h)=>{var y=h(45498);c.exports=function _defineProperty(c,m,h){return(m=y(m))in c?Object.defineProperty(c,m,{value:h,enumerable:!0,configurable:!0,writable:!0}):c[m]=h,c},c.exports.__esModule=!0,c.exports.default=c.exports},85869:(c,m,h)=>{"use strict";h.d(m,{$:()=>getDefaultState,s:()=>E});var y=h(68102),v=h(8118),g=h(25800),_=h(81133),b=h(6369),E=function(){function Mutation(c){this.options=(0,y.A)({},c.defaultOptions,c.options),this.mutationId=c.mutationId,this.mutationCache=c.mutationCache,this.observers=[],this.state=c.state||getDefaultState(),this.meta=c.meta}var c=Mutation.prototype;return c.setState=function setState(c){this.dispatch({type:"setState",state:c})},c.addObserver=function addObserver(c){-1===this.observers.indexOf(c)&&this.observers.push(c)},c.removeObserver=function removeObserver(c){this.observers=this.observers.filter(function(m){return m!==c})},c.cancel=function cancel(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(b.lQ).catch(b.lQ)):Promise.resolve()},c.continue=function _continue(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},c.execute=function execute(){var c,m=this,h="loading"===this.state.status,y=Promise.resolve();return h||(this.dispatch({type:"loading",variables:this.options.variables}),y=y.then(function(){null==m.mutationCache.config.onMutate||m.mutationCache.config.onMutate(m.state.variables,m)}).then(function(){return null==m.options.onMutate?void 0:m.options.onMutate(m.state.variables)}).then(function(c){c!==m.state.context&&m.dispatch({type:"loading",context:c,variables:m.state.variables})})),y.then(function(){return m.executeMutation()}).then(function(h){c=h,null==m.mutationCache.config.onSuccess||m.mutationCache.config.onSuccess(c,m.state.variables,m.state.context,m)}).then(function(){return null==m.options.onSuccess?void 0:m.options.onSuccess(c,m.state.variables,m.state.context)}).then(function(){return null==m.options.onSettled?void 0:m.options.onSettled(c,null,m.state.variables,m.state.context)}).then(function(){return m.dispatch({type:"success",data:c}),c}).catch(function(c){return null==m.mutationCache.config.onError||m.mutationCache.config.onError(c,m.state.variables,m.state.context,m),(0,v.t)().error(c),Promise.resolve().then(function(){return null==m.options.onError?void 0:m.options.onError(c,m.state.variables,m.state.context)}).then(function(){return null==m.options.onSettled?void 0:m.options.onSettled(void 0,c,m.state.variables,m.state.context)}).then(function(){throw m.dispatch({type:"error",error:c}),c})})},c.executeMutation=function executeMutation(){var c,m=this;return this.retryer=new _.eJ({fn:function fn(){return m.options.mutationFn?m.options.mutationFn(m.state.variables):Promise.reject("No mutationFn found")},onFail:function onFail(){m.dispatch({type:"failed"})},onPause:function onPause(){m.dispatch({type:"pause"})},onContinue:function onContinue(){m.dispatch({type:"continue"})},retry:null!=(c=this.options.retry)?c:0,retryDelay:this.options.retryDelay}),this.retryer.promise},c.dispatch=function dispatch(c){var m=this;this.state=function reducer(c,m){switch(m.type){case"failed":return(0,y.A)({},c,{failureCount:c.failureCount+1});case"pause":return(0,y.A)({},c,{isPaused:!0});case"continue":return(0,y.A)({},c,{isPaused:!1});case"loading":return(0,y.A)({},c,{context:m.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:m.variables});case"success":return(0,y.A)({},c,{data:m.data,error:null,status:"success",isPaused:!1});case"error":return(0,y.A)({},c,{data:void 0,error:m.error,failureCount:c.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,y.A)({},c,m.state);default:return c}}(this.state,c),g.j.batch(function(){m.observers.forEach(function(m){m.onMutationUpdate(c)}),m.mutationCache.notify(m)})},Mutation}();function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}},86830:(c,m,h)=>{"use strict";h.d(m,{t:()=>useBaseQuery});var y=h(41594),v=h.n(y),g=h(25800),_=h(24673),b=h(15292),E=h(70963);function useBaseQuery(c,m){var h=v().useRef(!1),y=v().useState(0)[1],P=(0,b.j)(),C=(0,_.h)(),w=P.defaultQueryObserverOptions(c);w.optimisticResults=!0,w.onError&&(w.onError=g.j.batchCalls(w.onError)),w.onSuccess&&(w.onSuccess=g.j.batchCalls(w.onSuccess)),w.onSettled&&(w.onSettled=g.j.batchCalls(w.onSettled)),w.suspense&&("number"!=typeof w.staleTime&&(w.staleTime=1e3),0===w.cacheTime&&(w.cacheTime=1)),(w.suspense||w.useErrorBoundary)&&(C.isReset()||(w.retryOnMount=!1));var S=v().useState(function(){return new m(P,w)})[0],T=S.getOptimisticResult(w);if(v().useEffect(function(){h.current=!0,C.clearReset();var c=S.subscribe(g.j.batchCalls(function(){h.current&&y(function(c){return c+1})}));return S.updateResult(),function(){h.current=!1,c()}},[C,S]),v().useEffect(function(){S.setOptions(w,{listeners:!1})},[w,S]),w.suspense&&T.isLoading)throw S.fetchOptimistic(w).then(function(c){var m=c.data;null==w.onSuccess||w.onSuccess(m),null==w.onSettled||w.onSettled(m,null)}).catch(function(c){C.clearReset(),null==w.onError||w.onError(c),null==w.onSettled||w.onSettled(void 0,c)});if(T.isError&&!C.isReset()&&!T.isFetching&&(0,E.G)(w.suspense,w.useErrorBoundary,[T.error,S.getCurrentQuery()]))throw T.error;return"tracked"===w.notifyOnChangeProps&&(T=S.trackResult(T,w)),T}},86956:c=>{"use strict";c.exports=elementorV2.ui},87026:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ImportContentDisplay;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(76492)),P=h(69378),C=g(h(37880)),w=g(h(40587)),S=g(h(54999)),T=h(37628);function ImportContentDisplay(c){var m=c.manifest,h=c.hasPro,v=c.hasPlugins,g=c.isAllRequiredPluginsSelected,_=c.onResetProcess,N=(0,b.useContext)(P.SharedContext),D=C.default.filter(function(c){var h=c.type,y=null==m?void 0:m["settings"===h?"site-settings":h];return!!(Array.isArray(y)?y.length:y)});return(0,b.useEffect)(function(){N.dispatch({type:"SET_CPT",payload:(0,T.cptObjectToOptionsArray)(null==m?void 0:m["custom-post-type-title"],"label")})},[]),!D.length&&v?b.default.createElement(w.default,{color:"info",label:y("Note:","elementor")},y("The Website Kit you’re using contains plugins for functionality, but no content or pages, etc.","elementor")):D.length?b.default.createElement(b.default.Fragment,null,!g&&b.default.createElement(w.default,{color:"warning",label:y("Required plugins are still missing.","elementor"),className:"e-app-import-content__plugins-notice"},y("If you don't include them, this kit may not work properly.","elementor")," ",b.default.createElement(S.default,{url:"/import/plugins"},y("Go Back","elementor"))),b.default.createElement(E.default,{contentData:D,hasPro:h})):b.default.createElement(w.default,{color:"danger"},y("You can’t use this Website Kit because it doesn’t contain any content, pages, etc. Try again with a different file.","elementor")," ",b.default.createElement(S.default,{onClick:_},y("Go Back","elementor")))}ImportContentDisplay.propTypes={manifest:v.object,hasPro:v.bool,hasPlugins:v.bool,isAllRequiredPluginsSelected:v.bool,onResetProcess:v.func}},87581:()=>{},87861:(c,m,h)=>{var y=h(91270);c.exports=function _inherits(c,m){if("function"!=typeof m&&null!==m)throw new TypeError("Super expression must either be null or a function");c.prototype=Object.create(m&&m.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),m&&y(c,m)},c.exports.__esModule=!0,c.exports.default=c.exports},89736:c=>{function _regeneratorDefine(m,h,y,v){var g=Object.defineProperty;try{g({},"",{})}catch(m){g=0}c.exports=_regeneratorDefine=function regeneratorDefine(c,m,h,y){if(m)g?g(c,m,{value:h,enumerable:!y,configurable:!y,writable:!y}):c[m]=h;else{var v=function o(m,h){_regeneratorDefine(c,m,function(c){return this._invoke(m,h,c)})};v("next",0),v("throw",1),v("return",2)}},c.exports.__esModule=!0,c.exports.default=c.exports,_regeneratorDefine(m,h,y,v)}c.exports=_regeneratorDefine,c.exports.__esModule=!0,c.exports.default=c.exports},89774:(c,m,h)=>{"use strict";h.d(m,{T:()=>b});var y=h(59994),v=h(6369),g=h(25800),_=h(74342),b=function(c){function QueriesObserver(m,h){var y;return(y=c.call(this)||this).client=m,y.queries=[],y.result=[],y.observers=[],y.observersMap={},h&&y.setQueries(h),y}(0,y.A)(QueriesObserver,c);var m=QueriesObserver.prototype;return m.onSubscribe=function onSubscribe(){var c=this;1===this.listeners.length&&this.observers.forEach(function(m){m.subscribe(function(h){c.onUpdate(m,h)})})},m.onUnsubscribe=function onUnsubscribe(){this.listeners.length||this.destroy()},m.destroy=function destroy(){this.listeners=[],this.observers.forEach(function(c){c.destroy()})},m.setQueries=function setQueries(c,m){this.queries=c,this.updateObservers(m)},m.getCurrentResult=function getCurrentResult(){return this.result},m.getOptimisticResult=function getOptimisticResult(c){return this.findMatchingObservers(c).map(function(c){return c.observer.getOptimisticResult(c.defaultedQueryOptions)})},m.findMatchingObservers=function findMatchingObservers(c){var m=this,h=this.observers,y=c.map(function(c){return m.client.defaultQueryObserverOptions(c)}),v=y.flatMap(function(c){var m=h.find(function(m){return m.options.queryHash===c.queryHash});return null!=m?[{defaultedQueryOptions:c,observer:m}]:[]}),g=v.map(function(c){return c.defaultedQueryOptions.queryHash}),_=y.filter(function(c){return!g.includes(c.queryHash)}),b=h.filter(function(c){return!v.some(function(m){return m.observer===c})}),E=_.map(function(c,h){if(c.keepPreviousData){var y=b[h];if(void 0!==y)return{defaultedQueryOptions:c,observer:y}}return{defaultedQueryOptions:c,observer:m.getObserver(c)}});return v.concat(E).sort(function sortMatchesByOrderOfQueries(c,m){return y.indexOf(c.defaultedQueryOptions)-y.indexOf(m.defaultedQueryOptions)})},m.getObserver=function getObserver(c){var m=this.client.defaultQueryObserverOptions(c),h=this.observersMap[m.queryHash];return null!=h?h:new _.$(this.client,m)},m.updateObservers=function updateObservers(c){var m=this;g.j.batch(function(){var h=m.observers,y=m.findMatchingObservers(m.queries);y.forEach(function(m){return m.observer.setOptions(m.defaultedQueryOptions,c)});var g=y.map(function(c){return c.observer}),_=Object.fromEntries(g.map(function(c){return[c.options.queryHash,c]})),b=g.map(function(c){return c.getCurrentResult()}),E=g.some(function(c,m){return c!==h[m]});(h.length!==g.length||E)&&(m.observers=g,m.observersMap=_,m.result=b,m.hasListeners()&&((0,v.iv)(h,g).forEach(function(c){c.destroy()}),(0,v.iv)(g,h).forEach(function(c){c.subscribe(function(h){m.onUpdate(c,h)})}),m.notify()))})},m.onUpdate=function onUpdate(c,m){var h=this.observers.indexOf(c);-1!==h&&(this.result=(0,v._D)(this.result,h,m),this.notify())},m.notify=function notify(){var c=this;g.j.batch(function(){c.listeners.forEach(function(m){m(c.result)})})},QueriesObserver}(h(13411).Q)},89994:(c,m,h)=>{"use strict";h.r(m);var y=h(632),v={};for(const c in y)"default"!==c&&(v[c]=()=>y[c]);h.d(m,v);var g=h(8882);v={};for(const c in g)["default","CancelledError","QueryCache","QueryClient","QueryObserver","QueriesObserver","InfiniteQueryObserver","MutationCache","MutationObserver","setLogger","notifyManager","focusManager","onlineManager","hashQueryKey","isError","isCancelledError","dehydrate","hydrate"].indexOf(c)<0&&(v[c]=()=>g[c]);h.d(m,v)},90878:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Table;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(85707)),E=v(h(18821)),P=h(12456),C=h(79397),w=v(h(21364)),S=v(h(73587)),T=v(h(5299)),N=v(h(47819)),D=v(h(82346));function Table(c){var m=c.className,h=c.initialSelected,y=c.initialDisabled,v=c.selection,g=c.children,w=c.onSelect,S=(0,_.useState)(h),T=(0,E.default)(S,2),N=T[0],D=T[1],A=(0,_.useState)(y),W=(0,E.default)(A,2),q=W[0],U=W[1],Q="eps-table",K=[Q,(0,b.default)({},Q+"--selection",v),m];return(0,_.useEffect)(function(){w&&w(N)},[N]),_.default.createElement(P.Context.Provider,{value:{selected:N,setSelected:D,disabled:q,setDisabled:U}},_.default.createElement("table",{className:(0,C.arrayToClassName)(K)},v&&_.default.createElement("colgroup",null,_.default.createElement("col",{className:Q+"__checkboxes-column"})),g))}h(7248),Table.Head=w.default,Table.Body=S.default,Table.Row=T.default,Table.Cell=N.default,Table.Checkbox=D.default,Table.propTypes={children:y.any.isRequired,className:y.string,headers:y.array,initialDisabled:y.array,initialSelected:y.array,rows:y.array,selection:y.bool,onSelect:y.func},Table.defaultProps={selection:!1,initialDisabled:[],initialSelected:[]}},91071:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ActionsFooter;var g=v(h(41594)),_=v(h(56757));function ActionsFooter(c){return g.default.createElement(_.default,{separator:!0,justify:"end",className:c.className},c.children)}ActionsFooter.propTypes={children:y.any,className:y.string}},91270:c=>{function _setPrototypeOf(m,h){return c.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(c,m){return c.__proto__=m,c},c.exports.__esModule=!0,c.exports.default=c.exports,_setPrototypeOf(m,h)}c.exports=_setPrototypeOf,c.exports.__esModule=!0,c.exports.default=c.exports},91618:()=>{},91669:(c,m,h)=>{"use strict";h.d(m,{m:()=>_});var y=h(59994),v=h(13411),g=h(6369),_=new(function(c){function FocusManager(){var m;return(m=c.call(this)||this).setup=function(c){var m;if(!g.S$&&(null==(m=window)?void 0:m.addEventListener)){var h=function listener(){return c()};return window.addEventListener("visibilitychange",h,!1),window.addEventListener("focus",h,!1),function(){window.removeEventListener("visibilitychange",h),window.removeEventListener("focus",h)}}},m}(0,y.A)(FocusManager,c);var m=FocusManager.prototype;return m.onSubscribe=function onSubscribe(){this.cleanup||this.setEventListener(this.setup)},m.onUnsubscribe=function onUnsubscribe(){var c;this.hasListeners()||(null==(c=this.cleanup)||c.call(this),this.cleanup=void 0)},m.setEventListener=function setEventListener(c){var m,h=this;this.setup=c,null==(m=this.cleanup)||m.call(this),this.cleanup=c(function(c){"boolean"==typeof c?h.setFocused(c):h.onFocus()})},m.setFocused=function setFocused(c){this.focused=c,c&&this.onFocus()},m.onFocus=function onFocus(){this.listeners.forEach(function(c){c()})},m.isFocused=function isFocused(){return"boolean"==typeof this.focused?this.focused:"undefined"==typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},FocusManager}(v.Q))},91819:(c,m,h)=>{var y=h(78113);c.exports=function _arrayWithoutHoles(c){if(Array.isArray(c))return y(c)},c.exports.__esModule=!0,c.exports.default=c.exports},91829:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784),g=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=ExportPluginsFooter;var _=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,_,b={__proto__:null,default:c};if(null===c||"object"!=g(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((_=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(_.get||_.set)?v(b,E,_):b[E]=c[E]);return b}(c,m)}(h(41594)),b=v(h(62688)),E=h(81160),P=h(14300),C=v(h(94026)),w=v(h(30242)),S=v(h(91071)),T=v(h(47483));function ExportPluginsFooter(c){var m=c.isKitReady,h=(0,_.useContext)(E.ExportContext),v=(0,w.default)(),g=v.isConnected,b=v.isConnecting,N=v.setConnecting,D=v.handleConnectSuccess,A=v.handleConnectError,W=(0,_.useRef)(),q=(0,C.default)({enabled:g}),U=q.data,Q=q.isLoading,K=q.refetch,H=(null==U?void 0:U.is_eligible)||!1;(0,_.useEffect)(function(){W.current&&jQuery(W.current).elementorConnect({popup:{width:600,height:700},success:function success(){D(),N(!0),K()},error:function error(){A()}})},[D,A,N,K]),(0,_.useEffect)(function(){b&&!Q&&(H?(h.dispatch({type:"SET_KIT_SAVE_SOURCE",payload:P.KIT_SOURCE_MAP.CLOUD}),window.location.href=elementorAppConfig.base_url+"#/export/process"):window.location.href=elementorAppConfig.base_url+"#/kit-library/cloud")},[b,Q,H,h]),(0,_.useEffect)(function(){b&&!Q&&N(!1)},[b,Q,N]);var G=function handleUpgradeClick(){window.location.href=elementorAppConfig.base_url+"#/kit-library/cloud"},V=function handleUploadClick(){h.dispatch({type:"SET_KIT_SAVE_SOURCE",payload:P.KIT_SOURCE_MAP.CLOUD})};return _.default.createElement(S.default,{className:"e-app-export-actions-container"},_.default.createElement(T.default,{text:y("Back","elementor"),variant:"contained",url:"/export"}),function renderCloudButton(){var c;return g?b||Q?_.default.createElement(T.default,{variant:"outlined",color:"secondary",icon:"eicon-loading eicon-animation-spin"}):H?_.default.createElement(T.default,{text:y("Save to library","elementor"),variant:"outlined",color:"secondary",url:"/export/process",onClick:V}):_.default.createElement(T.default,{text:y("Save to library","elementor"),variant:"outlined",color:"secondary",onClick:G}):_.default.createElement(T.default,{elRef:W,text:y("Save to library","elementor"),variant:"outlined",color:"secondary",url:(null===(c=elementorAppConfig)||void 0===c||null===(c=c["cloud-library"])||void 0===c||null===(c=c.library_connect_url)||void 0===c?void 0:c.replace(/&#038;/g,"&"))||"#"})}(),_.default.createElement(T.default,{text:y("Export as .zip","elementor"),variant:"contained",color:m&&!Q?"primary":"disabled",url:m&&!Q?"/export/process":"",hideText:Q,icon:Q?"eicon-loading eicon-animation-spin":"",onClick:function onClick(){h.dispatch({type:"SET_KIT_SAVE_SOURCE",payload:P.KIT_SOURCE_MAP.FILE})}}))}h(63523),ExportPluginsFooter.propTypes={isKitReady:b.default.bool.isRequired}},91976:()=>{},93279:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=List;var g=v(h(41594)),_=h(79397),b=v(h(45735));function List(c){var m,h="eps-list",y=[h,c.className];return Object.prototype.hasOwnProperty.call(c,"padding")&&(m={"--eps-list-padding":(0,_.pxToRem)(c.padding)},y.push(h+"--padding")),c.separated&&y.push(h+"--separated"),g.default.createElement("ul",{style:m,className:(0,_.arrayToClassName)(y)},c.children)}h(99835),List.propTypes={className:y.string,divided:y.any,separated:y.any,padding:y.string,children:y.oneOfType([y.object,y.arrayOf(y.object)]).isRequired},List.defaultProps={className:""},List.Item=b.default},93696:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),Object.defineProperty(m,"default",{enumerable:!0,get:function get(){return v.default}});var v=y(h(9952))},94010:()=>{},94026:(c,m,h)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.KEY=void 0,m.default=function useCloudKitsEligibility(){var c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,y.useQuery)([g],v.fetchCloudKitsEligibility,c)};var y=h(89994),v=h(23587),g=m.KEY="cloud-kits-availability"},94823:(c,m)=>{"use strict";Object.defineProperty(m,"__esModule",{value:!0}),m.default=function isValidRedirectUrl(c){try{var m=new URL(c);return m.hostname===window.location.hostname&&("http:"===m.protocol||"https:"===m.protocol)}catch(c){return!1}}},95239:(c,m,h)=>{"use strict";var y=h(8118),v=console;(0,y.B)(v)},95315:c=>{c.exports=function _regeneratorKeys(c){var m=Object(c),h=[];for(var y in m)h.unshift(y);return function e(){for(;h.length;)if((y=h.pop())in m)return e.value=y,e.done=!1,e;return e.done=!0,e}},c.exports.__esModule=!0,c.exports.default=c.exports},95689:()=>{},95799:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=PanelHeadline;var g=v(h(41594)),_=h(79397),b=v(h(35676));function PanelHeadline(c){return g.default.createElement(b.default.Headline,{className:(0,_.arrayToClassName)(["eps-panel__headline",c.className])},c.children)}PanelHeadline.propTypes={className:y.string,children:y.any.isRequired},PanelHeadline.defaultProps={className:""}},95801:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=MessageBanner;var g=v(h(41594)),_=v(h(85418)),b=v(h(55725)),E=v(h(21689)),P=v(h(3416));function MessageBanner(c){var m=c.heading,h=c.description,y=c.button;return g.default.createElement(E.default,{className:"e-app-import-export-message-banner",padding:"20"},g.default.createElement(P.default,{container:!0,alignItems:"center",justify:"space-between"},g.default.createElement(P.default,{item:!0},m&&g.default.createElement(_.default,{className:"e-app-import-export-message-banner__heading",variant:"h3",tag:"h3"},m),h&&g.default.createElement(b.default,{className:"e-app-import-export-message-banner__description"},function getDescriptionContent(){return Array.isArray(h)?h.join(g.default.createElement("br",null)):h}())),y&&g.default.createElement(P.default,{item:!0},y)))}h(14495),MessageBanner.propTypes={heading:y.string,description:y.oneOfType([y.string,y.array]),button:y.object}},96784:c=>{c.exports=function _interopRequireDefault(c){return c&&c.__esModule?c:{default:c}},c.exports.__esModule=!0,c.exports.default=c.exports},97088:()=>{},97295:()=>{},97769:(c,m,h)=>{"use strict";var y=h(62688),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=Included;var g=v(h(41594)),_=v(h(55725));function Included(c){var m=c.data;return g.default.createElement(_.default,{className:"e-app-import-export-kit-data__included"},m.filter(function(c){return c}).join(" | "))}Included.propTypes={data:y.array}},98092:(c,m,h)=>{"use strict";var y=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=TopBar;var v=y(h(41594)),g=y(h(78304)),_=y(h(85707)),b=y(h(40453)),E=h(86956),P=y(h(62688)),C=["children","sx"];function ownKeys(c,m){var h=Object.keys(c);if(Object.getOwnPropertySymbols){var y=Object.getOwnPropertySymbols(c);m&&(y=y.filter(function(m){return Object.getOwnPropertyDescriptor(c,m).enumerable})),h.push.apply(h,y)}return h}function _objectSpread(c){for(var m=1;m<arguments.length;m++){var h=null!=arguments[m]?arguments[m]:{};m%2?ownKeys(Object(h),!0).forEach(function(m){(0,_.default)(c,m,h[m])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(h)):ownKeys(Object(h)).forEach(function(m){Object.defineProperty(c,m,Object.getOwnPropertyDescriptor(h,m))})}return c}function TopBar(c){var m=c.children,h=c.sx,y=void 0===h?{}:h,_=(0,b.default)(c,C);return v.default.createElement(v.default.Fragment,null,v.default.createElement(E.AppBar,(0,g.default)({position:"static",elevation:0,sx:_objectSpread({backgroundColor:"background.default"},y)},_),v.default.createElement(E.Toolbar,{sx:{minHeight:{xs:56,sm:64},px:3,justifyContent:"space-between"}},m)),v.default.createElement(E.Divider,null))}TopBar.propTypes={children:P.default.node,sx:P.default.object}},98177:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=function KitContent(){var c=(0,E.useExportContext)(),m=c.data,h=c.dispatch;return g.default.createElement(_.Stack,{spacing:2},b.default.map(function(c){return g.default.createElement(_.Box,{key:c.type,sx:{mb:3,border:1,borderRadius:1,borderColor:"action.focus",p:2.5}},g.default.createElement(_.Box,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"}},g.default.createElement(_.Box,{sx:{flex:1}},g.default.createElement(_.FormControlLabel,{control:g.default.createElement(_.Checkbox,{checked:m.includes.includes(c.type),onChange:function onChange(){return function handleCheckboxChange(c){var y=m.includes.includes(c);h({type:y?"REMOVE_INCLUDE":"ADD_INCLUDE",payload:c})}(c.type)},sx:{py:0}}),label:g.default.createElement(_.Typography,{variant:"body1",sx:{fontWeight:500}},c.data.title)}),g.default.createElement(_.Typography,{variant:"body2",color:"text.secondary",sx:{mt:1,ml:4}},c.data.features.open.join(", "))),g.default.createElement(_.Link,{href:"#",sx:{alignSelf:"center"}},y("Edit","elementor"))))}))};var g=v(h(41594)),_=h(86956),b=v(h(19616)),E=h(72946)},98353:(c,m,h)=>{"use strict";h.d(m,{C:()=>b,L:()=>useHydrate});var y=h(41594),v=h.n(y),g=h(45206),_=h(15292);function useHydrate(c,m){var h=(0,_.j)(),y=v().useRef(m);y.current=m,v().useMemo(function(){c&&(0,g.Q)(h,c,y.current)},[h,c])}var b=function Hydrate(c){var m=c.children,h=c.options;return useHydrate(c.state,h),m}},98718:(c,m,h)=>{"use strict";var y=h(12470).__,v=h(62688),g=h(96784),_=h(10564);Object.defineProperty(m,"__esModule",{value:!0}),m.default=UploadFile;var b=function _interopRequireWildcard(c,m){if("function"==typeof WeakMap)var h=new WeakMap,y=new WeakMap;return function _interopRequireWildcard(c,m){if(!m&&c&&c.__esModule)return c;var v,g,b={__proto__:null,default:c};if(null===c||"object"!=_(c)&&"function"!=typeof c)return b;if(v=m?y:h){if(v.has(c))return v.get(c);v.set(c,b)}for(var E in c)"default"!==E&&{}.hasOwnProperty.call(c,E)&&((g=(v=Object.defineProperty)&&Object.getOwnPropertyDescriptor(c,E))&&(g.get||g.set)?v(b,E,g):b[E]=c[E]);return b}(c,m)}(h(41594)),E=g(h(47483)),P=h(79397);function UploadFile(c){var m,h=(0,b.useRef)(null),v=["e-app-upload-file",c.className];return b.default.createElement("div",{className:(0,P.arrayToClassName)(v)},b.default.createElement("input",{ref:h,type:"file",accept:c.filetypes.map(function(c){return"."+c}).join(", "),className:"e-app-upload-file__input",onChange:function onChange(m){var v=m.target.files[0];v&&(0,P.isOneOf)(v.type,c.filetypes)?c.onFileSelect(v,m,"browse"):(h.current.value="",c.onError({id:"file_not_allowed",message:y("This file type is not allowed","elementor")}))}}),b.default.createElement(E.default,{className:"e-app-upload-file__button",text:c.text,variant:c.variant,color:c.color,size:"lg",hideText:c.isLoading,icon:c.isLoading?"eicon-loading eicon-animation-spin":"",onClick:function onClick(){if(c.onFileChoose&&c.onFileChoose(),!c.isLoading)if(c.onButtonClick&&c.onButtonClick(),"file-explorer"===c.type)h.current.click();else if("wp-media"===c.type){if(m)return void m.open();(m=wp.media({multiple:!1,library:{type:["image","image/svg+xml"]}})).on("select",function(){c.onWpMediaSelect&&c.onWpMediaSelect(m)}),m.open()}}}))}h(14546),UploadFile.propTypes={className:v.string,type:v.string,onWpMediaSelect:v.func,text:v.string,onFileSelect:v.func,isLoading:v.bool,filetypes:v.array.isRequired,onError:v.func,variant:v.string,color:v.string,onButtonClick:v.func,onFileChoose:v.func},UploadFile.defaultProps={className:"",type:"file-explorer",text:y("Select File","elementor"),onError:function onError(){},variant:"contained",color:"primary"}},99293:(c,m,h)=>{"use strict";var y=h(41594),v=h(96784);Object.defineProperty(m,"__esModule",{value:!0}),m.default=void 0;var g=v(h(39805)),_=v(h(40989)),b=v(h(81069)),E=v(h(47485)),P=v(h(73139)),C=v(h(72696)),w=v(h(36625));m.default=function(){return(0,_.default)(function KitLibrary(){(0,g.default)(this,KitLibrary),this.hasAccessToModule()&&($e.components.register(new b.default),$e.components.register(new P.default),$e.components.register(new C.default),$e.components.register(new w.default),E.default.addRoute({path:"/kit-library/*",component:y.lazy(function(){return h.e(435).then(h.bind(h,67822))})}))},[{key:"hasAccessToModule",value:function hasAccessToModule(){var c;return null===(c=elementorAppConfig["kit-library"])||void 0===c?void 0:c.has_access_to_module}}])}()},99835:()=>{}},y={};function __webpack_require__(c){var m=y[c];if(void 0!==m)return m.exports;var v=y[c]={exports:{}};return h[c](v,v.exports,__webpack_require__),v.exports}__webpack_require__.m=h,__webpack_require__.n=c=>{var m=c&&c.__esModule?()=>c.default:()=>c;return __webpack_require__.d(m,{a:m}),m},__webpack_require__.d=(c,m)=>{for(var h in m)__webpack_require__.o(m,h)&&!__webpack_require__.o(c,h)&&Object.defineProperty(c,h,{enumerable:!0,get:m[h]})},__webpack_require__.f={},__webpack_require__.e=c=>Promise.all(Object.keys(__webpack_require__.f).reduce((m,h)=>(__webpack_require__.f[h](c,m),m),[])),__webpack_require__.u=c=>435===c?"kit-library.2b8b21c9592ab21984d3.bundle.min.js":1352===c?"onboarding.e14eaa7b001113fe8bea.bundle.min.js":void 0,__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(c){if("object"==typeof window)return window}}(),__webpack_require__.o=(c,m)=>Object.prototype.hasOwnProperty.call(c,m),c={},m="elementor:",__webpack_require__.l=(h,y,v,g)=>{if(c[h])c[h].push(y);else{var _,b;if(void 0!==v)for(var E=document.getElementsByTagName("script"),P=0;P<E.length;P++){var C=E[P];if(C.getAttribute("src")==h||C.getAttribute("data-webpack")==m+v){_=C;break}}_||(b=!0,(_=document.createElement("script")).charset="utf-8",_.timeout=120,__webpack_require__.nc&&_.setAttribute("nonce",__webpack_require__.nc),_.setAttribute("data-webpack",m+v),_.src=h),c[h]=[y];var onScriptComplete=(m,y)=>{_.onerror=_.onload=null,clearTimeout(w);var v=c[h];if(delete c[h],_.parentNode&&_.parentNode.removeChild(_),v&&v.forEach(c=>c(y)),m)return m(y)},w=setTimeout(onScriptComplete.bind(null,void 0,{type:"timeout",target:_}),12e4);_.onerror=onScriptComplete.bind(null,_.onerror),_.onload=onScriptComplete.bind(null,_.onload),b&&document.head.appendChild(_)}},__webpack_require__.r=c=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},(()=>{var c;__webpack_require__.g.importScripts&&(c=__webpack_require__.g.location+"");var m=__webpack_require__.g.document;if(!c&&m&&(m.currentScript&&"SCRIPT"===m.currentScript.tagName.toUpperCase()&&(c=m.currentScript.src),!c)){var h=m.getElementsByTagName("script");if(h.length)for(var y=h.length-1;y>-1&&(!c||!/^http(s?):/.test(c));)c=h[y--].src}if(!c)throw new Error("Automatic publicPath is not supported in this browser");c=c.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),__webpack_require__.p=c})(),(()=>{var c={1414:0};__webpack_require__.f.j=(m,h)=>{var y=__webpack_require__.o(c,m)?c[m]:void 0;if(0!==y)if(y)h.push(y[2]);else{var v=new Promise((h,v)=>y=c[m]=[h,v]);h.push(y[2]=v);var g=__webpack_require__.p+__webpack_require__.u(m),_=new Error;__webpack_require__.l(g,h=>{if(__webpack_require__.o(c,m)&&(0!==(y=c[m])&&(c[m]=void 0),y)){var v=h&&("load"===h.type?"missing":h.type),g=h&&h.target&&h.target.src;_.message="Loading chunk "+m+" failed.\n("+v+": "+g+")",_.name="ChunkLoadError",_.type=v,_.request=g,y[1](_)}},"chunk-"+m,m)}};var webpackJsonpCallback=(m,h)=>{var y,v,[g,_,b]=h,E=0;if(g.some(m=>0!==c[m])){for(y in _)__webpack_require__.o(_,y)&&(__webpack_require__.m[y]=_[y]);if(b)b(__webpack_require__)}for(m&&m(h);E<g.length;E++)v=g[E],__webpack_require__.o(c,v)&&c[v]&&c[v][0](),c[v]=0},m=self.webpackChunkelementor=self.webpackChunkelementor||[];m.forEach(webpackJsonpCallback.bind(null,0)),m.push=webpackJsonpCallback.bind(null,m.push.bind(m))})(),__webpack_require__.nc=void 0,(()=>{"use strict";var c,m=__webpack_require__(96784),h=m(__webpack_require__(41594)),y=m(__webpack_require__(18791)),v=m(__webpack_require__(38761)),g=m(__webpack_require__(5853)),_=m(__webpack_require__(75001)),b=m(__webpack_require__(99293)),E=m(__webpack_require__(84686)),P=__webpack_require__(40858),C=m(__webpack_require__(64095));new g.default,new b.default,new P.Module,new E.default,null!==(c=elementorCommon)&&void 0!==c&&null!==(c=c.config)&&void 0!==c&&null!==(c=c.experimentalFeatures)&&void 0!==c&&c["import-export-customization"]&&new _.default;var w=h.default.Fragment;y.default.render(h.default.createElement(w,null,h.default.createElement(C.default,null,h.default.createElement(v.default,null))),document.getElementById("e-app"))})()})();