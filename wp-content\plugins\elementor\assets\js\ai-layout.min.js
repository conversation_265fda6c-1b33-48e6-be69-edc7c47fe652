/*! elementor - v3.31.0 - 27-08-2025 */
/*! For license information please see ai-layout.min.js.LICENSE.txt */
(()=>{var l={3468:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=s(95034),m=c(s(48812));u.default=function useLayoutPrompt(l,u){return(0,m.default)(function(u,s){return u.variationType=l,(0,p.generateLayout)(u,s)},u)}},4353:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.USER_VARIATION_SOURCE=u.USER_URL_SOURCE=u.MENU_TYPE_LIBRARY=u.ELEMENTOR_LIBRARY_SOURCE=u.ATTACHMENT_TYPE_URL=u.ATTACHMENT_TYPE_JSON=void 0;var p=c(s(41594)),m=c(s(78304)),g=s(60992),h=c(s(25553)),y=c(s(45286)),v=c(s(68627)),_=c(s(65141)),b=s(12470),w=c(s(62688)),C=s(38298),x=s(86956),P=u.ATTACHMENT_TYPE_JSON="json",E=u.ATTACHMENT_TYPE_URL="url",O=u.MENU_TYPE_LIBRARY="library",S=(u.USER_VARIATION_SOURCE="user-variation",u.ELEMENTOR_LIBRARY_SOURCE="elementor-library",u.USER_URL_SOURCE="user-url",function Attachments(l){return l.attachments.length?p.default.createElement(x.Stack,{direction:"row",spacing:1},l.attachments.map(function(u,s){switch(u.type){case P:return p.default.createElement(h.default,(0,m.default)({key:s},l));case E:return p.default.createElement(y.default,(0,m.default)({key:s},l));default:return null}})):p.default.createElement(g.Menu,{disabled:l.disabled,onAttach:l.onAttach,items:[{title:(0,b.__)("Reference a website","elementor"),icon:v.default,type:E},{title:(0,b.__)("Create variations from Template Library","elementor"),icon:_.default,type:O}]})});S.propTypes={attachments:w.default.arrayOf(C.AttachmentPropType).isRequired,onAttach:w.default.func.isRequired,onDetach:w.default.func,disabled:w.default.bool};u.default=S},4508:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(78304)),g=c(s(85707)),h=c(s(40453)),y=s(86956),v=c(s(62688)),_=c(s(99476)),b=["sx"];function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,g.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var w=function WizardDialog(l){return p.default.createElement(y.Dialog,{open:!0,onClose:l.onClose,fullWidth:!0,hideBackdrop:!0,maxWidth:"lg",PaperProps:{sx:{height:"88vh"}},sx:{zIndex:9999}},l.children)};w.propTypes={onClose:v.default.func.isRequired,children:v.default.node.isRequired};var C=function WizardDialogContent(l){var u=l.sx,s=void 0===u?{}:u,c=(0,h.default)(l,b);return p.default.createElement(y.DialogContent,(0,m.default)({},c,{sx:_objectSpread({display:"flex",flexDirection:"column",justifyContent:"center"},s)}))};C.propTypes={sx:v.default.object},w.Header=_.default,w.Content=C;u.default=w},4974:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=c(s(40453)),y=c(s(78304)),v=c(s(85707)),_=s(86956),b=c(s(62688)),w=s(12470),C=c(s(53497)),x=s(91258),P=["onSubmit"];function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,v.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var E=(0,m.forwardRef)(function(l,u){return m.default.createElement(_.TextField,(0,y.default)({autoFocus:!0,multiline:!0,size:"small",maxRows:3,color:"secondary",variant:"standard"},l,{inputRef:u,InputProps:_objectSpread(_objectSpread({},l.InputProps),{},{type:"search",sx:{pt:0}})}))});E.propTypes={InputProps:b.default.object};var O=function PaperComponent(l){var u=(0,x.useConfig)().mode,s=x.MODE_VARIATION===u?"https://go.elementor.com/ai-prompt-library-variations/":"https://go.elementor.com/ai-prompt-library-containers/";return m.default.createElement(_.Paper,(0,y.default)({},l,{elevation:8,sx:{borderRadius:2}}),m.default.createElement(_.Typography,{component:_.Box,color:function color(l){return l.palette.text.tertiary},variant:"caption",paddingX:2,paddingY:1},(0,w.__)("Suggested Prompts","elementor")),m.default.createElement(_.Divider,null),l.children,m.default.createElement(_.Stack,{sx:{m:2}},m.default.createElement(C.default,{libraryLink:s})))};O.propTypes={children:b.default.node};var S=function PromptAutocomplete(l){var u=l.onSubmit,s=(0,h.default)(l,P),c=(0,m.useState)(!1),p=(0,g.default)(c,2),v=p[0],b=p[1],w=(0,_.useTheme)(),C=parseInt(w.spacing(4));return m.default.createElement(_.Autocomplete,(0,y.default)({PaperComponent:O,ListboxProps:{sx:{maxHeight:5*C}},renderOption:function renderOption(l,u){return m.default.createElement(_.Typography,(0,y.default)({},l,{title:u.text,noWrap:!0,variant:"body2",component:_.Box,sx:{"&.MuiAutocomplete-option":{display:"block",minHeight:C}}}),u.text)},freeSolo:!0,fullWidth:!0,disableClearable:!0,open:v,onClose:function onClose(l){var u;return b("A"===(null===(u=l.relatedTarget)||void 0===u?void 0:u.tagName))},onKeyDown:function onKeyDown(l){"Enter"!==l.key||l.shiftKey||v?"/"===l.key&&""===l.target.value&&(l.preventDefault(),b(!0)):u(l)}},s))};S.propTypes={onSubmit:b.default.func.isRequired},S.TextInput=E;u.default=S},7470:(l,u,s)=>{"use strict";var c=s(75206);u.createRoot=c.createRoot,u.hydrateRoot=c.hydrateRoot},8299:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=s(86956),h=s(12470),y=c(s(62688)),v=s(44048);var _=function Connect(l){var u=l.connectUrl,s=l.onSuccess,c=(0,m.useRef)();return(0,m.useEffect)(function(){jQuery.fn.elementorConnect&&jQuery(c.current).elementorConnect({success:function success(l,u){return s(u)},error:function error(){throw new Error("Elementor AI: Failed to connect.")}})},[]),m.default.createElement(g.Stack,{alignItems:"center",gap:2},m.default.createElement(v.AIIcon,{sx:{color:"text.primary",fontSize:"60px",mb:1}}),m.default.createElement(g.Typography,{variant:"h4",sx:{color:"text.primary"}},(0,h.__)("Step into the future with Elementor AI","elementor")),m.default.createElement(g.Typography,{variant:"body2"},(0,h.__)("Create smarter with AI text and code generators built right into the editor.","elementor")),m.default.createElement(g.Typography,{variant:"caption",sx:{maxWidth:520,textAlign:"center"}},(0,h.__)('By clicking "Connect", I approve the ',"elementor"),m.default.createElement(g.Link,{href:"https://go.elementor.com/ai-terms/",target:"_blank",color:"info.main"},(0,h.__)("Terms of Service","elementor"))," & ",m.default.createElement(g.Link,{href:"https://go.elementor.com/ai-privacy-policy/",target:"_blank",color:"info.main"},(0,h.__)("Privacy Policy","elementor")),(0,h.__)(" of the Elementor AI service.","elementor")),m.default.createElement(g.Button,{ref:c,href:u,variant:"contained",sx:{mt:1,"&:hover":{color:"primary.contrastText"}}},(0,h.__)("Connect","elementor")))};_.propTypes={connectUrl:y.default.string.isRequired,onSuccess:y.default.func.isRequired};u.default=_},9111:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),Object.defineProperty(u,"DraggableCore",{enumerable:!0,get:function(){return _.default}}),u.default=void 0;var c=function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;if(null===l||"object"!=typeof l&&"function"!=typeof l)return{default:l};var s=_getRequireWildcardCache(u);if(s&&s.has(l))return s.get(l);var c={},p=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var m in l)if("default"!==m&&Object.prototype.hasOwnProperty.call(l,m)){var g=p?Object.getOwnPropertyDescriptor(l,m):null;g&&(g.get||g.set)?Object.defineProperty(c,m,g):c[m]=l[m]}c.default=l,s&&s.set(l,c);return c}(s(41594)),p=_interopRequireDefault(s(62688)),m=_interopRequireDefault(s(75206)),g=_interopRequireDefault(s(38262)),h=s(32837),y=s(10402),v=s(26732),_=_interopRequireDefault(s(11060)),b=_interopRequireDefault(s(57988));function _interopRequireDefault(l){return l&&l.__esModule?l:{default:l}}function _getRequireWildcardCache(l){if("function"!=typeof WeakMap)return null;var u=new WeakMap,s=new WeakMap;return(_getRequireWildcardCache=function(l){return l?s:u})(l)}function _extends(){return _extends=Object.assign?Object.assign.bind():function(l){for(var u=1;u<arguments.length;u++){var s=arguments[u];for(var c in s)Object.prototype.hasOwnProperty.call(s,c)&&(l[c]=s[c])}return l},_extends.apply(this,arguments)}function _defineProperty(l,u,s){return(u=function _toPropertyKey(l){var u=function _toPrimitive(l,u){if("object"!=typeof l||null===l)return l;var s=l[Symbol.toPrimitive];if(void 0!==s){var c=s.call(l,u||"default");if("object"!=typeof c)return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(l)}(l,"string");return"symbol"==typeof u?u:String(u)}(u))in l?Object.defineProperty(l,u,{value:s,enumerable:!0,configurable:!0,writable:!0}):l[u]=s,l}class Draggable extends c.Component{static getDerivedStateFromProps(l,u){let{position:s}=l,{prevPropsPosition:c}=u;return!s||c&&s.x===c.x&&s.y===c.y?null:((0,b.default)("Draggable: getDerivedStateFromProps %j",{position:s,prevPropsPosition:c}),{x:s.x,y:s.y,prevPropsPosition:{...s}})}constructor(l){super(l),_defineProperty(this,"onDragStart",(l,u)=>{(0,b.default)("Draggable: onDragStart: %j",u);if(!1===this.props.onStart(l,(0,y.createDraggableData)(this,u)))return!1;this.setState({dragging:!0,dragged:!0})}),_defineProperty(this,"onDrag",(l,u)=>{if(!this.state.dragging)return!1;(0,b.default)("Draggable: onDrag: %j",u);const s=(0,y.createDraggableData)(this,u),c={x:s.x,y:s.y,slackX:0,slackY:0};if(this.props.bounds){const{x:l,y:u}=c;c.x+=this.state.slackX,c.y+=this.state.slackY;const[p,m]=(0,y.getBoundPosition)(this,c.x,c.y);c.x=p,c.y=m,c.slackX=this.state.slackX+(l-c.x),c.slackY=this.state.slackY+(u-c.y),s.x=c.x,s.y=c.y,s.deltaX=c.x-this.state.x,s.deltaY=c.y-this.state.y}if(!1===this.props.onDrag(l,s))return!1;this.setState(c)}),_defineProperty(this,"onDragStop",(l,u)=>{if(!this.state.dragging)return!1;if(!1===this.props.onStop(l,(0,y.createDraggableData)(this,u)))return!1;(0,b.default)("Draggable: onDragStop: %j",u);const s={dragging:!1,slackX:0,slackY:0};if(Boolean(this.props.position)){const{x:l,y:u}=this.props.position;s.x=l,s.y=u}this.setState(s)}),this.state={dragging:!1,dragged:!1,x:l.position?l.position.x:l.defaultPosition.x,y:l.position?l.position.y:l.defaultPosition.y,prevPropsPosition:{...l.position},slackX:0,slackY:0,isElementSVG:!1},!l.position||l.onDrag||l.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var l,u;return null!==(l=null===(u=this.props)||void 0===u||null===(u=u.nodeRef)||void 0===u?void 0:u.current)&&void 0!==l?l:m.default.findDOMNode(this)}render(){const{axis:l,bounds:u,children:s,defaultPosition:p,defaultClassName:m,defaultClassNameDragging:v,defaultClassNameDragged:b,position:w,positionOffset:C,scale:x,...P}=this.props;let E={},O=null;const S=!Boolean(w)||this.state.dragging,T=w||p,R={x:(0,y.canDragX)(this)&&S?this.state.x:T.x,y:(0,y.canDragY)(this)&&S?this.state.y:T.y};this.state.isElementSVG?O=(0,h.createSVGTransform)(R,C):E=(0,h.createCSSTransform)(R,C);const j=(0,g.default)(s.props.className||"",m,{[v]:this.state.dragging,[b]:this.state.dragged});return c.createElement(_.default,_extends({},P,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),c.cloneElement(c.Children.only(s),{className:j,style:{...s.props.style,...E},transform:O}))}}u.default=Draggable,_defineProperty(Draggable,"displayName","Draggable"),_defineProperty(Draggable,"propTypes",{..._.default.propTypes,axis:p.default.oneOf(["both","x","y","none"]),bounds:p.default.oneOfType([p.default.shape({left:p.default.number,right:p.default.number,top:p.default.number,bottom:p.default.number}),p.default.string,p.default.oneOf([!1])]),defaultClassName:p.default.string,defaultClassNameDragging:p.default.string,defaultClassNameDragged:p.default.string,defaultPosition:p.default.shape({x:p.default.number,y:p.default.number}),positionOffset:p.default.shape({x:p.default.oneOfType([p.default.number,p.default.string]),y:p.default.oneOfType([p.default.number,p.default.string])}),position:p.default.shape({x:p.default.number,y:p.default.number}),className:v.dontSetMe,style:v.dontSetMe,transform:v.dontSetMe}),_defineProperty(Draggable,"defaultProps",{..._.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})},9535:(l,u,s)=>{var c=s(89736);function _regenerator(){var u,s,p="function"==typeof Symbol?Symbol:{},m=p.iterator||"@@iterator",g=p.toStringTag||"@@toStringTag";function i(l,p,m,g){var y=p&&p.prototype instanceof Generator?p:Generator,v=Object.create(y.prototype);return c(v,"_invoke",function(l,c,p){var m,g,y,v=0,_=p||[],b=!1,w={p:0,n:0,v:u,a:d,f:d.bind(u,4),d:function d(l,s){return m=l,g=0,y=u,w.n=s,h}};function d(l,c){for(g=l,y=c,s=0;!b&&v&&!p&&s<_.length;s++){var p,m=_[s],C=w.p,x=m[2];l>3?(p=x===c)&&(y=m[(g=m[4])?5:(g=3,3)],m[4]=m[5]=u):m[0]<=C&&((p=l<2&&C<m[1])?(g=0,w.v=c,w.n=m[1]):C<x&&(p=l<3||m[0]>c||c>x)&&(m[4]=l,m[5]=c,w.n=x,g=0))}if(p||l>1)return h;throw b=!0,c}return function(p,_,C){if(v>1)throw TypeError("Generator is already running");for(b&&1===_&&d(_,C),g=_,y=C;(s=g<2?u:y)||!b;){m||(g?g<3?(g>1&&(w.n=-1),d(g,y)):w.n=y:w.v=y);try{if(v=2,m){if(g||(p="next"),s=m[p]){if(!(s=s.call(m,y)))throw TypeError("iterator result is not an object");if(!s.done)return s;y=s.value,g<2&&(g=0)}else 1===g&&(s=m.return)&&s.call(m),g<2&&(y=TypeError("The iterator does not provide a '"+p+"' method"),g=1);m=u}else if((s=(b=w.n<0)?y:l.call(c,w))!==h)break}catch(l){m=u,g=1,y=l}finally{v=1}}return{value:s,done:b}}}(l,m,g),!0),v}var h={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}s=Object.getPrototypeOf;var y=[][m]?s(s([][m]())):(c(s={},m,function(){return this}),s),v=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(y);function f(l){return Object.setPrototypeOf?Object.setPrototypeOf(l,GeneratorFunctionPrototype):(l.__proto__=GeneratorFunctionPrototype,c(l,g,"GeneratorFunction")),l.prototype=Object.create(v),l}return GeneratorFunction.prototype=GeneratorFunctionPrototype,c(v,"constructor",GeneratorFunctionPrototype),c(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName="GeneratorFunction",c(GeneratorFunctionPrototype,g,"GeneratorFunction"),c(v),c(v,g,"Generator"),c(v,m,function(){return this}),c(v,"toString",function(){return"[object Generator]"}),(l.exports=_regenerator=function _regenerator(){return{w:i,m:f}},l.exports.__esModule=!0,l.exports.default=l.exports)()}l.exports=_regenerator,l.exports.__esModule=!0,l.exports.default=l.exports},10402:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.canDragX=function canDragX(l){return"both"===l.props.axis||"x"===l.props.axis},u.canDragY=function canDragY(l){return"both"===l.props.axis||"y"===l.props.axis},u.createCoreData=function createCoreData(l,u,s){const p=!(0,c.isNum)(l.lastX),m=findDOMNode(l);return p?{node:m,deltaX:0,deltaY:0,lastX:u,lastY:s,x:u,y:s}:{node:m,deltaX:u-l.lastX,deltaY:s-l.lastY,lastX:l.lastX,lastY:l.lastY,x:u,y:s}},u.createDraggableData=function createDraggableData(l,u){const s=l.props.scale;return{node:u.node,x:l.state.x+u.deltaX/s,y:l.state.y+u.deltaY/s,deltaX:u.deltaX/s,deltaY:u.deltaY/s,lastX:l.state.x,lastY:l.state.y}},u.getBoundPosition=function getBoundPosition(l,u,s){if(!l.props.bounds)return[u,s];let{bounds:m}=l.props;m="string"==typeof m?m:function cloneBounds(l){return{left:l.left,top:l.top,right:l.right,bottom:l.bottom}}(m);const g=findDOMNode(l);if("string"==typeof m){const{ownerDocument:l}=g,u=l.defaultView;let s;if(s="parent"===m?g.parentNode:l.querySelector(m),!(s instanceof u.HTMLElement))throw new Error('Bounds selector "'+m+'" could not find an element.');const h=s,y=u.getComputedStyle(g),v=u.getComputedStyle(h);m={left:-g.offsetLeft+(0,c.int)(v.paddingLeft)+(0,c.int)(y.marginLeft),top:-g.offsetTop+(0,c.int)(v.paddingTop)+(0,c.int)(y.marginTop),right:(0,p.innerWidth)(h)-(0,p.outerWidth)(g)-g.offsetLeft+(0,c.int)(v.paddingRight)-(0,c.int)(y.marginRight),bottom:(0,p.innerHeight)(h)-(0,p.outerHeight)(g)-g.offsetTop+(0,c.int)(v.paddingBottom)-(0,c.int)(y.marginBottom)}}(0,c.isNum)(m.right)&&(u=Math.min(u,m.right));(0,c.isNum)(m.bottom)&&(s=Math.min(s,m.bottom));(0,c.isNum)(m.left)&&(u=Math.max(u,m.left));(0,c.isNum)(m.top)&&(s=Math.max(s,m.top));return[u,s]},u.getControlPosition=function getControlPosition(l,u,s){const c="number"==typeof u?(0,p.getTouch)(l,u):null;if("number"==typeof u&&!c)return null;const m=findDOMNode(s),g=s.props.offsetParent||m.offsetParent||m.ownerDocument.body;return(0,p.offsetXYFromParent)(c||l,g,s.props.scale)},u.snapToGrid=function snapToGrid(l,u,s){const c=Math.round(u/l[0])*l[0],p=Math.round(s/l[1])*l[1];return[c,p]};var c=s(26732),p=s(32837);function findDOMNode(l){const u=l.findDOMNode();if(!u)throw new Error("<DraggableCore>: Unmounted during event!");return u}},10564:l=>{function _typeof(u){return l.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(l){return typeof l}:function(l){return l&&"function"==typeof Symbol&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l},l.exports.__esModule=!0,l.exports.default=l.exports,_typeof(u)}l.exports=_typeof,l.exports.__esModule=!0,l.exports.default=l.exports},10739:l=>{l.exports=function _objectWithoutPropertiesLoose(l,u){if(null==l)return{};var s={};for(var c in l)if({}.hasOwnProperty.call(l,c)){if(-1!==u.indexOf(c))continue;s[c]=l[c]}return s},l.exports.__esModule=!0,l.exports.default=l.exports},10906:(l,u,s)=>{var c=s(91819),p=s(20365),m=s(37744),g=s(78687);l.exports=function _toConsumableArray(l){return c(l)||p(l)||m(l)||g()},l.exports.__esModule=!0,l.exports.default=l.exports},11018:l=>{l.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},l.exports.__esModule=!0,l.exports.default=l.exports},11060:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;if(null===l||"object"!=typeof l&&"function"!=typeof l)return{default:l};var s=_getRequireWildcardCache(u);if(s&&s.has(l))return s.get(l);var c={},p=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var m in l)if("default"!==m&&Object.prototype.hasOwnProperty.call(l,m)){var g=p?Object.getOwnPropertyDescriptor(l,m):null;g&&(g.get||g.set)?Object.defineProperty(c,m,g):c[m]=l[m]}c.default=l,s&&s.set(l,c);return c}(s(41594)),p=_interopRequireDefault(s(62688)),m=_interopRequireDefault(s(75206)),g=s(32837),h=s(10402),y=s(26732),v=_interopRequireDefault(s(57988));function _interopRequireDefault(l){return l&&l.__esModule?l:{default:l}}function _getRequireWildcardCache(l){if("function"!=typeof WeakMap)return null;var u=new WeakMap,s=new WeakMap;return(_getRequireWildcardCache=function(l){return l?s:u})(l)}function _defineProperty(l,u,s){return(u=function _toPropertyKey(l){var u=function _toPrimitive(l,u){if("object"!=typeof l||null===l)return l;var s=l[Symbol.toPrimitive];if(void 0!==s){var c=s.call(l,u||"default");if("object"!=typeof c)return c;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(l)}(l,"string");return"symbol"==typeof u?u:String(u)}(u))in l?Object.defineProperty(l,u,{value:s,enumerable:!0,configurable:!0,writable:!0}):l[u]=s,l}const _={start:"touchstart",move:"touchmove",stop:"touchend"},b={start:"mousedown",move:"mousemove",stop:"mouseup"};let w=b;class DraggableCore extends c.Component{constructor(){super(...arguments),_defineProperty(this,"dragging",!1),_defineProperty(this,"lastX",NaN),_defineProperty(this,"lastY",NaN),_defineProperty(this,"touchIdentifier",null),_defineProperty(this,"mounted",!1),_defineProperty(this,"handleDragStart",l=>{if(this.props.onMouseDown(l),!this.props.allowAnyClick&&"number"==typeof l.button&&0!==l.button)return!1;const u=this.findDOMNode();if(!u||!u.ownerDocument||!u.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:s}=u;if(this.props.disabled||!(l.target instanceof s.defaultView.Node)||this.props.handle&&!(0,g.matchesSelectorAndParentsTo)(l.target,this.props.handle,u)||this.props.cancel&&(0,g.matchesSelectorAndParentsTo)(l.target,this.props.cancel,u))return;"touchstart"===l.type&&l.preventDefault();const c=(0,g.getTouchIdentifier)(l);this.touchIdentifier=c;const p=(0,h.getControlPosition)(l,c,this);if(null==p)return;const{x:m,y}=p,_=(0,h.createCoreData)(this,m,y);(0,v.default)("DraggableCore: handleDragStart: %j",_),(0,v.default)("calling",this.props.onStart);!1!==this.props.onStart(l,_)&&!1!==this.mounted&&(this.props.enableUserSelectHack&&(0,g.addUserSelectStyles)(s),this.dragging=!0,this.lastX=m,this.lastY=y,(0,g.addEvent)(s,w.move,this.handleDrag),(0,g.addEvent)(s,w.stop,this.handleDragStop))}),_defineProperty(this,"handleDrag",l=>{const u=(0,h.getControlPosition)(l,this.touchIdentifier,this);if(null==u)return;let{x:s,y:c}=u;if(Array.isArray(this.props.grid)){let l=s-this.lastX,u=c-this.lastY;if([l,u]=(0,h.snapToGrid)(this.props.grid,l,u),!l&&!u)return;s=this.lastX+l,c=this.lastY+u}const p=(0,h.createCoreData)(this,s,c);(0,v.default)("DraggableCore: handleDrag: %j",p);if(!1!==this.props.onDrag(l,p)&&!1!==this.mounted)this.lastX=s,this.lastY=c;else try{this.handleDragStop(new MouseEvent("mouseup"))}catch(l){const u=document.createEvent("MouseEvents");u.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(u)}}),_defineProperty(this,"handleDragStop",l=>{if(!this.dragging)return;const u=(0,h.getControlPosition)(l,this.touchIdentifier,this);if(null==u)return;let{x:s,y:c}=u;if(Array.isArray(this.props.grid)){let l=s-this.lastX||0,u=c-this.lastY||0;[l,u]=(0,h.snapToGrid)(this.props.grid,l,u),s=this.lastX+l,c=this.lastY+u}const p=(0,h.createCoreData)(this,s,c);if(!1===this.props.onStop(l,p)||!1===this.mounted)return!1;const m=this.findDOMNode();m&&this.props.enableUserSelectHack&&(0,g.removeUserSelectStyles)(m.ownerDocument),(0,v.default)("DraggableCore: handleDragStop: %j",p),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,m&&((0,v.default)("DraggableCore: Removing handlers"),(0,g.removeEvent)(m.ownerDocument,w.move,this.handleDrag),(0,g.removeEvent)(m.ownerDocument,w.stop,this.handleDragStop))}),_defineProperty(this,"onMouseDown",l=>(w=b,this.handleDragStart(l))),_defineProperty(this,"onMouseUp",l=>(w=b,this.handleDragStop(l))),_defineProperty(this,"onTouchStart",l=>(w=_,this.handleDragStart(l))),_defineProperty(this,"onTouchEnd",l=>(w=_,this.handleDragStop(l)))}componentDidMount(){this.mounted=!0;const l=this.findDOMNode();l&&(0,g.addEvent)(l,_.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const l=this.findDOMNode();if(l){const{ownerDocument:u}=l;(0,g.removeEvent)(u,b.move,this.handleDrag),(0,g.removeEvent)(u,_.move,this.handleDrag),(0,g.removeEvent)(u,b.stop,this.handleDragStop),(0,g.removeEvent)(u,_.stop,this.handleDragStop),(0,g.removeEvent)(l,_.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,g.removeUserSelectStyles)(u)}}findDOMNode(){var l,u;return null!==(l=this.props)&&void 0!==l&&l.nodeRef?null===(u=this.props)||void 0===u||null===(u=u.nodeRef)||void 0===u?void 0:u.current:m.default.findDOMNode(this)}render(){return c.cloneElement(c.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}u.default=DraggableCore,_defineProperty(DraggableCore,"displayName","DraggableCore"),_defineProperty(DraggableCore,"propTypes",{allowAnyClick:p.default.bool,children:p.default.node.isRequired,disabled:p.default.bool,enableUserSelectHack:p.default.bool,offsetParent:function(l,u){if(l[u]&&1!==l[u].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:p.default.arrayOf(p.default.number),handle:p.default.string,cancel:p.default.string,nodeRef:p.default.object,onStart:p.default.func,onDrag:p.default.func,onStop:p.default.func,onMouseDown:p.default.func,scale:p.default.number,className:y.dontSetMe,style:y.dontSetMe,transform:y.dontSetMe}),_defineProperty(DraggableCore,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},11327:(l,u,s)=>{var c=s(10564).default;l.exports=function toPrimitive(l,u){if("object"!=c(l)||!l)return l;var s=l[Symbol.toPrimitive];if(void 0!==s){var p=s.call(l,u||"default");if("object"!=c(p))return p;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===u?String:Number)(l)},l.exports.__esModule=!0,l.exports.default=l.exports},12470:l=>{"use strict";l.exports=wp.i18n},14744:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=function isIP(l){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,c.default)(l);var s=("object"===_typeof(u)?u.version:arguments[1])||"";if(!s)return isIP(l,{version:4})||isIP(l,{version:6});if("4"===s.toString())return g.test(l);if("6"===s.toString())return y.test(l);return!1};var c=function _interopRequireDefault(l){return l&&l.__esModule?l:{default:l}}(s(93443));function _typeof(l){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(l){return typeof l}:function(l){return l&&"function"==typeof Symbol&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l},_typeof(l)}var p="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",m="(".concat(p,"[.]){3}").concat(p),g=new RegExp("^".concat(m,"$")),h="(?:[0-9a-fA-F]{1,4})",y=new RegExp("^("+"(?:".concat(h,":){7}(?:").concat(h,"|:)|")+"(?:".concat(h,":){6}(?:").concat(m,"|:").concat(h,"|:)|")+"(?:".concat(h,":){5}(?::").concat(m,"|(:").concat(h,"){1,2}|:)|")+"(?:".concat(h,":){4}(?:(:").concat(h,"){0,1}:").concat(m,"|(:").concat(h,"){1,3}|:)|")+"(?:".concat(h,":){3}(?:(:").concat(h,"){0,2}:").concat(m,"|(:").concat(h,"){1,4}|:)|")+"(?:".concat(h,":){2}(?:(:").concat(h,"){0,3}:").concat(m,"|(:").concat(h,"){1,5}|:)|")+"(?:".concat(h,":){1}(?:(:").concat(h,"){0,4}:").concat(m,"|(:").concat(h,"){1,6}|:)|")+"(?::((?::".concat(h,"){0,5}:").concat(m,"|(?::").concat(h,"){1,7}|:))")+")(%[0-9a-zA-Z.]{1,})?$");l.exports=u.default,l.exports.default=u.default},15118:(l,u,s)=>{var c=s(10564).default,p=s(36417);l.exports=function _possibleConstructorReturn(l,u){if(u&&("object"==c(u)||"function"==typeof u))return u;if(void 0!==u)throw new TypeError("Derived constructors may only return object or undefined");return p(l)},l.exports.__esModule=!0,l.exports.default=l.exports},18791:(l,u,s)=>{"use strict";var c=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;_interopRequireWildcard(s(41594));var p=_interopRequireWildcard(s(75206)),m=s(7470);function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,p=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=c(l)&&"function"!=typeof l)return h;if(m=u?p:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h})(l,u)}u.default={render:function render(l,u){var s;try{var c=(0,m.createRoot)(u);c.render(l),s=function unmountFunction(){c.unmount()}}catch(c){p.render(l,u),s=function unmountFunction(){p.unmountComponentAtNode(u)}}return{unmount:s}}}},18821:(l,u,s)=>{var c=s(70569),p=s(65474),m=s(37744),g=s(11018);l.exports=function _slicedToArray(l,u){return c(l)||p(l,u)||m(l,u)||g()},l.exports.__esModule=!0,l.exports.default=l.exports},20365:l=>{l.exports=function _iterableToArray(l){if("undefined"!=typeof Symbol&&null!=l[Symbol.iterator]||null!=l["@@iterator"])return Array.from(l)},l.exports.__esModule=!0,l.exports.default=l.exports},21806:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=function isFQDN(l,u){(0,c.default)(l),(u=(0,p.default)(u,m)).allow_trailing_dot&&"."===l[l.length-1]&&(l=l.substring(0,l.length-1));!0===u.allow_wildcard&&0===l.indexOf("*.")&&(l=l.substring(2));var s=l.split("."),g=s[s.length-1];if(u.require_tld){if(s.length<2)return!1;if(!u.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(g))return!1;if(/\s/.test(g))return!1}if(!u.allow_numeric_tld&&/^\d+$/.test(g))return!1;return s.every(function(l){return!(l.length>63&&!u.ignore_max_length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(l)&&(!/[\uff01-\uff5e]/.test(l)&&(!/^-|-$/.test(l)&&!(!u.allow_underscores&&/_/.test(l)))))})};var c=_interopRequireDefault(s(93443)),p=_interopRequireDefault(s(41398));function _interopRequireDefault(l){return l&&l.__esModule?l:{default:l}}var m={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};l.exports=u.default,l.exports.default=u.default},21995:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.8125 11.9996C7.29473 11.9996 6.875 12.4473 6.875 12.9996V18.9996C6.875 19.5519 7.29473 19.9996 7.8125 19.9996H17.1875C17.7053 19.9996 18.125 19.5519 18.125 18.9996V12.9996C18.125 12.4473 17.7053 11.9996 17.1875 11.9996H7.8125ZM5 12.9996C5 11.3428 6.2592 9.99963 7.8125 9.99963H17.1875C18.7408 9.99963 20 11.3428 20 12.9996V18.9996C20 20.6565 18.7408 21.9996 17.1875 21.9996H7.8125C6.2592 21.9996 5 20.6565 5 18.9996V12.9996Z"}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.5 3.90527C11.7044 3.90527 10.9413 4.22134 10.3787 4.78395C9.81607 5.34656 9.5 6.10962 9.5 6.90527V10.9053C9.5 11.4576 9.05228 11.9053 8.5 11.9053C7.94772 11.9053 7.5 11.4576 7.5 10.9053V6.90527C7.5 5.57919 8.02678 4.30742 8.96447 3.36974C9.90215 2.43206 11.1739 1.90527 12.5 1.90527C13.8261 1.90527 15.0979 2.43206 16.0355 3.36974C16.9732 4.30742 17.5 5.57919 17.5 6.90527V10.9053C17.5 11.4576 17.0523 11.9053 16.5 11.9053C15.9477 11.9053 15.5 11.4576 15.5 10.9053V6.90527C15.5 6.10962 15.1839 5.34656 14.6213 4.78395C14.0587 4.22134 13.2956 3.90527 12.5 3.90527Z"}),m.default.createElement("path",{d:"M6 12H19V20H6V12Z"}))});u.default=h},24954:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(61790)),m=c(s(85707)),g=c(s(58155)),h=c(s(18821)),y=s(41594),v=s(95034),_=c(s(62688));function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,m.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var b=function useUserInfo(){var l=arguments.length>0&&void 0!==arguments[0]&&arguments[0],u=(0,y.useState)(!1),s=(0,h.default)(u,2),c=s[0],m=s[1],_=(0,y.useState)(!1),b=(0,h.default)(_,2),w=b[0],C=b[1],x=(0,y.useState)({is_connected:!1,is_get_started:!1,connect_url:"",usage:{hasAiSubscription:!1,quota:0,usedQuota:0}}),P=(0,h.default)(x,2),E=P[0],O=P[1],S=E.usage.quota-E.usage.usedQuota,T=E.usage.quota?E.usage.usedQuota/E.usage.quota*100:0,R=function(){var u=(0,g.default)(p.default.mark(function _callee(){var u;return p.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return C(!0),s.next=1,(0,v.getUserInformation)(l);case 1:u=s.sent,O(function(l){return _objectSpread(_objectSpread({},l),u)}),m(!0),C(!1);case 2:case"end":return s.stop()}},_callee)}));return function fetchData(){return u.apply(this,arguments)}}();return c||w||R(),{isLoading:w,isLoaded:c,isConnected:E.is_connected,isGetStarted:E.is_get_started,connectUrl:E.connect_url,builderUrl:E.usage.builderUrl,hasSubscription:E.usage.hasAiSubscription,credits:S<0?0:S,usagePercentage:Math.round(T),fetchData:R}};b.propTypes={immediately:_.default.bool};u.default=b},25553:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.ThumbnailJson=void 0;var p=c(s(41594)),m=s(71338),g=c(s(62688)),h=s(86956),y=s(38298),v=u.ThumbnailJson=function ThumbnailJson(l){var u,s=null===(u=l.attachments)||void 0===u?void 0:u.find(function(l){return"json"===l.type});return s?s.previewHTML?p.default.createElement(m.Thumbnail,{html:s.previewHTML,disabled:l.disabled}):p.default.createElement(h.Skeleton,{animation:"wave",variant:"rounded",width:60,height:60}):null};v.propTypes={attachments:g.default.arrayOf(y.AttachmentPropType).isRequired,disabled:g.default.bool};u.default=v},25893:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var c=s(86956),p=(0,c.styled)(c.Box,{shouldForwardProp:function shouldForwardProp(l){return"outlineOffset"!==l}})(function(l){var u=l.theme,s=l.selected,c=l.height,p=l.disabled,m=l.outlineOffset,g=void 0===m?"0px":m,h=s?u.palette.text.primary:u.palette.text.disabled,y="2px solid ".concat(h);return{height:c,cursor:p?"default":"pointer",overflow:"hidden",boxSizing:"border-box",backgroundPosition:"top center",backgroundSize:"100% auto",backgroundRepeat:"no-repeat",backgroundColor:u.palette.common.white,borderRadius:.5*u.shape.borderRadius,outlineOffset:g,outline:y,opacity:p?"0.4":"1",transition:"all 50ms linear","&:hover":p?{}:{outlineColor:u.palette.text.primary}}});u.default=p},26732:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.dontSetMe=function dontSetMe(l,u,s){if(l[u])return new Error("Invalid prop ".concat(u," passed to ").concat(s," - do not set this, set it on the child."))},u.findInArray=function findInArray(l,u){for(let s=0,c=l.length;s<c;s++)if(u.apply(u,[l[s],s,l]))return l[s]},u.int=function int(l){return parseInt(l,10)},u.isFunction=function isFunction(l){return"function"==typeof l||"[object Function]"===Object.prototype.toString.call(l)},u.isNum=function isNum(l){return"number"==typeof l&&!isNaN(l)}},29402:l=>{function _getPrototypeOf(u){return l.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(l){return l.__proto__||Object.getPrototypeOf(l)},l.exports.__esModule=!0,l.exports.default=l.exports,_getPrototypeOf(u)}l.exports=_getPrototypeOf,l.exports.__esModule=!0,l.exports.default=l.exports},29664:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;u.default=function includes(l,u){return-1!==l.indexOf(u)};l.exports=u.default,l.exports.default=u.default},31593:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.53033 7.46967C9.82322 7.76256 9.82322 8.23744 9.53033 8.53033L6.81066 11.25H19C19.4142 11.25 19.75 11.5858 19.75 12C19.75 12.4142 19.4142 12.75 19 12.75H6.81066L9.53033 15.4697C9.82322 15.7626 9.82322 16.2374 9.53033 16.5303C9.23744 16.8232 8.76256 16.8232 8.46967 16.5303L4.46967 12.5303C4.17678 12.2374 4.17678 11.7626 4.46967 11.4697L8.46967 7.46967C8.76256 7.17678 9.23744 7.17678 9.53033 7.46967Z"}))});u.default=h},32837:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.addClassName=addClassName,u.addEvent=function addEvent(l,u,s,c){if(!l)return;const p={capture:!0,...c};l.addEventListener?l.addEventListener(u,s,p):l.attachEvent?l.attachEvent("on"+u,s):l["on"+u]=s},u.addUserSelectStyles=function addUserSelectStyles(l){if(!l)return;let u=l.getElementById("react-draggable-style-el");u||(u=l.createElement("style"),u.type="text/css",u.id="react-draggable-style-el",u.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",u.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",l.getElementsByTagName("head")[0].appendChild(u));l.body&&addClassName(l.body,"react-draggable-transparent-selection")},u.createCSSTransform=function createCSSTransform(l,u){const s=getTranslation(l,u,"px");return{[(0,p.browserPrefixToKey)("transform",p.default)]:s}},u.createSVGTransform=function createSVGTransform(l,u){return getTranslation(l,u,"")},u.getTouch=function getTouch(l,u){return l.targetTouches&&(0,c.findInArray)(l.targetTouches,l=>u===l.identifier)||l.changedTouches&&(0,c.findInArray)(l.changedTouches,l=>u===l.identifier)},u.getTouchIdentifier=function getTouchIdentifier(l){if(l.targetTouches&&l.targetTouches[0])return l.targetTouches[0].identifier;if(l.changedTouches&&l.changedTouches[0])return l.changedTouches[0].identifier},u.getTranslation=getTranslation,u.innerHeight=function innerHeight(l){let u=l.clientHeight;const s=l.ownerDocument.defaultView.getComputedStyle(l);return u-=(0,c.int)(s.paddingTop),u-=(0,c.int)(s.paddingBottom),u},u.innerWidth=function innerWidth(l){let u=l.clientWidth;const s=l.ownerDocument.defaultView.getComputedStyle(l);return u-=(0,c.int)(s.paddingLeft),u-=(0,c.int)(s.paddingRight),u},u.matchesSelector=matchesSelector,u.matchesSelectorAndParentsTo=function matchesSelectorAndParentsTo(l,u,s){let c=l;do{if(matchesSelector(c,u))return!0;if(c===s)return!1;c=c.parentNode}while(c);return!1},u.offsetXYFromParent=function offsetXYFromParent(l,u,s){const c=u===u.ownerDocument.body?{left:0,top:0}:u.getBoundingClientRect(),p=(l.clientX+u.scrollLeft-c.left)/s,m=(l.clientY+u.scrollTop-c.top)/s;return{x:p,y:m}},u.outerHeight=function outerHeight(l){let u=l.clientHeight;const s=l.ownerDocument.defaultView.getComputedStyle(l);return u+=(0,c.int)(s.borderTopWidth),u+=(0,c.int)(s.borderBottomWidth),u},u.outerWidth=function outerWidth(l){let u=l.clientWidth;const s=l.ownerDocument.defaultView.getComputedStyle(l);return u+=(0,c.int)(s.borderLeftWidth),u+=(0,c.int)(s.borderRightWidth),u},u.removeClassName=removeClassName,u.removeEvent=function removeEvent(l,u,s,c){if(!l)return;const p={capture:!0,...c};l.removeEventListener?l.removeEventListener(u,s,p):l.detachEvent?l.detachEvent("on"+u,s):l["on"+u]=null},u.removeUserSelectStyles=function removeUserSelectStyles(l){if(!l)return;try{if(l.body&&removeClassName(l.body,"react-draggable-transparent-selection"),l.selection)l.selection.empty();else{const u=(l.defaultView||window).getSelection();u&&"Caret"!==u.type&&u.removeAllRanges()}}catch(l){}};var c=s(26732),p=function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;if(null===l||"object"!=typeof l&&"function"!=typeof l)return{default:l};var s=_getRequireWildcardCache(u);if(s&&s.has(l))return s.get(l);var c={},p=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var m in l)if("default"!==m&&Object.prototype.hasOwnProperty.call(l,m)){var g=p?Object.getOwnPropertyDescriptor(l,m):null;g&&(g.get||g.set)?Object.defineProperty(c,m,g):c[m]=l[m]}c.default=l,s&&s.set(l,c);return c}(s(47350));function _getRequireWildcardCache(l){if("function"!=typeof WeakMap)return null;var u=new WeakMap,s=new WeakMap;return(_getRequireWildcardCache=function(l){return l?s:u})(l)}let m="";function matchesSelector(l,u){return m||(m=(0,c.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],function(u){return(0,c.isFunction)(l[u])})),!!(0,c.isFunction)(l[m])&&l[m](u)}function getTranslation(l,u,s){let{x:c,y:p}=l,m="translate(".concat(c).concat(s,",").concat(p).concat(s,")");if(u){const l="".concat("string"==typeof u.x?u.x:u.x+s),c="".concat("string"==typeof u.y?u.y:u.y+s);m="translate(".concat(l,", ").concat(c,")")+m}return m}function addClassName(l,u){l.classList?l.classList.add(u):l.className.match(new RegExp("(?:^|\\s)".concat(u,"(?!\\S)")))||(l.className+=" ".concat(u))}function removeClassName(l,u){l.classList?l.classList.remove(u):l.className=l.className.replace(new RegExp("(?:^|\\s)".concat(u,"(?!\\S)"),"g"),"")}},33057:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.55012 4.45178C9.23098 3.48072 11.1845 3.08925 13.1097 3.33767C15.035 3.58609 16.8251 4.46061 18.2045 5.82653C19.5838 7.19245 20.4757 8.97399 20.743 10.8967C20.8 11.307 20.5136 11.6858 20.1033 11.7428C19.6931 11.7998 19.3142 11.5135 19.2572 11.1032C19.0353 9.50635 18.2945 8.02677 17.149 6.89236C16.0035 5.75795 14.5167 5.03165 12.9178 4.82534C11.3189 4.61902 9.69644 4.94414 8.30047 5.75061C7.24361 6.36117 6.36093 7.22198 5.72541 8.24995H8.00009C8.41431 8.24995 8.75009 8.58574 8.75009 8.99995C8.75009 9.41417 8.41431 9.74995 8.00009 9.74995H4.51686C4.5055 9.75021 4.49412 9.75021 4.48272 9.74995H4.00009C3.58588 9.74995 3.25009 9.41417 3.25009 8.99995V4.99995C3.25009 4.58574 3.58588 4.24995 4.00009 4.24995C4.41431 4.24995 4.75009 4.58574 4.75009 4.99995V7.00691C5.48358 5.96916 6.43655 5.0951 7.55012 4.45178Z"}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.89686 12.2571C4.30713 12.2001 4.68594 12.4864 4.74295 12.8967C4.96487 14.4936 5.70565 15.9731 6.85119 17.1075C7.99673 18.242 9.48347 18.9683 11.0824 19.1746C12.6813 19.3809 14.3037 19.0558 15.6997 18.2493C16.7566 17.6387 17.6393 16.7779 18.2748 15.75H16.0001C15.5859 15.75 15.2501 15.4142 15.2501 15C15.2501 14.5857 15.5859 14.25 16.0001 14.25H19.4833C19.4947 14.2497 19.5061 14.2497 19.5175 14.25H20.0001C20.4143 14.25 20.7501 14.5857 20.7501 15V19C20.7501 19.4142 20.4143 19.75 20.0001 19.75C19.5859 19.75 19.2501 19.4142 19.2501 19V16.993C18.5166 18.0307 17.5636 18.9048 16.4501 19.5481C14.7692 20.5192 12.8157 20.9107 10.8904 20.6622C8.9652 20.4138 7.17504 19.5393 5.79572 18.1734C4.4164 16.8074 3.52443 15.0259 3.25723 13.1032C3.20022 12.6929 3.48658 12.3141 3.89686 12.2571Z"}))});u.default=h},33724:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.46967 3.46967C3.76256 3.17678 4.23744 3.17678 4.53033 3.46967L9.25 8.18934V6C9.25 5.58579 9.58579 5.25 10 5.25C10.4142 5.25 10.75 5.58579 10.75 6V10C10.75 10.4142 10.4142 10.75 10 10.75H6C5.58579 10.75 5.25 10.4142 5.25 10C5.25 9.58579 5.58579 9.25 6 9.25H8.18934L3.46967 4.53033C3.17678 4.23744 3.17678 3.76256 3.46967 3.46967ZM14 13.25H18C18.4142 13.25 18.75 13.5858 18.75 14C18.75 14.4142 18.4142 14.75 18 14.75H15.8107L20.5303 19.4697C20.8232 19.7626 20.8232 20.2374 20.5303 20.5303C20.2374 20.8232 19.7626 20.8232 19.4697 20.5303L14.75 15.8107V18C14.75 18.4142 14.4142 18.75 14 18.75C13.5858 18.75 13.25 18.4142 13.25 18V14C13.25 13.5858 13.5858 13.25 14 13.25Z"}))});u.default=h},33929:(l,u,s)=>{var c=s(67114),p=s(89736);l.exports=function AsyncIterator(l,u){function n(s,p,m,g){try{var h=l[s](p),y=h.value;return y instanceof c?u.resolve(y.v).then(function(l){n("next",l,m,g)},function(l){n("throw",l,m,g)}):u.resolve(y).then(function(l){h.value=l,m(h)},function(l){return n("throw",l,m,g)})}catch(l){g(l)}}var s;this.next||(p(AsyncIterator.prototype),p(AsyncIterator.prototype,"function"==typeof Symbol&&Symbol.asyncIterator||"@asyncIterator",function(){return this})),p(this,"_invoke",function(l,c,p){function f(){return new u(function(u,s){n(l,p,u,s)})}return s=s?s.then(f,f):f()},!0)},l.exports.__esModule=!0,l.exports.default=l.exports},34161:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=s(95034),m=c(s(48812)),g=s(91258),h=new Map([["media",p.getImagePromptEnhanced],["layout",p.getLayoutPromptEnhanced]]);u.default=function usePromptEnhancer(l,u){var s=(0,g.useConfig)().mode,c=(0,m.default)(function(){return function getResult(l,u,s){if(!h.has(u))throw new Error("Invalid prompt type: ".concat(u));return h.get(u)(l,s)}(l,u,s)},l),p=c.data,y=c.isLoading;return{enhance:c.send,isEnhancing:y,enhancedPrompt:null==p?void 0:p.result}}},35121:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=s(86956),y=s(12470),v=c(s(62688)),_=s(44048);var b=(0,h.styled)(h.Paper)(function(l){var u=l.theme;return{position:"relative",'[data-popper-placement="top"] &':{marginBottom:u.spacing(2.5)},'[data-popper-placement="bottom"] &':{marginTop:u.spacing(2.5)},padding:u.spacing(3),boxShadow:u.shadows[4],zIndex:"9999"}}),w=(0,h.styled)(h.Box)(function(l){var u=l.theme;return{width:u.spacing(5),height:u.spacing(2.5),position:"absolute",overflow:"hidden",left:"50% !important",transform:"translateX(-50%) rotate(var(--rotate, 0deg)) !important",'[data-popper-placement="top"] &':{top:"100%"},'[data-popper-placement="bottom"] &':{"--rotate":"180deg",top:"calc(".concat(u.spacing(2.5)," * -1)")},"&::after":{backgroundColor:u.palette.background.paper,content:'""',display:"block",position:"absolute",width:u.spacing(2.5),height:u.spacing(2.5),top:0,left:"50%",transform:"translateX(-50%) translateY(-50%) rotate(45deg)",boxShadow:"1px 1px 5px 0px rgba(0, 0, 0, 0.2)",backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))"}}}),C=[(0,y.__)("Get spot-on suggestions from AI Copilot and AI Context with appropriate designs, layouts, and content for your business.","elementor"),(0,y.__)("Generate professional texts about any topic, in any tone.","elementor"),(0,y.__)("Effortlessly create or enhance stunning images and bring your ideas to life.","elementor"),(0,y.__)("Unleash infinite possibilities with the custom code generator.","elementor"),(0,y.__)("Access 30-days of AI History with the AI Starter plan and 90-days with the Power plan.","elementor")],x=(0,h.styled)(h.Chip)(function(){return{"& .MuiChip-label":{lineHeight:1.5},"& .MuiSvgIcon-root.MuiChip-icon":{fontSize:"1.25rem"}}}),P=function UpgradeChip(l){var u=l.hasSubscription,s=void 0!==u&&u,c=l.usagePercentage,p=void 0===c?0:c,v=(0,m.useState)(!1),P=(0,g.default)(v,2),E=P[0],O=P[1],S=(0,m.useRef)(null),T=(0,m.useRef)(null),R="https://go.elementor.com/ai-popup-purchase-dropdown/";s&&(R=p>=100?"https://go.elementor.com/ai-popup-upgrade-limit-reached/":"https://go.elementor.com/ai-popup-upgrade-limit-reached-80-percent/");var j=s?(0,y.__)("Upgrade Elementor AI","elementor"):(0,y.__)("Get Elementor AI","elementor");return m.default.createElement(h.Box,{component:"span","aria-owns":E?"e-ai-upgrade-popover":void 0,"aria-haspopup":"true",onMouseEnter:function showPopover(){return O(!0)},onMouseLeave:function hidePopover(){return O(!1)},ref:S,display:"flex",alignItems:"center"},m.default.createElement(x,{color:"promotion",label:(0,y.__)("Upgrade","elementor"),icon:m.default.createElement(_.AIIcon,null),size:"small"}),m.default.createElement(h.Popper,{open:E,anchorEl:S.current,sx:{zIndex:"170001",maxWidth:300},modifiers:[{name:"arrow",enabled:!0,options:{element:T.current}}]},m.default.createElement(b,null,m.default.createElement(w,{ref:T}),m.default.createElement(h.Typography,{variant:"h5",color:"text.primary"},(0,y.__)("Unlimited access to Elementor AI","elementor")),m.default.createElement(h.List,{sx:{mb:1}},C.map(function(l,u){return m.default.createElement(h.ListItem,{key:u,disableGutters:!0,sx:{alignItems:"flex-start"}},m.default.createElement(h.ListItemIcon,null,m.default.createElement(_.CheckedCircleIcon,null)),m.default.createElement(h.ListItemText,{sx:{m:0}},m.default.createElement(h.Typography,{variant:"body2"},l)))})),m.default.createElement(h.Button,{variant:"contained",color:"promotion",size:"small",href:R,target:"_blank",startIcon:m.default.createElement(_.AIIcon,null),sx:{"&:hover":{color:"promotion.contrastText"}}},j))))};u.default=P;P.propTypes={hasSubscription:v.default.bool,usagePercentage:v.default.number}},35589:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.useRemoteConfig=u.RemoteConfigProvider=u.CONFIG_KEYS=void 0;var m=c(s(61790)),g=c(s(58155)),h=c(s(18821)),y=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),v=c(s(62688)),_=s(95034);var b=y.default.createContext({});u.useRemoteConfig=function useRemoteConfig(){return y.default.useContext(b)},u.CONFIG_KEYS={WEB_BASED_BUILDER_URL:"webBasedBuilderUrl",AUTH_TOKEN:"jwt"};(u.RemoteConfigProvider=function RemoteConfigProvider(l){var u=(0,y.useState)(!1),s=(0,h.default)(u,2),c=s[0],p=s[1],v=(0,y.useState)(!1),w=(0,h.default)(v,2),C=w[0],x=w[1],P=(0,y.useState)(!1),E=(0,h.default)(P,2),O=E[0],S=E[1],T=(0,y.useState)({}),R=(0,h.default)(T,2),j=R[0],M=R[1],I=function(){var l=(0,g.default)(m.default.mark(function _callee(){var l;return m.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return p(!0),S(!1),u.prev=1,u.next=2,(0,_.getRemoteConfig)().finally(function(){x(!0),p(!1)});case 2:if((l=u.sent).config){u.next=3;break}throw new Error("Invalid remote config");case 3:M(l.config),u.next=5;break;case 4:u.prev=4,u.catch(1),S(!0),x(!0),p(!1);case 5:case"end":return u.stop()}},_callee,null,[[1,4]])}));return function fetchData(){return l.apply(this,arguments)}}();return(0,y.useEffect)(function(){return window.addEventListener("elementor/connect/success",I),function(){window.removeEventListener("elementor/connect/success",I)}},[]),C||c||I(),y.default.createElement(b.Provider,{value:{isLoading:c,isLoaded:C,isError:O,remoteConfig:j}},l.children)}).propTypes={children:v.default.node.isRequired,onError:v.default.func.isRequired}},36417:l=>{l.exports=function _assertThisInitialized(l){if(void 0===l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l},l.exports.__esModule=!0,l.exports.default=l.exports},36833:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.renderLayoutApp=u.openPanel=u.onConnect=u.importToEditor=u.getUiConfig=u.closePanel=u.WEB_BASED_PROMPTS=u.VARIATIONS_PROMPTS=void 0;var p=c(s(41594)),m=c(s(61790)),g=c(s(58155)),h=c(s(18791)),y=s(74561),v=c(s(93569)),_=s(47547),b=s(40327),w=s(12470),C=c(s(47407)),x=s(40128),P=u.closePanel=function closePanel(){$e.run("panel/close"),$e.components.get("panel").blockUserInteractions()},E=u.openPanel=function openPanel(){$e.run("panel/open"),$e.components.get("panel").unblockUserInteractions()},O=u.onConnect=function onConnect(l){elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=l.kits_access_level||l.access_level||0,elementorCommon.config.library_connect.current_access_tier=l.access_tier},S=u.getUiConfig=function getUiConfig(){var l,u;return{colorScheme:(null===(l=elementor)||void 0===l||null===(u=l.getPreferences)||void 0===u?void 0:u.call(l,"ui_theme"))||"auto",isRTL:elementorCommon.config.isRTL}},T=u.VARIATIONS_PROMPTS=[{text:(0,w.__)("Minimalist design with bold typography about","elementor")},{text:(0,w.__)("Elegant style with serif fonts discussing","elementor")},{text:(0,w.__)("Retro vibe with muted colors and classic fonts about","elementor")},{text:(0,w.__)("Futuristic design with neon accents about","elementor")},{text:(0,w.__)("Professional look with clean lines for","elementor")},{text:(0,w.__)("Earthy tones and organic shapes featuring","elementor")},{text:(0,w.__)("Luxurious theme with rich colors discussing","elementor")},{text:(0,w.__)("Tech-inspired style with modern fonts about","elementor")},{text:(0,w.__)("Warm hues with comforting visuals about","elementor")}],R=u.WEB_BASED_PROMPTS=[{text:(0,w.__)("Change the content to be about [topic]","elementor")},{text:(0,w.__)("Generate lorem ipsum placeholder text for all paragraphs","elementor")},{text:(0,w.__)("Revise the content to focus on [topic] and then translate it into Spanish","elementor")},{text:(0,w.__)("Shift the focus of the content to [topic] in order to showcase our company's mission and values","elementor")},{text:(0,w.__)("Alter the content to provide helpful tips related to [topic]","elementor")},{text:(0,w.__)("Adjust the content to include FAQs and answers for common inquiries about [topic]","elementor")}],j=(0,w.__)("Press '/' for suggestions or describe the changes you want to apply (optional)...","elementor");u.renderLayoutApp=function renderLayoutApp(){var l,u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{parentContainer:null,mode:"",at:null,onClose:null,onGenerate:null,onInsert:null,onRenderApp:null,onSelect:null,attachments:[]};P();var s=(0,y.createPreviewContainer)(u.parentContainer,{at:u.at}),c=S(),b=c.colorScheme,w=c.isRTL,x=document.createElement("div");document.body.append(x);var M,I=window.elementorFrontend.elements.$window[0].getComputedStyle(window.elementorFrontend.elements.$body[0]),k=h.default.render(p.default.createElement(C.default,{isRTL:w,colorScheme:b},p.default.createElement(v.default,{mode:u.mode,currentContext:{body:{backgroundColor:I.backgroundColor,backgroundImage:I.backgroundImage}},attachmentsTypes:{json:{promptSuggestions:T,promptPlaceholder:j,previewGenerator:(M=(0,g.default)(m.default.mark(function _callee(l){var u;return m.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=1,(0,_.takeScreenshot)(l);case 1:return u=s.sent,s.abrupt("return",'<img src="'.concat(u,'" />'));case 2:case"end":return s.stop()}},_callee)})),function previewGenerator(l){return M.apply(this,arguments)})},url:{promptPlaceholder:j,promptSuggestions:R}},attachments:u.attachments||[],onClose:function onClose(){var l;s.destroy(),null===(l=u.onClose)||void 0===l||l.call(u),A(),x.remove(),E()},onConnect:O,onGenerate:function onGenerate(){var l;null===(l=u.onGenerate)||void 0===l||l.call(u,{previewContainer:s})},onData:function(){var l=(0,g.default)(m.default.mark(function _callee2(l){var u;return m.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.next=1,(0,_.takeScreenshot)(l);case 1:return u=s.sent,s.abrupt("return",{screenshot:u,template:l});case 2:case"end":return s.stop()}},_callee2)}));return function(u){return l.apply(this,arguments)}}(),onSelect:function onSelect(l){var c;null===(c=u.onSelect)||void 0===c||c.call(u),s.setContent(l)},onInsert:u.onInsert,hasPro:elementor.helpers.hasPro()})),x),A=k.unmount;null===(l=u.onRenderApp)||void 0===l||l.call(u,{previewContainer:s})},u.importToEditor=function importToEditor(l){var u=l.parentContainer,s=l.at,c=l.template,p=l.historyTitle,m=l.replace,g=void 0!==m&&m,h=(0,b.startHistoryLog)({type:"import",title:p});g&&$e.run("document/elements/delete",{container:u.children.at(s)}),$e.run("document/elements/create",{container:u,model:(0,x.generateIds)(c),options:{at:s,edit:!0}}),h()}},37744:(l,u,s)=>{var c=s(78113);l.exports=function _unsupportedIterableToArray(l,u){if(l){if("string"==typeof l)return c(l,u);var s={}.toString.call(l).slice(8,-1);return"Object"===s&&l.constructor&&(s=l.constructor.name),"Map"===s||"Set"===s?Array.from(l):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?c(l,u):void 0}},l.exports.__esModule=!0,l.exports.default=l.exports},38230:(l,u,s)=>{"use strict";const{default:c,DraggableCore:p}=s(9111);l.exports=c,l.exports.default=c,l.exports.DraggableCore=p},38262:(l,u,s)=>{"use strict";function r(l){var u,s,c="";if("string"==typeof l||"number"==typeof l)c+=l;else if("object"==typeof l)if(Array.isArray(l))for(u=0;u<l.length;u++)l[u]&&(s=r(l[u]))&&(c&&(c+=" "),c+=s);else for(u in l)l[u]&&(c&&(c+=" "),c+=u);return c}function clsx(){for(var l,u,s=0,c="";s<arguments.length;)(l=arguments[s++])&&(u=r(l))&&(c&&(c+=" "),c+=u);return c}s.r(u),s.d(u,{clsx:()=>clsx,default:()=>c});const c=clsx},38298:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.AttachmentsTypesPropType=u.AttachmentPropType=void 0;var p=c(s(62688));u.AttachmentPropType=p.default.shape({type:p.default.string,previewHTML:p.default.string,content:p.default.string,label:p.default.string,source:p.default.string}),u.AttachmentsTypesPropType=p.default.shape({type:p.default.shape({promptPlaceholder:p.default.string,promptSuggestions:p.default.arrayOf(p.default.shape({text:p.default.string.isRequired})),previewGenerator:p.default.func})})},39805:l=>{l.exports=function _classCallCheck(l,u){if(!(l instanceof u))throw new TypeError("Cannot call a class as a function")},l.exports.__esModule=!0,l.exports.default=l.exports},40128:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.RequestIdsProvider=void 0,u.generateIds=function generateIds(l){var u;l.id=v().toString(),null!==(u=l.elements)&&void 0!==u&&u.length&&l.elements.map(function(l){return generateIds(l)});return l},u.useRequestIds=u.getUniqueId=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=c(s(62688));var y=(0,m.createContext)({}),v=(u.useRequestIds=function useRequestIds(){var l=(0,m.useContext)(y);if(!l)throw new Error("useRequestIds must be used within a RequestIdsProvider");return l},u.getUniqueId=function getUniqueId(l){return l+"-"+Math.random().toString(16).substr(2,7)});window.EDITOR_SESSION_ID=window.EDITOR_SESSION_ID||v("editor-session"),(u.RequestIdsProvider=function RequestIdsProvider(l){var u=(0,m.useRef)(window.EDITOR_SESSION_ID),s=(0,m.useRef)(""),c=(0,m.useRef)(""),p=(0,m.useRef)(""),h=(0,m.useRef)("");s.current=v("session");var _=(0,m.useState)(0),b=(0,g.default)(_,2),w=b[0],C=b[1];return m.default.createElement(y.Provider,{value:{editorSessionId:u,sessionId:s,generateId:c,batchId:p,requestId:h,setGenerate:function setGenerate(){return c.current=v("generate"),c},setBatch:function setBatch(){return p.current=v("batch"),p},setRequest:function setRequest(){return h.current=v("request"),h},usagePercentage:w,updateUsagePercentage:function updateUsagePercentage(l){C(l)}}},l.children)}).propTypes={children:h.default.node.isRequired};u.default=y},40327:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.startHistoryLog=function startHistoryLog(l){var u=l.type,s=l.title,c=$e.internal("document/history/start-log",{type:u,title:s});return function(){return $e.internal("document/history/end-log",{id:c})}},u.toggleHistory=function toggleHistory(l){elementor.documents.getCurrent().history.setActive(l)}},40362:(l,u,s)=>{"use strict";var c=s(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,l.exports=function(){function shim(l,u,s,p,m,g){if(g!==c){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}function getShim(){return shim}shim.isRequired=shim;var l={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return l.PropTypes=l,l}},40453:(l,u,s)=>{var c=s(10739);l.exports=function _objectWithoutProperties(l,u){if(null==l)return{};var s,p,m=c(l,u);if(Object.getOwnPropertySymbols){var g=Object.getOwnPropertySymbols(l);for(p=0;p<g.length;p++)s=g[p],-1===u.indexOf(s)&&{}.propertyIsEnumerable.call(l,s)&&(m[s]=l[s])}return m},l.exports.__esModule=!0,l.exports.default=l.exports},40989:(l,u,s)=>{var c=s(45498);function _defineProperties(l,u){for(var s=0;s<u.length;s++){var p=u[s];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(l,c(p.key),p)}}l.exports=function _createClass(l,u,s){return u&&_defineProperties(l.prototype,u),s&&_defineProperties(l,s),Object.defineProperty(l,"prototype",{writable:!1}),l},l.exports.__esModule=!0,l.exports.default=l.exports},41398:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=function merge(){var l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},u=arguments.length>1?arguments[1]:void 0;for(var s in u)void 0===l[s]&&(l[s]=u[s]);return l},l.exports=u.default,l.exports.default=u.default},41594:l=>{"use strict";l.exports=React},43091:(l,u,s)=>{"use strict";var c=s(12470).sprintf,p=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=p(s(41594)),g=p(s(78304)),h=p(s(40453)),y=s(86956),v=s(12470),_=p(s(62688)),b=["error","onRetry","actionPosition"],w=function PromptErrorMessage(l){var u=l.error,s=l.onRetry,p=void 0===s?function(){}:s,_=l.actionPosition,w=void 0===_?"default":_,C=(0,h.default)(l,b);function getQuotaReachedTrailMessage(l){return l?{text:m.default.createElement(y.AlertTitle,null,c((0,v.__)("You've used all AI credits for %s.","elementor"),l.toLowerCase())),description:(0,v.__)("Upgrade now to keep using this feature. You still have credits for other AI features (Text, Code, Images, Containers, etc.)","elementor"),buttonText:(0,v.__)("Upgrade now","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}}:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("It's time to upgrade.","elementor")),description:(0,v.__)("Enjoy the free trial? Upgrade now for unlimited access to built-in image, text and custom code generators.","elementor"),buttonText:(0,v.__)("Upgrade","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}}}var x=function getErrorMessage(){var l,s=u.message||u,c=null===(l=u.extra_data)||void 0===l?void 0:l.featureName,g={default:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("There was a glitch.","elementor")),description:(0,v.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,v.__)("Try again","elementor"),buttonAction:p},service_outage_internal:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("There was a glitch.","elementor")),description:(0,v.__)("Wait a moment and give it another go.","elementor"),buttonText:(0,v.__)("Try again","elementor"),buttonAction:p},invalid_connect_data:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("There was a glitch.","elementor")),description:m.default.createElement(m.default.Fragment,null,(0,v.__)("Try exiting Elementor and sign in again.","elementor")," ",m.default.createElement("a",{href:"https://elementor.com/help/disconnecting-reconnecting-your-elementor-account/",target:"_blank",rel:"noreferrer"},(0,v.__)("Show me how","elementor"))),buttonText:(0,v.__)("Reconnect","elementor"),buttonAction:function buttonAction(){return window.open(window.ElementorAiConfig.connect_url)}},not_connected:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("You aren't connected to Elementor AI.","elementor")),description:(0,v.__)("Elementor AI is just a few clicks away. Connect your account to instantly create texts and custom code.","elementor"),buttonText:(0,v.__)("Connect","elementor"),buttonAction:function buttonAction(){return window.open(window.ElementorAiConfig.connect_url)}},quota_reached_trail:getQuotaReachedTrailMessage(c),quota_reached_subscription:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("Looks like you're out of credits.","elementor")),description:(0,v.__)("Ready to take it to the next level?","elementor"),buttonText:(0,v.__)("Upgrade now","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}},rate_limit_network:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("Whoa! Slow down there.","elementor")),description:(0,v.__)("We can’t process that many requests so fast. Try again in 15 minutes.","elementor")},invalid_prompts:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("We were unable to generate that prompt.","elementor")),description:(0,v.__)("Seems like the prompt contains words that could generate harmful content. Write a different prompt to continue.","elementor")},service_unavailable:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("There was a glitch.","elementor")),description:(0,v.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,v.__)("Try again","elementor"),buttonAction:p},request_timeout_error:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("There was a glitch.","elementor")),description:(0,v.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,v.__)("Try again","elementor"),buttonAction:p},invalid_token:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("Try again","elementor")),description:(0,v.__)("Try exiting Elementor and sign in again.","elementor"),buttonText:(0,v.__)("Reconnect","elementor"),buttonAction:p},file_too_large:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("The file is too large.","elementor")),description:(0,v.__)("Please upload a file that is less than 4MB.","elementor")},image_resolution_maximum_exceeded:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("The image resolution exceeds the maximum allowed size.","elementor")),description:(0,v.__)("Please upload a file with dimensions less than 2048x2048 pixels.","elementor")},external_service_unavailable:{text:m.default.createElement(y.AlertTitle,null,(0,v.__)("Temporary external service issue","elementor")),description:(0,v.__)("It seems that one of our partner services is temporarily unavailable. Please try again in a few minutes.","elementor"),buttonText:(0,v.__)("Try Again","elementor"),buttonAction:p}};return g[s]||g.default}(),P=(null==x?void 0:x.buttonText)&&m.default.createElement(y.Button,{color:"inherit",size:"small",variant:"outlined",onClick:x.buttonAction},x.buttonText);return m.default.createElement(y.Alert,(0,g.default)({severity:x.severity||"error",action:"default"===w&&P},C),x.text,x.description,"bottom"===w&&m.default.createElement(y.Box,{sx:{mt:1}},P))};w.propTypes={error:_.default.oneOfType([_.default.object,_.default.string]),onRetry:_.default.func,actionPosition:_.default.oneOf(["default","bottom"])};u.default=w},43220:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.UrlDialog=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=s(86956),y=c(s(62688)),v=s(12470),_=s(86353),b=s(77043),w=s(4353),C=s(35589),x=c(s(24954)),P=s(40128);(u.UrlDialog=function UrlDialog(l){var u=(0,b.useTimeout)(1e4),s=(0,g.default)(u,2),c=s[0],p=s[1],y=(0,x.default)(),E=y.isLoading,O=y.usagePercentage,S=(0,P.useRequestIds)().updateUsagePercentage,T=(0,m.useState)(!1),R=(0,g.default)(T,2),j=R[0],M=R[1],I=(0,C.useRemoteConfig)().remoteConfig[C.CONFIG_KEYS.WEB_BASED_BUILDER_URL],k=(I?new URL(I):{}).origin,A=(0,m.useRef)(!1);return(0,m.useEffect)(function(){j||E||!O&&0!==O||(S(O),M(!0))},[E,O,j,S]),(0,m.useEffect)(function(){if(!A.current)try{window.$e.run("ai-integration/open-choose-element",{url:l.url}),A.current=!0}catch(l){console.error(l)}},[A.current]),(0,m.useEffect)(function(){var u=function onMessage(u){if(u.origin===k){var s=u.data,c=s.type,m=s.html,g=s.url;switch(c){case"element-selector/close":A.current=!1,l.onClose();break;case"element-selector/loaded":p(),A.current=!0;break;case"element-selector/attach":l.onAttach([{type:"url",previewHTML:m,content:m,label:g?new URL(g).href:"",source:w.USER_URL_SOURCE}])}}};return window.addEventListener("message",u),function(){window.removeEventListener("message",u)}},[k,l,p]),m.default.createElement(m.default.Fragment,null,!A.current&&!c&&m.default.createElement(h.Dialog,{open:!0,maxWidth:"lg"},m.default.createElement(h.Typography,{sx:{textAlign:"center",padding:3}},(0,v.__)("Loading...","elementor"))),c&&m.default.createElement(_.AlertDialog,{message:(0,v.__)("The app is not responding. Please try again later. (#408)","elementor"),onClose:l.onClose}))}).propTypes={onAttach:y.default.func.isRequired,onClose:y.default.func.isRequired,url:y.default.string}},44048:l=>{"use strict";l.exports=elementorV2.icons},45286:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.ThumbnailUrl=void 0;var p=c(s(41594)),m=s(71338),g=s(12470),h=c(s(62688)),y=s(86956),v=s(44048),_=s(38298),b=u.ThumbnailUrl=function ThumbnailUrl(l){var u,s=null===(u=l.attachments)||void 0===u?void 0:u.find(function(l){return"url"===l.type});return s?p.default.createElement(y.Box,{sx:{position:"relative","&:hover::before":{content:'""',position:"absolute",userSelect:"none",inset:0,backgroundColor:"rgba(0,0,0,0.6)",borderRadius:1,zIndex:1},"&:hover .remove-attachment":{display:"flex"}}},p.default.createElement(y.IconButton,{className:"remove-attachment",size:"small","aria-label":(0,g.__)("Remove","elementor"),disabled:l.disabled,onClick:function onClick(u){u.stopPropagation(),l.onDetach()},sx:{display:"none",position:"absolute",insetInlineEnd:4,insetBlockStart:4,backgroundColor:"secondary.main",zIndex:1,borderRadius:1,p:"3px","&:hover":{backgroundColor:"secondary.dark"}}},p.default.createElement(v.TrashIcon,{sx:{fontSize:"1.125rem",color:"common.white"}})),p.default.createElement(m.Thumbnail,{disabled:l.disabled,html:s.previewHTML})):null};b.propTypes={attachments:h.default.arrayOf(_.AttachmentPropType).isRequired,disabled:h.default.bool,onDetach:h.default.func};u.default=b},45395:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.ProTemplateIndicator=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=s(12470),y=s(86956),v=c(s(21995));var _=(0,y.styled)(y.Paper)(function(l){var u=l.theme;return{position:"relative",padding:u.spacing(3),boxShadow:u.shadows[4],zIndex:"9999"}}),b=(0,y.styled)(y.Box)(function(l){var u=l.theme;return{position:"absolute",width:u.spacing(5),height:u.spacing(5),overflow:"hidden",left:"100% !important",transform:"translateX(-50%) translateY(-50%) rotate(var(--rotate, 0deg)) !important","&::after":{backgroundColor:u.palette.background.paper,content:'""',display:"block",position:"absolute",width:u.spacing(2.5),height:u.spacing(2.5),top:"50%",left:"50%",transform:"translateX(-50%) translateY(-50%) rotate(45deg)",boxShadow:"5px -5px 5px 0px rgba(0, 0, 0, 0.2)",backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))"}}});u.ProTemplateIndicator=function ProTemplateIndicator(){var l=(0,h.__)("Go Pro","elementor"),u=(0,m.useState)(!1),s=(0,g.default)(u,2),c=s[0],p=s[1],w=(0,m.useRef)(null),C=(0,m.useRef)(null);return m.default.createElement(y.Box,{flexDirection:"row-reverse",component:"span",display:"flex",onMouseLeave:function hidePopover(){return p(!1)},alignItems:"center"},m.default.createElement(y.IconButton,{ref:w,onMouseEnter:function showPopover(){return p(!0)},onClick:function onClick(l){return l.stopPropagation()},"aria-owns":c?"e-pro-upgrade-popover":void 0,"aria-haspopup":"true",sx:{m:1,"&:hover":{backgroundColor:"action.selected"}}},m.default.createElement(v.default,{sx:{color:"text.primary"}})),m.default.createElement(y.Popper,{open:c,popperOptions:{placement:"left-start",modifiers:[{name:"arrow",enabled:!0,options:{element:C.current,padding:5}},{name:"offset",options:{offset:[0,10]}}]},anchorEl:w.current,sx:{zIndex:"9999",maxWidth:300}},m.default.createElement(_,null,m.default.createElement(b,{ref:C}),m.default.createElement(y.Stack,{alignItems:"start",spacing:2},m.default.createElement(y.Chip,{color:"promotion",variant:"outlined",size:"small",label:(0,h.__)("Pro","elementor"),icon:m.default.createElement(v.default,null)}),m.default.createElement(y.Typography,{variant:"body2"},(0,h.__)("This result includes an Elementor Pro widget that's not available with your current plan. Upgrade to use all the widgets in this result.","elementor")),m.default.createElement(y.Button,{variant:"contained",color:"promotion",size:"small",href:"https://go.elementor.com/go-pro-ai/",target:"_blank",sx:{alignSelf:"flex-end"}},l)))))}},45498:(l,u,s)=>{var c=s(10564).default,p=s(11327);l.exports=function toPropertyKey(l){var u=p(l,"string");return"symbol"==c(u)?u:u+""},l.exports.__esModule=!0,l.exports.default=l.exports},45549:(l,u,s)=>{"use strict";s.r(u),s.d(u,{getFontEmbedCSS:()=>getFontEmbedCSS,toBlob:()=>toBlob,toCanvas:()=>toCanvas,toJpeg:()=>toJpeg,toPixelData:()=>toPixelData,toPng:()=>toPng,toSvg:()=>toSvg});const c=(()=>{let l=0;return()=>(l+=1,`u${`0000${(Math.random()*36**4|0).toString(36)}`.slice(-4)}${l}`)})();function toArray(l){const u=[];for(let s=0,c=l.length;s<c;s++)u.push(l[s]);return u}let p=null;function getStyleProperties(l={}){return p||(l.includeStyleProperties?(p=l.includeStyleProperties,p):(p=toArray(window.getComputedStyle(document.documentElement)),p))}function px(l,u){const s=(l.ownerDocument.defaultView||window).getComputedStyle(l).getPropertyValue(u);return s?parseFloat(s.replace("px","")):0}function getImageSize(l,u={}){return{width:u.width||function getNodeWidth(l){const u=px(l,"border-left-width"),s=px(l,"border-right-width");return l.clientWidth+u+s}(l),height:u.height||function getNodeHeight(l){const u=px(l,"border-top-width"),s=px(l,"border-bottom-width");return l.clientHeight+u+s}(l)}}const m=16384;function createImage(l){return new Promise((u,s)=>{const c=new Image;c.onload=()=>{c.decode().then(()=>{requestAnimationFrame(()=>u(c))})},c.onerror=s,c.crossOrigin="anonymous",c.decoding="async",c.src=l})}async function nodeToDataURL(l,u,s){const c="http://www.w3.org/2000/svg",p=document.createElementNS(c,"svg"),m=document.createElementNS(c,"foreignObject");return p.setAttribute("width",`${u}`),p.setAttribute("height",`${s}`),p.setAttribute("viewBox",`0 0 ${u} ${s}`),m.setAttribute("width","100%"),m.setAttribute("height","100%"),m.setAttribute("x","0"),m.setAttribute("y","0"),m.setAttribute("externalResourcesRequired","true"),p.appendChild(m),m.appendChild(l),async function svgToDataURL(l){return Promise.resolve().then(()=>(new XMLSerializer).serializeToString(l)).then(encodeURIComponent).then(l=>`data:image/svg+xml;charset=utf-8,${l}`)}(p)}const isInstanceOfElement=(l,u)=>{if(l instanceof u)return!0;const s=Object.getPrototypeOf(l);return null!==s&&(s.constructor.name===u.name||isInstanceOfElement(s,u))};function getPseudoElementStyle(l,u,s,c){const p=`.${l}:${u}`,m=s.cssText?function formatCSSText(l){const u=l.getPropertyValue("content");return`${l.cssText} content: '${u.replace(/'|"/g,"")}';`}(s):function formatCSSProperties(l,u){return getStyleProperties(u).map(u=>`${u}: ${l.getPropertyValue(u)}${l.getPropertyPriority(u)?" !important":""};`).join(" ")}(s,c);return document.createTextNode(`${p}{${m}}`)}function clonePseudoElement(l,u,s,p){const m=window.getComputedStyle(l,s),g=m.getPropertyValue("content");if(""===g||"none"===g)return;const h=c();try{u.className=`${u.className} ${h}`}catch(l){return}const y=document.createElement("style");y.appendChild(getPseudoElementStyle(h,s,m,p)),u.appendChild(y)}const g="application/font-woff",h="image/jpeg",y={woff:g,woff2:g,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:h,jpeg:h,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml",webp:"image/webp"};function getMimeType(l){const u=function getExtension(l){const u=/\.([^./]*?)$/g.exec(l);return u?u[1]:""}(l).toLowerCase();return y[u]||""}function isDataUrl(l){return-1!==l.search(/^(data:)/)}function makeDataUrl(l,u){return`data:${u};base64,${l}`}async function fetchAsDataURL(l,u,s){const c=await fetch(l,u);if(404===c.status)throw new Error(`Resource "${c.url}" not found`);const p=await c.blob();return new Promise((l,u)=>{const m=new FileReader;m.onerror=u,m.onloadend=()=>{try{l(s({res:c,result:m.result}))}catch(l){u(l)}},m.readAsDataURL(p)})}const v={};async function resourceToDataURL(l,u,s){const c=function getCacheKey(l,u,s){let c=l.replace(/\?.*/,"");return s&&(c=l),/ttf|otf|eot|woff2?/i.test(c)&&(c=c.replace(/.*\//,"")),u?`[${u}]${c}`:c}(l,u,s.includeQueryParams);if(null!=v[c])return v[c];let p;s.cacheBust&&(l+=(/\?/.test(l)?"&":"?")+(new Date).getTime());try{const c=await fetchAsDataURL(l,s.fetchRequestInit,({res:l,result:s})=>(u||(u=l.headers.get("Content-Type")||""),function getContentFromDataUrl(l){return l.split(/,/)[1]}(s)));p=makeDataUrl(c,u)}catch(u){p=s.imagePlaceholder||"";let c=`Failed to fetch resource: ${l}`;u&&(c="string"==typeof u?u:u.message),c&&console.warn(c)}return v[c]=p,p}async function cloneSingleNode(l,u){return isInstanceOfElement(l,HTMLCanvasElement)?async function cloneCanvasElement(l){const u=l.toDataURL();return"data:,"===u?l.cloneNode(!1):createImage(u)}(l):isInstanceOfElement(l,HTMLVideoElement)?async function cloneVideoElement(l,u){if(l.currentSrc){const u=document.createElement("canvas"),s=u.getContext("2d");return u.width=l.clientWidth,u.height=l.clientHeight,null==s||s.drawImage(l,0,0,u.width,u.height),createImage(u.toDataURL())}const s=l.poster,c=getMimeType(s);return createImage(await resourceToDataURL(s,c,u))}(l,u):isInstanceOfElement(l,HTMLIFrameElement)?async function cloneIFrameElement(l,u){var s;try{if(null===(s=null==l?void 0:l.contentDocument)||void 0===s?void 0:s.body)return await cloneNode(l.contentDocument.body,u,!0)}catch(l){}return l.cloneNode(!1)}(l,u):l.cloneNode(isSVGElement(l))}const isSlotElement=l=>null!=l.tagName&&"SLOT"===l.tagName.toUpperCase(),isSVGElement=l=>null!=l.tagName&&"SVG"===l.tagName.toUpperCase();function decorate(l,u,s){return isInstanceOfElement(u,Element)&&(!function cloneCSSStyle(l,u,s){const c=u.style;if(!c)return;const p=window.getComputedStyle(l);p.cssText?(c.cssText=p.cssText,c.transformOrigin=p.transformOrigin):getStyleProperties(s).forEach(s=>{let m=p.getPropertyValue(s);if("font-size"===s&&m.endsWith("px")){const l=Math.floor(parseFloat(m.substring(0,m.length-2)))-.1;m=`${l}px`}isInstanceOfElement(l,HTMLIFrameElement)&&"display"===s&&"inline"===m&&(m="block"),"d"===s&&u.getAttribute("d")&&(m=`path(${u.getAttribute("d")})`),c.setProperty(s,m,p.getPropertyPriority(s))})}(l,u,s),function clonePseudoElements(l,u,s){clonePseudoElement(l,u,":before",s),clonePseudoElement(l,u,":after",s)}(l,u,s),function cloneInputValue(l,u){isInstanceOfElement(l,HTMLTextAreaElement)&&(u.innerHTML=l.value),isInstanceOfElement(l,HTMLInputElement)&&u.setAttribute("value",l.value)}(l,u),function cloneSelectValue(l,u){if(isInstanceOfElement(l,HTMLSelectElement)){const s=u,c=Array.from(s.children).find(u=>l.value===u.getAttribute("value"));c&&c.setAttribute("selected","")}}(l,u)),u}async function cloneNode(l,u,s){return s||!u.filter||u.filter(l)?Promise.resolve(l).then(l=>cloneSingleNode(l,u)).then(s=>async function cloneChildren(l,u,s){var c,p;if(isSVGElement(u))return u;let m=[];return m=isSlotElement(l)&&l.assignedNodes?toArray(l.assignedNodes()):isInstanceOfElement(l,HTMLIFrameElement)&&(null===(c=l.contentDocument)||void 0===c?void 0:c.body)?toArray(l.contentDocument.body.childNodes):toArray((null!==(p=l.shadowRoot)&&void 0!==p?p:l).childNodes),0===m.length||isInstanceOfElement(l,HTMLVideoElement)||await m.reduce((l,c)=>l.then(()=>cloneNode(c,s)).then(l=>{l&&u.appendChild(l)}),Promise.resolve()),u}(l,s,u)).then(s=>decorate(l,s,u)).then(l=>async function ensureSVGSymbols(l,u){const s=l.querySelectorAll?l.querySelectorAll("use"):[];if(0===s.length)return l;const c={};for(let p=0;p<s.length;p++){const m=s[p].getAttribute("xlink:href");if(m){const s=l.querySelector(m),p=document.querySelector(m);s||!p||c[m]||(c[m]=await cloneNode(p,u,!0))}}const p=Object.values(c);if(p.length){const u="http://www.w3.org/1999/xhtml",s=document.createElementNS(u,"svg");s.setAttribute("xmlns",u),s.style.position="absolute",s.style.width="0",s.style.height="0",s.style.overflow="hidden",s.style.display="none";const c=document.createElementNS(u,"defs");s.appendChild(c);for(let l=0;l<p.length;l++)c.appendChild(p[l]);l.appendChild(s)}return l}(l,u)):null}const _=/url\((['"]?)([^'"]+?)\1\)/g,b=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,w=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;async function embed_resources_embed(l,u,s,c,p){try{const m=s?function resolveUrl(l,u){if(l.match(/^[a-z]+:\/\//i))return l;if(l.match(/^\/\//))return window.location.protocol+l;if(l.match(/^[a-z]+:/i))return l;const s=document.implementation.createHTMLDocument(),c=s.createElement("base"),p=s.createElement("a");return s.head.appendChild(c),s.body.appendChild(p),u&&(c.href=u),p.href=l,p.href}(u,s):u,g=getMimeType(u);let h;if(p){h=makeDataUrl(await p(m),g)}else h=await resourceToDataURL(m,g,c);return l.replace(function toRegex(l){const u=l.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return new RegExp(`(url\\(['"]?)(${u})(['"]?\\))`,"g")}(u),`$1${h}$3`)}catch(l){}return l}function shouldEmbed(l){return-1!==l.search(_)}async function embedResources(l,u,s){if(!shouldEmbed(l))return l;const c=function filterPreferredFontFormat(l,{preferredFontFormat:u}){return u?l.replace(w,l=>{for(;;){const[s,,c]=b.exec(l)||[];if(!c)return"";if(c===u)return`src: ${s};`}}):l}(l,s),p=function parseURLs(l){const u=[];return l.replace(_,(l,s,c)=>(u.push(c),l)),u.filter(l=>!isDataUrl(l))}(c);return p.reduce((l,c)=>l.then(l=>embed_resources_embed(l,c,u,s)),Promise.resolve(c))}async function embedProp(l,u,s){var c;const p=null===(c=u.style)||void 0===c?void 0:c.getPropertyValue(l);if(p){const c=await embedResources(p,null,s);return u.style.setProperty(l,c,u.style.getPropertyPriority(l)),!0}return!1}async function embedImages(l,u){isInstanceOfElement(l,Element)&&(await async function embedBackground(l,u){await embedProp("background",l,u)||await embedProp("background-image",l,u),await embedProp("mask",l,u)||await embedProp("-webkit-mask",l,u)||await embedProp("mask-image",l,u)||await embedProp("-webkit-mask-image",l,u)}(l,u),await async function embedImageNode(l,u){const s=isInstanceOfElement(l,HTMLImageElement);if((!s||isDataUrl(l.src))&&(!isInstanceOfElement(l,SVGImageElement)||isDataUrl(l.href.baseVal)))return;const c=s?l.src:l.href.baseVal,p=await resourceToDataURL(c,getMimeType(c),u);await new Promise((c,m)=>{l.onload=c,l.onerror=u.onImageErrorHandler?(...l)=>{try{c(u.onImageErrorHandler(...l))}catch(l){m(l)}}:m;const g=l;g.decode&&(g.decode=c),"lazy"===g.loading&&(g.loading="eager"),s?(l.srcset="",l.src=p):l.href.baseVal=p})}(l,u),await async function embedChildren(l,u){const s=toArray(l.childNodes).map(l=>embedImages(l,u));await Promise.all(s).then(()=>l)}(l,u))}const C={};async function fetchCSS(l){let u=C[l];if(null!=u)return u;const s=await fetch(l);return u={url:l,cssText:await s.text()},C[l]=u,u}async function embedFonts(l,u){let s=l.cssText;const c=/url\(["']?([^"')]+)["']?\)/g,p=(s.match(/url\([^)]+\)/g)||[]).map(async p=>{let m=p.replace(c,"$1");return m.startsWith("https://")||(m=new URL(m,l.url).href),fetchAsDataURL(m,u.fetchRequestInit,({result:l})=>(s=s.replace(p,`url(${l})`),[p,l]))});return Promise.all(p).then(()=>s)}function parseCSS(l){if(null==l)return[];const u=[];let s=l.replace(/(\/\*[\s\S]*?\*\/)/gi,"");const c=new RegExp("((@.*?keyframes [\\s\\S]*?){([\\s\\S]*?}\\s*?)})","gi");for(;;){const l=c.exec(s);if(null===l)break;u.push(l[0])}s=s.replace(c,"");const p=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,m=new RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let l=p.exec(s);if(null===l){if(l=m.exec(s),null===l)break;p.lastIndex=m.lastIndex}else m.lastIndex=p.lastIndex;u.push(l[0])}return u}async function parseWebFontRules(l,u){if(null==l.ownerDocument)throw new Error("Provided element is not within a Document");const s=toArray(l.ownerDocument.styleSheets),c=await async function getCSSRules(l,u){const s=[],c=[];return l.forEach(s=>{if("cssRules"in s)try{toArray(s.cssRules||[]).forEach((l,p)=>{if(l.type===CSSRule.IMPORT_RULE){let m=p+1;const g=fetchCSS(l.href).then(l=>embedFonts(l,u)).then(l=>parseCSS(l).forEach(l=>{try{s.insertRule(l,l.startsWith("@import")?m+=1:s.cssRules.length)}catch(u){console.error("Error inserting rule from remote css",{rule:l,error:u})}})).catch(l=>{console.error("Error loading remote css",l.toString())});c.push(g)}})}catch(p){const m=l.find(l=>null==l.href)||document.styleSheets[0];null!=s.href&&c.push(fetchCSS(s.href).then(l=>embedFonts(l,u)).then(l=>parseCSS(l).forEach(l=>{m.insertRule(l,m.cssRules.length)})).catch(l=>{console.error("Error loading remote stylesheet",l)})),console.error("Error inlining remote css file",p)}}),Promise.all(c).then(()=>(l.forEach(l=>{if("cssRules"in l)try{toArray(l.cssRules||[]).forEach(l=>{s.push(l)})}catch(u){console.error(`Error while reading CSS rules from ${l.href}`,u)}}),s))}(s,u);return function getWebFontRules(l){return l.filter(l=>l.type===CSSRule.FONT_FACE_RULE).filter(l=>shouldEmbed(l.style.getPropertyValue("src")))}(c)}function normalizeFontFamily(l){return l.trim().replace(/["']/g,"")}async function getWebFontCSS(l,u){const s=await parseWebFontRules(l,u),c=function getUsedFonts(l){const u=new Set;return function traverse(l){(l.style.fontFamily||getComputedStyle(l).fontFamily).split(",").forEach(l=>{u.add(normalizeFontFamily(l))}),Array.from(l.children).forEach(l=>{l instanceof HTMLElement&&traverse(l)})}(l),u}(l);return(await Promise.all(s.filter(l=>c.has(normalizeFontFamily(l.style.fontFamily))).map(l=>{const s=l.parentStyleSheet?l.parentStyleSheet.href:null;return embedResources(l.cssText,s,u)}))).join("\n")}async function toSvg(l,u={}){const{width:s,height:c}=getImageSize(l,u),p=await cloneNode(l,u,!0);await async function embedWebFonts(l,u){const s=null!=u.fontEmbedCSS?u.fontEmbedCSS:u.skipFonts?null:await getWebFontCSS(l,u);if(s){const u=document.createElement("style"),c=document.createTextNode(s);u.appendChild(c),l.firstChild?l.insertBefore(u,l.firstChild):l.appendChild(u)}}(p,u),await embedImages(p,u),function applyStyle(l,u){const{style:s}=l;u.backgroundColor&&(s.backgroundColor=u.backgroundColor),u.width&&(s.width=`${u.width}px`),u.height&&(s.height=`${u.height}px`);const c=u.style;return null!=c&&Object.keys(c).forEach(l=>{s[l]=c[l]}),l}(p,u);return await nodeToDataURL(p,s,c)}async function toCanvas(l,u={}){const{width:s,height:c}=getImageSize(l,u),p=await toSvg(l,u),g=await createImage(p),h=document.createElement("canvas"),y=h.getContext("2d"),v=u.pixelRatio||function getPixelRatio(){let l,u;try{u=process}catch(l){}const s=u&&u.env?u.env.devicePixelRatio:null;return s&&(l=parseInt(s,10),Number.isNaN(l)&&(l=1)),l||window.devicePixelRatio||1}(),_=u.canvasWidth||s,b=u.canvasHeight||c;return h.width=_*v,h.height=b*v,u.skipAutoScale||function checkCanvasDimensions(l){(l.width>m||l.height>m)&&(l.width>m&&l.height>m?l.width>l.height?(l.height*=m/l.width,l.width=m):(l.width*=m/l.height,l.height=m):l.width>m?(l.height*=m/l.width,l.width=m):(l.width*=m/l.height,l.height=m))}(h),h.style.width=`${_}`,h.style.height=`${b}`,u.backgroundColor&&(y.fillStyle=u.backgroundColor,y.fillRect(0,0,h.width,h.height)),y.drawImage(g,0,0,h.width,h.height),h}async function toPixelData(l,u={}){const{width:s,height:c}=getImageSize(l,u);return(await toCanvas(l,u)).getContext("2d").getImageData(0,0,s,c).data}async function toPng(l,u={}){return(await toCanvas(l,u)).toDataURL()}async function toJpeg(l,u={}){return(await toCanvas(l,u)).toDataURL("image/jpeg",u.quality||1)}async function toBlob(l,u={}){const s=await toCanvas(l,u),c=await function canvasToBlob(l,u={}){return l.toBlob?new Promise(s=>{l.toBlob(s,u.type?u.type:"image/png",u.quality?u.quality:1)}):new Promise(s=>{const c=window.atob(l.toDataURL(u.type?u.type:void 0,u.quality?u.quality:void 0).split(",")[1]),p=c.length,m=new Uint8Array(p);for(let l=0;l<p;l+=1)m[l]=c.charCodeAt(l);s(new Blob([m],{type:u.type?u.type:"image/png"}))})}(s);return c}async function getFontEmbedCSS(l,u={}){return getWebFontCSS(l,u)}},46313:(l,u,s)=>{var c=s(9535),p=s(33929);l.exports=function _regeneratorAsyncGen(l,u,s,m,g){return new p(c().w(l,u,s,m),g||Promise)},l.exports.__esModule=!0,l.exports.default=l.exports},47350:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.browserPrefixToKey=browserPrefixToKey,u.browserPrefixToStyle=function browserPrefixToStyle(l,u){return u?"-".concat(u.toLowerCase(),"-").concat(l):l},u.default=void 0,u.getPrefix=getPrefix;const s=["Moz","Webkit","O","ms"];function getPrefix(){var l;let u=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";const c=null===(l=window.document)||void 0===l||null===(l=l.documentElement)||void 0===l?void 0:l.style;if(!c)return"";if(u in c)return"";for(let l=0;l<s.length;l++)if(browserPrefixToKey(u,s[l])in c)return s[l];return""}function browserPrefixToKey(l,u){return u?"".concat(u).concat(function kebabToTitleCase(l){let u="",s=!0;for(let c=0;c<l.length;c++)s?(u+=l[c].toUpperCase(),s=!1):"-"===l[c]?s=!0:u+=l[c];return u}(l)):l}u.default=getPrefix()},47389:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(39805)),m=c(s(40989)),g=c(s(15118)),h=c(s(29402)),y=c(s(87861)),v=c(s(85707)),_=s(12470),b=s(36833),w=s(91258);function _isNativeReflectConstruct(){try{var l=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(l){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!l})()}u.default=function(l){function AiLayoutBehavior(){var l;(0,p.default)(this,AiLayoutBehavior);for(var u=arguments.length,s=new Array(u),c=0;c<u;c++)s[c]=arguments[c];return l=function _callSuper(l,u,s){return u=(0,h.default)(u),(0,g.default)(l,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,h.default)(l).constructor):u.apply(l,s))}(this,AiLayoutBehavior,[].concat(s)),(0,v.default)(l,"previewContainer",null),l}return(0,y.default)(AiLayoutBehavior,l),(0,m.default)(AiLayoutBehavior,[{key:"ui",value:function ui(){return{aiButton:".e-ai-layout-button",addTemplateButton:".elementor-add-template-button"}}},{key:"events",value:function events(){return{"click @ui.aiButton":"onAiButtonClick"}}},{key:"onAiButtonClick",value:function onAiButtonClick(l){l.stopPropagation(),window.elementorAiCurrentContext=this.getOption("context"),(0,b.renderLayoutApp)({parentContainer:elementor.getPreviewContainer(),mode:w.MODE_LAYOUT,at:this.view.getOption("at"),onInsert:this.onInsert.bind(this),onRenderApp:function onRenderApp(l){l.previewContainer.init()},onGenerate:function onGenerate(l){l.previewContainer.reset()}})}},{key:"hideDropArea",value:function hideDropArea(){this.view.onCloseButtonClick()}},{key:"onInsert",value:function onInsert(l){this.hideDropArea(),(0,b.importToEditor)({parentContainer:elementor.getPreviewContainer(),at:this.view.getOption("at"),template:l,historyTitle:(0,_.__)("AI Layout","elementor")})}},{key:"onRender",value:function onRender(){var l=jQuery("<button>",{type:"button",class:"e-ai-layout-button elementor-add-section-area-button e-button-primary",title:(0,_.__)("Build with AI","elementor"),"aria-label":(0,_.__)("Build with AI","elementor")});l.html('\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<i class="eicon-ai" aria-hidden="true"></i>\n\t\t'),this.ui.addTemplateButton.after(l)}}])}(Marionette.Behavior)},47407:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=s(86956),g=c(s(62688)),h=function LayoutAppWrapper(l){return p.default.createElement(m.DirectionProvider,{rtl:l.isRTL},p.default.createElement(m.ThemeProvider,{colorScheme:l.colorScheme},l.children))};h.propTypes={children:g.default.node,isRTL:g.default.bool,colorScheme:g.default.oneOf(["auto","light","dark"])};u.default=h},47547:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.takeScreenshot=void 0;var p=c(s(61790)),m=c(s(10906)),g=c(s(58155)),h=s(45549),y=s(40327),v=s(40128);u.takeScreenshot=function(){var l=(0,g.default)(p.default.mark(function _callee(l){var u,s,c;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:if(l){p.next=1;break}return p.abrupt("return","");case 1:return(0,y.toggleHistory)(!1),u=createHiddenWrapper(),wrapContainer(s=createContainer(l),u),elementor.getPreviewView().$childViewContainer[0].appendChild(u),p.next=2,waitForContainer(s.id);case 2:if(!l.elements.length){p.next=3;break}return p.next=3,Promise.all(l.elements.map(function(l){return waitForContainer(l.id)}));case 3:return p.prev=3,p.next=4,screenshotNode(s.view.$el[0]);case 4:c=p.sent,p.next=6;break;case 5:p.prev=5,p.catch(3),c="";case 6:return deleteContainer(s),u.remove(),(0,y.toggleHistory)(!0),p.abrupt("return",c);case 7:case"end":return p.stop()}},_callee,null,[[3,5]])}));return function takeScreenshot(u){return l.apply(this,arguments)}}();function screenshotNode(l){return function toWebp(l){return _toWebp.apply(this,arguments)}(l,{quality:.01,imagePlaceholder:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="})}function _toWebp(){return _toWebp=(0,g.default)(p.default.mark(function _callee3(l){var u,s,c,m=arguments;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return s=m.length>1&&void 0!==m[1]?m[1]:{},p.next=1,(0,h.toCanvas)(l,s);case 1:return c=p.sent,p.abrupt("return",c.toDataURL("image/webp",null!==(u=s.quality)&&void 0!==u?u:1));case 2:case"end":return p.stop()}},_callee3)})),_toWebp.apply(this,arguments)}function createHiddenWrapper(){var l=document.createElement("div");return l.style.position="fixed",l.style.opacity="0",l.style.inset="0",l}function createContainer(l){var u=(0,v.generateIds)(l);return u.id="e-ai-screenshot-container-".concat(u.id),$e.run("document/elements/create",{container:elementor.getPreviewContainer(),model:u,options:{edit:!1}})}function deleteContainer(l){return $e.run("document/elements/delete",{container:l})}function waitForContainer(l){var u=function sleep(l){return new Promise(function(u){return setTimeout(u,l)})}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3),s=new Promise(function(u){elementorFrontend.hooks.addAction("frontend/element_ready/global",function(){var s=(0,g.default)(p.default.mark(function _callee2(s){var c;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:if(s.data("id")!==l){p.next=2;break}return c=(0,m.default)(s[0].querySelectorAll("img")),p.next=1,Promise.all(c.map(waitForImage));case 1:u();case 2:case"end":return p.stop()}},_callee2)}));return function(l){return s.apply(this,arguments)}}())});return Promise.any([u,s])}function waitForImage(l){return l.complete?Promise.resolve():new Promise(function(u){l.addEventListener("load",u),l.addEventListener("error",function(){l.remove(),u()})})}function wrapContainer(l,u){var s=l.view.$el[0];s.parentNode.insertBefore(u,s),u.appendChild(s)}},48584:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(78304)),g=c(s(40453)),h=s(86956),y=s(12470),v=c(s(62688)),_=c(s(53532)),b=["isLoading"],w=(0,h.withDirection)(_.default),C=function EnhanceButton(l){var u=l.isLoading,s=(0,g.default)(l,b);return p.default.createElement(h.Tooltip,{title:(0,y.__)("Enhance prompt","elementor")},p.default.createElement(h.Box,{component:"span",sx:{cursor:s.disabled?"default":"pointer"}},p.default.createElement(h.IconButton,(0,m.default)({size:"small",color:"secondary"},s),u?p.default.createElement(h.CircularProgress,{color:"secondary",size:20}):p.default.createElement(w,{fontSize:"small"}))))};C.propTypes={disabled:v.default.bool,isLoading:v.default.bool};u.default=C},48812:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(61790)),m=c(s(85707)),g=c(s(58155)),h=c(s(18821)),y=c(s(40453)),v=s(41594),_=s(95034),b=s(40128),w=["text","response_id","usage","images"];function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,m.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var C=function normalizeResponse(l){var u=l.text,s=l.response_id,c=l.usage,p=l.images,m=(0,y.default)(l,w),g=c?c.quota-c.usedQuota:0,h={result:u||p,responseId:s,credits:Math.max(g,0),usagePercentage:null==c?void 0:c.usagePercentage};return m.base_template_id&&(h.baseTemplateId=m.base_template_id),h.type=m.template_type,h};u.default=function usePrompt(l,u){var s=(0,v.useState)(!1),c=(0,h.default)(s,2),m=c[0],y=c[1],w=(0,v.useState)(""),x=(0,h.default)(w,2),P=x[0],E=x[1],O=(0,v.useState)(u),S=(0,h.default)(O,2),T=S[0],R=S[1],j=(0,b.useRequestIds)(),M=j.updateUsagePercentage,I=j.usagePercentage,k=(0,v.useRef)(function(){var l=(0,g.default)(p.default.mark(function _callee(l){return p.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",l);case 1:case"end":return u.stop()}},_callee)}));return function(u){return l.apply(this,arguments)}}()),A=(0,v.useRef)(function(){});(0,v.useEffect)(function(){var l=null==T?void 0:T.usagePercentage;l&&l!==I&&M(l)},[T,I,M]);var D=(0,b.useRequestIds)(),L=D.setRequest,q=D.editorSessionId,B=D.sessionId,W=D.generateId,N=D.batchId;k.current=(0,v.useCallback)(function(){var u=(0,g.default)(p.default.mark(function _callee2(u){return p.default.wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.abrupt("return",new Promise(function(s,c){E(""),y(!0);var p=L(),m={editorSessionId:q.current,sessionId:B.current,generateId:W.current,batchId:N.current,requestId:p.current};u=_objectSpread(_objectSpread({},u),{},{requestIds:m}),l(u).then(function(l){var u=C(l);R(u),s(u)}).catch(function(l){var u=(null==l?void 0:l.responseText)||l;E(u),c(u)}).finally(function(){return y(!1)})}));case 1:case"end":return s.stop()}},_callee2)}));return function(l){return u.apply(this,arguments)}}(),[N,q,l,W,B,L]),A.current=(0,v.useCallback)(function(){var l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:T;return l.responseId&&(0,_.setStatusFeedback)(l.responseId)},[T]);return{isLoading:m,error:P,data:T,setResult:function setResult(l){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=_objectSpread({},T);s.result=l,u&&(s.responseId=u),R(s)},reset:function reset(){R(function(l){return{credits:l.credits,result:"",responseId:""}}),E(""),y(!1)},send:k.current,sendUsageData:A.current}}},48968:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(78304)),h=c(s(85707)),y=c(s(18821)),v=s(86956),_=c(s(62688)),b=c(s(38230)),w=c(s(99476));function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,h.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var C=function DraggablePaper(l){var u=(0,m.useState)({x:0,y:0}),s=(0,y.default)(u,2),c=s[0],p=s[1],h=(0,m.useRef)(null),_=(0,m.useRef)(null),w=function handlePositionBoundaries(){clearTimeout(_.current),_.current=setTimeout(function(){var l,u=null===(l=h.current)||void 0===l?void 0:l.getBoundingClientRect().top;u<0&&p(function(l){return _objectSpread(_objectSpread({},l),{},{y:l.y-u})})},50)};return(0,m.useEffect)(function(){var l=new ResizeObserver(w);return l.observe(h.current),function(){l.disconnect()}},[]),m.default.createElement(b.default,{position:c,onDrag:function onDrag(l,u){var s=u.x,c=u.y;return p({x:s,y:c})},handle:".MuiAppBar-root",cancel:'[class*="MuiDialogContent-root"]',bounds:"parent"},m.default.createElement(v.Paper,(0,g.default)({},l,{ref:h})))},x=function PromptDialog(l){return m.default.createElement(v.Dialog,(0,g.default)({scroll:"paper",open:!0,fullWidth:!0,hideBackdrop:!0,PaperComponent:C,disableScrollLock:!0,sx:{"& .MuiDialog-container":{alignItems:"flex-start",mt:"18vh"}},PaperProps:{sx:{m:0,maxHeight:"76vh"}}},l),l.children)};x.propTypes={onClose:_.default.func.isRequired,children:_.default.node,maxWidth:_.default.oneOf(["xs","sm","md","lg","xl",!1])},x.Header=w.default,x.Content=v.DialogContent;u.default=x},50923:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({},l,{ref:u}),m.default.createElement("svg",{width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},m.default.createElement("g",{clipPath:"url(#clip0_10743_8902)"},m.default.createElement("path",{d:"M2.75 10.0833H3.66667M11 2.75V3.66667M18.3333 10.0833H19.25M5.13333 5.13333L5.775 5.775M16.8667 5.13333L16.225 5.775",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.default.createElement("path",{d:"M9.16675 16.041C8.70841 15.1243 6.91205 13.2842 6.62523 12.366C6.3384 11.4477 6.34775 10.4626 6.65195 9.54997C6.95615 8.63738 7.53978 7.84362 8.32016 7.28116C9.10054 6.71869 10.0381 6.41602 11.0001 6.41602C11.962 6.41602 12.8996 6.71869 13.68 7.28116C14.4604 7.84362 15.044 8.63738 15.3482 9.54997C15.6524 10.4626 15.6618 11.4477 15.3749 12.366C15.0881 13.2842 13.2917 15.1243 12.8334 16.041C12.8334 16.041 12.7597 17.3762 12.8334 17.8743C12.8334 18.3606 12.6403 18.8269 12.2964 19.1707C11.9526 19.5145 11.4863 19.7077 11.0001 19.7077C10.5139 19.7077 10.0475 19.5145 9.70372 19.1707C9.3599 18.8269 9.16675 18.3606 9.16675 17.8743C9.2405 17.3762 9.16675 16.041 9.16675 16.041Z",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),m.default.createElement("path",{d:"M10.0833 16.5H11.9166",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),m.default.createElement("defs",null,m.default.createElement("clipPath",{id:"clip0_10743_8902"},m.default.createElement("rect",{width:"22",height:"22",fill:"white"})))))});u.default=h},51066:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9697 4.96967C14.6408 4.29858 15.5509 3.92157 16.5 3.92157C17.4491 3.92157 18.3592 4.29858 19.0303 4.96967C19.7014 5.64075 20.0784 6.55094 20.0784 7.5C20.0784 8.44905 19.7014 9.35924 19.0303 10.0303L8.53033 20.5303C8.38968 20.671 8.19891 20.75 8 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20V16C3.25 15.8011 3.32902 15.6103 3.46967 15.4697L13.9697 4.96967ZM16.5 5.42157C15.9488 5.42157 15.4201 5.64055 15.0303 6.03033L4.75 16.3107V19.25H7.68934L17.9697 8.96967C18.3595 8.57989 18.5784 8.05123 18.5784 7.5C18.5784 6.94876 18.3595 6.42011 17.9697 6.03033C17.5799 5.64055 17.0512 5.42157 16.5 5.42157Z"}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.9697 5.96967C13.2626 5.67677 13.7374 5.67677 14.0303 5.96967L18.0303 9.96967C18.3232 10.2626 18.3232 10.7374 18.0303 11.0303C17.7374 11.3232 17.2626 11.3232 16.9697 11.0303L12.9697 7.03033C12.6768 6.73743 12.6768 6.26256 12.9697 5.96967Z"}))});u.default=h},51550:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=_interopRequireWildcard(s(41594)),g=c(s(10906)),h=c(s(40453)),y=c(s(18821)),v=c(s(78304)),_=c(s(62688)),b=s(12470),w=s(86956),C=c(s(43091)),x=c(s(64162)),P=c(s(75690)),E=c(s(85076)),O=c(s(33057)),S=c(s(79919)),T=c(s(85614)),R=_interopRequireWildcard(s(68022)),j=c(s(33724)),M=c(s(53952)),I=s(91258),k=s(38298),A=s(54403),D=s(4353),L=c(s(89958)),q=c(s(94760)),B=s(90291),W=["children"];function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h})(l,u)}var N=(0,w.withDirection)(j.default),H=(0,w.withDirection)(M.default),U=function RegenerateButton(l){return m.default.createElement(w.Button,(0,v.default)({size:"small",color:"secondary",startIcon:m.default.createElement(O.default,null)},l),(0,b.__)("Regenerate","elementor"))},V=function UseLayoutButton(l){return m.default.createElement(w.Button,(0,v.default)({size:"small",variant:"contained"},l),(0,b.__)("Use Layout","elementor"))};V.propTypes={sx:_.default.object};var F=function isRegenerateButtonDisabled(l,u,s){return!(!u&&!s)||l.length>=R.SCREENSHOTS_PER_PAGE*R.MAX_PAGES},G=function FormLayout(l){var u,s,c=l.DialogHeaderProps,p=void 0===c?{}:c,_=l.DialogContentProps,O=void 0===_?{}:_,j=l.attachments,M=(0,I.useConfig)(),k=M.attachmentsTypes,G=M.onData,$=M.onInsert,Y=M.onSelect,z=M.onClose,K=M.onGenerate,X=(0,T.default)({onData:G}),Z=X.screenshots,Q=X.generate,J=X.regenerate,ee=X.isLoading,te=X.error,re=X.abort,ne=(0,R.default)({slidesCount:Z.length}),oe=ne.currentPage,ae=ne.setCurrentPage,ie=ne.pagesCount,le=ne.gapPercentage,ue=ne.slidesPerPage,se=ne.offsetXPercentage,ce=ne.slideWidthPercentage,de=(0,m.useState)(-1),fe=(0,y.default)(de,2),pe=fe[0],me=fe[1],ge=(0,m.useState)(!1),he=(0,y.default)(ge,2),ye=he[0],ve=he[1],_e=(0,m.useState)(!0),be=(0,y.default)(_e,2),we=be[0],Ce=be[1],xe=(0,m.useState)([]),Pe=(0,y.default)(xe,2),Ee=Pe[0],Oe=Pe[1],Se=(0,m.useState)(!1),Te=(0,y.default)(Se,2),Re=Te[0],je=Te[1],Me=(0,m.useState)(!1),Ie=(0,y.default)(Me,2),ke=Ie[0],Ae=Ie[1],De=(0,m.useRef)(function(){}),Le=(0,m.useRef)(null),qe=null===(u=Z[pe])||void 0===u?void 0:u.template,Be=O.children,We=(0,h.default)(O,W),Ne=!(!te||0!==Z.length),He=we||Ne,Ue=function abortAndClose(){re(),z()},Ve=function onCloseIntent(){if(""!==Le.current.value.trim()||Z.length>0)return ve(!0);Ue()},Fe=function handleScreenshotClick(l,u){return function(){He||(me(l),Y(u))}},Ge=function onAttach(l){l.forEach(function(l){if(!k[l.type])throw new Error("Invalid attachment type: ".concat(l.type));var u=k[l.type];!l.previewHTML&&u.previewGenerator&&u.previewGenerator(l.content).then(function(u){l.previewHTML=u,Oe(function(u){return u.map(function(u){return u.content===l.content?l:u})})})}),Oe(l),je(!1),Ce(!0)};return(0,m.useEffect)(function(){var l;(null===(l=Z[0])||void 0===l?void 0:l.template)&&(Y(Z[0].template),me(0))},[null===(s=Z[0])||void 0===s?void 0:s.template]),(0,m.useEffect)(function(){null!=j&&j.length&&Ge(j)},[]),m.default.createElement(P.default,{onClose:Ve},m.default.createElement(P.default.Header,(0,v.default)({onClose:Ve},p),p.children,m.default.createElement(w.Tooltip,{title:ke?(0,b.__)("Expand","elementor"):(0,b.__)("Minimize","elementor")},m.default.createElement(w.IconButton,{size:"small","aria-label":"minimize",onClick:function onClick(){return Ae(function(l){return!l})}},ke?m.default.createElement(H,null):m.default.createElement(N,null)))),m.default.createElement(P.default.Content,(0,v.default)({dividers:!0},We),m.default.createElement(w.Collapse,{in:!ke},Be&&m.default.createElement(w.Box,{sx:{pt:2,px:2,pb:0}},Be),Ee.length>0&&m.default.createElement(A.PromptPowerNotice,null),te&&m.default.createElement(w.Box,{sx:{pt:2,px:2,pb:0}},m.default.createElement(C.default,{error:te,onRetry:De.current})),ye&&m.default.createElement(x.default,{open:ye,title:(0,b.__)("Leave Elementor AI?","elementor"),text:(0,b.__)("Your progress will be deleted, and can't be recovered.","elementor"),onClose:Ue,onCancel:function onCancel(){return ve(!1)}}),Re&&m.default.createElement(L.default,{type:D.ATTACHMENT_TYPE_URL,url:Le.current.value,onAttach:Ge,onClose:function onClose(){je(!1)}}),m.default.createElement(E.default,{shouldResetPrompt:Re,ref:Le,isActive:He,isLoading:ee,showActions:Z.length>0||ee,attachmentsTypes:k,attachments:Ee,onAttach:Ge,onDetach:function onDetach(l){Oe(function(u){var s=(0,g.default)(u);return s.splice(l,1),s}),Ce(!0)},onSubmit:function handleGenerate(l,u){l.preventDefault(),""===u.trim()&&0===Ee.length||((0,q.default)(u)?je(!0):(K(),De.current=function(){me(-1),Q(u,Ee)},De.current(),Ce(!1),ae(1)))},onBack:function onBack(){return Ce(!1)},onEdit:function onEdit(){return Ce(!0)}}),(Z.length>0||ee)&&m.default.createElement(m.default.Fragment,null,m.default.createElement(w.Divider,null),m.default.createElement(w.Box,{sx:{p:1.5}},m.default.createElement(w.Box,{sx:{overflow:"hidden",p:.5}},m.default.createElement(w.Box,{sx:{display:"flex",transition:"all 0.4s ease",gap:"".concat(le,"%"),transform:"translateX(".concat(se,"%)")}},Z.map(function(l,u){var s=l.screenshot,c=l.type,p=l.template,g=l.isError,h=l.isPending;return m.default.createElement(S.default,{key:u,url:s,type:c,disabled:He,isPlaceholder:g,isLoading:h,isSelected:pe===u,onClick:Fe(u,p),outlineOffset:"2px",sx:{flex:"0 0 ".concat(ce,"%")}})}))),m.default.createElement(B.VoicePromotionAlert,{introductionKey:"ai-context-layout-promotion"})),Z.length>0&&m.default.createElement(w.Box,{sx:{pt:0,px:2,pb:2},display:"grid",gridTemplateColumns:"repeat(3, 1fr)",justifyItems:"center"},m.default.createElement(U,{onClick:function handleRegenerate(){De.current=function(){J(Le.current.value,Ee),ae(ie+1)},De.current()},disabled:F(Z,ee,He),sx:{justifySelf:"start"}}),Z.length>ue&&m.default.createElement(w.Pagination,{page:oe,count:ie,disabled:He,onChange:function onChange(l,u){return ae(u)}}),m.default.createElement(V,{onClick:function applyTemplate(){$(qe),Z[pe].sendUsageData(),Ue()},disabled:He||-1===pe,sx:{justifySelf:"end",gridColumn:3}}))))))};G.propTypes={DialogHeaderProps:_.default.object,DialogContentProps:_.default.object,attachments:_.default.arrayOf(k.AttachmentPropType)};u.default=G},51563:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(62688)),g=s(91258),h=s(45395),y=function TemplateBadge(l){var u=(0,g.useConfig)().hasPro;return"Pro"!==l.type||u?null:p.default.createElement(h.ProTemplateIndicator,null)};u.default=y;y.propTypes={type:m.default.string}},53051:(l,u,s)=>{var c=s(67114),p=s(9535),m=s(62507),g=s(46313),h=s(33929),y=s(95315),v=s(66961);function _regeneratorRuntime(){"use strict";var u=p(),s=u.m(_regeneratorRuntime),_=(Object.getPrototypeOf?Object.getPrototypeOf(s):s.__proto__).constructor;function n(l){var u="function"==typeof l&&l.constructor;return!!u&&(u===_||"GeneratorFunction"===(u.displayName||u.name))}var b={throw:1,return:2,break:3,continue:3};function a(l){var u,s;return function(c){u||(u={stop:function stop(){return s(c.a,2)},catch:function _catch(){return c.v},abrupt:function abrupt(l,u){return s(c.a,b[l],u)},delegateYield:function delegateYield(l,p,m){return u.resultName=p,s(c.d,v(l),m)},finish:function finish(l){return s(c.f,l)}},s=function t(l,s,p){c.p=u.prev,c.n=u.next;try{return l(s,p)}finally{u.next=c.n}}),u.resultName&&(u[u.resultName]=c.v,u.resultName=void 0),u.sent=c.v,u.next=c.n;try{return l.call(this,u)}finally{c.p=u.prev,c.n=u.next}}}return(l.exports=_regeneratorRuntime=function _regeneratorRuntime(){return{wrap:function wrap(l,s,c,p){return u.w(a(l),s,c,p&&p.reverse())},isGeneratorFunction:n,mark:u.m,awrap:function awrap(l,u){return new c(l,u)},AsyncIterator:h,async:function async(l,u,s,c,p){return(n(u)?g:m)(a(l),u,s,c,p)},keys:y,values:v}},l.exports.__esModule=!0,l.exports.default=l.exports)()}l.exports=_regeneratorRuntime,l.exports.__esModule=!0,l.exports.default=l.exports},53497:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=s(86956),g=s(12470),h=c(s(62688)),y=function PromptLibraryLink(l){return p.default.createElement(m.Typography,{variant:"body2",color:"text.secondary"},(0,g.__)("For more suggestions, explore our")," ",p.default.createElement(m.Link,{href:l.libraryLink,className:"elementor-clickable",target:"_blank"},(0,g.__)("prompt library")))};y.propTypes={libraryLink:h.default.string};u.default=y},53532:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 2.25C9.41421 2.25 9.75 2.58579 9.75 3C9.75 3.33152 9.8817 3.64946 10.1161 3.88388C10.3505 4.1183 10.6685 4.25 11 4.25C11.4142 4.25 11.75 4.58579 11.75 5C11.75 5.41421 11.4142 5.75 11 5.75C10.6685 5.75 10.3505 5.8817 10.1161 6.11612C9.8817 6.35054 9.75 6.66848 9.75 7C9.75 7.41421 9.41421 7.75 9 7.75C8.58579 7.75 8.25 7.41421 8.25 7C8.25 6.66848 8.1183 6.35054 7.88388 6.11612C7.64946 5.8817 7.33152 5.75 7 5.75C6.58579 5.75 6.25 5.41421 6.25 5C6.25 4.58579 6.58579 4.25 7 4.25C7.33152 4.25 7.64946 4.1183 7.88388 3.88388C8.1183 3.64946 8.25 3.33152 8.25 3C8.25 2.58579 8.58579 2.25 9 2.25ZM9 4.88746C8.98182 4.90673 8.96333 4.92576 8.94454 4.94454C8.92576 4.96333 8.90673 4.98182 8.88746 5C8.90673 5.01818 8.92576 5.03667 8.94454 5.05546C8.96333 5.07424 8.98182 5.09327 9 5.11254C9.01818 5.09327 9.03667 5.07424 9.05546 5.05546C9.07424 5.03667 9.09327 5.01818 9.11254 5C9.09327 4.98182 9.07424 4.96333 9.05546 4.94454C9.03667 4.92576 9.01818 4.90673 9 4.88746Z"}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 2.46967C18.2374 2.17678 17.7626 2.17678 17.4697 2.46967L2.46967 17.4697C2.17678 17.7626 2.17678 18.2374 2.46967 18.5303L5.46967 21.5303C5.76256 21.8232 6.23744 21.8232 6.53033 21.5303L21.5303 6.53033C21.8232 6.23744 21.8232 5.76256 21.5303 5.46967L18.5303 2.46967ZM18 7.93934L19.9393 6L18 4.06066L16.0607 6L18 7.93934ZM15 7.06066L16.9393 9L6 19.9393L4.06066 18L15 7.06066Z"}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.75 13C19.75 12.5858 19.4142 12.25 19 12.25C18.5858 12.25 18.25 12.5858 18.25 13C18.25 13.3315 18.1183 13.6495 17.8839 13.8839C17.6495 14.1183 17.3315 14.25 17 14.25C16.5858 14.25 16.25 14.5858 16.25 15C16.25 15.4142 16.5858 15.75 17 15.75C17.3315 15.75 17.6495 15.8817 17.8839 16.1161C18.1183 16.3505 18.25 16.6685 18.25 17C18.25 17.4142 18.5858 17.75 19 17.75C19.4142 17.75 19.75 17.4142 19.75 17C19.75 16.6685 19.8817 16.3505 20.1161 16.1161C20.3505 15.8817 20.6685 15.75 21 15.75C21.4142 15.75 21.75 15.4142 21.75 15C21.75 14.5858 21.4142 14.25 21 14.25C20.6685 14.25 20.3505 14.1183 20.1161 13.8839C19.8817 13.6495 19.75 13.3315 19.75 13ZM18.9445 14.9445C18.9633 14.9258 18.9818 14.9067 19 14.8875C19.0182 14.9067 19.0367 14.9258 19.0555 14.9445C19.0742 14.9633 19.0933 14.9818 19.1125 15C19.0933 15.0182 19.0742 15.0367 19.0555 15.0555C19.0367 15.0742 19.0182 15.0933 19 15.1125C18.9818 15.0933 18.9633 15.0742 18.9445 15.0555C18.9258 15.0367 18.9067 15.0182 18.8875 15C18.9067 14.9818 18.9258 14.9633 18.9445 14.9445Z"}))});u.default=h},53952:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25H8C8.41421 3.25 8.75 3.58579 8.75 4C8.75 4.41421 8.41421 4.75 8 4.75H5.81066L10.5303 9.46967C10.8232 9.76256 10.8232 10.2374 10.5303 10.5303C10.2374 10.8232 9.76256 10.8232 9.46967 10.5303L4.75 5.81066V8C4.75 8.41421 4.41421 8.75 4 8.75C3.58579 8.75 3.25 8.41421 3.25 8V4C3.25 3.58579 3.58579 3.25 4 3.25ZM13.4697 13.4697C13.7626 13.1768 14.2374 13.1768 14.5303 13.4697L19.25 18.1893V16C19.25 15.5858 19.5858 15.25 20 15.25C20.4142 15.25 20.75 15.5858 20.75 16V20C20.75 20.4142 20.4142 20.75 20 20.75H16C15.5858 20.75 15.25 20.4142 15.25 20C15.25 19.5858 15.5858 19.25 16 19.25H18.1893L13.4697 14.5303C13.1768 14.2374 13.1768 13.7626 13.4697 13.4697Z"}))});u.default=h},54403:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.PromptPowerNotice=void 0;var p=c(s(41594)),m=s(86956),g=s(12470),h=c(s(80366));u.PromptPowerNotice=function PromptPowerNotice(){var l=(0,h.default)("e-ai-builder-attachments-power"),u=l.isViewed,s=l.markAsViewed;return u?null:p.default.createElement(m.Box,{sx:{pt:2,px:2,pb:0}},p.default.createElement(m.Alert,{severity:"info",onClose:function onClose(){return s()}},p.default.createElement(m.Typography,{variant:"body2",display:"inline-block",sx:{paddingInlineEnd:1}},(0,g.__)("You’ve got the power.","elementor")),p.default.createElement(m.Typography,{variant:"body2",display:"inline-block"},(0,g.__)("Craft your prompt to affect content, images and/or colors - whichever you decide.","elementor"))))}},56441:l=>{"use strict";l.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},57988:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=function log(){0}},58155:l=>{function asyncGeneratorStep(l,u,s,c,p,m,g){try{var h=l[m](g),y=h.value}catch(l){return void s(l)}h.done?u(y):Promise.resolve(y).then(c,p)}l.exports=function _asyncToGenerator(l){return function(){var u=this,s=arguments;return new Promise(function(c,p){var m=l.apply(u,s);function _next(l){asyncGeneratorStep(m,c,p,_next,_throw,"next",l)}function _throw(l){asyncGeneratorStep(m,c,p,_next,_throw,"throw",l)}_next(void 0)})}},l.exports.__esModule=!0,l.exports.default=l.exports},60992:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.Menu=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=s(86956),y=c(s(65815)),v=c(s(96793)),_=c(s(62688)),b=s(89958),w=c(s(80366));(u.Menu=function Menu(l){var u=(0,m.useState)(!1),s=(0,g.default)(u,2),c=s[0],p=s[1],_=(0,m.useState)(null),C=(0,g.default)(_,2),x=C[0],P=C[1],E=(0,h.useTheme)().direction,O=(0,m.useRef)(null),S=(0,w.default)("e-ai-attachment-badge"),T=S.isViewed,R=S.markAsViewed;return m.default.createElement(m.default.Fragment,null,m.default.createElement(h.IconButton,{size:"small",ref:O,disabled:l.disabled,onClick:function onClick(){p(!0),T||R()},color:"secondary"},c?m.default.createElement(y.default,{fontSize:"small"}):T?m.default.createElement(v.default,{fontSize:"small"}):m.default.createElement(h.Badge,{color:"primary",badgeContent:" ",variant:"dot"},m.default.createElement(v.default,{fontSize:"small"}))),m.default.createElement(h.Popover,{open:c,anchorEl:O.current,onClose:function onClose(){return p(!1)},anchorOrigin:{vertical:"bottom",horizontal:"rtl"===E?"right":"left"},transformOrigin:{vertical:"top",horizontal:"rtl"===E?"right":"left"}},m.default.createElement(h.Stack,{sx:{width:440}},l.items.map(function(l){var u=l.icon;return m.default.createElement(h.MenuItem,{key:l.type,onClick:function onClick(){P(l.type),p(!1)}},m.default.createElement(h.ListItemIcon,null,m.default.createElement(u,null)),l.title)}))),m.default.createElement(b.AttachDialog,{type:x,onAttach:l.onAttach,onClose:function onClose(){p(!1),P(null)}}))}).propTypes={items:_.default.arrayOf(_.default.shape({title:_.default.string.isRequired,type:_.default.string.isRequired,icon:_.default.elementType})).isRequired,onAttach:_.default.func.isRequired,disabled:_.default.bool}},61790:(l,u,s)=>{var c=s(53051)();l.exports=c;try{regeneratorRuntime=c}catch(l){"object"==typeof globalThis?globalThis.regeneratorRuntime=c:Function("r","regeneratorRuntime = r")(c)}},62507:(l,u,s)=>{var c=s(46313);l.exports=function _regeneratorAsync(l,u,s,p,m){var g=c(l,u,s,p,m);return g.next().then(function(l){return l.done?l.value:g.next()})},l.exports.__esModule=!0,l.exports.default=l.exports},62688:(l,u,s)=>{l.exports=s(40362)()},62805:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(61790)),h=c(s(58155)),y=c(s(18821)),v=s(86956),_=s(12470),b=c(s(62688)),w=s(95034),C=s(44048);var x=function GetStarted(l){var u=l.onSuccess,s=(0,m.useState)(!1),c=(0,y.default)(s,2),p=c[0],b=c[1],x=function(){var l=(0,h.default)(g.default.mark(function _callee(){return g.default.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=1,(0,w.setGetStarted)();case 1:u();case 2:case"end":return l.stop()}},_callee)}));return function onGetStartedClick(){return l.apply(this,arguments)}}();return m.default.createElement(v.Stack,{alignItems:"center",gap:1.5},m.default.createElement(C.AIIcon,{sx:{color:"text.primary",fontSize:"60px",mb:1}}),m.default.createElement(v.Typography,{variant:"h4",sx:{color:"text.primary"}},(0,_.__)("Step into the future with Elementor AI","elementor")),m.default.createElement(v.Typography,{variant:"body2"},(0,_.__)("Create smarter with AI text and code generators built right into the editor.","elementor")),m.default.createElement(v.Stack,{direction:"row",gap:1.5,alignItems:"flex-start"},m.default.createElement(v.Checkbox,{id:"e-ai-terms-approval",color:"secondary",checked:p,onClick:function onClick(){return b(function(l){return!l})}}),m.default.createElement(v.Stack,null,m.default.createElement(v.Typography,{variant:"caption",sx:{maxWidth:520},component:"label",htmlFor:"e-ai-terms-approval"},(0,_.__)("I approve the ","elementor"),m.default.createElement(v.Link,{href:"https://go.elementor.com/ai-terms/",target:"_blank",color:"info.main"},(0,_.__)("Terms of Service","elementor"))," & ",m.default.createElement(v.Link,{href:"https://go.elementor.com/ai-privacy-policy/",target:"_blank",color:"info.main"},(0,_.__)("Privacy Policy","elementor")),(0,_.__)(" of the Elementor AI service.","elementor"),m.default.createElement("br",null),(0,_.__)("This includes consenting to the collection and use of data to improve user experience.","elementor")))),m.default.createElement(v.Button,{disabled:!p,variant:"contained",onClick:x,sx:{mt:1,"&:hover":{color:"primary.contrastText"}}},(0,_.__)("Get Started","elementor")))};x.propTypes={onSuccess:b.default.func.isRequired};u.default=x},63223:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(78304)),g=s(86956),h=s(12470),y=c(s(62688)),v=function GenerateSubmit(l){return p.default.createElement(g.Button,(0,m.default)({fullWidth:!0,size:"medium",type:"submit",variant:"contained"},l),l.children||(0,h.__)("Generate","elementor"))};v.propTypes={children:y.default.node};u.default=v},64162:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(78304)),g=c(s(40453)),h=s(86956),y=s(12470),v=c(s(62688)),_=["onClose","onCancel","title","text"],b=function UnsavedChangesAlert(l){var u=l.onClose,s=l.onCancel,c=l.title,v=l.text,b=(0,g.default)(l,_);return p.default.createElement(h.Dialog,(0,m.default)({"aria-labelledby":"unsaved-changes-alert-title","aria-describedby":"unsaved-changes-alert-description"},b),p.default.createElement(h.DialogTitle,{id:"unsaved-changes-alert-title"},c),p.default.createElement(h.DialogContent,null,p.default.createElement(h.DialogContentText,{id:"unsaved-changes-alert-description"},v)),p.default.createElement(h.DialogActions,null,p.default.createElement(h.Button,{onClick:s,color:"secondary"},(0,y.__)("Cancel","elementor")),p.default.createElement(h.Button,{onClick:u,color:"error",variant:"contained"},(0,y.__)("Yes, leave","elementor"))))};b.propTypes={title:v.default.string,text:v.default.string,onCancel:v.default.func,onClose:v.default.func};u.default=b},65141:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.6667 0.208496C17.0534 0.208496 17.4244 0.362142 17.6979 0.635632C17.9714 0.909123 18.125 1.28006 18.125 1.66683V11.6668C18.125 12.0536 17.9714 12.4245 17.6979 12.698C17.4244 12.9715 17.0534 13.1252 16.6667 13.1252H14.7917V16.6668C14.7917 17.0536 14.638 17.4245 14.3645 17.698C14.091 17.9715 13.7201 18.1252 13.3333 18.1252H3.33333C2.94656 18.1252 2.57563 17.9715 2.30214 17.698C2.02865 17.4245 1.875 17.0536 1.875 16.6668V6.66683C1.875 6.28005 2.02865 5.90912 2.30214 5.63563C2.57563 5.36214 2.94656 5.2085 3.33333 5.2085H5.20833V1.66683C5.20833 1.28005 5.36198 0.909122 5.63547 0.635632C5.90896 0.362142 6.27989 0.208496 6.66667 0.208496H16.6667ZM6.66667 1.4585C6.61141 1.4585 6.55842 1.48045 6.51935 1.51952C6.48028 1.55859 6.45833 1.61158 6.45833 1.66683V3.54183H8.54167V1.4585H6.66667ZM3.125 9.79183V16.6668C3.125 16.7221 3.14695 16.7751 3.18602 16.8141C3.22509 16.8532 3.27808 16.8752 3.33333 16.8752H13.3333C13.3886 16.8752 13.4416 16.8532 13.4806 16.8141C13.5197 16.7751 13.5417 16.7221 13.5417 16.6668V13.1252H6.66667C6.27989 13.1252 5.90896 12.9715 5.63547 12.698C5.36198 12.4245 5.20833 12.0536 5.20833 11.6668V9.79183H3.125ZM5.20833 8.54183H3.125V6.66683C3.125 6.61158 3.14695 6.55859 3.18602 6.51952C3.22509 6.48045 3.27808 6.4585 3.33333 6.4585H5.20833V8.54183ZM6.45833 11.6668C6.45833 11.7221 6.48028 11.7751 6.51935 11.8141C6.55842 11.8532 6.61141 11.8752 6.66667 11.8752H16.6667C16.7219 11.8752 16.7749 11.8532 16.814 11.8141C16.853 11.7751 16.875 11.7221 16.875 11.6668V4.79183H6.45833V11.6668ZM9.79167 1.4585V3.54183H16.875V1.66683C16.875 1.61157 16.853 1.55858 16.814 1.51952C16.7749 1.48045 16.7219 1.4585 16.6667 1.4585H9.79167Z"}))});u.default=h},65474:l=>{l.exports=function _iterableToArrayLimit(l,u){var s=null==l?null:"undefined"!=typeof Symbol&&l[Symbol.iterator]||l["@@iterator"];if(null!=s){var c,p,m,g,h=[],y=!0,v=!1;try{if(m=(s=s.call(l)).next,0===u){if(Object(s)!==s)return;y=!1}else for(;!(y=(c=m.call(s)).done)&&(h.push(c.value),h.length!==u);y=!0);}catch(l){v=!0,p=l}finally{try{if(!y&&null!=s.return&&(g=s.return(),Object(g)!==g))return}finally{if(v)throw p}}return h}},l.exports.__esModule=!0,l.exports.default=l.exports},65815:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{d:"M12 2.69231C6.8595 2.69231 2.69231 6.8595 2.69231 12C2.69231 17.1405 6.8595 21.3077 12 21.3077C17.1405 21.3077 21.3077 17.1405 21.3077 12C21.3077 6.8595 17.1405 2.69231 12 2.69231ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM9.14527 9.14527C9.47571 8.81483 10.0115 8.81483 10.3419 9.14527L12 10.8034L13.6581 9.14527C13.9885 8.81483 14.5243 8.81483 14.8547 9.14527C15.1852 9.47571 15.1852 10.0115 14.8547 10.3419L13.1966 12L14.8547 13.6581C15.1852 13.9885 15.1852 14.5243 14.8547 14.8547C14.5243 15.1852 13.9885 15.1852 13.6581 14.8547L12 13.1966L10.3419 14.8547C10.0115 15.1852 9.47571 15.1852 9.14527 14.8547C8.81483 14.5243 8.81483 13.9885 9.14527 13.6581L10.8034 12L9.14527 10.3419C8.81483 10.0115 8.81483 9.47571 9.14527 9.14527Z"}))});u.default=h},66942:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.LibraryDialog=void 0;var p=c(s(62688)),m=s(41594),g=s(4353);(u.LibraryDialog=function LibraryDialog(l){var u=(0,m.useRef)(!1);return(0,m.useEffect)(function(){var s=function onLibraryHide(){u.current||l.onClose()};return $e.components.get("library").layout.getModal().on("hide",s),function(){$e.components.get("library").layout.getModal().off("hide",s)}},[l]),(0,m.useEffect)(function(){var s=function onMessage(s){var c=s.data,p=c.type,m=c.json,h=c.html,y=c.label,v=c.source;switch(p){case"library/attach:start":u.current=!0;break;case"library/attach":l.onAttach([{type:g.ATTACHMENT_TYPE_JSON,previewHTML:h,content:m,label:y,source:v}]),u.current=!1,l.onClose()}};return window.addEventListener("message",s),function(){window.removeEventListener("message",s)}}),$e.run("library/open",{toDefault:!0,mode:"ai-attachment"}),u.current=!1,null}).propTypes={onAttach:p.default.func.isRequired,onClose:p.default.func.isRequired}},66961:(l,u,s)=>{var c=s(10564).default;l.exports=function _regeneratorValues(l){if(null!=l){var u=l["function"==typeof Symbol&&Symbol.iterator||"@@iterator"],s=0;if(u)return u.call(l);if("function"==typeof l.next)return l;if(!isNaN(l.length))return{next:function next(){return l&&s>=l.length&&(l=void 0),{value:l&&l[s++],done:!l}}}}throw new TypeError(c(l)+" is not iterable")},l.exports.__esModule=!0,l.exports.default=l.exports},67114:l=>{l.exports=function _OverloadYield(l,u){this.v=l,this.k=u},l.exports.__esModule=!0,l.exports.default=l.exports},68022:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.SCREENSHOTS_PER_PAGE=u.MAX_PAGES=void 0;var p=c(s(18821)),m=s(41594),g=u.SCREENSHOTS_PER_PAGE=3;u.MAX_PAGES=5,u.default=function useSlider(){var l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},u=l.slidesCount,s=void 0===u?0:u,c=l.slidesPerPage,h=void 0===c?g:c,y=l.gapPercentage,v=void 0===y?2:y,_=(0,m.useState)(1),b=(0,p.default)(_,2),w=b[0],C=b[1],x=(100-v*(h-1))/h,P=(x+v)*h*(w-1)*-1,E=Math.ceil(s/h);return(0,m.useEffect)(function(){w>1&&w>E&&C(E)},[E]),{currentPage:w,setCurrentPage:C,pagesCount:E,slidesPerPage:h,gapPercentage:v,offsetXPercentage:P,slideWidthPercentage:x}}},68627:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.16707 3.95837C4.11182 3.95837 4.05883 3.98032 4.01976 4.01939C3.98069 4.05846 3.95874 4.11145 3.95874 4.16671V6.04171H6.04207V3.95837H4.16707ZM4.16707 2.70837C3.7803 2.70837 3.40937 2.86202 3.13588 3.13551C2.86239 3.409 2.70874 3.77993 2.70874 4.16671V15.8334C2.70874 16.2201 2.86239 16.5911 3.13588 16.8646C3.40937 17.1381 3.7803 17.2917 4.16707 17.2917H15.8337C16.2205 17.2917 16.5914 17.1381 16.8649 16.8646C17.1384 16.5911 17.2921 16.2201 17.2921 15.8334V4.16671C17.2921 3.77993 17.1384 3.409 16.8649 3.13551C16.5914 2.86202 16.2205 2.70837 15.8337 2.70837H4.16707ZM7.29207 3.95837V6.04171H16.0421V4.16671C16.0421 4.11145 16.0201 4.05846 15.9811 4.01939C15.942 3.98032 15.889 3.95837 15.8337 3.95837H7.29207ZM16.0421 7.29171H3.95874V15.8334C3.95874 15.8886 3.98069 15.9416 4.01976 15.9807C4.05883 16.0198 4.11182 16.0417 4.16707 16.0417H15.8337C15.889 16.0417 15.942 16.0198 15.9811 15.9807C16.0201 15.9416 16.0421 15.8886 16.0421 15.8334V7.29171Z"}))});u.default=h},69371:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(61790)),m=c(s(58155)),g=c(s(18821)),h=s(41594),y=c(s(3468));u.default=function useScreenshot(l,u){var s=(0,h.useState)(""),c=(0,g.default)(s,2),v=c[0],_=c[1],b=(0,h.useState)(!1),w=(0,g.default)(b,2),C=w[0],x=w[1],P=(0,y.default)(l,null);return{generate:function generate(l,s){return x(!0),_(""),P.send(l,s).then(function(){var l=(0,m.default)(p.default.mark(function _callee(l){var s;return p.default.wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.next=1,u(l.result);case 1:return(s=c.sent).sendUsageData=function(){return P.sendUsageData(l)},s.baseTemplateId=l.baseTemplateId,s.type=l.type,c.abrupt("return",s);case 2:case"end":return c.stop()}},_callee)}));return function(u){return l.apply(this,arguments)}}()).catch(function(l){throw _(l.extra_data?l:l.message||l),l}).finally(function(){return x(!1)})},error:v,isLoading:C}}},70569:l=>{l.exports=function _arrayWithHoles(l){if(Array.isArray(l))return l},l.exports.__esModule=!0,l.exports.default=l.exports},71338:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.Thumbnail=u.THUMBNAIL_SIZE=void 0;var p,m=c(s(41594)),g=c(s(98832)),h=s(86956),y=s(12470),v=c(s(62688)),_=u.THUMBNAIL_SIZE=64,b=(0,h.styled)("body")(p||(p=(0,g.default)(["\n\thtml, body {\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\toverflow: hidden;\n\t}\n\n\tbody > * {\n\t\twidth: 100% !important;\n\t}\n\n\tbody > img {\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\tbody:has(> img) {\n\t\theight: ","px\n\t}\n"])),_);(u.Thumbnail=function Thumbnail(l){var u,s,c=null===(u=l.html.match('data-width="(?<width>\\d+)"'))||void 0===u||null===(u=u.groups)||void 0===u?void 0:u.width,p=null===(s=l.html.match('data-height="(?<height>\\d+)"'))||void 0===s||null===(s=s.groups)||void 0===s?void 0:s.height,g=c?parseInt(c):_,v=p?parseInt(p):_,w=Math.min(v,g),C=_/w,x=v>g?(_-_*(v/g))/2:0,P=g>v?(_-_*(g/v))/2:0;return m.default.createElement(h.Box,{dir:"ltr",sx:{position:"relative",cursor:"default",overflow:"hidden",border:"1px solid",borderColor:"grey.300",borderRadius:1,boxSizing:"border-box",width:_,height:_,opacity:l.disabled?.5:1}},m.default.createElement("iframe",{title:(0,y.__)("Preview","elementor"),sandbox:"",srcDoc:"<style>"+b.componentStyle.rules.join("")+"</style>"+l.html,style:{border:"none",overflow:"hidden",width:g,height:v,transform:"scale(".concat(C,")"),transformOrigin:"".concat(P,"px ").concat(x,"px")}}))}).propTypes={html:v.default.string.isRequired,disabled:v.default.bool}},73319:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(78304)),g=c(s(85707)),h=c(s(40453)),y=s(86956),v=c(s(62688)),_=["sx","BoxProps"];function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,g.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var b=function Loader(l){var u=l.sx,s=void 0===u?{}:u,c=l.BoxProps,g=void 0===c?{}:c,v=(0,h.default)(l,_);return p.default.createElement(y.Box,(0,m.default)({width:"100%",display:"flex",alignItems:"center"},g,{sx:_objectSpread({px:1.5,minHeight:function minHeight(l){return l.spacing(5)}},g.sx||{})}),p.default.createElement(y.LinearProgress,(0,m.default)({color:"secondary"},v,{sx:_objectSpread({width:"100%"},s)})))};b.propTypes={sx:v.default.object,BoxProps:v.default.object};u.default=b},74561:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.createPreviewContainer=function createPreviewContainer(l){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=new Map,c=function createIdleContainer(l){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=createContainer(l,{elType:"container"},u);return s.view.$el.addClass(v),s}(l,u);function getAllContainers(){return[].concat((0,m.default)(s.values()),[c])}return{init:function init(){showContainer(c)},reset:function reset(){deleteContainers((0,m.default)(s.values())),s.clear(),showContainer(c)},setContent:function setContent(c){if(c){if(function hideContainers(l){l.forEach(function(l){l.view.$el.addClass(y)})}(getAllContainers()),!s.has(c)){var p=createContainer(l,c,u);s.set(c,p)}showContainer(s.get(c))}},destroy:function destroy(){deleteContainers(getAllContainers()),s.clear()}}};var p=c(s(85707)),m=c(s(10906)),g=s(40327);function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,p.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var h="e-ai-preview-container",y=h+"--hidden",v=h+"--idle";function createContainer(l,u){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(0,g.toggleHistory)(!1);var c=$e.run("document/elements/create",{container:l,model:_objectSpread(_objectSpread({},u),{},{id:"".concat(h,"-").concat(elementorCommon.helpers.getUniqueId().toString())}),options:_objectSpread(_objectSpread({},s),{},{edit:!1})});return(0,g.toggleHistory)(!0),c.view.$el.addClass(y),c}function showContainer(l){l.view.$el.removeClass(y),setTimeout(function(){l.view.$el[0].scrollIntoView({behavior:"smooth",block:"start"})})}function deleteContainers(l){(0,g.toggleHistory)(!1),$e.run("document/elements/delete",{containers:l}),(0,g.toggleHistory)(!0)}},75206:l=>{"use strict";l.exports=ReactDOM},75690:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(78304)),h=c(s(85707)),y=c(s(18821)),v=c(s(40453)),_=s(86956),b=s(12470),w=c(s(62688)),C=c(s(48968)),x=s(44048),P=["sx","PaperProps"];function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,h.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var E=(0,_.styled)(C.default)(function(){return{"& .MuiDialog-container":{marginTop:0,alignItems:"flex-end",paddingBottom:"16vh"},"& .MuiPaper-root":{margin:0,maxHeight:"80vh"}}}),O=function DialogHeader(l){var u=l.onClose,s=l.children;return m.default.createElement(_.AppBar,{sx:{fontWeight:"normal"},color:"transparent",position:"relative"},m.default.createElement(_.Toolbar,{variant:"dense"},m.default.createElement(x.AIIcon,{sx:{mr:1}}),m.default.createElement(_.Typography,{component:"span",variant:"subtitle2",sx:{fontWeight:"bold",textTransform:"uppercase"}},(0,b.__)("AI","elementor")),m.default.createElement(_.Chip,{label:(0,b.__)("Beta","elementor"),color:"default",size:"small",sx:{ml:1}}),m.default.createElement(_.Stack,{direction:"row",spacing:1,alignItems:"center",sx:{ml:"auto"}},s,m.default.createElement(_.IconButton,{size:"small","aria-label":"close",onClick:u,sx:{"&.MuiButtonBase-root":{mr:-1}}},m.default.createElement(x.XIcon,null)))))};O.propTypes={children:w.default.node,onClose:w.default.func.isRequired};var S=(0,_.styled)(C.default.Content)(function(){return{"&.MuiDialogContent-root":{padding:0}}}),T=function LayoutDialog(l){var u=l.sx,s=void 0===u?{}:u,c=l.PaperProps,p=void 0===c?{}:c,h=(0,v.default)(l,P),_=(0,m.useState)({pointerEvents:"none"}),b=(0,y.default)(_,2),w=b[0],C=b[1],x=(0,m.useRef)(null);return m.default.createElement(E,(0,g.default)({maxWidth:"md",PaperProps:_objectSpread({sx:{pointerEvents:"auto"},onMouseEnter:function onMouseEnter(){clearTimeout(x.current),C({pointerEvents:"all"})},onMouseLeave:function onMouseLeave(){clearTimeout(x.current),x.current=setTimeout(function(){C({pointerEvents:"none"})},200)}},p)},h,{sx:_objectSpread(_objectSpread({},w),s)}))};T.propTypes={sx:w.default.object,PaperProps:w.default.object},T.Header=O,T.Content=S;u.default=T},77043:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.useTimeout=void 0;var p=c(s(18821)),m=s(41594);u.useTimeout=function useTimeout(l){var u=(0,m.useState)(!1),s=(0,p.default)(u,2),c=s[0],g=s[1],h=(0,m.useRef)(null);return(0,m.useEffect)(function(){return h.current=setTimeout(function(){g(!0)},l),function(){clearTimeout(h.current)}},[l]),[c,function turnOffTimeout(){clearTimeout(h.current),g(!1)}]}},78113:l=>{l.exports=function _arrayLikeToArray(l,u){(null==u||u>l.length)&&(u=l.length);for(var s=0,c=Array(u);s<u;s++)c[s]=l[s];return c},l.exports.__esModule=!0,l.exports.default=l.exports},78304:l=>{function _extends(){return l.exports=_extends=Object.assign?Object.assign.bind():function(l){for(var u=1;u<arguments.length;u++){var s=arguments[u];for(var c in s)({}).hasOwnProperty.call(s,c)&&(l[c]=s[c])}return l},l.exports.__esModule=!0,l.exports.default=l.exports,_extends.apply(null,arguments)}l.exports=_extends,l.exports.__esModule=!0,l.exports.default=l.exports},78687:l=>{l.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},l.exports.__esModule=!0,l.exports.default=l.exports},79919:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(85707)),g=s(86956),h=c(s(62688)),y=c(s(25893)),v=c(s(93264)),_=c(s(51563));function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,m.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var b="138px",w=function Screenshot(l){var u=l.url,s=l.type,c=l.isLoading,m=void 0!==c&&c,h=l.isSelected,w=void 0!==h&&h,C=l.isPlaceholder,x=l.disabled,P=l.onClick,E=l.sx,O=void 0===E?{}:E,S=l.outlineOffset;return C?p.default.createElement(g.Box,{sx:_objectSpread({height:b},O)}):m?p.default.createElement(g.Skeleton,{width:"100%",animation:"wave",variant:"rounded",height:b,sx:O}):u?p.default.createElement(y.default,{selected:w,disabled:x,sx:_objectSpread({backgroundImage:"url('".concat(u,"')")},O),onClick:P,height:b,outlineOffset:S},p.default.createElement(_.default,{type:s})):p.default.createElement(v.default,{selected:w,disabled:x,sx:O,onClick:P,height:b,outlineOffset:S})};w.propTypes={isSelected:h.default.bool,isLoading:h.default.bool,isPlaceholder:h.default.bool,disabled:h.default.bool,onClick:h.default.func.isRequired,url:h.default.string,type:h.default.string,sx:h.default.object,outlineOffset:h.default.string};u.default=w},80366:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=function useIntroduction(l){var u,s,c,g=window.elementor?null===(u=window.elementor.config)||void 0===u?void 0:u.user:null===(s=window.elementorAdmin)||void 0===s||null===(s=s.config)||void 0===s?void 0:s.user,h=(0,m.useState)(!(null==g||null===(c=g.introduction)||void 0===c||!c[l])),y=(0,p.default)(h,2),v=y[0],_=y[1];return{isViewed:v,markAsViewed:function markAsViewed(){return l?new Promise(function(u,s){v&&s(),_(!0),elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:l},error:function error(){_(!1),s()},success:function success(){_(!0),null!=g&&g.introduction&&(g.introduction[l]=!0),u()}})}):Promise.reject()}}};var p=c(s(18821)),m=s(41594)},85076:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=c(s(78304)),y=c(s(40453)),v=s(86956),_=s(12470),b=c(s(62688)),w=c(s(4974)),C=c(s(48584)),x=c(s(63223)),P=c(s(31593)),E=c(s(51066)),O=c(s(34161)),S=c(s(4353)),T=s(91258),R=s(38298),j=["tooltip"];var M=Object.freeze([{text:(0,_.__)("Hero section on [topic] with heading, text, buttons on the right, and an image on the left","elementor.com")},{text:(0,_.__)("About Us section on [topic] with heading, text, and big image below","elementor.com")},{text:(0,_.__)("Team section with four image boxes showcasing team members","elementor.com")},{text:(0,_.__)("FAQ section with a toggle widget showcasing FAQs about [topic]","elementor.com")},{text:(0,_.__)("Gallery section with a carousel displaying three images at once","elementor.com")},{text:(0,_.__)("Contact section with a form for [topic]","elementor.com")},{text:(0,_.__)("Client section featuring companies' logos","elementor.com")},{text:(0,_.__)("Testimonial section with testimonials, each featuring a star rating and an image","elementor.com")},{text:(0,_.__)("Service section about [topic], showcasing four services with buttons","elementor.com")},{text:(0,_.__)("Stats section with counters displaying data about [topic]","elementor.com")},{text:(0,_.__)("Quote section with colored background, featuring a centered quote","elementor.com")},{text:(0,_.__)("Pricing section for [topic] with a pricing list","elementor.com")},{text:(0,_.__)("Subscribe section featuring a simple email form, inviting users to stay informed on [topic]","elementor.com")}]),I=function IconButtonWithTooltip(l){var u=l.tooltip,s=(0,y.default)(l,j);return m.default.createElement(v.Tooltip,{title:u},m.default.createElement(v.Box,{component:"span",sx:{cursor:s.disabled?"default":"pointer"}},m.default.createElement(v.IconButton,s)))};I.propTypes={tooltip:b.default.string,disabled:b.default.bool};var k=function BackButton(l){return m.default.createElement(I,(0,h.default)({size:"small",color:"secondary",tooltip:(0,_.__)("Back to results","elementor")},l),m.default.createElement(P.default,null))},A=function EditButton(l){return m.default.createElement(I,(0,h.default)({size:"small",color:"primary",tooltip:(0,_.__)("Edit prompt","elementor")},l),m.default.createElement(E.default,null))},D=function GenerateButton(l){return m.default.createElement(x.default,(0,h.default)({size:"small",fullWidth:!1},l),(0,_.__)("Generate","elementor"))},L=(0,m.forwardRef)(function(l,u){var s,c=l.attachments,p=l.isActive,y=l.isLoading,b=l.showActions,x=void 0!==b&&b,P=l.onAttach,E=l.onDetach,R=l.onSubmit,j=l.onBack,I=l.onEdit,L=l.shouldResetPrompt,q=void 0!==L&&L,B=(0,m.useState)(""),W=(0,g.default)(B,2),N=W[0],H=W[1];(0,m.useEffect)(function(){q&&H("")},[q]);var U=(0,O.default)(N,"layout"),V=U.isEnhancing,F=U.enhance,G=(0,m.useRef)(""),$=(0,T.useConfig)().attachmentsTypes,Y=y||V||!p,z=""===N&&!c.length,K=Y||z,X=$[(null===(s=c[0])||void 0===s?void 0:s.type)||""],Z=(null==X?void 0:X.promptSuggestions)||M,Q=(null==X?void 0:X.promptPlaceholder)||(0,_.__)("Press '/' for suggested prompts or describe the layout you want to create","elementor");return m.default.createElement(v.Stack,{component:"form",onSubmit:function onSubmit(l){return R(l,N)},direction:"row",sx:{p:3},alignItems:"start",gap:1},m.default.createElement(v.Stack,{direction:"row",alignItems:"start",flexGrow:1,spacing:2},x&&(p?m.default.createElement(k,{disabled:y||V,onClick:function handleBack(){H(G.current),j()}}):m.default.createElement(A,{disabled:y,onClick:function handleEdit(){G.current=N,I()}})),m.default.createElement(S.default,{attachments:c,onAttach:P,onDetach:E,disabled:Y}),m.default.createElement(w.default,{value:N,disabled:Y,onSubmit:function onSubmit(l){return R(l,N)},options:Z,onChange:function onChange(l,u){return H(u.text+" ")},renderInput:function renderInput(l){return m.default.createElement(w.default.TextInput,(0,h.default)({},l,{ref:u,onChange:function onChange(l){return H(l.target.value)},placeholder:Q}))}})),m.default.createElement(C.default,{size:"small",disabled:K||""===N,isLoading:V,onClick:function onClick(){return F().then(function(l){var u=l.result;return H(u)})}}),m.default.createElement(D,{disabled:K}))});L.propTypes={isActive:b.default.bool,onAttach:b.default.func,onDetach:b.default.func,isLoading:b.default.bool,showActions:b.default.bool,onSubmit:b.default.func.isRequired,onBack:b.default.func.isRequired,onEdit:b.default.func.isRequired,attachments:b.default.arrayOf(R.AttachmentPropType),shouldResetPrompt:b.default.bool};u.default=L},85614:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(61790)),m=c(s(10906)),g=c(s(58155)),h=c(s(18821)),y=s(41594),v=c(s(69371)),_=s(91258),b=s(40128),w={isPending:!0};u.default=function useScreenshots(l){var u=l.onData,s=(0,y.useState)([]),c=(0,h.default)(s,2),C=c[0],x=c[1],P=(0,_.useConfig)().currentContext,E=(0,b.useRequestIds)(),O=E.editorSessionId,S=E.sessionId,T=E.setRequest,R=E.setBatch,j=E.setGenerate,M=(0,y.useRef)(""),I=R(),k=[(0,v.default)(0,u),(0,v.default)(1,u),(0,v.default)(2,u)],A=k.length,D=k.every(function(l){return null==l?void 0:l.error})?k[0].error:"",L=k.some(function(l){return null==l?void 0:l.isLoading}),q=(0,y.useRef)(null),B=function(){var l=(0,g.default)(p.default.mark(function _callee(l,u){var s,c,g,h;return p.default.wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return q.current=new AbortController,s=function onGenerate(l){return x(function(u){var s=(0,m.default)(u),c=s.indexOf(w);return s[c]=l,s}),!0},c=function onError(){return x(function(l){var u=(0,m.default)(l),s=u.lastIndexOf(w);return u[s]={isError:!0},u}),!1},g=k.map(function(p){var m=p.generate,g=C.map(function(l){return l.baseTemplateId||""});return m({prompt:l,prevGeneratedIds:g,currentContext:P,ids:{editorSessionId:O.current,sessionId:S.current,generateId:M.current,batchId:I.current,requestId:T().current},attachments:u.map(function(l){return{type:l.type,content:l.content,label:l.label,source:l.source}})},q.current.signal).then(s).catch(c)}),p.next=1,Promise.all(g);case 1:h=p.sent,h.every(function(l){return!1===l})&&x(function(l){var u=(0,m.default)(l);return u.splice(-1*A),u});case 2:case"end":return p.stop()}},_callee)}));return function createScreenshots(u,s){return l.apply(this,arguments)}}();return{generate:function generate(l,u){var s=Array(A).fill(w);M.current=j().current,x(s),B(l,u)},regenerate:function regenerate(l,u){var s=Array(A).fill(w);x(function(l){return[].concat((0,m.default)(l),(0,m.default)(s))}),B(l,u)},screenshots:C,isLoading:L,error:D,abort:function abort(){var l;return null===(l=q.current)||void 0===l?void 0:l.abort()}}}},85707:(l,u,s)=>{var c=s(45498);l.exports=function _defineProperty(l,u,s){return(u=c(u))in l?Object.defineProperty(l,u,{value:s,enumerable:!0,configurable:!0,writable:!0}):l[u]=s,l},l.exports.__esModule=!0,l.exports.default=l.exports},86353:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.AlertDialog=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=s(86956),y=s(12470),v=c(s(62688));(u.AlertDialog=function AlertDialog(l){var u=(0,m.useState)(!0),s=(0,g.default)(u,2),c=s[0],p=s[1];return c?m.default.createElement(h.Dialog,{open:!0,maxWidth:"lg"},m.default.createElement(h.DialogContent,{sx:{padding:0}},m.default.createElement(h.Typography,{sx:{textAlign:"center",padding:3}},l.message),m.default.createElement(h.Stack,{alignItems:"center",spacing:2,marginBottom:2},m.default.createElement(h.Button,{variant:"contained",type:"button",color:"primary",onClick:function onClick(){var u;p(!1),null===(u=l.onClose)||void 0===u||u.call(l)}},(0,y.__)("Close","elementor"))))):null}).propTypes={message:v.default.string.isRequired,onClose:v.default.func}},86956:l=>{"use strict";l.exports=elementorV2.ui},87861:(l,u,s)=>{var c=s(91270);l.exports=function _inherits(l,u){if("function"!=typeof u&&null!==u)throw new TypeError("Super expression must either be null or a function");l.prototype=Object.create(u&&u.prototype,{constructor:{value:l,writable:!0,configurable:!0}}),Object.defineProperty(l,"prototype",{writable:!1}),u&&c(l,u)},l.exports.__esModule=!0,l.exports.default=l.exports},89736:l=>{function _regeneratorDefine(u,s,c,p){var m=Object.defineProperty;try{m({},"",{})}catch(u){m=0}l.exports=_regeneratorDefine=function regeneratorDefine(l,u,s,c){if(u)m?m(l,u,{value:s,enumerable:!c,configurable:!c,writable:!c}):l[u]=s;else{var p=function o(u,s){_regeneratorDefine(l,u,function(l){return this._invoke(u,s,l)})};p("next",0),p("throw",1),p("return",2)}},l.exports.__esModule=!0,l.exports.default=l.exports,_regeneratorDefine(u,s,c,p)}l.exports=_regeneratorDefine,l.exports.__esModule=!0,l.exports.default=l.exports},89958:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.AttachDialog=void 0;var p=c(s(41594)),m=s(43220),g=c(s(62688)),h=s(66942),y=s(4353),v=u.AttachDialog=function AttachDialog(l){var u=l.type,s=l.url;switch(u){case y.ATTACHMENT_TYPE_URL:return p.default.createElement(m.UrlDialog,{url:s,onAttach:l.onAttach,onClose:l.onClose});case y.MENU_TYPE_LIBRARY:return p.default.createElement(h.LibraryDialog,{onAttach:l.onAttach,onClose:l.onClose})}return null};v.propTypes={type:g.default.string,onAttach:g.default.func,onClose:g.default.func,url:g.default.string};u.default=v},90291:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.VoicePromotionAlert=void 0;var p=c(s(85707)),m=c(s(41594)),g=s(86956),h=c(s(50923)),y=c(s(80366)),v=c(s(62688)),_=s(12470);function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,p.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var b=u.VoicePromotionAlert=function VoicePromotionAlert(l){var u=(0,y.default)(l.introductionKey),s=u.isViewed,c=u.markAsViewed;return s?null:m.default.createElement(g.Box,{sx:_objectSpread({mt:2},l.sx),alignItems:"top"},m.default.createElement(g.Alert,{severity:"info",variant:"standard",icon:m.default.createElement(h.default,{sx:{alignSelf:"flex-start"}}),onClose:c},(0,_.__)("Get improved results from AI by adding personal context.","elementor"),m.default.createElement(g.Link,{onClick:function onClick(){return $e.route("panel/global/menu")},className:"elementor-clickable",style:{textDecoration:"none"},color:"info.main",href:"#"},(0,_.__)("Let’s do it","elementor"))))};b.propTypes={sx:v.default.object,introductionKey:v.default.string};u.default=b},91258:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.useConfig=u.default=u.MODE_VARIATION=u.MODE_LAYOUT=u.LAYOUT_APP_MODES=u.ConfigProvider=void 0;var p=c(s(41594)),m=c(s(62688)),g=u.MODE_LAYOUT="layout",h=u.MODE_VARIATION="variation",y=u.LAYOUT_APP_MODES=[g,h],v=p.default.createContext({});u.useConfig=function useConfig(){return p.default.useContext(v)};(u.ConfigProvider=function ConfigProvider(l){return p.default.createElement(v.Provider,{value:{mode:l.mode,attachmentsTypes:l.attachmentsTypes,onClose:l.onClose,onConnect:l.onConnect,onData:l.onData,onInsert:l.onInsert,onSelect:l.onSelect,onGenerate:l.onGenerate,currentContext:l.currentContext,hasPro:l.hasPro}},l.children)}).propTypes={mode:m.default.oneOf(y).isRequired,children:m.default.node.isRequired,attachmentsTypes:m.default.object.isRequired,onClose:m.default.func.isRequired,onConnect:m.default.func.isRequired,onData:m.default.func.isRequired,onInsert:m.default.func.isRequired,onSelect:m.default.func.isRequired,onGenerate:m.default.func.isRequired,currentContext:m.default.object,hasPro:m.default.bool};u.default=v},91270:l=>{function _setPrototypeOf(u,s){return l.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(l,u){return l.__proto__=u,l},l.exports.__esModule=!0,l.exports.default=l.exports,_setPrototypeOf(u,s)}l.exports=_setPrototypeOf,l.exports.__esModule=!0,l.exports.default=l.exports},91819:(l,u,s)=>{var c=s(78113);l.exports=function _arrayWithoutHoles(l){if(Array.isArray(l))return c(l)},l.exports.__esModule=!0,l.exports.default=l.exports},92263:(l,u,s)=>{"use strict";var c,p=s(36833),m=p.renderLayoutApp,g=p.importToEditor,h=s(91258).MODE_VARIATION,y=s(12470).__,v=s(4353),_=v.ATTACHMENT_TYPE_JSON,b=v.ELEMENTOR_LIBRARY_SOURCE;c=Marionette.Behavior.extend({ui:{applyButton:".elementor-template-library-template-apply-ai",generateVariation:".elementor-template-library-template-generate-variation"},events:{"click @ui.applyButton":"onApplyButtonClick","click @ui.generateVariation":"onGenerateVariationClick"},onGenerateVariationClick:function onGenerateVariationClick(){var l,u={model:this.view.model},s=$e.components.get("library"),c=null===(l=s.manager.modalConfig)||void 0===l||null===(l=l.importOptions)||void 0===l?void 0:l.at;s.downloadTemplate(u,function(l){var s=u.model,p={type:_,previewHTML:'<img src="'.concat(s.get("thumbnail"),'" />'),content:l.content[0],label:"".concat(s.get("template_id")," - ").concat(s.get("title")),source:b};m({parentContainer:elementor.getPreviewContainer(),mode:h,at:c,attachments:[p],onInsert:function onInsert(l){g({parentContainer:elementor.getPreviewContainer(),at:c,template:l,historyTitle:y("AI Variation from library","elementor")})}}),$e.run("library/close")})},onApplyButtonClick:function onApplyButtonClick(){var l={model:this.view.model};this.ui.applyButton.addClass("elementor-disabled");var u=l.model.get("source");!elementor.hooks.applyFilters("templates/source/is-remote","remote"===u,u)||elementor.config.library_connect.is_connected?$e.run("library/generate-ai-variation",l):$e.route("library/connect",l)}}),l.exports=c},93264:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=ScreenshotUnavailable;var p=c(s(41594)),m=c(s(78304)),g=c(s(85707)),h=c(s(62688)),y=s(12470),v=c(s(25893));function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,g.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}function ScreenshotUnavailable(l){return p.default.createElement(v.default,(0,m.default)({},l,{sx:_objectSpread(_objectSpread({},l.sx||{}),{},{display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"background.paper",color:"text.tertiary",fontStyle:"italic",fontSize:"12px",paddingInline:12,textAlign:"center",lineHeight:1.5})}),(0,y.__)("Preview unavailable","elementor"))}ScreenshotUnavailable.propTypes={sx:h.default.object}},93443:(l,u)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=function assertString(l){if(null==l)throw new TypeError("Expected a string but received a ".concat(l));if("String"!==l.constructor.name)throw new TypeError("Expected a string but received a ".concat(l.constructor.name))},l.exports=u.default,l.exports.default=u.default},93569:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(62688)),g=c(s(94459)),h=s(38298),y=s(91258),v=s(35589),_=s(40128),b=function LayoutApp(l){return p.default.createElement(v.RemoteConfigProvider,{onError:l.onClose},p.default.createElement(_.RequestIdsProvider,null,p.default.createElement(y.ConfigProvider,{mode:l.mode,attachmentsTypes:l.attachmentsTypes,onClose:l.onClose,onConnect:l.onConnect,onData:l.onData,onInsert:l.onInsert,onSelect:l.onSelect,onGenerate:l.onGenerate,currentContext:l.currentContext,hasPro:l.hasPro},p.default.createElement(g.default,{attachments:l.attachments}))))};b.propTypes={mode:m.default.oneOf(y.LAYOUT_APP_MODES).isRequired,attachmentsTypes:h.AttachmentsTypesPropType,attachments:m.default.arrayOf(h.AttachmentPropType),onClose:m.default.func.isRequired,onConnect:m.default.func.isRequired,onData:m.default.func.isRequired,onInsert:m.default.func.isRequired,onSelect:m.default.func.isRequired,onGenerate:m.default.func.isRequired,currentContext:m.default.object,hasPro:m.default.bool};u.default=b},94459:(l,u,s)=>{"use strict";var c=s(96784),p=s(10564);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var m=function _interopRequireWildcard(l,u){if("function"==typeof WeakMap)var s=new WeakMap,c=new WeakMap;return function _interopRequireWildcard(l,u){if(!u&&l&&l.__esModule)return l;var m,g,h={__proto__:null,default:l};if(null===l||"object"!=p(l)&&"function"!=typeof l)return h;if(m=u?c:s){if(m.has(l))return m.get(l);m.set(l,h)}for(var y in l)"default"!==y&&{}.hasOwnProperty.call(l,y)&&((g=(m=Object.defineProperty)&&Object.getOwnPropertyDescriptor(l,y))&&(g.get||g.set)?m(h,y,g):h[y]=l[y]);return h}(l,u)}(s(41594)),g=c(s(18821)),h=c(s(8299)),y=c(s(51550)),v=c(s(62805)),_=c(s(73319)),b=c(s(35121)),w=c(s(24954)),C=c(s(4508)),x=c(s(75690)),P=c(s(62688)),E=s(38298),O=s(91258),S=s(40128);var T=function LayoutContent(l){var u=(0,w.default)(),s=u.isLoading,c=u.isConnected,p=u.isGetStarted,P=u.connectUrl,E=u.fetchData,T=u.hasSubscription,R=u.usagePercentage,j=(0,O.useConfig)(),M=j.onClose,I=j.onConnect,k=(0,S.useRequestIds)(),A=k.updateUsagePercentage,D=k.usagePercentage,L=(0,m.useState)(!1),q=(0,g.default)(L,2),B=q[0],W=q[1];if((0,m.useEffect)(function(){B||s||!R&&0!==R||(A(R),W(!0))},[s,R,B,A]),s||!B)return m.default.createElement(x.default,{onClose:M},m.default.createElement(x.default.Header,{onClose:M}),m.default.createElement(x.default.Content,{dividers:!0},m.default.createElement(_.default,{BoxProps:{sx:{px:3}}})));if(!c)return m.default.createElement(C.default,{onClose:M},m.default.createElement(x.default,{onClose:M}),m.default.createElement(C.default.Content,{dividers:!0},m.default.createElement(h.default,{connectUrl:P,onSuccess:function onSuccess(l){I(l),E()}})));if(!p)return m.default.createElement(C.default,{onClose:M},m.default.createElement(x.default,{onClose:M}),m.default.createElement(C.default.Content,{dividers:!0},m.default.createElement(v.default,{onSuccess:E})));var N=!T||80<=D;return m.default.createElement(y.default,{attachments:l.attachments,DialogHeaderProps:{children:N&&m.default.createElement(b.default,{hasSubscription:T,usagePercentage:D})}})};T.propTypes={attachments:P.default.arrayOf(E.AttachmentPropType)};u.default=T},94760:(l,u,s)=>{"use strict";Object.defineProperty(u,"__esModule",{value:!0}),u.default=function isURL(l,u){if((0,c.default)(l),!l||/[\s<>]/.test(l))return!1;if(0===l.indexOf("mailto:"))return!1;if((u=(0,y.default)(u,v)).validate_length&&l.length>u.max_allowed_length)return!1;if(!u.allow_fragments&&(0,m.default)(l,"#"))return!1;if(!u.allow_query_components&&((0,m.default)(l,"?")||(0,m.default)(l,"&")))return!1;var s,b,w,C,x,P,E,O;if(E=l.split("#"),l=E.shift(),E=l.split("?"),l=E.shift(),(E=l.split("://")).length>1){if(s=E.shift().toLowerCase(),u.require_valid_protocol&&-1===u.protocols.indexOf(s))return!1}else{if(u.require_protocol)return!1;if("//"===l.slice(0,2)){if(!u.allow_protocol_relative_urls)return!1;E[0]=l.slice(2)}}if(""===(l=E.join("://")))return!1;if(E=l.split("/"),""===(l=E.shift())&&!u.require_host)return!0;if((E=l.split("@")).length>1){if(u.disallow_auth)return!1;if(""===E[0])return!1;if((b=E.shift()).indexOf(":")>=0&&b.split(":").length>2)return!1;var S=function _slicedToArray(l,u){return function _arrayWithHoles(l){if(Array.isArray(l))return l}(l)||function _iterableToArrayLimit(l,u){var s=null==l?null:"undefined"!=typeof Symbol&&l[Symbol.iterator]||l["@@iterator"];if(null!=s){var c,p,m,g,h=[],y=!0,v=!1;try{if(m=(s=s.call(l)).next,0===u){if(Object(s)!==s)return;y=!1}else for(;!(y=(c=m.call(s)).done)&&(h.push(c.value),h.length!==u);y=!0);}catch(l){v=!0,p=l}finally{try{if(!y&&null!=s.return&&(g=s.return(),Object(g)!==g))return}finally{if(v)throw p}}return h}}(l,u)||function _unsupportedIterableToArray(l,u){if(l){if("string"==typeof l)return _arrayLikeToArray(l,u);var s={}.toString.call(l).slice(8,-1);return"Object"===s&&l.constructor&&(s=l.constructor.name),"Map"===s||"Set"===s?Array.from(l):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?_arrayLikeToArray(l,u):void 0}}(l,u)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(b.split(":"),2),T=S[0],R=S[1];if(""===T&&""===R)return!1}C=E.join("@"),P=null,O=null;var j=C.match(_);j?(w="",O=j[1],P=j[2]||null):(w=(E=C.split(":")).shift(),E.length&&(P=E.join(":")));if(null!==P&&P.length>0){if(x=parseInt(P,10),!/^[0-9]+$/.test(P)||x<=0||x>65535)return!1}else if(u.require_port)return!1;if(u.host_whitelist)return(0,p.default)(w,u.host_whitelist);if(""===w&&!u.require_host)return!0;if(!((0,h.default)(w)||(0,g.default)(w,u)||O&&(0,h.default)(O,6)))return!1;if(w=w||O,u.host_blacklist&&(0,p.default)(w,u.host_blacklist))return!1;return!0};var c=_interopRequireDefault(s(93443)),p=_interopRequireDefault(s(99e3)),m=_interopRequireDefault(s(29664)),g=_interopRequireDefault(s(21806)),h=_interopRequireDefault(s(14744)),y=_interopRequireDefault(s(41398));function _interopRequireDefault(l){return l&&l.__esModule?l:{default:l}}function _arrayLikeToArray(l,u){(null==u||u>l.length)&&(u=l.length);for(var s=0,c=Array(u);s<u;s++)c[s]=l[s];return c}var v={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0,max_allowed_length:2084},_=/^\[([^\]]+)\](?::([0-9]+))?$/;l.exports=u.default,l.exports.default=u.default},95034:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.uploadImage=u.toggleFavoriteHistoryItem=u.setStatusFeedback=u.setGetStarted=u.getUserInformation=u.getTextToImageGeneration=u.getRemoteFrontendConfig=u.getRemoteConfig=u.getProductImageUnification=u.getLayoutPromptEnhanced=u.getImageToImageUpscale=u.getImageToImageReplaceBackground=u.getImageToImageRemoveText=u.getImageToImageRemoveBackground=u.getImageToImageOutPainting=u.getImageToImageMaskGeneration=u.getImageToImageMaskCleanup=u.getImageToImageIsolateObjects=u.getImageToImageGeneration=u.getImagePromptEnhanced=u.getHistory=u.getFeaturedImage=u.getExcerpt=u.getEditText=u.getCustomCode=u.getCustomCSS=u.getCompletionText=u.getAnimation=u.generateLayout=u.deleteHistoryItem=void 0;var p=c(s(85707));function ownKeys(l,u){var s=Object.keys(l);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(l);u&&(c=c.filter(function(u){return Object.getOwnPropertyDescriptor(l,u).enumerable})),s.push.apply(s,c)}return s}function _objectSpread(l){for(var u=1;u<arguments.length;u++){var s=null!=arguments[u]?arguments[u]:{};u%2?ownKeys(Object(s),!0).forEach(function(u){(0,p.default)(l,u,s[u])}):Object.getOwnPropertyDescriptors?Object.defineProperties(l,Object.getOwnPropertyDescriptors(s)):ownKeys(Object(s)).forEach(function(u){Object.defineProperty(l,u,Object.getOwnPropertyDescriptor(s,u))})}return l}var m=function request(l){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],c=arguments.length>3?arguments[3]:void 0;return Object.keys(u).length&&(window.elementorAiCurrentContext?u.context=window.elementorAiCurrentContext:u.context=window.elementorWpAiCurrentContext),new Promise(function(p,m){var g=elementorCommon.ajax.addRequest(l,{success:p,error:m,data:u,unique_id:u.unique_id},s);c&&g.jqXhr&&c.addEventListener("abort",g.jqXhr.abort)})};u.getUserInformation=function getUserInformation(l){return m("ai_get_user_information",void 0,l)},u.getRemoteConfig=function getRemoteConfig(){return m("ai_get_remote_config")},u.getRemoteFrontendConfig=function getRemoteFrontendConfig(l,u){return m("ai_get_remote_frontend_config",{payload:l},u)},u.getCompletionText=function getCompletionText(l){return m("ai_get_completion_text",{payload:l})},u.getExcerpt=function getExcerpt(l){return m("ai_get_excerpt",{payload:l})},u.getFeaturedImage=function getFeaturedImage(l){return m("ai_get_featured_image",{payload:l})},u.getEditText=function getEditText(l){return m("ai_get_edit_text",{payload:l})},u.getCustomCode=function getCustomCode(l){return m("ai_get_custom_code",{payload:l})},u.getCustomCSS=function getCustomCSS(l){return m("ai_get_custom_css",{payload:l})},u.setGetStarted=function setGetStarted(){return m("ai_set_get_started")},u.setStatusFeedback=function setStatusFeedback(l){return m("ai_set_status_feedback",{response_id:l},!0)},u.getTextToImageGeneration=function getTextToImageGeneration(l){return m("ai_get_text_to_image",{payload:l})},u.getImageToImageGeneration=function getImageToImageGeneration(l){return m("ai_get_image_to_image",{payload:l})},u.getImageToImageMaskCleanup=function getImageToImageMaskCleanup(l){return m("ai_get_image_to_image_mask_cleanup",{payload:l})},u.getImageToImageMaskGeneration=function getImageToImageMaskGeneration(l){return m("ai_get_image_to_image_mask",{payload:l})},u.getImageToImageOutPainting=function getImageToImageOutPainting(l){return m("ai_get_image_to_image_outpainting",{payload:l})},u.getImageToImageUpscale=function getImageToImageUpscale(l){return m("ai_get_image_to_image_upscale",{payload:l})},u.getImageToImageRemoveBackground=function getImageToImageRemoveBackground(l){return m("ai_get_image_to_image_remove_background",{payload:l})},u.getImageToImageIsolateObjects=function getImageToImageIsolateObjects(l){return m("ai_get_image_to_image_isolate_objects",{payload:l})},u.getImageToImageReplaceBackground=function getImageToImageReplaceBackground(l){return m("ai_get_image_to_image_replace_background",{payload:l})},u.getImageToImageRemoveText=function getImageToImageRemoveText(l){return m("ai_get_image_to_image_remove_text",{image:l})},u.getImagePromptEnhanced=function getImagePromptEnhanced(l){return m("ai_get_image_prompt_enhancer",{prompt:l})},u.getProductImageUnification=function getProductImageUnification(l,u){return m("ai_get_product_image_unification",{payload:l},u)},u.getAnimation=function getAnimation(l){return m("ai_get_animation",{payload:l})},u.uploadImage=function uploadImage(l){return m("ai_upload_image",_objectSpread(_objectSpread({},l),{},{editor_post_id:l.image.editor_post_id,unique_id:l.image.unique_id}))},u.generateLayout=function generateLayout(l,u){return m("ai_generate_layout",l,!0,u)},u.getLayoutPromptEnhanced=function getLayoutPromptEnhanced(l,u){return m("ai_get_layout_prompt_enhancer",{prompt:l,enhance_type:u})},u.getHistory=function getHistory(l,u,s){return m("ai_get_history",{type:l,page:u,limit:s})},u.deleteHistoryItem=function deleteHistoryItem(l){return m("ai_delete_history_item",{id:l})},u.toggleFavoriteHistoryItem=function toggleFavoriteHistoryItem(l){return m("ai_toggle_favorite_history_item",{id:l})}},95315:l=>{l.exports=function _regeneratorKeys(l){var u=Object(l),s=[];for(var c in u)s.unshift(c);return function e(){for(;s.length;)if((c=s.pop())in u)return e.value=c,e.done=!1,e;return e.done=!0,e}},l.exports.__esModule=!0,l.exports.default=l.exports},96784:l=>{l.exports=function _interopRequireDefault(l){return l&&l.__esModule?l:{default:l}},l.exports.__esModule=!0,l.exports.default=l.exports},96793:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(78304)),m=c(s(41594)),g=s(86956),h=m.default.forwardRef(function(l,u){return m.default.createElement(g.SvgIcon,(0,p.default)({viewBox:"0 0 24 24"},l,{ref:u}),m.default.createElement("path",{d:"M12 2.69231C6.8595 2.69231 2.69231 6.8595 2.69231 12C2.69231 17.1405 6.8595 21.3077 12 21.3077C17.1405 21.3077 21.3077 17.1405 21.3077 12C21.3077 6.8595 17.1405 2.69231 12 2.69231ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM12 7.76923C12.4673 7.76923 12.8462 8.14807 12.8462 8.61538V11.1538H15.3846C15.8519 11.1538 16.2308 11.5327 16.2308 12C16.2308 12.4673 15.8519 12.8462 15.3846 12.8462H12.8462V15.3846C12.8462 15.8519 12.4673 16.2308 12 16.2308C11.5327 16.2308 11.1538 15.8519 11.1538 15.3846V12.8462H8.61538C8.14807 12.8462 7.76923 12.4673 7.76923 12C7.76923 11.5327 8.14807 11.1538 8.61538 11.1538H11.1538V8.61538C11.1538 8.14807 11.5327 7.76923 12 7.76923Z"}))});u.default=h},98832:l=>{l.exports=function _taggedTemplateLiteral(l,u){return u||(u=l.slice(0)),Object.freeze(Object.defineProperties(l,{raw:{value:Object.freeze(u)}}))},l.exports.__esModule=!0,l.exports.default=l.exports},99e3:(l,u)=>{"use strict";function isRegExp(l){return"[object RegExp]"===Object.prototype.toString.call(l)}Object.defineProperty(u,"__esModule",{value:!0}),u.default=function checkHost(l,u){for(var s=0;s<u.length;s++){var c=u[s];if(l===c||isRegExp(c)&&c.test(l))return!0}return!1},l.exports=u.default,l.exports.default=u.default},99476:(l,u,s)=>{"use strict";var c=s(96784);Object.defineProperty(u,"__esModule",{value:!0}),u.default=void 0;var p=c(s(41594)),m=c(s(78304)),g=s(86956),h=s(12470),y=c(s(62688)),v=s(44048),_=(0,g.styled)(function ElementorLogo(l){return p.default.createElement(g.SvgIcon,(0,m.default)({viewBox:"0 0 32 32"},l),p.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.69648 24.8891C0.938383 22.2579 0 19.1645 0 16C0 11.7566 1.68571 7.68687 4.68629 4.68629C7.68687 1.68571 11.7566 0 16 0C19.1645 0 22.2579 0.938383 24.8891 2.69648C27.5203 4.45459 29.5711 6.95344 30.7821 9.87706C31.9931 12.8007 32.3099 16.0177 31.6926 19.1214C31.0752 22.2251 29.5514 25.0761 27.3137 27.3137C25.0761 29.5514 22.2251 31.0752 19.1214 31.6926C16.0177 32.3099 12.8007 31.9931 9.87706 30.7821C6.95344 29.5711 4.45459 27.5203 2.69648 24.8891ZM12.0006 9.33281H9.33437V22.6665H12.0006V9.33281ZM22.6657 9.33281H14.6669V11.9991H22.6657V9.33281ZM22.6657 14.6654H14.6669V17.3316H22.6657V14.6654ZM22.6657 20.0003H14.6669V22.6665H22.6657V20.0003Z"}))})(function(l){var u=l.theme;return{width:u.spacing(3),height:u.spacing(3),"& path":{fill:u.palette.text.primary}}}),b=function DialogHeader(l){return p.default.createElement(g.AppBar,{sx:{fontWeight:"normal"},color:"transparent",position:"relative"},p.default.createElement(g.Toolbar,{variant:"dense"},p.default.createElement(_,{sx:{mr:1}}),p.default.createElement(g.Typography,{component:"span",variant:"subtitle2",sx:{fontWeight:"bold",textTransform:"uppercase"}},(0,h.__)("AI","elementor")),p.default.createElement(g.Chip,{label:(0,h.__)("Beta","elementor"),color:"default",size:"small",sx:{ml:1}}),p.default.createElement(g.Stack,{direction:"row",spacing:1,alignItems:"center",sx:{ml:"auto"}},l.children,p.default.createElement(g.IconButton,{size:"small","aria-label":"close",onClick:l.onClose,sx:{"&.MuiButtonBase-root":{mr:-1}}},p.default.createElement(v.XIcon,null)))))};b.propTypes={onClose:y.default.func.isRequired,children:y.default.oneOfType([y.default.arrayOf(y.default.node),y.default.node])};u.default=b}},u={};function __webpack_require__(s){var c=u[s];if(void 0!==c)return c.exports;var p=u[s]={exports:{}};return l[s](p,p.exports,__webpack_require__),p.exports}__webpack_require__.d=(l,u)=>{for(var s in u)__webpack_require__.o(u,s)&&!__webpack_require__.o(l,s)&&Object.defineProperty(l,s,{enumerable:!0,get:u[s]})},__webpack_require__.o=(l,u)=>Object.prototype.hasOwnProperty.call(l,u),__webpack_require__.r=l=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(l,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(l,"__esModule",{value:!0})};(()=>{"use strict";var l=__webpack_require__(96784);var u=l(__webpack_require__(61790)),s=l(__webpack_require__(58155)),c=l(__webpack_require__(39805)),p=l(__webpack_require__(40989)),m=l(__webpack_require__(15118)),g=l(__webpack_require__(29402)),h=l(__webpack_require__(87861)),y=l(__webpack_require__(85707)),v=l(__webpack_require__(47389)),_=__webpack_require__(36833),b=__webpack_require__(12470),w=__webpack_require__(91258),C=l(__webpack_require__(92263)),x=__webpack_require__(4353);function _isNativeReflectConstruct(){try{var l=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(l){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!l})()}var P="ai-attachment";new(function(l){function Module(){var l;(0,c.default)(this,Module);for(var p=arguments.length,h=new Array(p),v=0;v<p;v++)h[v]=arguments[v];return l=function _callSuper(l,u,s){return u=(0,g.default)(u),(0,m.default)(l,_isNativeReflectConstruct()?Reflect.construct(u,s||[],(0,g.default)(l).constructor):u.apply(l,s))}(this,Module,[].concat(h)),(0,y.default)(l,"registerVariationsContextMenu",function(l,c){var p=l.find(function(l){return"save"===l.name});if(!p)return l;var m,g={name:"ai",icon:"eicon-ai",isEnabled:function isEnabled(){return 0!==c.getContainer().children.length},title:(0,b.__)("Generate variations with AI","elementor"),callback:(m=(0,s.default)(u.default.mark(function _callee(){var l,s,p;return u.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:l=c.getContainer(),s=l.model.toJSON({remove:["default"]}),p=[{type:"json",previewHTML:"",content:s,label:l.model.get("title"),source:x.USER_VARIATION_SOURCE}],(0,_.renderLayoutApp)({parentContainer:l.parent,mode:w.MODE_VARIATION,at:l.view._index,attachments:p,onSelect:function onSelect(){l.view.$el.hide()},onClose:function onClose(){l.view.$el.show()},onInsert:function onInsert(u){(0,_.importToEditor)({parentContainer:l.parent,at:l.view._index,template:u,historyTitle:(0,b.__)("AI Variation","elementor"),replace:!0})}});case 1:case"end":return u.stop()}},_callee)})),function callback(){return m.apply(this,arguments)})};return p.actions.unshift(g),l}),l}return(0,h.default)(Module,l),(0,p.default)(Module,[{key:"onElementorInit",value:function onElementorInit(){var l=this;elementor.hooks.addFilter("views/add-section/behaviors",this.registerAiLayoutBehavior),elementor.hooks.addFilter("elements/container/contextMenuGroups",this.registerVariationsContextMenu),elementor.hooks.addFilter("elementor/editor/template-library/template/behaviors",this.registerLibraryActionButtonBehavior),elementor.hooks.addFilter("elementor/editor/template-library/template/action-button",this.filterLibraryActionButtonTemplate,11),$e.commands.register("library","generate-ai-variation",function(u){return l.applyTemplate(u)})}},{key:"applyTemplate",value:function applyTemplate(l){window.postMessage({type:"library/attach:start"}),$e.components.get("library").downloadTemplate(l,function(u){var s=l.model;window.postMessage({type:"library/attach",json:u.content[0],html:'<img src="'.concat(s.get("thumbnail"),'" />'),label:"".concat(s.get("template_id")," - ").concat(s.get("title")),source:x.ELEMENTOR_LIBRARY_SOURCE},window.location.origin)})}},{key:"registerLibraryActionButtonBehavior",value:function registerLibraryActionButtonBehavior(l){return l.applyAiTemplate={behaviorClass:C.default},l}},{key:"registerAiLayoutBehavior",value:function registerAiLayoutBehavior(l){return l.ai={behaviorClass:v.default,context:{documentType:window.elementor.documents.getCurrent().config.type}},l}},{key:"filterLibraryActionButtonTemplate",value:function filterLibraryActionButtonTemplate(l){var u=$e.components.get("library").manager.modalConfig;return"#tmpl-elementor-template-library-insert-button"!==l||"library/templates/blocks"!==$e.routes.current.library?l:l=P===u.mode?"#tmpl-elementor-template-library-apply-ai-button":"#tmpl-elementor-template-library-insert-and-ai-variations-buttons"}}])}(elementorModules.editor.utils.Module))})()})();