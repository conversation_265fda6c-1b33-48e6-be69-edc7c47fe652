{"translation-revision-date": "2025-09-04 07:49:29+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "it"}, "Backdrop filters": ["Filtri per gli sfondi"], "Filters": ["<PERSON><PERSON><PERSON>"], "Has styles": ["Ha stili"], "Has overridden styles": ["Ha stili sovrasscritti"], "Has effective styles": ["Ha stili efficaci"], "Flex wrap": ["Flex wrap"], "Flex Size": ["Dimensione flex"], "Flex direction": ["Direzione flex"], "%s$1 %s$2 edited": ["Lo stile %s$1 %s$2 è stato modificato"], "%s edited": ["%s è stato modificato"], "Base": ["Base"], "Class": ["Classe"], "Style origin": ["Origine dello stile"], "Inherited from base styles": ["Ereditato dagli stili di base"], "With your current role,": ["Con il tuo ruolo attuale,"], "With your current role, you can only use existing states.": ["Con il tuo ruolo attuale, puoi utilizzare solo gli stati esistenti."], "Inheritance item: %s": ["Elemento ereditato: %s"], "%s created": ["la classe %s è stata creata"], "you can only use existing classes.": ["puoi usare solo le classi esistenti."], "class %s applied": ["classe %s applicata"], "With your current role, you can use existing classes but can’t modify them.": ["Con il tuo ruolo attuale, puoi utilizzare le classi esistenti, ma non puoi modificarle."], "Column gap": ["Spaziatura della colonna"], "class %s removed": ["classe %s rimossa"], "Dynamic tags": ["Tag dinamici"], "Word spacing": ["Spaziatura parole"], "Text transform": ["Trasformazione del testo"], "Line-through": ["Barrato"], "Text color": ["Colore del testo"], "Text align": ["Allineamento testo"], "Line height": ["Altezza della linea"], "900 - Black": ["900 - Nero"], "800 - Extra bold": ["800 - Grassetto accentuato"], "700 - Bold": ["700 - Grassetto"], "600 - Semi bold": ["600 - Semigrassetto"], "500 - Medium": ["500 - Medio"], "400 - Normal": ["400 - Normale"], "300 - Light": ["300 - <PERSON><PERSON>"], "200 - Extra light": ["200 - <PERSON><PERSON><PERSON> chiaro"], "100 - Thin": ["100 - <PERSON><PERSON><PERSON>"], "Font style": ["Stile del font"], "Font family": ["Famiglia di font"], "Max width": ["<PERSON><PERSON><PERSON><PERSON> massima"], "Min width": ["Larghezza minima"], "Object position": ["Posizione oggetto"], "Scale down": ["<PERSON><PERSON><PERSON><PERSON>"], "Z-index": ["Z-index"], "Align content": ["Allineamento contenuto"], "Border radius": ["Raggio del bordo"], "Classes": ["Classi"], "States": ["Stati"], "Radial Gradient": ["Gradiente radiale"], "Linear Gradient": ["Gradiente lineare"], "Bottom right": ["In basso a destra"], "Bottom left": ["In basso a sinistra"], "Border type": ["Tipo di bordo"], "Border color": ["Colore del bordo"], "Type class name": ["Digita nome della classe"], "This has value from another style": ["Questo ha valore da un altro stile"], "Try something else.": ["<PERSON><PERSON> qualcos'altro."], "Clear & try again": ["Cancella e riprova"], "Streamline your workflow with dynamic tags": ["Semplifica il tuo flusso di lavoro con i tag dinamici"], "Line decoration": ["Decorazione della linea"], "Anchor offset": ["Offset dell'ancora"], "Inline-flex": ["Inline-flex"], "In-flx": ["In-flx"], "Inline-block": ["Inline-block"], "In-blk": ["In-blk"], "Adjust corners": ["Regola angoli"], "Adjust borders": ["<PERSON><PERSON> bordi"], "This value is overridden by another style": ["Questo valore è sovrascritto da un altro stile"], "This is the final value": ["Questo è il valore finale"], "You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.": ["Hai raggiunto il limite di 50 classi. Rimuovi una classe esistente per crearne una nuova."], "Letter spacing": ["Spaziatura tra caratteri"], "You'll need Elementor Pro to use this feature.": ["Per utilizzare questa funzionalità passa a Elementor Pro."], "Underline": ["Sottolineato"], "Overline": ["Sopralineato"], "Object fit": ["Adattamento dell'oggetto"], "Border width": ["Spessore del bordo"], "Top right": ["In alto a destra"], "Top left": ["In alto a sinistra"], "Has style": ["Ha uno stile"], "Text stroke": ["Contornato"], "Open CSS Class Menu": ["Apri menu delle classi CSS"], "No wrap": ["Nessun wrap"], "Justify content": ["Giustifica il contenuto"], "Custom order": ["Ordine personalizzato"], "Flex": ["Flex"], "Block": ["Blocco"], "Align items": ["Allinea elementi"], "local": ["locale"], "Flex child": ["Flex figlio"], "Reversed wrap": ["Wrap invertito"], "Basis": ["Base"], "Last": ["Ultimo"], "First": ["Primo"], "Reversed column": ["<PERSON>onna invertita"], "Reversed row": ["Riga invertita"], "Space evenly": ["Spazia in modo uniforme"], "Space around": ["Spazia intorno"], "Align self": ["Auto allineamento"], "Effects": ["<PERSON><PERSON><PERSON>"], "Inset": ["Inset"], "Visible": ["Visibile"], "Relative": ["Relativo"], "Static": ["Statico"], "Box shadow": ["Ombra del riquadro"], "Outset": ["Outset"], "Ridge": ["In rilievo"], "Remove dynamic value": ["Rimuovi il valore dinamico"], "Show more": ["Mostra altro"], "Show less": ["<PERSON>ra meno"], "Sorry, nothing matched": ["<PERSON><PERSON><PERSON> corrisp<PERSON>e"], "Font weight": ["Font weight"], "Font size": ["Dimensione del font"], "Search dynamic tags…": ["Cerca tag dinamici…"], "Max height": ["Altezza massima"], "Min height": ["Altezza minima"], "Dimensions": ["Dimensioni"], "Space between": ["Spazio intermedio"], "Sticky": ["In evidenza"], "Wrap": ["Wrap"], "Shrink": ["Restringi"], "Grow": ["<PERSON><PERSON><PERSON><PERSON>"], "Groove": ["Scanalatura"], "Rename": ["Rinomina"], "Order": ["Ordinamento"], "Row": ["Riga"], "Gaps": ["Spaziature"], "Google Fonts": ["Google Fonts"], "Fill": ["Riempi"], "Contain": ["Contenitore"], "Cover": ["Cover"], "Auto": ["Automatica"], "Fixed": ["<PERSON><PERSON>"], "Absolute": ["Assoluto"], "Hidden": ["Nascosto"], "Overflow": ["Eccedenza"], "Opacity": ["Opacità"], "Position": ["Posizione"], "Custom Fonts": ["<PERSON><PERSON>"], "Remove": ["<PERSON><PERSON><PERSON><PERSON>"], "End": ["Fine"], "Start": ["<PERSON><PERSON><PERSON>"], "Edit %s": ["Modifica %s"], "General": ["Generale"], "Background": ["Sfondo"], "Direction": ["Direzione"], "Spacing": ["Spaziatura"], "Left to right": ["Da sinistra a destra"], "Display": ["Visualizzazione"], "Settings": ["Impostazioni"], "Clear": ["Can<PERSON><PERSON>"], "Right": ["Destra"], "Left": ["Sinistra"], "System": ["Sistema"], "Solid": ["Solido"], "Double": ["<PERSON><PERSON><PERSON>"], "Dashed": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "None": ["<PERSON><PERSON><PERSON>"], "Columns": ["Colonne"], "Style": ["Stile"], "Custom": ["<PERSON><PERSON><PERSON><PERSON>"], "Normal": ["Normale"], "Column": ["<PERSON>onna"], "Center": ["Centra"], "Margin": ["<PERSON><PERSON><PERSON>"], "Typography": ["Tipografia"], "Width": ["<PERSON><PERSON><PERSON><PERSON>"], "Height": ["Altezza"], "Stretch": ["Estendi"], "Size": ["Dimensione"], "Border": ["<PERSON><PERSON>"], "Aspect Ratio": ["Proporzioni"], "Layout": ["Layout"], "Dotted": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Padding": ["Rientro"], "Top": ["Sopra"], "Bottom": ["<PERSON>tto"], "Justify": ["Giustifica"], "Capitalize": ["Iniziale ma<PERSON>cola"], "Lowercase": ["<PERSON><PERSON><PERSON><PERSON>"], "Uppercase": ["<PERSON><PERSON><PERSON><PERSON>"], "Right to left": ["Da destra a sinistra"], "Italic": ["Corsivo"]}}, "comment": {"reference": "assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js"}}