<?php
if (!defined('ABSPATH')) {
    exit;
}

class WEE_Admin {
    
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        
        // Enqueue admin assets
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // Handle form submission
        add_action('admin_post_wee_export_emails', [$this, 'handle_export_emails']);
        
        // Add admin notices
        add_action('admin_notices', [$this, 'show_admin_notices']);
    }
    
    /**
     * Add submenu under WooCommerce
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Email Ordini', 'woo-email-export'),
            __('Email Ordini', 'woo-email-export'),
            'manage_woocommerce',
            'wee-email-export',
            [$this, 'render_admin_page']
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if ('woocommerce_page_wee-email-export' !== $hook) {
            return;
        }
        
        // Enqueue WordPress date picker
        wp_enqueue_script('jquery-ui-datepicker');
        wp_enqueue_style('jquery-ui-datepicker-style', 'https://code.jquery.com/ui/1.12.1/themes/ui-lightness/jquery-ui.css');
        
        // Enqueue custom CSS
        wp_enqueue_style(
            'wee-admin-style',
            WEE_PLUGIN_URL . 'assets/css/admin.css',
            [],
            WEE_VERSION
        );
        
        // Enqueue custom JS
        wp_enqueue_script(
            'wee-admin-script',
            WEE_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery', 'jquery-ui-datepicker'],
            WEE_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('wee-admin-script', 'wee_admin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wee_admin_nonce'),
            'strings' => [
                'select_date_range' => __('Please select a valid date range.', 'woo-email-export'),
                'select_status' => __('Please select at least one order status.', 'woo-email-export'),
                'processing' => __('Processing...', 'woo-email-export')
            ]
        ]);
    }
    
    /**
     * Render the admin page
     */
    public function render_admin_page() {
        // Get available order statuses
        $order_statuses = wc_get_order_statuses();
        ?>
        <div class="wrap">
            <h1><?php _e('Export Email Ordini', 'woo-email-export'); ?></h1>
            <p><?php _e('Export customer email addresses from WooCommerce orders based on date range and order status.', 'woo-email-export'); ?></p>
            
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" id="wee-export-form">
                <?php wp_nonce_field('wee_export_emails', 'wee_nonce'); ?>
                <input type="hidden" name="action" value="wee_export_emails">
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="start_date"><?php _e('Start Date', 'woo-email-export'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="start_date" name="start_date" class="wee-datepicker" required>
                            <p class="description"><?php _e('Select the start date for the order range.', 'woo-email-export'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="end_date"><?php _e('End Date', 'woo-email-export'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="end_date" name="end_date" class="wee-datepicker" required>
                            <p class="description"><?php _e('Select the end date for the order range.', 'woo-email-export'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="order_statuses"><?php _e('Order Status', 'woo-email-export'); ?></label>
                        </th>
                        <td>
                            <select id="order_statuses" name="order_statuses[]" multiple="multiple" class="wee-multiselect" required>
                                <?php foreach ($order_statuses as $status_key => $status_label): ?>
                                    <option value="<?php echo esc_attr($status_key); ?>">
                                        <?php echo esc_html($status_label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php _e('Select one or more order statuses to filter by. Hold Ctrl/Cmd to select multiple.', 'woo-email-export'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(__('Export to CSV', 'woo-email-export'), 'primary', 'submit', false, ['id' => 'wee-submit-btn']); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * Handle the export form submission
     */
    public function handle_export_emails() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['wee_nonce'], 'wee_export_emails')) {
            wp_die(__('Security check failed.', 'woo-email-export'));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have permission to perform this action.', 'woo-email-export'));
        }
        
        // Sanitize and validate input
        $start_date = sanitize_text_field($_POST['start_date']);
        $end_date = sanitize_text_field($_POST['end_date']);
        $order_statuses = isset($_POST['order_statuses']) ? array_map('sanitize_text_field', $_POST['order_statuses']) : [];
        
        // Validate dates
        if (empty($start_date) || empty($end_date)) {
            wp_redirect(add_query_arg(['page' => 'wee-email-export', 'error' => 'invalid_dates'], admin_url('admin.php')));
            exit;
        }
        
        // Validate order statuses
        if (empty($order_statuses)) {
            wp_redirect(add_query_arg(['page' => 'wee-email-export', 'error' => 'no_statuses'], admin_url('admin.php')));
            exit;
        }
        
        // Convert dates to proper format
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));
        
        // Validate date range
        if ($start_date > $end_date) {
            wp_redirect(add_query_arg(['page' => 'wee-email-export', 'error' => 'invalid_range'], admin_url('admin.php')));
            exit;
        }
        
        // Generate and download CSV
        $this->generate_csv($start_date, $end_date, $order_statuses);
    }
    
    /**
     * Generate and download CSV file
     */
    private function generate_csv($start_date, $end_date, $order_statuses) {
        // Query orders - exclude refunds and other non-order types
        $orders = wc_get_orders([
            'status' => $order_statuses,
            'date_created' => $start_date . '...' . $end_date,
            'limit' => -1,
            'return' => 'objects',
            'type' => 'shop_order' // Only get actual orders, not refunds
        ]);

        // Extract unique email addresses
        $emails = [];
        foreach ($orders as $order) {
            // Double check that this is actually a WC_Order object
            if (!is_a($order, 'WC_Order')) {
                continue;
            }

            $email = $order->get_billing_email();
            if (!empty($email) && !in_array($email, $emails)) {
                $emails[] = $email;
            }
        }
        
        // Check if any emails found
        if (empty($emails)) {
            wp_redirect(add_query_arg(['page' => 'wee-email-export', 'error' => 'no_emails'], admin_url('admin.php')));
            exit;
        }
        
        // Generate CSV filename
        $filename = 'email-ordini-' . $start_date . '-to-' . $end_date . '-' . date('Y-m-d-H-i-s') . '.csv';
        
        // Set headers for CSV download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        // Create file pointer
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Add CSV header
        fputcsv($output, [__('Email Address', 'woo-email-export')]);
        
        // Add email addresses
        foreach ($emails as $email) {
            fputcsv($output, [$email]);
        }
        
        fclose($output);
        exit;
    }
    
    /**
     * Show admin notices
     */
    public function show_admin_notices() {
        if (!isset($_GET['page']) || $_GET['page'] !== 'wee-email-export') {
            return;
        }
        
        if (isset($_GET['error'])) {
            $error = sanitize_text_field($_GET['error']);
            $message = '';
            
            switch ($error) {
                case 'invalid_dates':
                    $message = __('Please select valid start and end dates.', 'woo-email-export');
                    break;
                case 'no_statuses':
                    $message = __('Please select at least one order status.', 'woo-email-export');
                    break;
                case 'invalid_range':
                    $message = __('Start date must be before or equal to end date.', 'woo-email-export');
                    break;
                case 'no_emails':
                    $message = __('No orders found matching the specified criteria.', 'woo-email-export');
                    break;
            }
            
            if ($message) {
                echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($message) . '</p></div>';
            }
        }
    }
}
