== Changelog ==

= 3.31.3 - 2025-08-27 =

* Tweak: Disabled indication popover on repeater items – Editor V4
* Tweak: Enforced numeric keys in `promotionWidgets` and ensured consistent Pro widget injection
* Fix: Floating Bar disappears from the frontend after activating Elementor Pro

= 3.31.2 - 2025-08-11 =

* Tweak: Improved permissions functionality in Class Management - Editor V4
* Fix: Page failed to save on certain hosting providers due to blocked requests – Editor V4 ([#31992](https://github.com/elementor/elementor/issues/31992))
* Fix: Renaming a class causes it to lose applied styles – Editor V4
* Fix: Inherited values not working as expected with custom units in responsive modes – Editor V4
* Fix: Visual Choose control not displaying properly in Firefox for Shape Divider
* Fix: Icon alignment not working in Icon Box widget

= 3.31.1 - 2025-08-06 =

* Tweak: Updated `form-data` package version
* Fix: Custom order field disappeared when set to 0 or cleared - Editor V4

= 3.31.0 - 2025-08-05 =

* New: Introducing Variables - define reusable color and font values to ensure consistent and scalable design across your site - Editor V4
* New: Introducing Inherited Values - style properties now indicate when values are inherited from Classes or Base styles - Editor V4
* New: Introducing Filters - add visual effects like blur, brightness, and contrast with full design control - Editor V4
* New: Introducing Backdrop Filters - apply visual effects behind elements for layered design - Editor V4
* New: Introducing Divider - a modular layout element for visual separation - Editor V4
* New: Class Usage Overview – gain insights into class usage directly from the Class Manager - Editor V4
* New: Class Locator - locate all elements using a specific class across your design - Editor V4
* Tweak: Removed background videos from the accessibility tree to improve screen reader behavior ([#23880](https://github.com/elementor/elementor/issues/23880))
* Tweak: Search by Class name - quickly locate existing classes in the Class Manager - Editor V4
* Tweak: Improved style detection across breakpoints when using Classes - Editor V4
* Tweak: Added Custom option to Object Position in Size section - Editor V4
* Tweak: Added ID control in YouTube element - Editor V4
* Tweak: Added Opacity control - set element transparency - Editor V4
* Tweak: Display Base Style indication - clearly shows when Base styles are applied to an element - Editor V4
* Tweak: Improved drag and drop behavior into Flexbox and Div Blocks inside the Canvas - Editor V4
* Tweak: Replaced select control with a visual choice control in Shape Dividers
* Tweak: Replaced SASS mixins and functions with native CSS logical properties
* Tweak: Added support for `lh` and `rlh` CSS units in Typography Line Height control
* Tweak: Added responsive capabilities to Custom Mask
* Tweak: Activated "Element Caching" experiment for all sites
* Tweak: Updated Icon List widget to use CSS logical properties
* Tweak: Updated Star Rating widget to use CSS logical properties
* Tweak: Updated Alert widget to use CSS logical properties
* Tweak: Updated Accordion widget to use CSS logical properties
* Tweak: Updated Toggle widget to use CSS logical properties
* Tweak: Updated Icon Box widget to use CSS logical properties
* Tweak: Updated Menu Anchor widget to use CSS logical properties
* Tweak: Updated Tabs widget to use CSS logical properties
* Tweak: Updated Testimonial widget to use CSS logical properties
* Tweak: Updated Text Editor widget to use CSS logical properties
* Tweak: Updated Paragraph Spacing in Site Settings Typography to use CSS logical properties
* Tweak: Optimized CSS output for Masks

= 3.30.4 - 2025-07-30 =

* Tweak: Improved redirection functionality during the website template import process

= 3.30.3 - 2025-07-22 =

* Tweak: Improved performance of the first-time Editor load
* Security Fix: Improved code security enforcement in Import process
* Security Fix: Improved code security enforcement in Text Path widget
* Security Fix: Improved content sanitization in multiple widgets
* Fix: Link control label is missing in all elements - Editor V4

= 3.30.2 - 2025-07-09 =

* Fix: Editor failing to load on certain hosting providers due to blocked requests

= 3.30.1 - 2025-07-07 =

* Fix: Focus not applied correctly to chosen property in popovers - Editor V4
* Fix: Font Family popover not loading as expected - Editor V4
* Fix: Empty list displayed when opening the Class Manager - Editor V4

= 3.30.0 - 2025-07-01 =

* New: Added support for setting custom units in size controls - Editor V4 ([#31287](https://github.com/elementor/elementor/issues/31287))
* New: Added reset control visibility via the floating action bar - Editor V4 ([#31356](https://github.com/elementor/elementor/issues/31356))
* New: Added JS handler infrastructure for the new elements system - Editor V4
* New: Introduced modular YouTube element built with new structure and JS handlers – Editor V4
* New: Added support for context-aware editing memory - Editor V4
* New: Enabled Smart Unit Typing - allows typing values with units directly - Editor V4
* New: Added ID control to Settings section under General tab - Editor V4
* New: Added Anchor Offset control to Position section under Style tab - Editor V4
* New: Added Display None control to Layout section under Style tab - Editor V4
* New: Added Object Fit control to Size section under Style tab - Editor V4
* New: Added Columns control to Typography section under Style tab - Editor V4
* New: Added Aspect Ratio control to Size section under Style tab - Editor V4
* New: Added group-level style indicators to show where class-based styles are applied – Editor V4
* New: Added Indications Popover for visualizing class-based style origins – Editor V4
* New: Introduced class permissions for non-admin users - Editor V4
* Tweak: Added title hover and focus color options to Icon Box and Image Box widgets ([#29948](https://github.com/elementor/elementor/issues/29948))
* Tweak: Added support for registering custom mask shapes ([#19396](https://github.com/elementor/elementor/issues/19396))
* Tweak: User-defined class names now appear as-is in the final code output in Class Management - Editor V4 ([#31055](https://github.com/elementor/elementor/issues/31055))
* Tweak: Renamed "Kits" to "Website Template" across the interface
* Tweak: Added Settings section inside the General tab - Editor V4
* Tweak: Merged "Editor Top Bar" feature into the core version
* Tweak: Merged "Load Google Fonts locally" feature into the core version
* Tweak: Activated "Optimized Markup" feature for new sites
* Tweak: Promoted "Element Caching" feature to Stable status
* Tweak: Added new mask shapes
* Tweak: Replaced select control with a visual choice control in Mask shapes
* Tweak: Add image `height`, `object-fit`, `object-position`, `box-shadow` in Image Box widget
* Tweak: Standardized naming convention for items in the editor `app-bar`
* Tweak: Consolidated control visibility and layout into a single Style tab in Progress Bar widget
* Tweak: Updated minimum required WordPress version to 6.6
* Fix: Global CSS transition with higher specificity prevents Container transitions from being applied ([#30460](https://github.com/elementor/elementor/issues/30460))
* Fix: Global CSS classes are lost when publishing from multiple tabs - Editor V4
* Fix: Redundant spacing appears below the image in the Image Box widget
* Fix: Scrolling to anchors no longer works inside the Editor

= 3.29.2 - 2025-06-04 =

* Fix: Missing responsive resize handles in responsive mode preventing manual preview adjustments
* Fix: Style sections expanded by default instead of remaining collapsed - Editor V4
* Fix: Publish button not triggered when applying a class - Editor V4

= 3.29.1 - 2025-05-28 =

* Tweak: Disabled the ability to use elements caching shortcode via the interface
* Security Fix: Improved code security enforcement in Shortcode widget
* Fix: Information modals are not showing as expected in the Editor - Editor V4
* Fix: Error on the frontend when background overlay is set to hidden - Editor V4
* Fix: Editor load issue when a widget does not register the "Advanced" tab

= 3.29.0 - 2025-05-19 =

* New: Introduced the first Alpha release of the next-generation Editor - a faster, modular, CSS-based infrastructure available as an opt-in experience - Editor V4
* New: Combine familiar and new Editor elements for a seamless editing experience - Editor V4
* New: Introduced cleaner code and fewer div wrappers for faster performance and a lighter editing experience - Editor V4
* New: Gain full responsive control - adjust every style property individually for each device, without limitations - Editor V4
* New: Introduced Classes - a reusable styling system that brings consistency, modularity, and CSS-based workflows to your designs - Editor V4
* New: Introduced Class Manager - centrally manage, rename, reorder, and delete Classes across your site - Editor V4
* New: Introduced States - define hover, focus, and active styles for Classes to create dynamic, interactive designs - Editor V4
* New: Introduced Local Class - a fixed, non-removable class with highest styling priority, ensuring unique styling for each element - Editor V4
* New: Introduced Class Indicators - color-coded visual cues that show where styles originate, inherit, or conflict - Editor V4
* New: Introduced a unified Style Tab - a consistent, CSS-based styling system shared across all elements, streamlining design workflows - Editor V4
* New: Introduced Style Repeaters - create and manage multiple background and box shadow layers with full control over their order and visibility - Editor V4
* New: Introduced Actions Floating Bar - a cleaner, context-aware toolbar that appears on hover, starting with Dynamic Tags support - Editor V4
* New: Introduced Link Control - add and customize links across elements with clean HTML, while preventing nested links - Editor V4
* New: Introduced Logical Properties - styling adapts automatically to different writing directions for global language support - Editor V4
* New: Added DIV Block element - a container element for structuring layouts with customizable display options - Editor V4
* New: Added Flexbox element - a layout element for precise alignment and distribution - Editor V4
* New: Added Heading element - a standalone heading element with flexible styling - Editor V4
* New: Added Paragraph element - a simple, dedicated text element for paragraphs - Editor V4
* New: Added Image element - a modular image element with clean markup and consistent styling - Editor V4
* New: Added Button element - a customizable button element for modular design - Editor V4
* New: Added SVG element - a lightweight element for adding scalable vector graphics - Editor V4
* Tweak: Added `AVIF` image format support to Lightbox ([#28256](https://github.com/elementor/elementor/issues/28256), [#25175](https://github.com/elementor/elementor/issues/25175), [#28169](https://github.com/elementor/elementor/issues/28169), [#29624](https://github.com/elementor/elementor/issues/29624))
* Tweak: Renamed "My Templates" to "Templates" in the Templates Library
* Tweak: Marked local templates as "Site Templates" to distinguish them from Cloud Templates
* Tweak: Excluded Shape Dividers from the accessibility tree
* Tweak: Improved accessibility with `role` attributes in the Social Icons widget
* Tweak: Added Display Title control to the Progress Bar widget to enhance accessibility
* Fix: Strings are not translating properly in the Global Style Guide ([#23237](https://github.com/elementor/elementor/issues/23237))
* Fix: Mask shape URLs use double slashes instead of single slashes ([#17534](https://github.com/elementor/elementor/issues/17534))
* Fix: Common scripts are loaded late when using the Theme Builder
* Fix: 'New Prompt' Button does not work in Generate Code AI feature

= 3.28.4 - 2025-04-22 =

* Tweak: Added Site Planner as an option in the onboarding wizard
* Tweak: Changed the default theme to Hello Biz in the onboarding wizard
* Tweak: Added inline editing support for controls using multiple repeaters

= 3.28.3 - 2025-04-01 =

* Tweak: Added Isolate object tool to Elementor AI image editing capabilities
* Fix: Third dropdown in template display conditions not working in Theme Builder ([#30729](https://github.com/elementor/elementor/issues/30729), [#30732](https://github.com/elementor/elementor/issues/30732))

= 3.28.2 - 2025-03-30 =

* New: Generate logo with AI
* Tweak: Adjusted supported attributes and elements in SVG files ([#29340](https://github.com/elementor/elementor/issues/29340), [#30132](https://github.com/elementor/elementor/issues/30132))
* Fix: "Create a page" button is not working in Elementor Home
* Fix: Icons in Button widgets are not vertically aligned when changing the text line-height ([#30606](https://github.com/elementor/elementor/issues/30606))

= 3.28.1 - 2025-03-23 =

* Fix: Font URL is not updating after domain change when "Load Google Fonts Locally" feature is activated

= 3.28.0 - 2025-03-17 =

* New: Added a Clear Files & Data button to the WordPress frontend admin bar for quick and easy cache clearing ([#21642](https://github.com/elementor/elementor/issues/21642))
* Tweak: Added a note clarifying that the Image Resolution control doesn't support dynamic background images ([#21359](https://github.com/elementor/elementor/issues/21359), [#22055](https://github.com/elementor/elementor/issues/22055))
* Tweak: Added hover state Box Shadow controls to Button widget ([#4859](https://github.com/elementor/elementor/issues/4859), [#30210](https://github.com/elementor/elementor/issues/30210), [#17509](https://github.com/elementor/elementor/issues/17509))
* Tweak: Renamed "Regenerate CSS & Data" to "Elementor Cache" in Elementor Tools for better clarity
* Tweak: Hide Landing Page feature when no landing page posts exist on the site
* Tweak: Promoted Load Google Fonts Locally feature to Stable status and enabled it by default for all websites
* Tweak: Improved rendering functionality when updating CSS ID control to enhance editing performance in Accordion widget
* Tweak: Improved rendering functionality when updating CSS ID control to enhance editing performance in Tabs widget
* Tweak: Removed unused deprecated methods from `elementorCommon.helpers`
* Tweak: Optimized style loading for the Text Editor widget by applying styles at the control level ensuring Drop Cap styles load only when enabled
* Tweak: Updated Drop Cap controls to use CSS logical properties in Text Editor widget
* Tweak: Updated minimum required WordPress version to 6.5
* Tweak: Removed Modest Branding control from Video widget
* Tweak: Promoted Optimized Markup feature to Beta status
* Tweak: Added link color controls in Text Editor widget
* Tweak: Applied hover color on focus for keyboard users in the Heading widget
* Fix: Button icon alignment breaks when Inline Font Icons feature is activated ([#16077](https://github.com/elementor/elementor/issues/16077), [#16511](https://github.com/elementor/elementor/issues/16511), [#17692](https://github.com/elementor/elementor/issues/17692), [#17922](https://github.com/elementor/elementor/issues/17922), [#19253](https://github.com/elementor/elementor/issues/19253), [#19370](https://github.com/elementor/elementor/issues/19370), [#21236](https://github.com/elementor/elementor/issues/21236), [#21844](https://github.com/elementor/elementor/issues/21844), [#28080](https://github.com/elementor/elementor/issues/28080))
* Fix: Editor fails to load in some edge cases when ACF Term fields are used as Dynamic Tags ([#29160](https://github.com/elementor/elementor/issues/29160))
* Fix: Improved sanitization of titles to ensure consistent handling for Admin users in the Heading widget
* Fix: Hover link color transition duration is not being applied in Heading widget

= 3.27.7 - 2025-03-13 =

* Security Fix: Improved code security enforcement in Admin Notices

= 3.27.6 - 2025-02-18 =

* Fix: Dimension Controls displayed `undefined` as a placeholder value when empty ([#30277](https://github.com/elementor/elementor/issues/30277))

= 3.27.5 - 2025-02-16 =

* Security Fix: Improved code security enforcement in Dimensions control

= 3.27.4 - 2025-02-13 =

* Fix: Preventing page saving in Gutenberg with ACF and active Elementor post settings ([#30160](https://github.com/elementor/elementor/issues/30160))

= 3.27.3 - 2025-02-03 =

* Tweak: Added support for Early Access fonts when using Load Google Fonts Locally experiment
* Tweak: Updated minified `flatpickr` CSS file to v4.6.13
* Fix: Kits could not be imported due to compatibility issue

= 3.27.2 - 2025-01-27 =

* Fix: Kits could not be imported due to a compatibility issue

= 3.27.1 - 2025-01-23 =

* Fix: Document is not being saved due to compatibility conflicts with third-party plugins ([#29970](https://github.com/elementor/elementor/issues/29970), [#29991](https://github.com/elementor/elementor/issues/29991))

= 3.27.0 - 2025-01-20 =

* New: Introducing local loading of Google Fonts to improve performance and enhance user privacy ([#4544](https://github.com/elementor/elementor/issues/4544), [#19966](https://github.com/elementor/elementor/issues/19966), [#23932](https://github.com/elementor/elementor/issues/23932), [#21716](https://github.com/elementor/elementor/issues/21716))
* New: Introducing advanced layout customization for Grid Container with column and row span controls for precise grid-based designs ([#25256](https://github.com/elementor/elementor/issues/25256))
* New: Added the option to animate with AI motion effects for creating AI-generated animations
* Tweak: Add 'YouTube shorts' support in the video widget ([#24220](https://github.com/elementor/elementor/issues/24220), [#20330](https://github.com/elementor/elementor/issues/20330))
* Tweak: Added Safari browser compatibility for the 'Fit to Size' option in the Icon Widget ([#27679](https://github.com/elementor/elementor/issues/27679))
* Tweak: Moved style loading to the head instead of the footer to improve CLS
* Tweak: Enabled conditional loading of `Swiper.js` based on widget dependencies to reduce unnecessary assets and improve page load times
* Tweak: Removed the `elementor-widget-container` div from the Spacer Widget as part of the Optimized Markup experiment to improve HTML structure
* Tweak: Improved keyboard accessibility for the nested container presets area
* Tweak: Added accessible and descriptive names to the icon link in the Icon Box widget
* Tweak: Optimize background video CSS by merging `elementor-html5-video` and `elementor-background-video-hosted` into a single class
* Tweak: Added the ability to disable the Element Cache
* Tweak: Removed animation class when no animations are set in Icon and Icon Widget widgets
* Tweak: Added support for captions on YouTube videos
* Tweak: Removed the limitation restricting heading hover color styling to links only
* Tweak: Removed `aspect-ratio` workaround for unsupported Safari browsers
* Fix: Switching between images in a lightbox on responsive mode displayed a tall blue rectangle ([#12830](https://github.com/elementor/elementor/issues/12830))
* Fix: Image editing tools with AI are not working when launched in WooCommerce
* Fix: Improved HTML markup validity by removing the `type` attribute from `<script>` tags

= 3.26.5 - 2025-01-15 =

* Fix: Background Slideshow option not working for columns

= 3.26.4 - 2025-01-07 =

* Fix: Responsive Visibility "Hide On Mobile Portrait" option not working for hidden Sections and Containers on older sites
* Fix: Editor fails to load when clicking on "Jumpstart your web-creation" links in Elementor Home after importing a kit

= 3.26.3 - 2024-12-22 =

* Tweak: Registered `swiper.js` script to ensure Swiper is properly declared and loaded ([#29612](https://github.com/elementor/elementor/issues/29612), [#29616](https://github.com/elementor/elementor/issues/29616))

= 3.26.2 - 2024-12-19 =

* Fix: Reverted Activated "Element Caching" feature for existing sites ([#27502](https://github.com/elementor/elementor/issues/29613))
* Fix: Compatibility issue with third-party plugin with `data-settings` attribute missing when Optimized Markup experiment is activated

= 3.26.1 - 2024-12-19

* Tweak: Return a specific error to users when images provider is not working in Elementor AI
* Fix: Typo in `Isolation Manager`

= 3.26.0 - 2024-12-16 =

* New: Unify product images seamlessly with AI
* Tweak: Added "Space Between Dots" control to Image Carousel widget ([#2526](https://github.com/elementor/elementor/issues/2526), [#3277](https://github.com/elementor/elementor/issues/3277), [#21697](https://github.com/elementor/elementor/issues/21697))
* Tweak: Added paragraph spacing control in Text Editor widget ([#25431](https://github.com/elementor/elementor/issues/25431), [#20144](https://github.com/elementor/elementor/issues/20144))
* Tweak: Added hover and transition color controls for a link in Heading widget ([#12877](https://github.com/elementor/elementor/issues/12877))
* Tweak: Updated Image Spacing control to support only PX units in Image Carousel widget ([#21827](https://github.com/elementor/elementor/issues/21827))
* Tweak: Created CSS variables for Row and Column gaps value in Container ([#24178](https://github.com/elementor/elementor/issues/24178))
* Tweak: Added the ability to set `aria-label` in Image Carousel widget ([#28355](https://github.com/elementor/elementor/issues/28355))
* Tweak: Replaced the Wrap Align Content select control with a choose control in Container ([#22640](https://github.com/elementor/elementor/issues/22640))
* Tweak: Implemented accessibility improvements for pagination bullets in Image Carousel widget ([#28674](https://github.com/elementor/elementor/issues/28674))
* Tweak: Replaced hidden `elementor-screen-only` div with `aria-label` attributes
* Tweak: Improved behavior and messaging for the warning displayed when navigating between documents
* Tweak: Load Nested Elements styles only when they are in use
* Tweak: Added support for rendering Gutenberg blocks on the frontend
* Tweak: Removed `elementor-widget-container` div from Elementor widgets as part of the Optimized Markup experiment
* Tweak: Updated CSS to utilize `inset` CSS logical property in various locations
* Tweak: Updated minimum required Safari version to 15.5
* Tweak: Transition Duration controls are displayed only when a background color is set
* Tweak: Transition Duration controls are displayed only when a border is set
* Tweak: Improved keyboard accessibility for the section and container presets area
* Tweak: Merged "Upgrade Swiper Library" feature to version
* Tweak: Merged "Optimized Control Loading" feature to version
* Tweak: Activated "Element Caching" feature for existing sites
* Tweak: Promoted "Nested Elements" feature to Stable status
* Tweak: Removed `elementor/core/schemes`
* Fix: Container Gap values not transferring correctly between sites when using Cross-site Copy and Paste ([#24111](https://github.com/elementor/elementor/issues/24111), [#28343](https://github.com/elementor/elementor/issues/28343))
* Fix: Removed anchor scroll warnings from the console ([#29199](https://github.com/elementor/elementor/issues/29199), [#29350](https://github.com/elementor/elementor/issues/29350))
* Fix: Frontend template rendering is broken with fatal error ([#29582](https://github.com/elementor/elementor/issues/29582), [#29579](https://github.com/elementor/elementor/issues/29579))
* Fix: PHP error log appearing due to the Usage Data Sharing cron job (([#29153](https://github.com/elementor/elementor/issues/29153))
* Fix: Deprecated `print_emoji_styles` warning in Theme Builder screen ([#27502](https://github.com/elementor/elementor/issues/27502))
* Fix: Display Condition feature is available without an active Elementor license in Floating Elements
* Fix: "Generate with Elementor AI" button is missing in the Media Library list mode
* Fix: Resized file size exceeding the maximum limit does not display an error in AI Image
* Fix: Images from URL incorrectly open the Generate modal
* Fix: Dropdown area is not opening correctly in certain scenarios in Menu widget
* Fix: Swiper styling missing from Lightbox inside Gallery widgets

= 3.25.11 - 2024-12-10 =

* Tweak: Updated `eicons` library to v5.34.0
* Security Fix: Improved code security enforcement in Image widget
* Security Fix: Improved code security enforcement in Connect process
* Security Fix: Improved code security enforcement in Progress bar widget
* Fix: YouTube video in lightbox is not presented as expected in Video widget ([#29241](https://github.com/elementor/elementor/issues/29241))

= 3.25.10 - 2024-11-24 =

* Security Fix: Improved code security enforcement in Typography control
* Fix: Link URL actions not functioning as expected inside the editor

= 3.25.9 - 2024-11-20 =

* Fix: 3D Rotate transform is not functioning as expected when the Optimized Control Loading feature is activated

= 3.25.8 - 2024-11-19 =

* Security Fix: Improved code security enforcement in Icon widget

= 3.25.7 - 2024-11-18 =

* Security Fix: Improved code security enforcement in Post functionality

= 3.25.6 - 2024-11-13 =

* Tweak: Updated `eicons` library to v5.32.0
* Tweak: Adjusted Kit import flow to allow extendability
* Tweak: Added extendability functionality to the common Advanced Tab controls
* Fix: PHP error appears when an undefined color or typography value is used in Site Settings
* Fix: Permanently deleting a global widget causes a fatal error on pages where it is used
* Fix: Nested Elements are activated even when the Container experiment is inactive

= 3.25.5 - 2024-11-12 =

* Security Fix: Improved code security enforcement in Post functionality
* Security Fix: Improved code security enforcement in Floating Elements
* Security Fix: Improved code security enforcement in Import and Export functionality

= 3.25.4 - 2024-11-03 =

* Fix: Console warning errors related to popups ([#29137](https://github.com/elementor/elementor/issues/29137), [#29141](https://github.com/elementor/elementor/issues/29141))

= 3.25.3 - 2024-10-30 =

* Fix: Global Widgets are not displaying correctly in both the editor and frontend ([#29112](https://github.com/elementor/elementor/issues/29112))

= 3.25.2 - 2024-10-29 =

* Fix: Global Widgets are not displaying correctly in the editor ([#29092](https://github.com/elementor/elementor/issues/29092))
* Fix: Image captions are not displaying for non-logged-in users in Image Carousel widget

= 3.25.1 - 2024-10-28 =

* Fix: Custom template fails to insert into the page and displays a permission error ([#29076](https://github.com/elementor/elementor/issues/29076))
* Fix: Global Widgets not displaying correctly on the frontend ([#29076](https://github.com/elementor/elementor/issues/29076))

= 3.25.0 - 2024-10-28 =

* New: Introducing Optimized Markup as an Alpha experiment - Reduce DOM size by eliminating unnecessary HTML wrappers in various elements and widgets
* Tweak: Reduced the use of JavaScript for smooth scrolling by implementing a modern CSS-based solution ([#13773](https://github.com/elementor/elementor/issues/13773))
* Tweak: Updated smooth scroll behavior to respect the user's reduced motion preference setting
* Tweak: Removed `elementor-button-wrapper` div from Button widget as part of the Optimized Markup experiment
* Tweak: Load styles for Floating Elements separately to enhance performance
* Tweak: Load styles for Link in Bio widgets separately to enhance performance
* Tweak: Optimized `global.css` to exclude style generation for unused widgets
* Tweak: Removed the Floating Elements modules from `module.js`
* Tweak: Added "Edit with Elementor" button to WooCommerce's new product editor
* Tweak: Merged "Build with AI" feature to version
* Tweak: Merged "Grid Container" to "Flexbox Container" feature
* Tweak: Activated "Editor Top Bar" feature for existing sites
* Tweak: Activated "Optimized Control Loading" for existing sites and promoted the feature to stable
* Tweak: Activated "Nested Elements" feature for existing sites
* Tweak: Removed compatibility CSS support for the Twenty Fifteen theme
* Tweak: Renamed the "Flexbox Container" feature to "Container" feature
* Fix: Expand Icon in the Accordion widget would not display when nested inside another Accordion widget ([#24086](https://github.com/elementor/elementor/issues/24086))
* Fix: Swiper incorrectly applied the `inert` attribute to a slide in the Nested Carousel ([#23039](https://github.com/elementor/elementor/issues/23039))
* Fix: Footer would overlap the Archive Posts, Portfolio, and Gallery widgets in mobile view when using the Archive template
* Fix: "Edit with Elementor AI" button is visible for unsupported file types
* Fix: Structure panel indicator color is not visible in dark mode
* Fix: Indentation issue with empty elements in the Structure panel
* Fix: "Reference a website" feature in Elementor AI failed to work in Safari 17

= 3.24.8 - 2024-10-28 =

* Security Fix: Improved code security enforcement in Template Library

= 3.24.7 - 2024-10-15 =

* Fix: Flow issue preventing progress beyond the Features screen in Onboarding wizard

= 3.24.6 - 2024-10-09 =

* Security Fix: Improved code security enforcement in Notices
* Security Fix: Improved code security enforcement in Media control

= 3.24.5 - 2024-10-01 =

* Fix: Responsive styles not loading properly for Apple WebKit
* Fix: Responsive styles not loading properly for Lightbox

= 3.24.4 - 2024-09-23 =

* Security Fix: Improved code security enforcement in Heading and Counter widgets
* Security Fix: Improved code security enforcement in Template Library

= 3.24.3 - 2024-09-18 =

* Fix: 404 console error when loading custom animations ([#28727](https://github.com/elementor/elementor/issues/28727))

= 3.24.2 - 2024-09-13 =

* Fix: Widget styles not loading correctly affecting the layout on front ([#28693](https://github.com/elementor/elementor/issues/28693))

= 3.24.1 - 2024-09-12 =

* Fix: Widget styles not loading correctly on front causing alignment inconsistencies ([#28676](https://github.com/elementor/elementor/issues/28676))

= 3.24.0 - 2024-09-10 =

* New: Introducing Floating Bars - including Call to Action, Ticker, and Coupon Bars, to enhance visitor engagement and boost conversions
* New: Added Cleanup image edit tool with AI
* Tweak: Improved performance by loading nested element styles only when they are in use
* Tweak: Load wp-admin-bar styles only when the user is logged in
* Tweak: Improved performance by loading Lightbox styles only when they are in use
* Tweak: Load floating button styles only when they are in use
* Tweak: Load Link-in-bio styles only when they are in use
* Tweak: Load text-path styles only when they are in use
* Tweak: Load shape dividers styles only when they are in use
* Tweak: Load `flatpickr-calendar` styles only when forms are in use
* Tweak: Load social icons compatibility styles only when they are in use
* Tweak: Load dialog styles only when they are in use
* Tweak: Split `animations.min.css` into multiple CSS files and load them conditionally
* Tweak: Conditionally loading widget styles only when the respective widgets are used
* Tweak: Load `get_style_depends()` CSS files in the body to improve CLS
* Tweak: Removed inline CSS from the "Improved CSS Loading" experiment
* Tweak: Removed `waypoints.js` library from the code
* Tweak: Ensured consistent icon shapes in Icon, Icon Box and Social Icons widgets
* Tweak: Merged "Lazy Load Background Images" feature and moved to the Performance tab
* Tweak: Merged "Floating Buttons" feature into the version
* Tweak: Merged "Link In Bio" feature into the version
* Tweak: Merged "Improved CSS Loading" feature into the version
* Tweak: Promoted "Editor Top Bar" feature to Stable status
* Tweak: Activated "Nested Elements" feature by default for new sites
* Tweak: Removed Floating Elements document from the query control
* Tweak: Added "Performance" and "Integrations" links to Elementor Finder
* Tweak: Removed Floating Elements from post types list
* Tweak: Updated minimum required WordPress version to 6.3
* Tweak: Updated `flatpickr` library to v4.6.13
* Tweak: Updated `eicons` library to v5.31.0
* Tweak: Added the ability for users to opt out of AI features on a per-user basis ([#22796](https://github.com/elementor/elementor/issues/22796), [#22853](https://github.com/elementor/elementor/issues/22853))
* Tweak: Added the ability to generate container names in the Structure feature using AI
* Tweak: Added preview functionality in the AI CSS feature
* Tweak: Enhanced prompts in the Custom CSS feature for improved usability
* Fix: Similar widgets in subsequent pop-ups do not load properly when Improved CSS Loading is activated ([#21488](https://github.com/elementor/elementor/issues/21488), [#25436](https://github.com/elementor/elementor/issues/25436), [#25436](https://github.com/elementor/elementor/issues/25436))
* Fix: Element Caching prevented the repeater control from loading Dynamic Tags ([#28137](https://github.com/elementor/elementor/issues/28137))
* Fix: Improved CSS loading experiment overrides child theme styles and breaks the cascade due to higher specificity ([#15746](https://github.com/elementor/elementor/issues/15746))
* Fix: Lottie Animation settings inside Popups are overridden when the Improved CSS Loading experiment is activated ([#17814](https://github.com/elementor/elementor/issues/17814))
* Fix: Prevent duplicate trailing slash from landing pages (props [@diiegopereira](https://github.com/diiegopereira))
* Fix: Corrected typos in the code (props [@szepeviktor](https://github.com/szepeviktor))
* Fix: User consent modal is being loaded after the request has been made in Text feature in Elementor AI
* Fix: Improved code security enforcement in Testimonial widget
* Fix: Modal doesn't adjust height to content in AI Text
* Fix: "Full width on mobile" isn't working in the "Single Bar" floating Button
* Fix: Lightbox video aspect ratio issues on the front
* Fix: PHP errors caused by using Global Color for container background gradient

= 3.23.4 - 2024-08-05 =

* Fix: Links to Elementor Settings are broken in various locations

= 3.23.3 - 2024-07-25 =

* Fix: UI fixes to improve user experience

= 3.23.2 - 2024-07-23 =
* Security Fix: Improved code security enforcement in link URL
* Fix: Facebook Messenger username is not working on mobile devices in Floating Buttons ([#28103](https://github.com/elementor/elementor/issues/28103))
* Fix: RTL issues with Floating Buttons templates
* Fix: WordPress 6.6 does not support certain features

= 3.23.1 - 2024-07-15 =
* Fix: Slow loading of WordPress admin and Elementor screens due to AI health check

= 3.23.0 - 2024-07-15 =

* New: Elevate engagement with Floating Buttons - Convert visitors into leads, making it easier than ever to start a conversation on any platform
* New: Introducing Link In Bio - allowing you to create a digital business card and share it easily
* New: Generate Featured Image with AI
* New: Generate texts using AI within WordPress
* Tweak: Added support for the `overscroll-behavior` CSS property to improve scrolling experience
* Tweak: Activated Editor Top Bar feature by default for new sites
* Tweak: Activated Element Caching feature for new sites and promoted to Beta status
* Tweak: Promotes "Lazy Load Backgrounds Images" feature to Stable status
* Tweak: Hide "Default Device View" from User Preferences when the Editor Top Bar is active
* Tweak: Increased the height of the Globals popover to improve usability
* Tweak: Improved Image style presets and added two additional ones in AI Images
* Tweak: Added Excerpt generation option with AI in the editor
* Fix: Performance issues causing lag and slowness while editing Nested Elements ([#24076](https://github.com/elementor/elementor/issues/24076))
* Fix: Video autoplay issues in various scenarios (props [@JxxIT](https://github.com/JxxIT), [#24324](https://github.com/elementor/elementor/issues/24324), [#14437](https://github.com/elementor/elementor/issues/14437), [#7964](https://github.com/elementor/elementor/issues/7964))
* Fix: Writing permissions issues related to WordPress root directory ([#21036](https://github.com/elementor/elementor/issues/21036), [#17255](https://github.com/elementor/elementor/issues/17255))
* Fix: Restored deprecated Google fonts that were deprecated in the previous update ([#27701](https://github.com/elementor/elementor/issues/27701))
* Fix: Vimeo link structure with privacy hash is not working correctly with dynamic link action using the lightbox
* Fix: Validate prompt and canvas changes to allow generation in AI image edit generative fill
* Fix: Elementor Core versions are being incorrectly added to Elementor Pro versions' history
* Fix: AI Connect not launching when the user doesn't have prior connect data

= 3.22.3 - 2024-06-26 =

* Fix: PHP error appears on the front when using gradient background with the Optimized Control Loading feature activated ([#27733](https://github.com/elementor/elementor/issues/27733))

= 3.22.2 - 2024-06-24 =

* Security Fix: Improved code security enforcement in Shape Divider
* Fix: Refresh the media library after inserting an AI edited image

= 3.22.1 - 2024-06-17 =

* Fix: Editor not loading due to compatibility issue between Elementor AI and third-party plugins

= 3.22.0 - 2024-06-16 =

* New: Introducing Element Caching experiment - Enhance site speed by caching elements for faster rendering
* New: Introducing the Performance Tab - Centralizing all stable performance features in one accessible location ([#21194](https://github.com/elementor/elementor/issues/21194))
* New: Generate post excerpts with AI for concise summaries
* New: Generate and edit images with AI from WordPress media library
* Tweak: Updated Google Fonts list with 170+ new fonts ([#25095](https://github.com/elementor/elementor/issues/25095))
* Tweak: Improved Elementor translation functionality with `just-in-time` translation loading (props [@swissspidy](https://github.com/swissspidy), [#27199](https://github.com/elementor/elementor/issues/27199))
* Tweak: Added View Page link to the Editor Top Bar ([#21925](https://github.com/elementor/elementor/issues/21925))
* Tweak: Replaced `waypoints.js` library with the native Intersection Observer API
* Tweak: Changed child containers' Content Width to Full Width as default
* Tweak: Reorganized User Preferences panel for improved user experience
* Tweak: Hide icon position & spacing controls if there is an icon but no text in Button widget
* Tweak: Extended Alignment and Position capabilities in Button widget
* Tweak: Implemented CSS logical properties to Icon Spacing control in Button widget
* Tweak: Implemented CSS logical properties to Icon Position control in Button widget
* Tweak: Promoted Grid Container feature to Stable status
* Tweak: Activated Optimized Control Loading by default for new sites
* Tweak: Changed Generative Fill AI image feature to use Clipdrop text-inpainting
* Tweak: Added functionality to delay the running of the ready triggers on inner elements
* Tweak: Deactivated Landing Page feature for new sites
* Fix: "Exit to" functionality is not working with the Editor Top Bar ([#22828](https://github.com/elementor/elementor/issues/22828))
* Fix: Row-reversed direction is not working on mobile portrait breakpoint in Container ([#23710](https://github.com/elementor/elementor/issues/23710))
* Fix: The What's' New string in the Editor is not translatable (props [@DAnn2012](https://github.com/DAnn2012))
* Fix: Fatal error appears when updating a page with Tabs widget
* Fix: Transparency indicator not visible in UI Light theme for Color Picker
* Fix: AI Expand Images not working on local and protected websites
* Fix: `all: unset` assigned to buttons cause focus issues
* Fix: Links to Elementor Settings are broken in various locations
* Fix: `RunReadyTrigger` returning incorrectly in various scenarios

= 3.21.8 - 2024-05-26 =

* Fix: Can't click on the AI consent modal checkbox

= 3.21.7 - 2024-05-22 =

* Fix: Unnecessary commas cause Cron Jobs to fail in various scenarios ([#25803](https://github.com/elementor/elementor/issues/25803))
* Fix: AI Consent modal requires multiple clicks to check the box

= 3.21.6 - 2024-05-20 =

* Security Fix: Improved code security enforcement in Icon Box and Image Box widgets
* Fix: Console error related to the AI tooltip
* Fix: Redirecting issue in AI Connect flow

= 3.21.5 - 2024-05-08 =

* Tweak: Improved AI Image variations to use an updated and simplified model
* Fix: Editor History actions are not working as expected when using keyboard shortcuts ([#27030](https://github.com/elementor/elementor/issues/27030), [#25861](https://github.com/elementor/elementor/issues/25861))

= 3.21.4 - 2024-04-30 =

* Fix: Improved code enforcement of post Preview mode ([#25860](https://github.com/elementor/elementor/issues/25622))
* Fix: Accordion is not displayed as expected when using Safari browser in Accordion widget ([#25905](https://github.com/elementor/elementor/issues/25905), [#25789](https://github.com/elementor/elementor/issues/25789))

= 3.21.3 - 2024-04-25 =

* Fix: Error notices appear in the editor and front end when using Icon Box widget ([#25837](https://github.com/elementor/elementor/issues/25837))

= 3.21.2 - 2024-04-24 =

* Fix: Improved code security enforcement in Heading widget
* Fix: Icon Box widget disappears on the frontend if its Title and Description fields are empty

= 3.21.1 - 2024-04-18 =

* Fix: Compatibility issue between Elementor Settings screen and 3rd party plugins
* Fix: AI-generated images are consistently square and do not follow the selected aspect ratio

= 3.21.0 - 2024-04-15 =

* New: Introducing Optimized Control Loading experiment - Improved TTFB by excluding UI controls from the frontend load, enhancing overall site speed
* New: Meet Elementor Home - Jumpstart your web creation and expand your design toolkit
* Tweak: Enhanced the mechanism of Lazy Load Background Images experiment for better performance
* Tweak: Added additional styling options to Counter widget ([#9068](https://github.com/elementor/elementor/issues/9068))
* Tweak: Add additional unit options to Width and Height in Shape Dividers
* Tweak: Optimized Button widget to eliminate unnecessary markup when no text is present
* Tweak: Optimized Icon widget to eliminate unnecessary markup when no icon is present
* Tweak: Optimized Spacer widget to eliminate unnecessary markup when no space is defined or set to 0
* Tweak: Optimized Progress Bar widget to eliminate unnecessary markup when both title and percentage are empty.
* Tweak: Optimized Testimonial widget to eliminate unnecessary markup when content controls are left empty
* Tweak: Optimized Shortcode widget to eliminate unnecessary markup when the shortcode field is left empty
* Tweak: Optimized Menu Anchor widget to eliminate unnecessary markup when the anchor ID is left empty
* Tweak: Optimized Text Editor widget to eliminate unnecessary markup when the editor content is empty
* Tweak: Optimized Icon Box widget to eliminate unnecessary markup when there is no content
* Tweak: Optimized Image Box widget to eliminate unnecessary markup when there is no content
* Tweak: Optimized Alert widget to eliminate unnecessary markup when the content is empty
* Tweak: Improved Asset Loading feature merged to version
* Tweak: Activated Grid Container feature for existing sites
* Tweak: Promoted Editor Top Bar to Beta status
* Tweak: Activated Lazy Load Background Images by default to new sites and changed status to Beta
* Tweak: Optimized Gutenberg Loading feature merged to version and moved to Settings
* Tweak: Optimize Image Loading feature merged to version and moved to Settings
* Fix: Edit with Elementor button is missing in various scenarios when using WordPress 6.5.2
* Fix: RTL websites using a LTR UI are flipping between 'left' and 'right' inside the editor
* Fix: Deprecated styling has been removed from the Editor's tabs
* Fix: Replaced deprecated `elementor.$previewElementorEl` with `documents.getCurrent().$element` (props [@vHeemstra](https://github.com/vHeemstra))

= 3.20.4 - 2024-04-10 =

* Fix: Image prompt enhancement not functioning as expected in AI Images

= 3.20.3 - 2024-03-26 =

* Fix: Edit with Elementor button is missing for pages that had been previously edited with Elementor when using WordPress 6.5 ([#25495](https://github.com/elementor/elementor/issues/25495), [#25496](https://github.com/elementor/elementor/issues/25496))
* Security Fix: Improved code security enforcement in Text Path Widget

= 3.20.2 - 2024-03-20 =

* Fix: Media file is not allowed to be uploaded within the editor ([#25077](https://github.com/elementor/elementor/issues/25077), [#25187](https://github.com/elementor/elementor/issues/25187))

= 3.20.1 - 2024-03-13 =

* Fix: Radial background gradient is not working as expected in responsive mode ([#25038](https://github.com/elementor/elementor/issues/25038))
* Fix: Tabs widget remains visible when disabled in the Element Manager

= 3.20.0 - 2024-03-11 =

* Tweak: Enanached TTFB metric by removing UI controls and some arguments from frontend loading for better site performance
* Tweak: Resolved excessive DOM output when using Grid Container and Flexbox Container widgets
* Tweak: Added X icon to Font Awesome icon library ([#23345](https://github.com/elementor/elementor/issues/23345))
* Tweak: Added Threads icon to Font Awesome icon library ([#23345](https://github.com/elementor/elementor/issues/23345))
* Tweak: Restricted HTML widget privileges to admin users only for enhanced security and control
* Tweak: Introduced the capability for admin users to grant HTML widget usage permissions to non-admin users in the Role Manager
* Tweak: Refined Editor's unit selection DOM output for better efficiency
* Tweak: Implemented accessibility improvements in Dynamic Tags control
* Tweak: Implemented accessibility improvements in widget panel sections
* Tweak: Implemented accessibility improvements in Site Settings items
* Tweak: Implemented accessibility improvements in the Elements panel keyboard accessible
* Tweak: Implemented accessibility improvements in Color Picker control
* Tweak: Implemented accessibility improvements in Site Settings header buttons
* Tweak: Implemented accessibility improvements for actions in the History panel
* Tweak: Implemented accessibility improvements for revisions in History panel
* Tweak: Added semantic `<time>` wrapper for items in History panel
* Tweak: Added semantic `<search>` wrapper to Widgets search
* Tweak: Promoted Build with AI feature to Stable status
* Tweak: Global Style Guide feature merged to version
* Tweak: Promoted Inline Font Icons feature to Stable status
* Tweak: Activated Grid Container feature by default for new sites
* Tweak: Removed separator-none argument from all Editor controls
* Tweak: Relocated Icon and Content Spacing controls to the Box section in Icon Box widget
* Tweak: Relocated Image and Content Spacing controls to the Box section in Image Box widget
* Tweak: Relocated Alignment control from Content tab to Style tab in Button widget
* Tweak: Replaced Position select control with toggle control in Tabs widget
* Tweak: Replaced Icon Position select control with toggle control in Button widget
* Tweak: Hide the Border Radius control of Drop Cap in Text Editor widget
* Tweak: Hide image controls when image is not selected in Image widget
* Tweak: Removed `Size` control for new usage in Button Widget
* Tweak: Replaced Dismiss Icon select control with toggle control in Alert widget
* Tweak: Replaced Additional Options select controls with toggle controls in Image Carousel widget
* Tweak: Added missing `title` attribute to the Editor preview iframe
* Tweak: Added a new reference to the Extensions page from Add New Plugin screen
* Tweak: Improved flexibility of the widget promotion area for better extendability and customization
* Tweak: Implemented general improvements to i18n for enhanced global support
* Tweak: Updated `eicons` library to v5.28.0
* Tweak: Standardized section names across various Elementor widgets for uniformity and clarity
* Fix: Compatibility issues with various 3rd party plugins when Optimize Image Loading feature is activated ([#24226](https://github.com/elementor/elementor/issues/24226))
* Fix: `install_compare()` in Experiments Manager throw an error in PHP 8.2+ if `get_installs_history()` returned an empty result

= 3.19.4 - 2024-02-28 =

* Fix: Widgets are not appearing in the widget panel when using various 3rd party plugins

= 3.19.3 - 2024-02-26 =

* Fix: Template is not importing when the Unfiltered File option is disabled ([#25071](https://github.com/elementor/elementor/issues/25071))

= 3.19.2 - 2024-02-07 =

* Fix: Integration script not functioning as expected in Build with AI feature

= 3.19.1 - 2024-02-05 =

* Tweak: Improved text-based AI capabilities within the widget type
* Security Fix: Addressed security weaknesses in template library mechanism
* Fix: Improved code security enforcement in Media control
* Fix: SVG Icons break the title layout when using Firefox and Safari browsers in Accordion widget ([#24925](https://github.com/elementor/elementor/issues/24925))
* Fix: Missing hover state to dropdown in AI Containers modal

= 3.19.0 - 2024-01-29 =

* New: Generate Container Variations with AI directly from Elementor's Blocks Template Library
* New: Introducing the 'What's New' screen - Insights on the latest features, enhancements, and updates
* Tweak: Added responsive controls to Gradient in Background and Background Overlay ([#10247](https://github.com/elementor/elementor/issues/10247), [#6783](https://github.com/elementor/elementor/issues/6783), [#10449](https://github.com/elementor/elementor/issues/10449), [#15377](https://github.com/elementor/elementor/issues/15377), [#17000](https://github.com/elementor/elementor/issues/17000))
* Tweak: Added caption space option in Basic Gallery and Image Carousel widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo), [#12533](https://github.com/elementor/elementor/issues/12533), [#21128](https://github.com/elementor/elementor/issues/21128))
* Tweak: Added responsive control to Size Resolution in Testimonial widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Restricted JSON file upload privileges to admin users only for enhanced security and control
* Tweak: Introduced the ability for admin users to grant JSON file upload permissions to non-admin users in Role Manager
* Tweak: Raised the minimum required PHP version from 7.3 to 7.4
* Tweak: Incorporated workflow hints within the media control to improve accessibility
* Tweak: Incorporated workflow hints within the gallery control to improve accessibility
* Tweak: Implemented CSS logical properties in Divider widget
* Tweak: Removed `Size` control for new usage in Heading Widget
* Tweak: Removed `Size` control for new usage in Button Widget
* Tweak: Removed `Progress Type` control for new usage in Progress Bar widget
* Tweak: Added responsive control to Vertical Align in Icon Box widget
* Tweak: Added responsive control to Image Position in Image Box widget
* Tweak: Added All Statuses filter in Element Manager
* Tweak: Added additional size units and custom units in all elements
* Tweak: Limit pagination for five attempts in Build with AI modal
* Tweak: Replaced select control with choose control for Image Position in Testimonial widget
* Tweak: Shifted alignment controls from the Content tab to the Style tab in Heading, Icon, and Image widgets
* Tweak: Promoted Optimized Gutenberg Loading feature to Stable status
* Tweak: Promoted Optimize Image Loading feature to Stable status
* Tweak: Promoted Build with AI feature to Beta status and activated for all users
* Tweak: Optimized DOM Output feature merged to version
* Tweak: Added new Notice control for Editor panels
* Tweak: Added new Alert control for Editor panels
* Fix: Deprecation notices in console log in History panel ([#19456](https://github.com/elementor/elementor/issues/19456), [#22159](https://github.com/elementor/elementor/issues/22159))
* Fix: Dark Mode affects the link options modal in Text Editor widget ([#23477](https://github.com/elementor/elementor/issues/23477))
* Fix: Enhanced code quality checks within the template export process
* Fix: Added better output escaping to Image Size control attributes
* Fix: Added better output escaping to URL controls
* Fix: Justify items not working when grid is set to full width in Grid Container
* Fix: Asymmetric icons are not displayed correctly in Accordion widget
* Fix: Share copy options were not functioning properly with VideoPress integration in Video widget

= 3.18.3 - 2023-12-20 =

* Fix: Modified control sanitization to enforce better security policies in Dynamic tags
* Fix: Elementor Editor is slow when using Safari 17 and Firefox on macOS
* Fix: Inner containers added to the top of the page when using AI Variations

= 3.18.2 - 2023-12-08 =

* Security Fix: Addressed security weaknesses in template upload mechanism

= 3.18.1 - 2023-12-06 =

* Fix: Improved code security enforcement in File Upload mechanism
* Fix: Error appears on front when using various 3rd party plugins and Themes
* Fix: Reverted Elementor Editor is slow when using Safari 17 and Firefox on macOS

= 3.18.0 - 2023-12-04 =

* New: Introducing Element Manager - Overview of all installed widgets on your site, allowing the deactivation of unused widgets for a personalized widget panel ([#9647](https://github.com/elementor/elementor/issues/9647))
* New: Introducing Elementor AI for Text-Based Container Generation – Effortlessly generate uniquely designed containers by simply providing a single textual prompt
* New: Introducing Elementor AI for Elementor-Based Container Variations – Customize your existing Elementor layouts by editing container content, images, fonts, colors, and icons
* Tweak: Added support for Elementor AI Web-Based Container generation – Create containers by providing a specified URL to generate Elementor-compatible layouts
* Tweak: Enhanced TTFB metric by removing UI controls and some arguments from frontend loading for better site performance
* Tweak: Implemented accessibility improvements in Progress Bar widget
* Tweak: Added responsive control and size units to navigation size in Image Carousel widget
* Tweak: Promoted Global Style Guide feature to a Stable status
* Tweak: Activated Optimize Image Loading experiment by default for all sites
* Tweak: Activated Optimized Gutenberg Loading experiment by default for all sites
* Tweak: Activated Inline Font Icons experiment by default for new sites
* Tweak: Renamed the 'Image Size' control into 'Image Resolution'
* Tweak: Updated `eicons` library to v5.25.0
* Tweak: Updated context menu UI
* Fix: Elementor Editor is slow when using Safari 17 and Firefox on macOS ([#24260](https://github.com/elementor/elementor/issues/24260))
* Fix: Locale with "comma" as decimal separator brakes generated CSS styles for decimal values ([#10992](https://github.com/elementor/elementor/issues/10992))
* Fix: Changed Vimeo Background video player to transparent background player ([#16336](https://github.com/elementor/elementor/issues/16336))
* Fix: Dynamic property creation is deprecated in container layout with PHP 8.2 ([#23830](https://github.com/elementor/elementor/issues/23830))
* Fix: Ensure that the tab elements open correctly with a click in Tabs widget

= 3.17.3 - 2023-11-08 =

* Fix: Modified control sanitization to enforce better security policies in Dynamic tags

= 3.17.2 - 2023-11-01 =

* Fix: Modified controls sanitization to enforce better security policies in Heading, Icon, Image Box, and Testimonial widgets

= 3.17.1 - 2023-10-25 =

* Fix: Error message appears when loading the Theme Builder

= 3.17.0 - 2023-10-25 =

* New: AI History - Quickly restore AI-generated content, enhancing your workflow by improving content recovery and acceleration
* New: Revamped Rating widget - enhanced flexibility for customizable visual rating scales, elevating your designs to be more engaging and interactive ([#18793](https://github.com/elementor/elementor/issues/18793), [#10529](https://github.com/elementor/elementor/issues/10529), [#18169](https://github.com/elementor/elementor/issues/18169), [#20651](https://github.com/elementor/elementor/issues/20651), [#10127](https://github.com/elementor/elementor/issues/10127))
* New: Introducing Gutenberg Asset Loading as a Beta feature - enhance performance by avoiding unnecessary block editor assets
* New: Introducing Optimize Image Loading as a Beta feature - Apply default optimization strategies during content rendering for improved LCP
* Tweak: Allowed using TinyMCE Full Screen Toolbar with Editor Top Bar ([#23463](https://github.com/elementor/elementor/issues/23463), [#22873](https://github.com/elementor/elementor/issues/22873))
* Tweak: Make the Container element extendable (props [@HadyShaltout](https://github.com/HadyShaltout))
* Tweak: Added a FAQ Schema support in Accordion widget
* Tweak: Added VideoPress integration for self-hosted videos in Video widget
* Tweak: Implemented accessibility improvements in Accordion widget
* Tweak: Upgraded minimum required PHP version to 7.3
* Fix: Responsive settings for Templates wouldn't save completely when Additional Custom Breakpoints feature is activated ([#19394](https://github.com/elementor/elementor/issues/19394), [#22829](https://github.com/elementor/elementor/issues/22829))
* Fix: Error messages appear on Theme Builder when using PHP 8.X ([#22991](https://github.com/elementor/elementor/issues/22991))
* Fix: Disabling options in URL Control does not hide the options icon in External URL field ([#11214](https://github.com/elementor/elementor/issues/11214))
* Fix: UI Glitch in widget Style and Advanced tabs ([#23402](https://github.com/elementor/elementor/issues/23402))
* Fix: PHP Memory Limit displayed in System Info wasn't accurate in various scenarios

= 3.16.6 - 2023-10-17 =

* Fix: Improved code security enforcement in Dynamic Tag Fallback control

= 3.16.5 - 2023-10-09 =

* Fix: Widget panel appears empty for Editor role with "Access to edit content only" permission ([#23696](https://github.com/elementor/elementor/issues/23696))
* Fix: Improved code security enforcement in SVG Icon control

= 3.16.4 - 2023-09-20 =

* Fix: HTML tags are not rendering on title field in Tabs widget ([#23752](https://github.com/elementor/elementor/issues/23752))
* Fix: Tabs is using the 'active' color for both 'active' and 'hover' states in the accordion layout in Tabs widget
* Fix: Gaps values aren't displayed inside the Editor in Container widget

= 3.16.3 - 2023-09-14 =

* Tweak: Changed Improved CSS loading feature to inactive for existing sites
* Fix: Dynamic tag for ACF image field is not working as expected ([#23757](https://github.com/elementor/elementor/issues/23757))
* Fix: Display issues when using long titles inside Tabs widget ([#23670](https://github.com/elementor/elementor/issues/23670))
* Fix: Gaps control does not present values as expected in Container widget

= 3.16.2 - 2023-09-13 =

* Fix: Various issues with deprecation updates and 3rd party plugins

= 3.16.1 - 2023-09-12 =

* Fix: Flexbox Container feature is activated by default for existing sites

= 3.16.0 - 2023-09-12 =

* New: Grid widget is now accessible in the widgets panel, enhancing workflow and user experience
* Tweak: Enhanced TTFB metric by optimizing controls display condition functions in the Editor (props [@DMajorChump](https://github.com/MajorChump), [#21762](https://github.com/elementor/elementor/issues/21762))
* Tweak: Implemented accessibility improvements in Nested Tabs ([#22935](https://github.com/elementor/elementor/issues/22935))
* Tweak: Promoted Flexbox Container feature to Stable status
* Tweak: Enabled Flexbox Container feature by default for new installations
* Tweak: Substituted 'Gap between elements' control in site settings with the new Container Gap control
* Tweak: Upgraded HTML Structure for Tabs Widget
* Tweak: Implemented CSS logical properties for border-radius in Elementor Editor
* Tweak: Implemented CSS logical properties in Container, Accordion and Tabs widgets
* Tweak: Enhanced dropdown preview for Global Typography styles
* Tweak: Enabled Improved Asset Loading feature by default for all sites
* Tweak: Promoted Grid Container feature to Beta status
* Tweak: Replace CSS `float` with other layouts in the Editor
* Tweak: Concealed the 'Empty View' cell within the Grid Container to prevent distortion of the preview
* Tweak: Updated `eicons` library to v5.23.0
* Tweak: Added keyboard accessibility to a link in Icon Box widget
* Tweak: Added keyboard accessibility to a link in Image Box widget
* Tweak: Added 'Download System Info" button to the top of Elementor System Info screen
* Fix: Justify Content and Align Items controls are not functioning as expected in responsive mode in Container widget ([#19363](https://github.com/elementor/elementor/issues/19363))
* Fix: Wrong structure of predefined Container layouts in RTL ([#20184](https://github.com/elementor/elementor/issues/20184))
* Fix: Editing a post or page created in Gutenberg with Elementor resulted in sections being displayed instead of containers ([#20282](https://github.com/elementor/elementor/issues/20282))
* Fix: Custom unit inheritance problem in mobile mode in Grid Container ([#22289](https://github.com/elementor/elementor/issues/22289))
* Fix: Spacer not functioning as intended in Container widget ([#20023](https://github.com/elementor/elementor/issues/20023))
* Fix: Translated blending mode options in `heading.php` for improved usability (props [@DAnn2012](https://github.com/DAnn2012))
* Fix: Use default placeholder set in URL control (props [@DAnn2012](https://github.com/DAnn2012))
* Fix: Improved translation functionality in the Editor (props [@DAnn2012](https://github.com/DAnn2012))
* Fix: Improved code security enforcement in Text Path widget
* Fix: "Add New Container" functionality is not functioning correctly in Finder
* Fix: Styling from the parent Accordion was inherited by an Accordion placed inside it in Accordion widget
* Fix: Title is not breaking in Recently Edited modal in Editor Top Bar
* Fix: Edit page with WordPress Editor issue when using WordPress 6.3

= 3.15.3 - 2023-08-20 =

* Fix: Elements can't be edited after accessing the page settings panel ([#23365](https://github.com/elementor/elementor/issues/23365))
* Fix: Reverted Responsive settings for Section Templates won't entirely save when Additional Breakpoints feature is active
* Fix: Redundant accordion icon appears when using Safari browser in Accordion widget
* Fix: Elementor Admin Top Bar affects styling on non-elementor screens in WordPress
* Fix: Changed CSS class for Apps page for better i18n support

= 3.15.2 - 2023-08-09 =

* Fix: Popular Apps notification is not presented as expected in RTL websites ([#23307](https://github.com/elementor/elementor/issues/23307))
* Fix: Typo in Popular Apps notification ([#23329](https://github.com/elementor/elementor/issues/23329))
* Fix: Redundant accordion icon appears when using Safari browser in Accordion widget

= 3.15.1 - 2023-08-02 =

* Fix: Error notices appear on front after schemes code deprecated

= 3.15.0 - 2023-07-31 =

* New: Introducing Accordion widget - Unleash your design creativity with nesting capabilities and layout flexibility ([#2587](https://github.com/elementor/elementor/issues/2587))
* New: Enhancements Elementor AI for Images - Seamlessly Remove and Replace Backgrounds
* New: Introducing Apps Page - Discover a selection of tools, specifically tailored to enhance your Elementor-powered website
* Tweak: Added a "Container" option to the 'Hover Area' dropdown in Lottie widget ([#20360](https://github.com/elementor/elementor/issues/20360))
* Tweak: Updated several URLs from `http:` to `https` for enhanced compliance (props [@DAnn2012](https://github.com/DAnn2012))
* Tweak: Added Image Size control to Gravatar image in Author Box widget
* Tweak: Remove deprecated schemes area
* Tweak: Accessibility Improvements feature merged to version
* Tweak: Deleted `aspect-ratio` from SASS files
* Tweak: Added keyboard accessibility to Repeater control
* Tweak: Unified the appearance of `stretch` and `center` buttons in Tabs widgets
* Tweak: Updated `eicons` library to v5.21.0
* Tweak: Group together position controls in Icon Box widget
* Tweak: Group together position controls in Image Box widget
* Tweak: Added additional units to lightbox controls
* Tweak: Implemented CSS logical properties in Elementor App
* Tweak: Implemented CSS logical properties in Elementor Editor
* Tweak: Enhanced `text-align` property by implementing CSS logical properties
* Tweak: Improved panel UI in Video widget
* Tweak: Replaced Display Percentage select control to toggle control in Progress Bar widget
* Tweak: Added "Title HTML Tag" to Title in Progress Bar widget
* Tweak: Updated recommended PHP version
* Tweak: Added `classes_dictionary` support for editor controls
* Tweak: Expanded AI resize option elevating the resolution limit to 2048px
* Tweak: Landing Pages feature downgraded to BETA status
* Tweak: Promoted "Global Style Preview" feature to BETA status
* Fix: Responsive settings for Section Templates won't entirely save when Additional Breakpoints feature is active ([#19394](https://github.com/elementor/elementor/issues/19394), [#19394](https://github.com/elementor/elementor/issues/19394))
* Fix: Video lightbox incorrect aspect ratio when multiple video widgets are utilized on the same page ([#21649](https://github.com/elementor/elementor/issues/21649))
* Fix: Disabling popup overlay does not disable it in the editor ([#22412](https://github.com/elementor/elementor/issues/22412))
* Fix: Corrected typo in `DONOTCACHCEOBJECT` to `DONOTCACHEOBJECT` for accurate `do_not_cache` definition ([#22786](https://github.com/elementor/elementor/issues/22786))
* Fix: Stretch option is not working as expected when items are set to the sides in Tabs widget ([#22774](https://github.com/elementor/elementor/issues/22774))
* Fix: Color picker is not working as expected on document settings ([#22867](https://github.com/elementor/elementor/issues/22867))
* Fix: Updated text domain of multiple strings to exclude 'elementor-pro' for better compatibility (props [@DAnn2012](https://github.com/DAnn2012))
* Fix: Video overlay image `aspect-ratio` fixes
* Fix: Rectified template categories filter to reference the correct ID accurately
* Fix: Lightbox overlay color was not visible when previewing the lightbox within the editor

= 3.14.1 - 2023-06-26 =

* Fix: Video background added on the parent tab container only works on the first tab in Tabs widget
* Fix: AI Image screen is not loading when there is no image in the control

= 3.14.0 - 2023-06-19 =

* New: Introducing Elementor AI for Image Creation – Unleash your creativity, improve and accelerate your workflow by instantly generating any type of image
* New: Add widgets to the Editor with a click - streamline and accelerate your workflow ([#20142](https://github.com/elementor/elementor/issues/20142), [#21965](https://github.com/elementor/elementor/issues/21965))
* New: Global Styles Preview - Showcase your global colors and fonts for better insight of your website's design system
* Tweak: Added a None option to the breakpoint options in Tabs widget ([#7742](https://github.com/elementor/elementor/issues/7742))
* Tweak: Updated "Manage Website" button functionality and name in Editor Top Bar ([#22359](https://github.com/elementor/elementor/issues/22359), [#22464](https://github.com/elementor/elementor/issues/22464))
* Tweak: Added a horizontal scrolling option in Tabs widget
* Tweak: Implemented "Add new page" option in recently edited documents dropdown in Editor Top Bar
* Tweak: Allow loading a document without reloading when using recently edited documents dropdown in Editor Top Bar
* Tweak: Changed document name simultaneously when changing it in the document settings panel and the Editor Top Bar
* Tweak: Moved the 'Container Type' dropdown to the top of the container widget in Grid Container experiment
* Tweak: Added Fit to Size control to SVG file in Icon widget
* Tweak: Added "Object Position" control to Image widget
* Tweak: Added "Order" control to page settings panel
* Tweak: Added "Comments" control to page settings panel
* Tweak: Updated the layout of Keyboard Shortcuts panel
* Tweak: Added "Page Settings" hotkey to the Keyboard Shortcuts panel
* Tweak: Added "User Preferences" hotkey to the Keyboard Shortcuts panel
* Tweak: Added "Notes" feature hotkey to the Keyboard Shortcuts panel
* Tweak: Use CSS Logical Properties in Elementor Admin SCSS files
* Tweak: Move the "Elementor Loading" HTML block from the preview area
* Tweak: Use `media_types` array in Media controls
* Tweak: Added keyboard accessibility to Navigator items
* Tweak: Added keyboard accessibility to Basic Gallery widget
* Tweak: Added keyboard accessibility to Image Carousel widget
* Tweak: Improved accessibility to Button widget
* Tweak: Added focus state to Editor buttons
* Tweak: Replaced select control with choose control for Vertical Alignment control in Icon Box widget
* Tweak: Replaced select control with choose control for Vertical Alignment control in Image Box widget
* Fix: Not-crawlable link error in Accordion widget ([#14371](https://github.com/elementor/elementor/issues/14371), [#20214](https://github.com/elementor/elementor/issues/20214), [#20477](https://github.com/elementor/elementor/issues/20477), [#8943](https://github.com/elementor/elementor/issues/8943), [#11611](https://github.com/elementor/elementor/issues/11611))
* Fix: Not-crawlable link error in Toggle widget ([#14371](https://github.com/elementor/elementor/issues/14371), [#20214](https://github.com/elementor/elementor/issues/20214), [#20477](https://github.com/elementor/elementor/issues/20477), [#8943](https://github.com/elementor/elementor/issues/8943), [#11611](https://github.com/elementor/elementor/issues/11611))
* Fix: "Convert to Containers" functionality appears on a page without Sections ([#19361](https://github.com/elementor/elementor/issues/19361))
* Fix: Responsive settings for templates don't work as expected when Additional Custom Breakpoints feature is active ([#16819](https://github.com/elementor/elementor/issues/16819), [#19394](https://github.com/elementor/elementor/issues/19394))
* Fix: ARIA `role` attributes in Accordion widget
* Fix: ARIA `role` attributes in Toggle widget
* Fix: Background video is not working as expected inside the editor on container level in Nested Elements
* Fix: Dropdown select control UI background color glitch on dark mode in the Editor
* Fix: Special characters are not displaying as expected when using recently edited documents dropdown in Editor Top Bar

= 3.13.4 - 2023-05-28 =

* Fix: Unable to save a new Global Widget ([#22544](https://github.com/elementor/elementor/issues/22544))

= 3.13.3 - 2023-05-22 =

* Security Fix: Addressed security weaknesses in template creation mechanism
* Tweak: Improved error messages when a kit is not downloaded successfully in Kit Library

= 3.13.2 - 2023-05-11 =

* Security Fix: Addressed security weaknesses in access management related functions

= 3.13.1 - 2023-05-09 =

* Tweak: Removed autocomplete functionality in Color Picker
* Fix: 'Editing Handle' location issue in various places in the Editor

= 3.13.0 - 2023-05-08 =

* New: Introducing Elementor AI Write - Boost your productivity and efficiency, while elevating your website's design and content.
* New: CSS Grid layout in Container - ideal for grid-like layouts and achieving infinite design possibilities ([#18839](https://github.com/elementor/elementor/issues/18839))
* Tweak: Added RGBA and HSLA types to color picker ([#18308](https://github.com/elementor/elementor/issues/18308))
* Tweak: Improved items behavior when the width of all the tabs titles exceeds the parent container width ([#21650](https://github.com/elementor/elementor/issues/21650))
* Tweak: Adjusted Favorites section functionality for better user experience ([#20939](https://github.com/elementor/elementor/issues/20939))
* Tweak: Added quick site navigation within the editor's new Top Bar
* Tweak: Added the Save Options functionality to new Editor Top Bar
* Tweak: Added "Site Settings" hotkey to keyboard shortcuts panel
* Tweak: Removed Pojo integration from Elementor
* Tweak: General improvements in Elementor's new color scheme
* Tweak: Updated `Pickr` library to v1.8.0
* Tweak: Updated `eicons` library to v5.20.0
* Tweak: Added Lazy Load support to various Elementor Editor and Admin images
* Tweak: Added Lazy Load support for embedded video thumbnail image
* Tweak: Improved accessibility in Lightbox functionality
* Tweak: Added keyboard accessibility to Elementor Editor tabs
* Tweak: Added keyboard accessibility to Elementor Editor icons
* Tweak: Added keyboard accessibility to Elementor widget categories
* Tweak: Added keyboard accessibility to header and footer in widgets panel
* Tweak: Added keyboard accessibility to "Edit with Elementor" button
* Tweak: Added keyboard accessibility to navigation arrows in Image Carousel widget
* Tweak: Added keyboard accessibility to URL control "Link Options" button
* Tweak: Added keyboard accessibility to editor "mode switcher"
* Tweak: Added keyboard accessibility to close and toggle buttons in Navigator
* Tweak: HTML structure changed in Elementor Editor components for better readability
* Fix: Can't paste widgets into the 'Drag widget here' area when using Container ([#21652](https://github.com/elementor/elementor/issues/21652), [#22142](https://github.com/elementor/elementor/issues/22142))
* Fix: Removed redundant scroll in Finder
* Fix: Carousel widgets are not working correctly inside Nested Tabs widget
* Fix: Lightbox image captions are not aligned to the center
* Fix: Border radius not affecting the Video and Slideshow background types in Container
* Fix: Update translation strings escaping for safe use in attributes output
* Fix: Added `aspect-ratio` fallback in Video widget for old browsers
* Fix: Disabled 'Dominant Color' on PNG and GIF images in Lazy Load Background Images experiment
* Fix: Wrong ARIA text value in Progress Bar widget

= 3.12.1 - 2023-04-02 =

* Fix: Elementor's dark mode color scheme affects the front in various scenarios ([#21809](https://github.com/elementor/elementor/issues/21809), [#21832](https://github.com/elementor/elementor/issues/21832))
* Fix: Reverted inline editing fix that caused DOM change in Button widget

= 3.12.0 - 2023-03-29 =

* New: A new color scheme for Elementor - the Editor and other Elementor screens have been recolored, simplified, and optimized for accessibility
* New: Introducing a new Editor Top Bar as an Alpha experiment - consolidates all the common actions you need to perform on your website in a central and accessible location
* Tweak: Added a vertical alignment control to icons in Icon List widget ([#16464](https://github.com/elementor/elementor/issues/16464), [#16056](https://github.com/elementor/elementor/issues/16056), [#19237](https://github.com/elementor/elementor/issues/19237), [#16237](https://github.com/elementor/elementor/issues/16237), [#18420](https://github.com/elementor/elementor/issues/18420))
* Tweak: Adjusted the increments of `em` and `rem` when using the number scrubbing functionality ([#19399](https://github.com/elementor/elementor/issues/19399))
* Tweak: Added `vw` unit to margin and padding in Column, Section, and Container elements ([#20890](https://github.com/elementor/elementor/issues/20890), props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added additional size units for icon in Social Icons widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Separated title and description control labels in Icon Box and Image Box widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added units to `container_width` and `widgets_gap` in site settings (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added units for Word Spacing control in Text Path widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Extracted Stretch Section handler to a more generic Stretch Element
* Tweak: Refactored show and hide tab content callbacks to be more generic in Tabs widget
* Tweak: Updated minimum required WordPress version to 5.9
* Tweak: Separated overview dashboard function to multiple functions in WordPress dashboard
* Tweak: Added thumbnail to most recently installed kit in Elementor Import/export screen
* Tweak: Added Transition Duration control to text hover color in Icon List widget
* Tweak: Display normal & hover icon colors in tabs view in Icon List widget
* Tweak: Added additional size units and custom units in all elements
* Tweak: Added remove current kit alert before applying a new kit in import process
* Tweak: Removed redundant default units in various elements
* Tweak: Upgrade Swiper Library feature promoted to Stable status
* Tweak: Save as Default feature merged to version
* Fix: Padding is set to text span instead of icon span in Icon List widget ([#9831](https://github.com/elementor/elementor/issues/9831), props [@cirkut](https://github.com/cirkut))
* Fix: Save as Default functionality breaks Image Carousel and Loop Carousel widgets in various scenarios ([#21371](https://github.com/elementor/elementor/issues/21371))
* Fix: Content styling controls are not targeting the right container in Tabs widget
* Fix: Containers are still editable in Editor preview mode
* Fix: "Choose Image" control is missing in Safari 14 in various image elements

= 3.11.5 - 2023-03-14 =

* Tweak: Improved SVG file upload sanitization for better security enforcement
* Tweak: Improved code security enforcement in Text Path widget

= 3.11.4 - 2023-03-12 =

* Fix: Sticky and Motion Effects not working after latest Chrome update ([#21612](https://github.com/elementor/elementor/issues/21612))
* Fix: Extra spacing appears on the bottom in Video widget

= 3.11.3 - 2023-03-07 =

* Fix: Lightbox is not presented as expected when Upgrade Swiper Library experiment is activated ([#21413](https://github.com/elementor/elementor/issues/21413))
* Fix: "Choose Image" control is missing in Safari 14 in various image elements

= 3.11.2 - 2023-02-22 =

* Fix: Passing an HTMLElement to the Swiper instance utility doesn't work

= 3.11.1 - 2023-02-15 =

* Fix: Featured Image dynamic tag is not working in Background images ([#21313](https://github.com/elementor/elementor/issues/21313))

= 3.11.0 - 2023-02-13 =

* New: Introducing Copy and Paste Between Websites functionality ([#9424](https://github.com/elementor/elementor/issues/9424), [#19183](https://github.com/elementor/elementor/issues/19183))
* New: Responsive background Image Size - Adjust image size per device to improve performance ([#6778](https://github.com/elementor/elementor/issues/6778), [#3722](https://github.com/elementor/elementor/issues/3722))
* New: Updated Swiper Library to 8.4.5 as a Beta experiment ([#18724](https://github.com/elementor/elementor/issues/18724))
* Tweak: Updated Google Fonts list with 125 new fonts ([#20229](https://github.com/elementor/elementor/issues/20229))
* Tweak: Added `accent-color` support to Form fields in Site Settings
* Tweak: Use `aspect-ratio` property instead of CSS trick in Video widget
* Tweak: Updated `eicons` library to v5.18.0
* Tweak: Added `generator` meta tag to identify active performance settings and experiments
* Tweak: Improved logo visibility in Site Logo widget
* Tweak: Updated error messages when replacing URLs in Elementor Tools screen
* Tweak: Updated error messages on import/export functionality
* Tweak: Added a responsive control to custom image spacing in Image Carousel widget
* Tweak: Renamed "Experiments" settings tab to "Features" for better clarity
* Tweak: Merged "Hide WP widgets from search" experiment to the version
* Tweak: Promoted "Nested Elements" experiment to BETA status
* Tweak: Promoted "Flexbox Container" experiment to RC status
* Tweak: Promoted "Save as Default" experiment to Stable status
* Fix: Rename `e-action-hash` attribute to `data-e-action-hash` attribute ([#20513](https://github.com/elementor/elementor/issues/20513), [#16418](https://github.com/elementor/elementor/issues/16418), props [@huubl](https://github.com/huubl))
* Fix: Justify Content icons are inverted in RTL when direction is set to column in Container ([#20083](https://github.com/elementor/elementor/issues/20083))
* Fix: Various issues when using the convert-to-container functionality
* Fix: Tabs widget changes width size when direction set to row or column in Container
* Fix: Adjusted right-click menu dark mode for blocked options
* Fix: Can't rollback Elementor version to older Core version

= 3.10.2 - 2023-01-29 =

* Fix: PHP 8.x throws errors and notices in some cases ([#21087](https://github.com/elementor/elementor/issues/21087))
* Fix: Keyboard actions are not blocked on main tab container in Tabs widget

= 3.10.1 - 2023-01-17 =

* Fix: Child containers inheriting styles from parent container ([#20669](https://github.com/elementor/elementor/issues/20669))
* Fix: Lazyload not working after load more action in loop builder items
* Fix: Tab toggle is not working as expected after dragging a widget to any tab in Tabs widget
* Fix: Elementor Top bar is not fully responsive when WP sidebar is collapsed

= 3.10.0 - 2023-01-09 =

* New: Introducing the renewed Tabs widget - Enable more design options, and nesting capabilities ([#2587](https://github.com/elementor/elementor/issues/2587))
* New: Introducing Custom Units - A new way to choose any unit you want, including CSS Math Functions ([#2219](https://github.com/elementor/elementor/issues/2219), [#19935](https://github.com/elementor/elementor/issues/19935), [#18738](https://github.com/elementor/elementor/issues/18738), [#8307](https://github.com/elementor/elementor/issues/8307), [#11335](https://github.com/elementor/elementor/issues/11335))
* New: Container-based website assets - Kickstart your website creation and design process with container-based full website kits, templates and blocks
* New: Added an option to disable Google Fonts integration from font-family control
* Tweak: Added `preload="metadata"` to self hosted videos in Video widget ([#17308](https://github.com/elementor/elementor/issues/17308))
* Tweak: Added `preload` selector to self hosted videos in Video widget ([#17308](https://github.com/elementor/elementor/issues/17308))
* Tweak: Added `loading="lazy"` for custom size in media control ([#17884](https://github.com/elementor/elementor/issues/17884))
* Tweak: Added additional units for width and height in Icon List widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added additional units for icon size in Icon List Widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added `loading="lazy"` to Google Map widget
* Tweak: Updated `eicons` library to 5.17.0
* Tweak: Merged various experiments to the version
* Tweak: Promoted various experiments to Beta and Stable status
* Tweak: Added Undo option into success toast in "Save as Default" experiment
* Tweak: Media control return image URL from WordPress in Style CSS
* Tweak: General infrastructure changes to Import Export Website Kit experiment
* Fix: RTL text is printed backward in Text Path widget ([#17309](https://github.com/elementor/elementor/issues/17309))
* Fix: Unnecessary `non-existing control` errors are thrown on page load ([#20027](https://github.com/elementor/elementor/issues/20027))
* Fix: Post type is overridden on autosave for library documents ([#1994](https://github.com/elementor/elementor/issues/1994))
* Fix: Compatibility issue with Rank Math plugin due to `wp_print_media_template()` in onboarding module ([#18368](https://github.com/elementor/elementor/issues/18368))
* Fix: Auto detection of dark mode not working in the Theme Builder ([#19670](https://github.com/elementor/elementor/issues/19670))
* Fix: Missing escaping translation in Experiments screen
* Fix: Motion effect on background image disables background overlay in Container

= 3.9.2 - 2022-12-21 =

* Fix: Images not loading in Template widget inside the Editor when using Lazy Load Background Images experiment ([#20635](https://github.com/elementor/elementor/issues/20635))
* Fix: Wrong share URL for XING network ([#13112](https://github.com/elementor/elementor/issues/13112))
* Fix: Browser ignores space in mail share URL ([#10803](https://github.com/elementor/elementor/issues/10803))
* Fix: Responsive values are not saving as part of Save as Default functionality

= 3.9.1 - 2022-12-14 =

* Fix: Copy-Paste Style prevents saving the page when Repeater controls exist in "non-content" tabs ([#19895](https://github.com/elementor/elementor/issues/19895), [#20637](https://github.com/elementor/elementor/issues/20637))
* Fix: Font looks blurry in the Admin Top Bar (props [@CodeExplore](https://github.com/CodeExplore))
* Fix: The not active tab in the media modal is missing
* Fix: Background Overlay is not uploading in Container when using the Lazy Load experiment

= 3.9.0 - 2022-12-06 =

* New: Introducing Save as Default as a Beta experiment - Create your default settings for every element for better consistency
* New: Introducing Background images Lazy Load as an Alpha experiment
* Tweak: Separate "Default" and "None" values in Border Type control ([#11565](https://github.com/elementor/elementor/issues/11565), [#13328](https://github.com/elementor/elementor/issues/13328), [#11723](https://github.com/elementor/elementor/issues/11723))
* Tweak: Added `dnt` param to Vimeo embed background and Video widget ([#13797](https://github.com/elementor/elementor/issues/13797), [#13631](https://github.com/elementor/elementor/issues/13631))
* Tweak: Added Transform section to Advanced tab in Flexbox Container ([#18648](https://github.com/elementor/elementor/issues/18648), [#18268](https://github.com/elementor/elementor/issues/18268))
* Tweak: Added responsive control for Border Radius in Button widget ([#18914](https://github.com/elementor/elementor/issues/18914))
* Tweak: Added responsive control to opacity of background overlay image in Sections and Containers ([#19659](https://github.com/elementor/elementor/issues/19659))
* Tweak: Removed extra SQL queries on every page for better performance ([#12162](https://github.com/elementor/elementor/issues/12162))
* Tweak: Added background color option to the video background control ([#4353](https://github.com/elementor/elementor/issues/4353))
* Tweak: Added responsive control for caption to Image, Image Gallery and Image Carousel widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added responsive control to icon border radius in Icon and Social Icons widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added `em` unit to Word Spacing control in Text Path widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added responsive control to image border radius in Image Carousel, Basic Gallery and Testimonial widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Make Elementor compatible with WebP uploads via Performance Lab plugin
* Tweak: Added `em` units to border radius control in various elements
* Tweak: Upgraded the `autoprefixer` package to better minify CSS files
* Tweak: Added more units options to Border Width control in various elements
* Tweak: Adjusted Dark Mode in Navigator when using Container
* Tweak: Added `rel=preconnect` tag to Google Fonts to improve performance
* Tweak: Added escape translation strings for safe use in HTML output
* Tweak: Removed redundant labels from group controls
* Tweak: Added new margin controls to Post and Page Settings
* Tweak: Allow saving and reloading a page while in-place editing of documents
* Tweak: Added compatibility for Yoast Duplicate Post plugin
* Tweak: Added new hook action when element handler ready
* Fix: Predefined container structure with default padding is not working ([#19990](https://github.com/elementor/elementor/issues/19990))
* Fix: Repeater controls in `non-content` tabs cause JS errors when running Copy-Paste Style ([#19895](https://github.com/elementor/elementor/issues/19895))
* Fix: Various widgets disappear in certain scenarios when choosing direction Row or Column in Container
* Fix: Align items controls are not working as expected in container when using Spacer widget with custom width
* Fix: Pasting an element into the Preview Container throws a JS error in console
* Fix: Can't drag widgets into a Column after it's being emptied
* Fix: First container handle is not centered on an RTL language

= 3.8.1 - 2022-11-13 =

* Fix: PHP error notice appears while saving in Site Settings when using PHP8.0+ ([#20062](https://github.com/elementor/elementor/issues/20062), props [@gerasimovdaniel](https://github.com/gerasimovdaniel))

= 3.8.0 - 2022-10-30 =
* New: Revert your Website to its Previous Condition - Allow removing the last imported Kit
* New: The container experiment is now officially set as beta
* Tweak: Increase inputs in Replace URL tool to support long URLs ([#19559](https://github.com/elementor/elementor/issues/19559))
* Tweak: Added Dynamic Tags for Global Colors ([#15135](https://github.com/elementor/elementor/issues/15135))
* Tweak: Improved performance of Inline Fonts Icons experiment ([#19447](https://github.com/elementor/elementor/issues/19447))
* Tweak: Improved browser responsiveness during Elementor's initialization process ([#15228](https://github.com/elementor/elementor/issues/15228), props [@FlyingDR](https://github.com/FlyingDR))
* Tweak: Added Responsive control to Text Align in Columns and Sections ([#13199](https://github.com/elementor/elementor/issues/13199), props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added size units to Letter Spacing in Typography modal ([#19726](https://github.com/elementor/elementor/issues/19726))
* Tweak: Added more size units to Size and Spacing controls Icon and Icon box widgets ([#19496](https://github.com/elementor/elementor/issues/19496))
* Tweak: Added labels to font weight numeric values ([#18761](https://github.com/elementor/elementor/issues/18761))
* Tweak: Added `rem` unit to gap between elements control in Container widget ([#18261](https://github.com/elementor/elementor/issues/18261))
* Tweak: Added clarification for the `a` tag to the container element (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added text stroke to number in Counter widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added text stroke to title in Counter widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added text stroke to text in Divider widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added `em` unit for border radius to layout elements (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added text stroke to title in Image Box widget (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added more size units to Spacing and Image Size in Testimonial and Image Box widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Replaced `e-container` class name to `e-con` in Container
* Tweak: Replaced `e-container--width-boxed` class name to `e-con-boxed` in Container
* Tweak: Replaced `e-container--width-full` class name to `e-con-full` in Container
* Tweak: Replaced `e-container__inner` class name to `e-con-inner` in Container
* Tweak: Custom links in menu items should be relative to the site address when importing a Kit
* Tweak: Import/Export CLI and UI mechanisms were merged into a unified service
* Tweak: Improved the UX of dependencies between experiments
* Tweak: Changed Full-width and Boxed content width functionality in Container
* Tweak: Changed default Google fonts loading method to "Swap" on new sites
* Tweak: Changed default direction for Featured sorting to ASC in Kit Library
* Tweak: Added migrate script to handle retro PHP8 type error on image custom size
* Tweak: Re-organized SCSS files for the Container
* Tweak: Changed default Content Width when adding a new Container from right click context-menu
* Fix: `isolation: isolate` property causing z-index issues in various scenarios when using Container ([#19834](https://github.com/elementor/elementor/issues/19834), [#19845](https://github.com/elementor/elementor/issues/19845), [#19705](https://github.com/elementor/elementor/issues/19705), [#20011](https://github.com/elementor/elementor/issues/20011))
* Fix: Responsive controls with responsive conditions whose values are arrays (Base Multiple) did not apply in the frontend for non-desktop devices ([#19924](https://github.com/elementor/elementor/issues/19924), [#19917](https://github.com/elementor/elementor/issues/19917), [#19922](https://github.com/elementor/elementor/issues/19922), [#19894](https://github.com/elementor/elementor/issues/19894), [#19930](https://github.com/elementor/elementor/issues/19930), [#20001](https://github.com/elementor/elementor/issues/20001))
* Fix: Importer WordPress root write permissions check causes import failures ([#17255](https://github.com/elementor/elementor/issues/17255))
* Fix: Removed unnecessary default `max-width` and `flex-grow` settings in various widgets when using Container ([#19891](https://github.com/elementor/elementor/issues/19891))
* Fix: Transitions functionality is not working as expected in Container ([#19913](https://github.com/elementor/elementor/issues/19913))
* Fix: Progress Bar layout is not presented as expected when direction is set to row in Container
* Fix: Insert library button is clickable while inserting a template
* Fix: Go pro link UI glitch in export kit tool
* Fix: Export kit doesn't work in a Multisite Network
* Fix: WooCommerce products and categories are not imported as expected when assigned to a menu
* Fix: CSS minified files not generated on build
* Fix: Editor controls color issues in dark mode scheme

= 3.7.8 - 2022-10-02 =
* Fix: Using responsive control values in selectors and selector values causes a fatal error ([#19894](https://github.com/elementor/elementor/issues/19894))
* Fix: Error message appears after connecting account and refreshing in a promotion screen
* Fix: UI glitch when searching for non-existing terms in Template Library

= 3.7.7 - 2022-09-20 =
* Tweak: Removed the option to create a custom logo in Site Settings ([#19823](https://github.com/elementor/elementor/issues/19823))
* Fix: Custom X Position of background image doesn't work for non-desktop devices ([#19487](https://github.com/elementor/elementor/issues/19487), [#19662](https://github.com/elementor/elementor/issues/19662), [#19669](https://github.com/elementor/elementor/issues/19669), [#19527](https://github.com/elementor/elementor/issues/19527))
* Fix: Connect & activate to Elementor account issue with various WordPress site languages

= 3.7.6 - 2022-09-15 =
* Fix: Replaced link for better clarity in Site Settings

= 3.7.5 - 2022-09-14 =
* Tweak: Added an option to create a custom logo with AI Logo Maker by Fiverr in Site Settings
* Fix: Background Image Custom Position and Size controls are not visible for Mobile and Tablet devices ([#19487](https://github.com/elementor/elementor/issues/19487), [#19669](https://github.com/elementor/elementor/issues/19669), [#19662](https://github.com/elementor/elementor/issues/19662))
* Fix: Custom Width on Tablet and Mobile devices generates wrong values when Desktop is set to default ([#19487](https://github.com/elementor/elementor/issues/19487), [#19669](https://github.com/elementor/elementor/issues/19669), [#19662](https://github.com/elementor/elementor/issues/19662), [#19528](https://github.com/elementor/elementor/issues/19528), [#19542](https://github.com/elementor/elementor/issues/19542))
* Fix: Empty state placeholder is not displayed in various widgets ([#19446](https://github.com/elementor/elementor/issues/19446))
* Fix: When pasting a widget on a page the widget is being pasted into a Section when the Container experiment is active ([#19452](https://github.com/elementor/elementor/issues/19452))
* Fix: Custom Image Size generates a fatal error after updating to PHP 8+
* Fix: `is_current_user_can_edit` not working correctly when `$post_id` missing

= 3.7.4 - 2022-08-31 =
* Tweak: Removed redundant code in various widgets that includes images ([#12268](https://github.com/elementor/elementor/issues/12268), props [@ibndawood](https://github.com/ibndawood))
* Fix: Error message appears on front if WooCommerce is activated ([#19553](https://github.com/elementor/elementor/issues/19553))
* Fix: Web CLI requires jQuery to avoid errors when jQuery is loaded in the footer (props [@nicomollet](https://github.com/nicomollet))

= 3.7.3 - 2022-08-29 =
* Fix: Errors in deprecation module prevent Elementor editor to load ([#19390](https://github.com/elementor/elementor/issues/19390), [#19562](https://github.com/elementor/elementor/issues/19562))
* Fix: `add_link_attributes` function does not overwrite all in foreach loop item link attributes ([#11498](https://github.com/elementor/elementor/issues/11498), props [@sol1](https://github.com/afoster))
* Tweak: Removed the clickable logo link in Theme Builder

= 3.7.2 - 2022-08-21 =
* Fix: Motion Effects applied to a Column in any global Theme Builder template prevent Elementor editor to load ([#19390](https://github.com/elementor/elementor/issues/19390))
* Fix: Experiments - Learn more button does not open the Help center in the notice ([#19448](https://github.com/elementor/elementor/issues/19448))
* Fix: Widget width is not working as expected on frontend in Container element ([#19398](https://github.com/elementor/elementor/issues/19398))
* Fix: Horizontal scrolling appears in the edit area when viewing it with different devices ([#19049](https://github.com/elementor/elementor/issues/19049))
* Fix: Default Flex Grow affects the layout when the Container element is set to direction Column in Divider widget ([#19325](https://github.com/elementor/elementor/issues/19325))
* Fix: Background Fallback image is hiding the background video in Container element ([#19413](https://github.com/elementor/elementor/issues/19413))
* Fix: Previously edited with Elementor posts were imported with irrelevant content in Export/Import tool
* Fix: Width and Elements gap values are not working as expected in various responsive devices in Container widget

= 3.7.1 - 2022-08-14 =
* Fix: Controls do not implement a value of 0 ([#19410](https://github.com/elementor/elementor/issues/19410), [#19391](https://github.com/elementor/elementor/issues/19391), [#19393](https://github.com/elementor/elementor/issues/19393), [#19386](https://github.com/elementor/elementor/issues/19386))
* Fix: Dynamic fields are missing in any number input field ([#19419](https://github.com/elementor/elementor/issues/19419))
* Fix: Close window button isn't working in Theme Builder
* Fix: Breakpoints manager shouldn't run deprecated hook

= 3.7.0 - 2022-08-08 =
* New: Use the user language in the Elementor Editor regardless of the Site's language ([#5148](https://github.com/elementor/elementor/issues/5148))
* New: "Exit To…" location setting when exiting the Editor
* Tweak: Added filters to allow modifying upload temp paths in Uploads Manager ([#18565](https://github.com/elementor/elementor/issues/18565), props [@patrick-leb](https://github.com/patrick-leb))
* Tweak: Added `EM` unit for border-radius controls in some widgets (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added `EM` unit to Border radius controls in general widget settings and global styles (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Added thousand separators to Counter widget (props [@rb-ar](https://github.com/rb-ar))
* Tweak: Allow manual insertion of negative values to numeric inputs for better UX
* Tweak: Added the option for number scrubbing in numeric controls for better UX
* Tweak: Custom size control rejects non-numeric characters in Image widget
* Tweak: Added custom icons selection to Alert widget
* Tweak: Added custom icons selection to Video widget
* Tweak: Added custom icons selection to Image Carousel widget
* Tweak: Rearrange the Container panel for better controls discoverability and usability
* Tweak: Updated HTML wrapper `a` tag note in Container element
* Tweak: Added "Add New Container" right-click option to Container element
* Tweak: Added new Layout section to elements panel
* Tweak: Updated content and labels on Container element for better usability
* Tweak: Implemented Nested Elements capability infrastructure
* Tweak: Added the Revisions link to Import/Export intro screen
* Tweak: Open the admin dashboard "Go Pro" link in a new tab
* Tweak: Open the Theme Builder "Go Pro" link in new tab
* Tweak: Added `$info-wc-primary-text` CSS variable for better compatibility
* Tweak: Merged similar translation strings for better i18n
* Tweak: Increase server memory in order to prevent stuck spinner on Editor load
* Tweak: Added `EM` unit to Elements Gap in Container widget
* Tweak: Added default padding values to Container Element
* Tweak: Added site-wide containers padding option to Site Settings
* Tweak: Kit Types filtering was removed from Kits Library sidebar
* Tweak: Modified some Experiments descriptive texts
* Tweak: Promoted "Improved CSS Loading" experiment to RC status
* Tweak: Promoted "Improved Asset Loading" experiment to Stable status
* Tweak: Promoted "Additional Custom Breakpoints" experiment to Stable status
* Tweak: Added promotion for performance experiments
* Tweak: Added an outline instead of underline to Tabs widget for better accessibility
* Tweak: PHP 5.6 is deprecated
* Tweak: Added a hook to get manifest data in the import CLI command
* Fix: Responsive conditional controls depends on Desktop value only ([#16290](https://github.com/elementor/elementor/issues/16290), [#18054](https://github.com/elementor/elementor/issues/18054), [#11618](https://github.com/elementor/elementor/issues/11618))
* Fix: Gradient control doesn't work on frontend when using Global colors ([#13288](https://github.com/elementor/elementor/issues/13288))
* Fix: Direction control stays unselected when choosing Column based structure from the pre-designed container structures ([#18390](https://github.com/elementor/elementor/issues/18390))
* Fix: Columns control stays unselected when choosing a column structure from the pre-designed container structures ([#18390](https://github.com/elementor/elementor/issues/18390))
* Fix: Overlay background is not visible when using a background video or Slideshow in Container ([#18433](https://github.com/elementor/elementor/issues/18433), [#18391](https://github.com/elementor/elementor/issues/18391))
* Fix: Horizontal scroll appears when using direction Column in Container element ([#18662](https://github.com/elementor/elementor/issues/18662))
* Fix: Advanced padding doesn't work as expected in Container element ([#18314](https://github.com/elementor/elementor/issues/18314), [#18414](https://github.com/elementor/elementor/issues/18414))
* Fix: Responsive state doesn't work correctly in Container ([#18551](https://github.com/elementor/elementor/issues/18551))
* Fix: Widgets disappear when the direction is set to a column in Container ([#18880](https://github.com/elementor/elementor/issues/18880))
* Fix: Only the first 100 sites are processed by default usage of `get_sites()` (props [@vdwijngaert](https://github.com/vdwijngaert), [#18639](https://github.com/elementor/elementor/issues/18639))
* Fix: Typo in the onboarding flow ([#19104](https://github.com/elementor/elementor/issues/19104))
* Fix: Missing escaping translation to Onboarding module (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Fix: Missing escaping for translation strings in some group controls (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Fix: Editor freezes when changing control value in edge cases in Container element
* Fix: Element placed at end instead of original position when dragged into a Container
* Fix: `max-width` is causing grow and shrink properties issues in Container element
* Fix: Structure presets are in the wrong direction on RTL websites in Container element
* Fix: Spacer widget is not working as expected when placed inside a Container element
* Fix: Divider widget is not working as expected when placed inside a Container element
* Fix: User can drag a parent container into its child container in Container element
* Fix: Widgets overlapping on mobile devices when using Container element
* Fix: Widget width is not working as expected In Container
* Fix: Container outputs redundant CSS lines
* Fix: Notice Bar can't be closed in the editor
* Fix: Overlay of populated image control appears in dark mode when the Editor is set to light mode
* Fix: Pasting a term in the widget search bar doesn't show the results
* Fix: Custom icons disappear on frontend if the pack name contains numbers
* Fix: Custom fonts disappear if the name contains numbers only
* Fix: Select2 controls gets a value of `null` when cleared in single value mode
* Fix: PHP Error is being thrown when fetching System Info for Experiments that don't have a title
* Fix: PHP warnings are thrown in System info when using PHP 8.1
* Fix: Word spacing in Global Font Typography affects all texts on the site
* Fix: Gradient background doesn't work in the Editor using global colors in Site Settings
* Fix: Thumbnail files are not deleted when deleting the main image/attachment
* Fix: Go pro link is too wide in Export kit tool
* Fix: Import Kit wizard doesn't close the app when triggered from the Kit Library
* Fix: Shortcode doesn't work in popups or templates
* Fix: Style is broken after changing site URL when using "Improved CSS Loading" experiment
* Fix: Missing translations in responsive controls
* Fix: Added missing documentation for deprecated `Control_Icon` class
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-7-planned-deprecations/)

= 3.6.8 - 2022-07-27 =
* Fix: Align-self set to stretch is not working as expected in Container ([#17052](https://github.com/elementor/elementor/issues/17052))
* Fix: Bad request error is being thrown when trying to import a template
* Fix: License status is not being updated immediately after license renewal

= 3.6.7 - 2022-07-03 =
* Tweak: Optimized file handling for better security policies
* Fix: Modified controls sanitization to enforce better security policies in Text Editor widget
* Fix: Modified controls sanitization to enforce better security policies in Anchor widget

= 3.6.6 - 2022-06-08 =
* Tweak: Added "Skip & Deactivate" button in plugin deactivation survey
* Tweak: Removed data sharing checkbox in onboarding flow
* Tweak: Added a promotion to Notes feature in the Editor panel
* Fix: Critical error appeared in external apps when no page is selected as homepage in WordPress Reading Settings
* Fix: Font Awesome 5 migration process is not optimal in Elementor tools screen

= 3.6.5 - 2022-04-27 =
* Fix: PHP Error is thrown in System Info report for experiments that don't have a title
* Fix: Optimized template file uploads for better security enforcement

= 3.6.4 - 2022-04-13 =
* Fix: Optimized controls sanitization to enforce better security policies in Onboarding wizard

= 3.6.3 - 2022-04-12 =
* Tweak: Verify if SVG file exists before updating `_elementor_inline_svg` (props [@filipecsweb](https://github.com/filipecsweb), [#18155](https://github.com/elementor/elementor/issues/18155))
* Tweak: Added "Uploading" screen to the Pro upload process in Onboarding wizard
* Tweak: Added a notice for when Elementor Pro is installed successfully in Onboarding wizard
* Fix: JS error is thrown after installing Elementor Pro in the Onboarding wizard
* Fix: Wrong return type in `Skin Base get_instance_value()` method's PHPDoc
* Fix: "Create my account" should lead to "Sign Up" instead of "Login" in Onboarding wizard
* Fix: Allow file uploads based on the user capability to enforce better security policies in Onboarding wizard

= 3.6.2 - 2022-04-04 =
* Tweak: Added plugins support to the CLI Kit import process
* Tweak: Updated strings for several screens in Onboarding wizard
* Tweak: Added structure preset to include both row and column directions in Container element
* Fix: Alignment control doesn’t affect additional custom breakpoints in Icon List widget ([#16291](https://github.com/elementor/elementor/issues/16291))
* Fix: Carousel widgets do not being displayed correctly when placed in a Container element ([#18298](https://github.com/elementor/elementor/issues/16291))
* Fix: Import flow fails when trying to import unregistered taxonomies
* Fix: Compatibility issues for several widgets in iOS 14 and macOS 13 devices
* Fix: Inner Container element gets duplicated when dragging it from the handle
* Fix: Trying to D&D widgets into a Container will make the dragged element position in an incorrect location when using column direction
* Fix: When copying and pasting a Container, it's being pasted in a Section
* Fix: When converting a section with `z-index` the value is active but can't be edited using the interface

= 3.6.1 - 2022-03-23 =
* Fix: Editor fails to load due to 3rd party deprecation conflicts ([#18235](https://github.com/elementor/elementor/issues/18235))

= 3.6.0 - 2022-03-22 =
* New: Introducing Flexbox Container element as an alpha status experiment
* Tweak: Removed `elementor-section-wrap` by adding it to the DOM experiment ([#16950](https://github.com/elementor/elementor/issues/16950), [#10633](https://github.com/elementor/elementor/issues/10633))
* Tweak: Updated Google Fonts list ([#13501](https://github.com/elementor/elementor/issues/13501), [#17930](https://github.com/elementor/elementor/issues/17930), [#16516](https://github.com/elementor/elementor/issues/16516))
* Tweak: Allowed rearranging global colors and fonts ([#12203](https://github.com/elementor/elementor/issues/12203))
* Tweak: Adding Responsive option to Text Stroke ([#17212](https://github.com/elementor/elementor/issues/17212))
* Tweak: Added responsive capability to Icon Position control in Icon Box widget ([#3040](https://github.com/elementor/elementor/issues/3040))
* Tweak: Added an option to Export and Import WP repository plugins as part of the Kit content
* Tweak: Added an option to Export and Import specific Custom post types as part of the Kit content
* Tweak: Updated `eicons` library to v5.15.0
* Tweak: Added an option to change the color of the navigation dots in Image Carousel widget
* Tweak: Added the Revisions link to Import / Export tools screen
* Tweak: Added an indication when a widget is added to the Favorites section
* Tweak: Added a deprecation notice for PHP 5.6 in WP dashboard
* Tweak Added previous active Kit reference to the site options
* Tweak: Added Kit reference to its imported items
* Tweak: Added `Difference`, `Exclusion` and `Hue` to Column and Section blend mode options
* Tweak: Add border options in Image Box widget
* Tweak: Removed legacy style tab in Elementor dashboard settings screen
* Tweak: Prompt the user permission to allow unfiltered file uploads in Import Template flow
* Tweak: Promoted some experiments to Stable status
* Tweak: Navigator appears by default when loading the editor for the first time
* Tweak: Made typography weight strings translatable
* Tweak: Adjusted the inline icon control for design flexibility
* Tweak: Adjusted the Document settings import prompt texts to be more friendly
* Tweak: Added Kit Library to the Finder
* Tweak: Adding Import Export to the Finder
* Tweak: Added WordPress menus to Export / Import Kit flow
* Tweak: Added Lazy load option to Image Carousel widget
* Tweak: Added Lazy load option to Background Slideshow
* Tweak: Added informative summary screen to Export / Import Kit flow
* Tweak: Added focus state and description on the play icon in Video widget
* Tweak: Added dynamic tag controls to Menu Anchor widget
* Tweak: Added dynamic tag controls to Image Carousel widget
* Tweak: Added dynamic tag controls to Icon Box widget
* Tweak: Added dynamic tag controls to HTML widget
* Tweak: Added a reusable button trait
* Fix: Sticky caused scrolling issues after clicking an element that expands the page height ([#17821](https://github.com/elementor/elementor/issues/17821), [#17839](https://github.com/elementor/elementor/issues/17839), [#18069](https://github.com/elementor/elementor/issues/18069))
* Fix: Missing escaping for WordPress dashboard strings (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Fix: Widescreen breakpoint values affect other devices in the Editor preview in some cases
* Fix: Dynamic references are not being reassigned in Export / Import Kit flow
* Fix: System info file displays inaccurate WP memory limit
* Fix: Widget appears empty while using PHP 8.1 in Posts and Posts archive widgets
* Fix: Lower custom breakpoints didn't inherit upper breakpoints values in frontend
* Fix: JS Error is thrown when switching between documents in some cases
* Fix: CLI Import command caused the import process to fail
* Fix: JS error is thrown when clearing select2 control
* Fix: Dev Edition notice appears inside the Form Submission window
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-6-planned-deprecations/)

= 3.5.6 - 2022-02-28 =
* Fix: Favorite WooCommerce widgets causes fatal error when WooCommerce is deactivated ([#17641](https://github.com/elementor/elementor/issues/17641))
* Fix: Video inserted with privacy mode are not playing in the Video widget lightbox ([#17749](https://github.com/elementor/elementor/issues/17749))
* Fix: Global widgets search didn't work properly ([#17270](https://github.com/elementor/elementor/issues/17270))
* Fix: `remove_responsive_control()` doesn't remove controls for Additional Custom breakpoints which generates PHP errors
* Fix: Lightbox video icon color control not working when Inline Font Icons experiment is active
* Fix: Hardened the Lightbox action module sanitization to prevent potential security issues

= 3.5.5 - 2022-02-03 =
* Tweak: Changed Developer Edition promotional notice triggers
* Tweak: Added a filter to allow modifying the imported Kit temp directory path
* Fix: When trying to import a Kit and getting a general error the try-again action is incorrect
* Fix: Hardened the Lightbox action module sanitization to prevent potential security issues

= 3.5.4 - 2022-01-23 =
* Tweak: Update `e-icons` library to `v5.14.0`
* Fix: Can't edit the page if Favorite Widgets are in use in edge cases ([#17364](https://github.com/elementor/elementor/issues/17364))
* Fix: `onError` throws an error because of bad parameters in Debug Util ([#14571](https://github.com/elementor/elementor/issues/14571))
* Fix: Swiper Util accepts only `jQuery` instances as the container parameter ([#17262](https://github.com/elementor/elementor/issues/17262))
* Fix: "Page template" string presented as "Library Page" in Finder results
* Fix: SVG icons are not being imported properly in Template Library
* Fix: Dynamic Tag switcher disappear in RTL

= 3.5.3 - 2021-12-28 =
* Fix: Global Widgets appears with the default widget style and not with the styling of the Global widget([#17296](https://github.com/elementor/elementor/issues/17296))

= 3.5.2 - 2021-12-22 =
* Fix: Responsive Reverse Columns control are not working properly ([#17240](https://github.com/elementor/elementor/issues/17240), [#17174](https://github.com/elementor/elementor/issues/17174))
* Fix: Favorite widgets are being reset after page reload ([#17219](https://github.com/elementor/elementor/issues/17219))
* Fix: Inner Section can’t be dragged into a column ([#17249](https://github.com/elementor/elementor/issues/17249))
* Fix: Elements are pasted in reverse order when copying and pasting multiple elements

= 3.5.1 - 2021-12-20 =
* Tweak: Reverted Experiments auto deactivation in Safe Mode ([#17195](https://github.com/elementor/elementor/issues/17195))
* Fix: Experiments are not working when Safe mode is enabled ([#17195](https://github.com/elementor/elementor/issues/17195))
* Fix: Editing handles `z-index` issue ([#17187](https://github.com/elementor/elementor/issues/17187))
* Fix: Missing wrapper section when Inner Section widget is dragged directly to the drag area ([#17187](https://github.com/elementor/elementor/issues/17187))
* Fix: SVG and JSON files were not being uploaded when Dragged from Desktop ([#17194](https://github.com/elementor/elementor/issues/17194))
* Fix: Several functions are being executed when not supposed to in all WordPress Dashboard screens

= 3.5.0 - 2021-12-14 =
* New: Introducing CSS Transform - rotate, scale, skew, offset and flip any element ([#12451](https://github.com/elementor/elementor/issues/12451))
* New: Meet Multi-Select for page elements - improve your workflow process by making bulk actions ([#8006](https://github.com/elementor/elementor/issues/8006), [#879](https://github.com/elementor/elementor/issues/879))
* New: Meet Favorite widgets section - save your most useful widgets for easier accessibility ([#2184](https://github.com/elementor/elementor/issues/2184), [#11443](https://github.com/elementor/elementor/issues/11443))
* New: Added Text Stroke control - highlight titles with colorful outlines to in Heading, Icon Box, Tabs, Accordion, and Text path widgets ([#11158](https://github.com/elementor/elementor/issues/11158))
* Experiment: Hide native WordPress widgets from panel search results - remove unwanted widgets from the widgets panel
* Tweak: Adjusted Inline Font Icons experiment to work with `eicons` library and save up to 110KB of asset loading ([#8572](https://github.com/elementor/elementor/issues/8572), [#11361](https://github.com/elementor/elementor/issues/11361))
* Tweak: Detect and present contextual errors messages in the Import/Export flow for better troubleshooting ([#15630](https://github.com/elementor/elementor/issues/15630), [#15715](https://github.com/elementor/elementor/issues/15715))
* Tweak: Added Elementor license plan filter to Kit Library ([#16075](https://github.com/elementor/elementor/issues/16075))
* Tweak: Added Reverse columns option to Additional Custom Breakpoints Experiment ([#16322](https://github.com/elementor/elementor/issues/16322), [#12925](https://github.com/elementor/elementor/issues/12925))
* Tweak: Adjusted widgets to work with Inline-CSS Experiment and Additional Custom Breakpoints experiment ([#16126](https://github.com/elementor/elementor/issues/16126))
* Tweak: Added the option to search by tag names in the Kit Library ([#16075](https://github.com/elementor/elementor/issues/16075))
* Tweak: Added a quick Apply Kit modal option in the Kit Library
* Tweak: Updated Experiments screen structure for better experiments discoverabilty
* Tweak: Added Word Spacing control to typography group control
* Tweak: Added Pro promotion screen for Custom Code feature
* Tweak: Removed the Archive Posts and Archive Title widgets from widgets panel search results in non Archive templates
* Tweak: Prompt the user permission to allow unfiltered file uploads in the Import Kit flow
* Tweak: Optimized Kit library index page performance
* Tweak: Improved performance of `WP_Query` by adding `no_found_rows=true` parameter
* Tweak: Improved performance of loading the "My templates" while loading the Templates library
* Tweak: Minimum height wasn't defined to all responsive devices
* Tweak: Experiments status added to the system info for better debugging
* Tweak: Safe mode now deactivates Elementor Experiments
* Tweak: UI improvement for cascaded responsive control units
* Tweak: Updated Dialog library to `4.9.0`
* Tweak: Added a unified components registration for widgets, controls, form actions, dynamic tags, and finder categories
* Tweak: Disabled the option to change the desktop height and width fields in the responsive bar
* Tweak: Redundant inline CSS code was removed with the use of `eicons`
* Tweak: Implemented Redux framework for future features support
* Tweak: Allow "Edit Buttons" options to be extended by 3rd party developers
* Tweak: `Editor/Documents` moved to `/editor/components` folder
* Tweak: Added a filter to Context Menu Groups
* Fix: Hamburger button didn't open on mobile in Nav menu widget ([#16682](https://github.com/elementor/elementor/issues/16682))
* Fix: Adding multiple repeater controls in one section creates a onflict ([#13117](https://github.com/elementor/elementor/issues/13117))
* Fix: Finder incorrectly identifies pages created through Finder as a Post type instead of Page type ([#12502](https://github.com/elementor/elementor/issues/12502))
* Fix: Unexpected behavior if `ctrl` + mouse click was used on mac ([#5228](https://github.com/elementor/elementor/issues/5228))
* Fix: Edit header handles don't show up if the `z-index` of the header section is over 99 ([#16214](https://github.com/elementor/elementor/issues/16214))
* Fix: Slides per view can't be set to default if there is a widescreen value in the Testimonial Carousel widget ([#16210](https://github.com/elementor/elementor/issues/16210))
* Fix: Dividers not vertically centered in Icon List widget ([#16431](https://github.com/elementor/elementor/issues/16431))
* Fix: Text typography control is missing in Text Path widget ([#16805](https://github.com/elementor/elementor/issues/16805))
* Fix: Column padding affected absolute positioned elements ([#16572](https://github.com/elementor/elementor/issues/16572))
* Fix: Some controls disappear in responsive devices when Additional Custom Breakpoint experiment is active ([#16608](https://github.com/elementor/elementor/issues/16608))
* Fix: Menu Anchor widget didn't scroll to the appropriate ID if Scroll Snap is active ([#17035](https://github.com/elementor/elementor/issues/17035))
* Fix: SVG sanitizer is failing if there is a line break after `</svg>`
* Fix: Image Carousel widget ignored padding and margin inside columns
* Fix: Theme Builder close button not functioning if the page was refreshed
* Fix: Pro widget promotional popover CTA text was incorrect when Elementor Pro is activated but not connected
* Fix: Section editing handle is not accessible when Section is placed below a Header
* Fix: Run DB calculations only on sites that are required to
* Fix: UI glitch in Google maps widgets
* Fix: CLI 'library import' deletes the original imported file when importing a template
* Fix: Updated Admin Top Bar font for Non-English languages
* Fix: Elementor top bar is not presented in License page
* Fix: Template name changed if a dash mark was used in the Import process
* Fix: Empty state background was missing from the Media controls
* Fix: Apply Kit option threw an error when used with PHP 8.0
* Fix: Sticky option threw an error and cause the editor not to work
* Fix: UI glitch in "Get beta updates" modal
* Fix: Scroll snap caused UI glitches and threw undefined error on Archive pages
* Fix: Vertical align control disappeared if used in a custom position on a custom breakpoint
* Fix: Choose controls in RTL sites were inverted
* Fix: Elementor notices were being displayed in the plugin update screen
* Fix: Z-index control overrides negative values
* Fix: Text Path widget causes redundant file system calls
* Deprecated: `get_add_new_landing_page_url` (props [@HeyMehedi](https://github.com/HeyMehedi))
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-5-planned-deprecations/)

= 3.4.8 - 2021-11-16 =
* Fix: Font Awesome 4 support option is set to Yes in new websites
* Fix: CSS Printing method option is set to Internal Embedding in new websites
* Fix: Hardened Lightbox capabilities to enforce better security policies
* Fix: After downgrading from a version with Container experiment active a PHP error is thrown

= 3.4.7 - 2021-10-31 =
* Tweak: Added role restriction to Version control feature
* Fix: Private Vimeo videos are not loading in Video widget ([#16741](https://github.com/elementor/elementor/issues/16741))
* Fix: Custom width responsive control disappeared until section init in the common Positioning section
* Fix: PayPal button widget is not functioning in future Pro versions
* Fix: Unwanted HTML escaping in Pro features promotion

= 3.4.6 - 2021-10-19 =
* Tweak: Updated `eicons` library to 5.13.0
* Fix: Custom SVG is not supported in some servers environments in Text Path widget
* Fix: Lottie animations are not imported in Template import process
* Fix: Can't connect to Library via CLI
* Fix: Scroll event listener is not optimized in Counter widget

= 3.4.5 - 2021-10-12 =
* Tweak: Updated featured video in the WordPress repository plugin page
* Tweak: Added support for future features compatibility
* Fix: Additional breakpoints responsive CSS classes clash when Optimized DOM Output experiment is inactive [#16322](https://github.com/elementor/elementor/issues/16322)
* Fix: Escaped several translation strings for better security (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Fix: Slides per view and slides to scroll controls disappeared when using multiple breakpoints in Testimonial Carousel widget
* Fix: Padding not applied properly with absolute positioned widgets when the Optimized DOM Output experiment is active
* Fix: Missing padding in the widgets panel layout

= 3.4.4 - 2021-09-13 =
* Fix: Custom popover responsive controls breaks when used in responsive breakpoints view when Additional Breakpoints experiment is active ([#16312](https://github.com/elementor/elementor/issues/16312))
* Fix: Data Updater causes fatal error due to DB corruption ([#16100](https://github.com/elementor/elementor/issues/16100))
* Fix: Page settings layout description strings were inaccurate
* Fix: Specific image sizes causes the first image to load in large dimensions on load in Image Carousel widget

= 3.4.3 - 2021-08-29 =
* Tweak: Added "Justified" option in Typography text alignment in Column & Section elements ([#11480](https://github.com/elementor/elementor/issues/11480))
* Tweak: Enlarged cards view in the Kit Library for better visibility
* Tweak: Changed Kit Library tab title
* Fix: Landing pages experiment causes 404 errors with attachment pages ([#15943](https://github.com/elementor/elementor/issues/15943))
* Fix: Can't upload SVG files using Elementor ([#16084](https://github.com/elementor/elementor/issues/16084), [#16119](https://github.com/elementor/elementor/issues/16119), [#16088](https://github.com/elementor/elementor/issues/16088))
* Fix: `wp_kses_post` strips `srcset` attribute from images ([#16111](https://github.com/elementor/elementor/issues/16111))
* Fix: Inline CSS is parsed to an invalid characters ([#16140](https://github.com/elementor/elementor/issues/16140))
* Fix: Animated elements disappear before entering the viewport ([#2806](https://github.com/elementor/elementor/issues/2806))
* Fix: Autoplay not working for Vimeo videos in Lightbox ([#16068](https://github.com/elementor/elementor/issues/16068))
* Fix: HTML captions are stripped in Image carousel widget ([#16073](https://github.com/elementor/elementor/issues/16073))
* Fix: Carousels are not working properly in the Editor when Additional Custom Breakpoints experiment is active
* Fix: Responsive values are not being reflected in Slider controls
* Fix: Elementor Top Admin Bar loads in WordPress dashboard when the experiment is active
* Fix: Prevent Admin Top Bar to conflict with WordPress customizer
* Fix: Can't change alignment of icons when Font Awesome Inline experiment is active in Icon List widget
* Fix: Import deeplink redirect loses target screen parameters when user needs to log in
* Deprecated: Removed all usages of `Elementor\Utils::get_create_new_post_url()`

= 3.4.2 - 2021-08-19 =
* Fix: Responsive Site settings are not being applied on frontend when Additional Custom Breakpoints is active ([#16055](https://github.com/elementor/elementor/issues/16055), [#16033](https://github.com/elementor/elementor/issues/16033))
* Fix: Global colors are applied even when they are disabled on Frontend ([#16037](https://github.com/elementor/elementor/issues/16037))
* Fix: Conditional controls missing when using Dynamic tags ([#16050](https://github.com/elementor/elementor/issues/16050))
* Fix: Motion Effects popover is not visible ([#16044](https://github.com/elementor/elementor/issues/16044))
* Fix: Control conditions are not being executed when has capital letter in the control slug ([#16003](https://github.com/elementor/elementor/issues/16003))

= 3.4.1 - 2021-08-18 =
* Tweak: Missing post types in Site Parts overview screen in Kit Library ([#15249](https://github.com/elementor/elementor/issues/15249))
* Tweak: Added source=generic parameter when connecting through the top bar
* Tweak: Added Inline SVG support to the generic video Play icon
* Fix: Control conditions are not being executed when has dash in the control slug ([#16003](https://github.com/elementor/elementor/issues/16003))
* Fix: Gradient control doesn't work on frontend when using Global Colors ([#13288](https://github.com/elementor/elementor/issues/13288))
* Fix: Placeholder values of Column width control shouldn't cascade to Mobile device
* Fix: Alignment control doesn't work in responsive view in Testimonial widget
* Fix: Certain responsive controls pass the desktop default to other devices accidentally
* Fix: Alignment control doesn't work with Additional Custom Breakpoints in Button widget
* Fix: SVG Icon is not being displayed in the Basic Gallery widget
* Fix: Activation bug for IDN domains

= 3.4.0 - 2021-08-17 =
* New: Introducing Additional Custom Breakpoints - Pixel Perfect Design for up to 7 Devices ([#2043](https://github.com/elementor/elementor/issues/2043), [#15488](https://github.com/elementor/elementor/issues/15488), [#15782](https://github.com/elementor/elementor/issues/15782))
* Experiment: Font Awesome Inline SVG for Optimized performance ([#11772](https://github.com/elementor/elementor/issues/11772))
* Tweak: Responsive values visibility in Elementor controls ([#3356](https://github.com/elementor/elementor/issues/3356))
* Tweak: Improved asset loading performance by serving lighter JS files ([#8572](https://github.com/elementor/elementor/issues/8572))
* Tweak: Scale controls were added to responsive mode UI ([#15485](https://github.com/elementor/elementor/issues/15485))
* Tweak: Responsive devices order was changed ([#14662](https://github.com/elementor/elementor/issues/14662), [#2043](https://github.com/elementor/elementor/issues/2043))
* Tweak: Excluded Elementor Templates from WordPress default sitemap ([#14578](https://github.com/elementor/elementor/issues/14578), [Topic](https://wordpress.org/support/topic/how-to-stop-elementor-templates-appearing-in-wp-sitemap/))
* Tweak: Responsive capability was added to Alignment control in Testimonial widget ([#11542](https://github.com/elementor/elementor/issues/11542))
* Tweak: Added translation function to several locations (props [@rodolphebertozzo](https://github.com/rodolphebertozzo))
* Tweak: Allow extending Section elements using Elementor API (props [@Oxibug](https://github.com/oxibug))
* Tweak: Converted responsive controls generation in the Editor to JS ([Developers Blog Post](https://developers.elementor.com/additional-custom-breakpoints-technical-details-and-gotchas/))
* Tweak: Updated Webpack library to v5.40.0
* Tweak: Added an option to deep-link into Revisions panel screen
* Tweak: Changed the Kit Library back button text to "Back to Library"
* Tweak: Added additional information to usage data
* Tweak: Added compatibility for future feature in Site Settings
* Tweak: Added CLI support for Import Kit by ID
* Tweak: Added informative tooltip for Finder element in Elementor admin top bar
* Tweak: Use template literals in Elementor filter names
* Tweak: Updated "Getting Started" video playlist
* Tweak: Enforced better security policies
* Fix: Dynamic content disappeared if chosen in Code Highlight widget ([#14766](https://github.com/elementor/elementor/issues/14766))
* Fix: Text size increased on Icon list widget with size set to `em` and a link ([#14829](https://github.com/elementor/elementor/issues/14829))
* Fix: Undefined `"pro_widgets"` array key message in edge cases (props [@jahir07](https://github.com/jahir07), [@codersaiful](https://github.com/codersaiful), [#15542](https://github.com/elementor/elementor/issues/15542))
* Fix: UI glitch in Select2 control in Dark mode ([#15473](https://github.com/elementor/elementor/issues/15473))
* Fix: Re-migrate globals are not working properly in edge cases ([#15477](https://github.com/elementor/elementor/issues/15477), props [@jvernooy](https://github.com/jvernooy))
* Fix: Gallery widget with Dynamic gallery applied can't be edited or deleted ([#15529](https://github.com/elementor/elementor/issues/15529))
* Fix: Improved Responsive bar compatibility to RTL sites
* Fix: Dynamic tags UI glitch in Code Highlight widget
* Fix: Default Mobile width changed to 360px in Responsive mode
* Fix: Viewport meta tag was escaped in Canvas page layout
* Fix: Setting zero offset to Sticky elements is not working properly
* Fix: Icons are not vertically aligned in Icon List widget
* Fix: WordPress Audio widget only shows correct styling in live preview but not in the actual site
* Fix: New tabs do not appear instantly if alignment is not set to Default or Start in Tabs widget
* Fix: Edit popover was trimmed by the template card in Theme Builder
* Fix: Duplicate `<style>` tags were printed in several cases
* Fix: Theme Builder is not working if Import/Export experiment is inactive
* Fix: UI glitch in Responsive bar in RTL sites
* Fix: "Edit with Elementor" isn’t working on static Homepages
* Fix: UI glitches in Kit Library
* Fix: Library connect issues after URL change in Kit and template Library
* Fix: Kit Library not showing all categories
* Fix: Pro widget promotions flickers after clicking on "See it in action" button
* Fix: Incorrect hover default color in Text Path widget
* Fix: Text decoration is not working in Text Path widget
* Fix: Browser's autocomplete is taking over the results in the Finder
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-4-planned-deprecations/)

= 3.3.1 - 2021-07-20 =
* Tweak: Added a back to Kit Library button to the Import Kit screen
* Tweak: Updated `eicons` library to 5.12.0
* Tweak: Declared compatibility for WordPress 5.8
* Fix: Some widget style breaks when Improved CSS Loading Experiment is active in certain cases ([#15632](https://github.com/elementor/elementor/issues/15632), [#15683](https://github.com/elementor/elementor/issues/15683), [#15660](https://github.com/elementor/elementor/issues/15660))
* Fix: Improved CSS Loading experiment learn more link leads to the wrong doc ([#15622](https://github.com/elementor/elementor/issues/15622))
* Fix: JS error is thrown when using Global widget since Elementor v3.3.0 ([#15648](https://github.com/elementor/elementor/issues/15648), [#15672](https://github.com/elementor/elementor/issues/15672))
* Fix: Removed deprecated classes calls in System Info screen

= 3.3.0 - 2021-07-13 =
* New: Introducing Kits Library - Create Entire Websites Faster Than Ever ([#4417](https://github.com/elementor/elementor/issues/4417), [#11341](https://github.com/elementor/elementor/issues/11341))
* New: Meet the Color Color Sampler - fetch colors from every image and populated color control ([#14868](https://github.com/elementor/elementor/issues/14868))
* Experiment: Improved CSS Loading - Load widgets CSS only when needed ([#13533](https://github.com/elementor/elementor/issues/13533), [#8572](https://github.com/elementor/elementor/issues/8572))
* Tweak: Added Animations CSS library to be loaded conditionally when needed when Improved Asset Load experiment is active ([#13533](https://github.com/elementor/elementor/issues/13533), [#8572](https://github.com/elementor/elementor/issues/8572))
* Tweak: Added the ability to fetch Image from external URL ([#413](https://github.com/elementor/elementor/issues/413))
* Tweak: Added responsive capabilities to Content width in Section and Inner Section elements ([#12963](https://github.com/elementor/elementor/issues/12963))
* Tweak: Added “Hidden Elements” User Preference control to choose whether to show or hide hidden elements in responsive mode ([#12316](https://github.com/elementor/elementor/issues/12316))
* Tweak: Added User Preference control to set the default responsive device when the responsive view is triggered ([#14662](https://github.com/elementor/elementor/issues/14662))
* Tweak: Added an Elementor top bar to all Elementor screens in the WordPress admin pages for better discoverability ([#15276](https://github.com/elementor/elementor/issues/15276), [#15250](https://github.com/elementor/elementor/issues/15250))
* Tweak: Added gradient button capabilities to Button instances in Theme Style ([#14731](https://github.com/elementor/elementor/issues/14731))
* Tweak: Changed Landing Pages experiment to be active by default on all websites
* Tweak: Improved Kit Import and Export tool to include Posts, Pages, Custom Post Types and WooCommerce Products
* Tweak: Allow unlimited export of Elementor-created content in Import/Export tool
* Tweak: Included Homepage setting in Import/Export tool
* Tweak: Throw an error when running Import process via CLI if user is not Administrator role
* Tweak: Added Informative modal links in Import/Export tool
* Tweak: Modified Kit information area to be collapsible in Import/Export tool
* Tweak: Added custom fields support to Import/Export Experiment
* Tweak: Added future support for Additional Breakpoints
* Tweak: Changing the `content-filters` to be accessed publicly
* Tweak: Regenerate Files admin button resets all page-assets data
* Tweak: Improved Kit Import and Export tool to include conditions conflicts resolver
* Tweak: Added Kit information to Import and Export tool
* Tweak: Added summary screens to Import and Export tool
* Tweak: Converted Improved Asset Loading Experiment registration to the post save process instead of loading process
* Tweak: Widgets search can accept non-english strings for better UX
* Tweak: Polished Responsive top bar UI
* Tweak: Created an Uploads manager util to better handle files being uploaded
* Tweak: Added default height to Tablet and Mobile devices
* Tweak: Polished Select2 control UI
* Tweak: Added Pro promotion in Submission feature page
* Tweak: Updated Font Awesome icons library to v5.15.3
* Tweak: Added a new filter `elementor/document/save/data` to allow manipulation when document save starts
* Tweak: Added usage schema in `schemas/usage`
* Tweak: Recalculate elements usage each upgrade
* Tweak: Added "Recreate Kit" button in Elementor settings only when the Default Kit does not exist
* Tweak: Made `elementorFrontend.getDeviceSetting()` method dynamic
* Fix: Inline editing not working when the Optimized DOM experiment is on ([#14703](https://github.com/elementor/elementor/issues/14703))
* Fix: Exit animation flickers in several cases ([#14459](https://github.com/elementor/elementor/issues/14459), [#13180](https://github.com/elementor/elementor/issues/14703))
* Fix: Lightbox is not working in the frontend ([#15080](https://github.com/elementor/elementor/issues/15080), [#15085](https://github.com/elementor/elementor/issues/15085))
* Fix: Prevent Default Kit from being recreated when kit not exists ([#13299](https://github.com/elementor/elementor/issues/13299))
* Fix: Social icons alignment shift right in Safari browsers ([#13122](https://github.com/elementor/elementor/issues/13122))
* Fix: Gradient control doesn't work when using Global Colors ([#13288](https://github.com/elementor/elementor/issues/13288))
* Fix: Social Icons and Share button space disappeared when optimizing the HTML ([#13279](https://github.com/elementor/elementor/issues/13279))
* Fix: UI glitch in the empty state of Global widgets tab
* Fix: Breakpoints values in the Panel are not enforced
* Fix: Error thrown in Template library if Expert template been imported
* Fix: Panel search bar appears above the overlay on load
* Fix: Slides widget is not displaying properly in the Editor
* Fix: Activation account connection error on IDN domains
* Fix: Video item didn’t started playing without adding an image overlay in Media Carousel widget
* Fix: Inline editing not working when the Optimized DOM experiment is active
* Fix: Site Identity data been transferred when importing a Kit
* Fix: WordPress content Featured images are not being imported when applying a Kit
* Fix: Post Excerpt is not imported when applying a Kit via Import Kit tool
* Fix: Default Kit file not being created in Multisite WordPress instances
* Fix: Dark mode UI glitches in Import/Export tool
* Fix: Close and back to dashboard buttons are not being redirected to the correct page in Import/Export experiment
* Fix: Collapse button disappeared on mobile if the user not logged in Video Playlist widget
* Fix: Import process fail message is shown when importing a Kit from ThemeForest
* Fix: Large images optimized by WordPress can't be displayed properly via dynamic Toolset fields
* Fix: Dynamic control is not working in Text Path widget
* Fix: Column removed from a section in the Navigator resulted in an empty section
* Fix: Unable to drag and drop columns from one above other
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-3-planned-deprecations/)

= 3.2.5 - 2021-06-16 =
* Fix: Reverted Replace URL fix that caused the Default Kit to get regenerated in certain cases ([#14892](https://github.com/elementor/elementor/issues/14892))

= 3.2.4 - 2021-05-26 =
* Tweak: Changed Google Maps widget to use API v3 instead of deprecated v2 ([#15090](https://github.com/elementor/elementor/issues/15090))
* Tweak: Updated Google Fonts list to 05/2021 ([#14732](https://github.com/elementor/elementor/issues/14732), [#14536](https://github.com/elementor/elementor/issues/14536), [#13595](https://github.com/elementor/elementor/issues/13595))
* Tweak: Changed "Missing Header" text for Compatibility tags in System info screen
* Fix: Google Maps widget is not loading due to API deprecation ([#15090](https://github.com/elementor/elementor/issues/15090))
* Fix: Disable Lightbox user preference does not affect the Basic Gallery widget ([#12913](https://github.com/elementor/elementor/issues/12913))
* Fix: Hardened Lightbox capabilities to enforce better security policies

= 3.2.3 - 2021-05-05 =
* Fix: `eicons` file couldn't be found when using Custom Breakpoints ([#14718](https://github.com/elementor/elementor/issues/14718), [#14712](https://github.com/elementor/elementor/issues/14712))
* Fix: Deregister `eicons` fonts action is not working ([#14712](https://github.com/elementor/elementor/issues/14712))
* Fix: Responsive custom CSS is not properly generated when using custom breakpoints ([#14711](https://github.com/elementor/elementor/issues/14711))
* Fix: Minimize the Editor window caused to preview to collapse behind the Panel

= 3.2.2 - 2021-04-26 =
* Fix: Multiple carousels in the same page stopped functioning when Improved Asset Load experiment is active ([#14663](https://github.com/elementor/elementor/issues/14663), [#14675](https://github.com/elementor/elementor/issues/14675))
* Fix: Section Style tab panel is grayed in several site languages ([#14642](https://github.com/elementor/elementor/issues/14642))
* Fix: Responsive mode UI glitch in WordPress versions under 5.6

= 3.2.1 - 2021-04-21 =
* Tweak: Changed the minimum width of Mobile breakpoint to 320px
* Fix: Global Typography settings were printed as undefined variables
* Fix: Close button in responsive bar didn't worked in the Desktop breakpoint
* Fix: UI glitch in the dashboard admin notices
* Fix: Right click contextual menu appears underneath the Navigator
* Fix: Panel resize caused UI glitches in RTL websites
* Fix: Responsive device icons adjustments in RTL websites

= 3.2.0 - 2021-04-19 =
* New: Meet Text Path widget - Add more text design options to your headings
* New: Meet the new Responsive Mode * Easily edit your responsive design
* New: Introducing Mask option - Add a mask to every element on your page ([#13736](https://github.com/elementor/elementor/issues/13736))
* Experiment: Template Kit Import & Export feature - Move all of your Elementor content from one site to another
* Experiment: Added `elementor-image` & `elementor-text-editor` classes to DOM Optimization Experiment
* Tweak: Split `eicons` library CSS file to Editor & Frontend usages as part of the Improved Asset Loading Experiment ([#8572](https://github.com/elementor/elementor/issues/8572))
* Tweak: Load the Lightbox assets only if Lightbox is used on the page as part of the Improved Asset Loading Experiment ([#8572](https://github.com/elementor/elementor/issues/8572))
* Tweak: Added Google Fonts loading method option in Elementor settings screen to control over `font-display` option ([#14236](https://github.com/elementor/elementor/issues/14236))
* Tweak: Added Gradient background option to Button widget ([#3142](https://github.com/elementor/elementor/issues/3142))
* Tweak: Added FAQ Schema support to Accordion and Toggle Widgets (Props [@pacotole](https://github.com/pacotole), [#13425](https://github.com/elementor/elementor/issues/13425), [#9053](https://github.com/elementor/elementor/issues/9053))
* Tweak: Improved the widgets panel responsiveness ([#14026](https://github.com/elementor/elementor/issues/14026))
* Tweak: Added dynamic option to video link inside Sections ([#6116](https://github.com/elementor/elementor/issues/6116))
* Tweak: Added Text shadow control to Text widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Icon Box widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Start Rating widget([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Icon List widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Icon widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Image Gallery widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Image Carousel widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Counter widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Progress Bar widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Testimonials widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Tabs widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Toggle widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Accordion widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added Text shadow control to Alert widget ([#10033](https://github.com/elementor/elementor/issues/10033), [#2263](https://github.com/elementor/elementor/issues/2263), [#5579](https://github.com/elementor/elementor/issues/5579))
* Tweak: Added support for Expert tier templates in Templates Library
* Tweak: Adjusted Media control to better handle SVG uploads
* Tweak: Converted script loading method to use an independent Util instead of JS `import` function for better performance
* Tweak: Changed the widgets search field to be sticky
* Tweak: Added "Fit to Screen" height option in Inner Section widget
* Tweak: Added `vh` and `vw` units to Min-height control in Inner Section widget
* Tweak: Added `vh` unit to Height control in Google Maps widget
* Tweak: Changed breakpoints mechanism to use dynamic breakpoint references to allow future addition of breakpoints
* Tweak: Added Dynamic capabilities support in Code Highlight widget
* Tweak: Added ability to access Site Settings using `Cmd + K` / `CTRL + K`
* Tweak: Added ability to access Site Settings via Admin top bar
* Tweak: Updated Elementor admin notices UI
* Tweak: Added userAgent/browser classes to the admin area body class
* Tweak: Manage all the subscription plans from Connect module in Template Library
* Tweak: Removed "Meet right click" introduction
* Fix: Edit section labels in Navigator didn't triggered the Update button ([#10772](https://github.com/elementor/elementor/issues/10772))
* Fix: Custom fonts file URLs are static and are not changed using the Replace URL tool ([#10382](https://github.com/elementor/elementor/issues/10382))
* Fix: "Undefined" error message is thrown when server fails to save or insert a template ([#12377](https://github.com/elementor/elementor/issues/12377))
* Fix: Popup Dynamic Link options modal is misplaced ([#13257](https://github.com/elementor/elementor/issues/13257))
* Fix: Navigator indicators are missing since WordPress v5.7 ([#14094](https://github.com/elementor/elementor/issues/14094))
* Fix: PHP error was thrown when using PHP 8 in the System Info screen
* Fix: Hardened SVG uploads capabilities to enforce better security policies
* Fix: Redundant space when deleting an icon from Icon Box widget
* Fix: Library Connect completion redirects to wrong page
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-2-planned-deprecations/)

= 3.1.4 - 2021-03-08 =
* Fix: Self hosted video didn't work with Image Overlay ([#14038](https://github.com/elementor/elementor/issues/14038))
* Fix: Clicking on Video widget Image Overlay doesn't plat video for Vimeo ([#14095](https://github.com/elementor/elementor/issues/14095))
* Fix: Carousels navigation arrows `z-index` issue in Safari ([#13791](https://github.com/elementor/elementor/issues/13791))
* Fix: Landing Pages can cause a fatal error for Author roles in the WordPress dashboard
* Fix: `<a>` tags placed in an SVG file trigger the Lightbox
* Fix: Hardened allowed options in the editor to enforce better security policies
* Fix: Removed `html` option in Lightbox module to prevent security issues

= 3.1.3 - 2021-03-03 =
* Tweak: Added `aria-expanded` property to Tabs, Accordion and Toggle widgets ([#11246](https://github.com/elementor/elementor/issues/11246), [#3576](https://github.com/elementor/elementor/issues/3576))
* Fix: Values are being cleared after save action in Query control ([#14098](https://github.com/elementor/elementor/issues/14098))

= 3.1.2 - 2021-03-02 =
* Tweak: Added Lazy load option for YouTube in Video widget to prevent conflicts ([#13898](https://github.com/elementor/elementor/issues/13898))
* Tweak: Allow overwriting the assets URL when using a mirror domain ([#13701](https://github.com/elementor/elementor/issues/13701))
* Tweak: Removed deprecated `jQuery` functions for WP 5.7 compatibility
* Tweak: Added a `mixin` for `webkit-scrollbar` design in Table of Content widget CSS
* Tweak: Added `--force` CLI replace command to always return the number of replaces
* Tweak: Added a Lazy Load control for YouTube source in Background Video
* Tweak: Updated `eicons` library to v5.11.0
* Fix: Youtube videos didn't work properly when Privacy mode is enabled and "Improved Asset Loading" experiment is disabled ([#13711](https://github.com/elementor/elementor/issues/13711))
* Fix: Error event will not always have an `originalEvent` (props [@enisdenjo](https://github.com/enisdenjo))
* Fix: Background Video protocol wasn't using `https` for `youtube-nocookie` option in Video widget
* Fix: Youtube API script was loaded when Image overlay is used
* Fix: Navigator overlapped preview area if attached to the right
* Fix: Column layout glitch when changing the section structure
* Fix: Landing page created via the Admin bar wasn't created with Canvas layout
* Fix: Sanitized options in the editor to enforce better security policies

= 3.1.1 - 2021-01-31 =
* Tweak: Minor UI improvements in Compatibility tag
* Fix: Async JS loading conflicted with `wp.i18n` ([#13708](https://github.com/elementor/elementor/issues/13708), [#13746](https://github.com/elementor/elementor/issues/13746), [Topic](https://wordpress.org/support/topic/console-error-43/))
* Fix: Elementor loads unnecessary `wp-polyfills.min.js` script in Frontend ([#13720](https://github.com/elementor/elementor/issues/13720))
* Fix: Landing Pages experiment removes content in static blog pages ([#13706](https://github.com/elementor/elementor/issues/13706), [#13728](https://github.com/elementor/elementor/issues/13728), [#13725](https://github.com/elementor/elementor/issues/13725), [Topic](https://wordpress.org/support/topic/blog-page-not-showing-after-update-3/))
* Fix: Background Slideshow shifted if Section is stretched and has Full Width layout ([#13750](https://github.com/elementor/elementor/issues/13750), [Topic](https://wordpress.org/support/topic/slide-show-background-2/), [Topic](https://wordpress.org/support/topic/slider-background-section-layout-issue/), [Topic](https://wordpress.org/support/topic/elementor-v-3-0-16-breaks-full-width-header/))
* Fix: Entrance animation set to `none` caused a delay in the widget load
* Fix: Navigator rearrangement glitches when Optimized DOM experiment is active
* Fix: Keyboard navigation isn't working in Tabs widget when Accessibility Improvements experiment is active
* Fix: Minor UI glitches in Experiments screen
* Fix: Wrong check of a deprecated method caused a notice when `ELEMENTOR_DEBUG` is enabled
* Fix: Arrow keys navigation is not working in Finder
* Fix: Theme Style overrides icon size when set in `em` in Social Icon widget

= 3.1.0 - 2021-01-24 =
* New: Elementor Experiments - Experience new features before they're officially released ([Developer Documentation](https://developers.elementor.com/elementor-experiments/))
* New: Compatibility Tag - Make sure your website plugins are compatible with Elementor ([Developer Documentation](https://developers.elementor.com/compatibility-tag/))
* Experiment: Landing Pages - Create beautiful landing pages in a streamlined workflow
* Experiment: Accessibility Improvements - Make Elementor widgets more accessible (may include markup changes) ([#13191](https://github.com/elementor/elementor/issues/13191))
* Experiment: Improved performance by loading JS and Swiper assets conditionally in frontend ([#8572](https://github.com/elementor/elementor/issues/8572), [Developer Documentation](https://developers.elementor.com/experiment-optimized-asset-loading))
* Tweak: Improved Tabs widget accessibility ([#11779](https://github.com/elementor/elementor/issues/11779), [#11561](https://github.com/elementor/elementor/issues/11561))
* Tweak: Added alignment options for Tabs widget ([#11997](https://github.com/elementor/elementor/issues/11997))
* Tweak: Updated Font Awesome icons library to v5.15.1 ([#12057](https://github.com/elementor/elementor/issues/12057))
* Tweak: Added "Custom" Columns Gap option in Section element ([#11978](https://github.com/elementor/elementor/issues/11978))
* Tweak: Added Border Radius support in Google Maps widget ([#11359](https://github.com/elementor/elementor/issues/11359))
* Tweak: Added dynamic capabilities to Tab Title control in Tabs widget ([#9710](https://github.com/elementor/elementor/issues/9710))
* Tweak: Added dynamic capabilities to Toggle Content control in Toggle widget ([#12405](https://github.com/elementor/elementor/issues/12405))
* Tweak: Added `em` unit to Border Radius control in Button widget ([#11561](https://github.com/elementor/elementor/issues/11561))
* Tweak: Introduced a new method for attaching a JS handler to an element ([Developer Documentation](https://developers.elementor.com/a-new-method-for-attaching-a-js-handler-to-an-element/))
* Tweak: Added the option to add custom menu items in Site Settings ([Developer Documentation](https://github.com/elementor/elementor/pull/13243))
* Tweak: Added an option to choose Text element HTML tag in Divider widget ([#11499](https://github.com/elementor/elementor/issues/11499))
* Tweak: Updated `eicons` library to v5.10.0
* Tweak: Refactored YouTube source to use YouTube API in Video widget
* Tweak: Added dynamic capabilities to Poster control in Video widget
* Tweak: Global dropdown controls are now displayed for all user roles
* Tweak: Improved browsers detection utility functionality
* Tweak: Added "Find an Expert" link to the Admin Dashboard Overview widget
* Tweak: Added the new Theme Builder as a Finder item
* Tweak: Added `+` icon in multi-select control for better UX
* Tweak: Improved Select2 controls load
* Tweak: Added Elementor Beta (Developer Edition) promotion in WordPress dashboard
* Tweak: Migrated DOM Improvements to Elementor Experiments
* Tweak: Removed redundant extra padding in responsive controls
* Tweak: Modified the Theme Builder app link UI in the Admin Bar menu
* Fix: Some keyboards layout cannot open the keyboard shortcuts dialog ([#6145](https://github.com/elementor/elementor/issues/6145))
* Fix: Change Page Layout doesn't update the preview on the first attempt ([#13245](https://github.com/elementor/elementor/issues/13245))
* Fix: Update Post Title via Site Settings removes style from Post Title widget ([#12605](https://github.com/elementor/elementor/issues/12605))
* Fix: Can't rearrange items between sections in Navigator since WordPress 5.6 update ([#12256](https://github.com/elementor/elementor/issues/12256))
* Fix: Double click needed to play YouTube video when Poster image exists in Video widget
* Fix: Autoplay option doesn't work in mobile devices when YouTube source is muted in Video widget
* Fix: YouTube End Time option doesn't work when Loop option is active in Video widget
* Fix: Incompatible variable names in Base Swiper class and Elementor Pro carousels
* Fix: Landing Pages menu item directs to the wrong page
* Fix: Redundant spacing is added to WYSIWYG control if rich editing is disabled
* Fix: Editor Autoplay is not working consistently in Image Carousel widget
* Fix: Console errors are thrown when entering the Revisions menu in edge cases
* Fix: Column height is not correct when Optimized DOM experiment is inactive
* Fix: Wrong translation function caused errors in the Revisions panel
* Fix: Widgets empty state is not visible in WordPress 5.6
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-1-planned-deprecations/)

= 3.0.16 - 2021-01-06 =
* Tweak: String changes in Delete Site Settings screen
* Fix: Clicking "Recalculate" button in System info throws a PHP error ([#13100](https://github.com/elementor/elementor/issues/13100 ))
* Fix: Regenerate CSS is not working properly in large scale servers
* Fix: Template Library title sanitization to avoid security issues
* Fix: `libxml_disable_entity_loader` warning is thrown in PHP 8.0 instances

= 3.0.15 - 2020-12-21 =
* Tweak: Added "Theme" option to Page Layout options in Page Settings to allow customization of Site Setting value
* Tweak: Added a confirmation message before deleing Default Kit to trash to avoid unintentional Site Settings deletion
* Fix: Named parameters used in Dynamic Tags causes PHP errors in PHP 8.0 ([#13269](https://github.com/elementor/elementor/issues/13269))
* Fix: "Edit with Elementor" menu does not expands in the top admin-bar menu in WordPress 5.6 ([#13256](https://github.com/elementor/elementor/issues/13256))
* Fix: Stretch Section causes horizontal scroll when the vertical scrollbar is visible in WordPress 5.6 ([#13260](https://github.com/elementor/elementor/issues/13260))
* Fix: Unable to save Templates in WordPress 5.6 ([#12273](https://github.com/elementor/elementor/issues/12273), [Topic](https://wordpress.org/support/topic/save-the-template-and-page-builder-loding/))
* Fix: Ninja Forms plugin conflict in WordPress 5.6 ([#13281](https://github.com/elementor/elementor/issues/13281), [Topic](https://wordpress.org/support/topic/elementor-ninja-forms-bug/), [Topic](https://wordpress.org/support/topic/elementor-bug-ninja-forms/))
* Fix: Pinterest social sharing is not working when displaying a large image in Lightbox
* Fix: Manage Global Colors and Fonts buttons is not leading to the correct screen
* Fix: Permission error when trying to update site description with WP-CLI

= 3.0.14 - 2020-11-25 =
* Tweak: Added 3rd party plugins compatibility to versions rollback mechanism
* Fix: Updated Popup Builder promotional image
* Fix: Upload SVG files only when the user allowed to prevent security issues
* Fix: String updates in Style Settings tab

= 3.0.13 - 2020-11-04 =
* Tweak: Added compatibility with WordPress v5.6 to the Editor panel ([#12958](https://github.com/elementor/elementor/issues/12958))
* Tweak: Updated translation strings in Theme Builder promotional screen
* Fix: Tab content is animated when active in Tabs widget ([#12724](https://github.com/elementor/elementor/issues/12724))
* Fix: "Edit with Elementor" button in admin top-bar is missing in some cases ([#12951](https://github.com/elementor/elementor/issues/12951), [#12995](https://github.com/elementor/elementor/issues/12995))
* Fix: "Edit with Elementor" button in a post removes draft
* Fix: "Edit with Elementor" doesn't work for empty Elementor posts
* Fix: Some settings are not being updated in the Preview in Image Carousel widget
* Fix: Console error message when Global value is not found
* Fix: Revisions are not being saved in Site Settings
* Fix: Apply or Restore revision actions triggers console errors
* Fix: Paste action without any data throws a console error
* Fix: Pasting style with Global values is not reflected in the Panel
* Fix: Can't submit forms from templates when the parent post is empty

= 3.0.12 - 2020-10-20 =
* Tweak: Added "Read More" link to major version upgrade notice
* Tweak: Added System Info CLI command
* Fix: Redundant padding in Social Icons widget ([#12833](https://github.com/elementor/elementor/issues/12833))
* Fix: Editor role can't see "Edit with Elementor" button in top admin-bar ([#12175](https://github.com/elementor/elementor/issues/12175))
* Fix: Dynamic content promotion is shown when self hosted option is selected in Video widget ([#11468](https://github.com/elementor/elementor/issues/11468))
* Fix: 'description' is not supported in the color control ([#12747](https://github.com/elementor/elementor/issues/12747))
* Fix: Some Shape Dividers overlap other elements since Chrome 85 ([#12393](https://github.com/elementor/elementor/issues/12393))
* Fix: Image preview area disappears when 'label_block' is `false` in Media Control ([#11756](https://github.com/elementor/elementor/issues/11756))
* Fix: Missing compatibility for Advanced Editor Tools (previously TinyMCE Advanced) ([#12768](https://github.com/elementor/elementor/issues/12768))
* Fix: Different panel tab is shown when panel tab includes only sections with display conditions that are unmet ([#12658](https://github.com/elementor/elementor/issues/12658))
* Fix: Default Generic Fonts are not printed along with a global value ([#12410](https://github.com/elementor/elementor/issues/12410))
* Fix: Remove source map comment to prevent 404 console warning ([Topic](https://wordpress.org/support/topic/issue-with-picker-js-map))
* Fix: Buttons are cropped when applying some hover animations to Social Icons widget
* Fix: Custom Panel tab not working when added to Page Settings
* Fix: Globals Dropdown popover is not aligned to the picker when panel is wide
* Fix: Popups are missing from "Edit With Elementor" top admin-bar list
* Fix: Global Color panel UI glitch on upgrade to v3.0.x when user has RGBA values
* Fix: Can't upload SVG files to Favicon control in Site Settings

= 3.0.11 - 2020-09-30 =
* Tweak: Added "Loading" state for Global controls to reflect data loading state
* Fix: Inaccurate height in Divider widget ([#12569](https://github.com/elementor/elementor/issues/12569), [#12630](https://github.com/elementor/elementor/issues/12630))
* Fix: Global Colors and Fonts are not being generated on non Elementor pages ([#12637](https://github.com/elementor/elementor/issues/12637))
* Fix: Inactive "Delicious" social network from Social Icons widget
* Fix: Can't restore the Site Settings document revisions
* Fix: Creating a new Global Color or Font won't reflect in other dropdowns until the controls section init
* Fix: Selecting a new Global Color or Font won't be indicated in the dropdown until the controls section init

= 3.0.10 - 2020-09-23 =
* Fix: `space_between_widgets` is missing on upgrade in some cases ([#12298](https://github.com/elementor/elementor/issues/12298))
* Fix: Global Color does not apply to some patterns in Divider widget ([#12501](https://github.com/elementor/elementor/issues/12501))
* Fix: Users with "Access to edit content only" aren't able to access the Editor ([#12521](https://github.com/elementor/elementor/issues/12521))
* Fix: If the default page layout is set to "Canvas" Headers and Footers cannot be edited ([#12509](https://github.com/elementor/elementor/issues/12509))
* Fix: Global Color and Fonts that were deleted might cause style removal from the same element
* Fix: Opacity indication is missing in the Global Color control dropdown and in the creation prompt
* Fix: Wrong placement of "Add New" section in a Popup when using Optimized DOM mode
* Fix: "Edit with Elementor" button is not in the correct location
* Fix: Global Dropdown scrollbar has redundant border in Chromium based browsers
* Fix: Entrance animation isn't working on edge cases in Tabs widget

= 3.0.9 - 2020-09-17 =
* Fix: Kit settings get deleted when modifying the Site Title or Tagline in WordPress Settings and Customizer screens ([#12540](https://github.com/elementor/elementor/issues/12540), [#12538](https://github.com/elementor/elementor/issues/12538), [#12562](https://github.com/elementor/elementor/issues/12562))

= 3.0.8.1 - 2020-09-14 =
* Fix: Reverted Shape Dividers are in front other elements in Chrome 85 fix due to display glitches ([#12393](https://github.com/elementor/elementor/issues/12393))
* Fix: Auto Columns control setting causes layout to be displayed in one row in a Social icons ([Topic](https://wordpress.org/support/topic/sloppy-updates/), [#12519](https://github.com/elementor/elementor/issues/12519))

= 3.0.8 - 2020-09-14 =
* Fix: Additional cases of Global Style inconsistencies in Editor and Frontend ([#12363](https://github.com/elementor/elementor/issues/12363))
* Fix: Edit with Elementor button is missing from the admin top bar in some cases since WordPress 5.4 ([#11728](https://github.com/elementor/elementor/issues/11728), [#12175](https://github.com/elementor/elementor/issues/12175))
* Fix: Unexpected columns view when Inner Section is muted ([#12376](https://github.com/elementor/elementor/issues/12376))
* Fix: Some Shape Dividers are in front other elements in Chrome 85 ([#12393](https://github.com/elementor/elementor/issues/12393))
* Fix: JS error `elementorCommon` is undefined ([#12323](https://github.com/elementor/elementor/issues/12323), Props [@shimondoodkin](https://github.com/shimondoodkin))
* Fix: Site description doesn't get updated from the 'Customizer' screen
* Fix: When Global values aren't available don't return an empty object
* Fix: Message After Submit RTL icon spacing glitch
* Fix: Select2 control dimensions adjustments

= 3.0.7 - 2020-09-09 =
* Fix: Additional cases of Global Style inconsistencies in Editor and Frontend ([#12363](https://github.com/elementor/elementor/issues/12363))
* Fix: Plugins conflict on non-admin login to the dashboard ([#12383](https://github.com/elementor/elementor/issues/12383), [#12388](https://github.com/elementor/elementor/issues/12388))
* Fix: PHP error undefined method `add_repeater_row` caused data updater issues and server overload ([#12305](https://github.com/elementor/elementor/issues/12305))
* Fix: Global Colors and Fonts not being saved when created in Site Settings ([#12272](https://github.com/elementor/elementor/issues/12272))
* Fix: Shared link is attached to post text in Twitter Share Button widget (Props [@LensDigitalUK](https://github.com/LensDigitalUK))
* Fix: "No route was found matching the URL and request method" error when using plain permalinks
* Fix: Site favicon that was set from WordPress customizer is missing in Site Identity screen

= 3.0.6 - 2020-09-06 =
* Tweak: Added dismiss button for data updater notices
* Fix: Global Colors and Fonts are missing on front-end ([#12363](https://github.com/elementor/elementor/issues/12363))
* Fix: Database update script causes unexpected errors ([#12305](https://github.com/elementor/elementor/issues/12305))
* Fix: Custom Breakpoints stopped working since v3.0 ([#12320](https://github.com/elementor/elementor/issues/12320))
* Fix: Shortcuts for Navigator is not correct ([#12365](https://github.com/elementor/elementor/issues/12365))
* Fix: Can't close a Navigator since v3.0 ([#11836](https://github.com/elementor/elementor/issues/11836))
* Fix: Select field dropdown caret visibility issue in edge cases in Form widget ([#12053](https://github.com/elementor/elementor/issues/12053))
* Fix: Missing iFrame `title` attribute in Google Maps widget ([#9955](https://github.com/elementor/elementor/issues/9955))
* Fix: Missing default size unit selection in default Global Font properties `font-size` and `line-height` controls
* Fix: Toggle widget title style missing in Editor
* Fix: Exiting Site Settings menu after accessing it from "Manage Global Fonts/Colors" Global dropdown menu throws a JS error

= 3.0.5 - 2020-08-31 =
* Fix: Motion Effects not working when assigned to a column and throws JS error when DOM optimization is disabled ([#12299](https://github.com/elementor/elementor/issues/12299), [#12275](https://github.com/elementor/elementor/issues/12275))
* Fix: Elements behave unexpectedly in the Editor when a custom repeater control is added to Column and Section elements ([#12275](https://github.com/elementor/elementor/issues/12275),  [#12242](https://github.com/elementor/elementor/issues/12242))
* Fix: Glitches in migration script for Gap Between Widgets global setting ([#12298](https://github.com/elementor/elementor/issues/12298))
* Fix: Console error `elementorCommon is undefined` (Props [@shimondoodkin](https://github.com/shimondoodkin))
* Fix: Missing translation string for 'color_picker' in the Color Picker title
* Fix: Revisions won't get updated to the correct one

= 3.0.4 - 2020-08-30 =
* Tweak: Added Skype and Viber to the allowed URI protocols ([#11619](https://github.com/elementor/elementor/issues/11619))
* Tweak: Replaced WordPress "Learn More" links with dynamic links for better control over time ([#12312](https://github.com/elementor/elementor/issues/12312))
* Fix: Global Colors and Fonts inconsistencies between Editor and Frontend ([#12245](https://github.com/elementor/elementor/issues/12245), [#12235](https://github.com/elementor/elementor/issues/12235), [#12303](https://github.com/elementor/elementor/issues/12303), [#12249](https://github.com/elementor/elementor/issues/12249))
* Fix: Global dropdown is not available for all users
* Fix: Removed unused strings

= 3.0.3 - 2020-08-27 =
* Tweak: Added option to re-migrate Global Colors and Fonts from earlier versions
* Fix: Repeater controls doesn't work in some edge cases
* Fix: Load Theme Builder with relevant UI theme
* Fix: Dialog texts are not center aligned in Theme Builder
* Fix: Changes are not reflecting on frontend when CSS Print Method is set to Internal Embedding
* Fix: Icon List widget does not use render attribute (Props [@ibndawood](https://github.com/ibndawood))

= 3.0.2 - 2020-08-26 =
* Tweak: Added Select2 And File Import Components to Theme Builder application
* Tweak: Added default values as placeholders to the number inputs in the Breakpoints settings section
* Tweak: Minor UI tweaks to Globals dropdown
* Tweak: Updated video tutorials in Theme Builder
* Fix: Column & Section Elements fires `elementor/element/after_add_attributes` early ([#12185](https://github.com/elementor/elementor/issues/12185), Props [@ibndawood](https://github.com/ibndawood))
* Fix: `hexdec()` returns a deprecation notice ([#12161](https://github.com/elementor/elementor/issues/12161))
* Fix: Repeater controls issue after the moving to `container.repeaters` ([#12239](https://github.com/elementor/elementor/issues/12239), [#12221](https://github.com/elementor/elementor/issues/12221))
* Fix: Column Widget Space is not working when DOM Improvement is disabled ([#12256](https://github.com/elementor/elementor/issues/12256), [Topic](https://wordpress.org/support/topic/widgets-space-doesnt-work-after-updating-to-3-0/))
* Fix: Resetting a style throws JS errors
* Fix: Repeater controls backwards compatibility
* Fix: Global Color dropdown shows Primary as marked when a custom value is added
* Fix: Adding new Global Fonts won't be added to the dropdown list until page reloads
* Fix: Clicking on the Theme Builder Logo reopens it
* Fix: Finder gets loaded in preview iframes

= 3.0.1 - 2020-08-25 =
* Tweak: Changed "Exit to Dashboard" and "View Page" panel menu links to use `<a>` tag
* Fix: `set-error-handler` conflict with 3rd party addons that uses REST-API ([#12219](https://github.com/elementor/elementor/issues/12219))
* Fix: Conflict between responsive options and the Navigator "Hide" option ([#12123](https://github.com/elementor/elementor/issues/12123))
* Fix: Radio inputs layout breaks when using Multi-Step Form
* Fix: Global typography popover glitch in RTL websites

= 3.0.0 - 2020-08-23 =
* New: Introducing Site Settings - manage your entire site within the Editor
* New: Meet Global Fonts - the smartest way to manage your text styles ([#1553](https://github.com/elementor/elementor/issues/1553), [#1863](https://github.com/elementor/elementor/issues/1863))
* New: Meet Global Colors - manage your site colors more consistently ([#1553](https://github.com/elementor/elementor/issues/1553), [#1324](https://github.com/elementor/elementor/issues/1324))
* Tweak: Improved Elementor's frontend dynamic CSS rendering performance significantly ([#8053](https://github.com/elementor/elementor/issues/8053))
* Tweak: Removed `.elementor-inner`, `.elementor-row` and `.elementor-column-wrap` from DOM output to improve performance (#7351, #7817, Developers Blog Post - https://developers.elementor.com/dom-improvements-ahead-html-wrappers-removal-from-v3-0/)
* Tweak: Added an option to set columns in Social Icons widget ([#11295](https://github.com/elementor/elementor/issues/11295))
* Tweak: Added Default Page Layout to Global Layout Settings ([#4925](https://github.com/elementor/elementor/issues/4925))
* Tweak: Made 'z-index' control responsive in widgets Advanced tab ([#10918](https://github.com/elementor/elementor/issues/10918))
* Tweak: Added `rem` unit support for Padding and Margins controls ([#2810](https://github.com/elementor/elementor/issues/2810))
* Tweak: Added control for setting link width in Icon List widget ([#11945](https://github.com/elementor/elementor/issues/11945))
* Tweak: Added image Border Radius control to Image Box widget ([#9301](https://github.com/elementor/elementor/issues/9301))
* Tweak: Added more Google Fonts subsets ([#1630](https://github.com/elementor/elementor/issues/1630), [#1915](https://github.com/elementor/elementor/issues/1915), [#8186](https://github.com/elementor/elementor/issues/8186), Props [@andrejm](https://github.com/andrejm))
* Tweak: Improved Lightbox accessibility (Props [@ramiy](https://github.com/ramiy))
* Tweak: Added "Height" & "Object Fit" controls to Image Widget ([#10962](https://github.com/elementor/elementor/issues/10962), Props [@ramiy](https://github.com/ramiy))
* Tweak: Added a filter for allowing adding Google Fonts subsets (Props [@andrejm](https://github.com/andrejm))
* Tweak: Changed Elementor grid to work with CSS Variables ([Developers Blog Post](https://developers.elementor.com/elementor-dropping-support-ie/))
* Tweak: Updated `eicons` library to `v5.9.1`
* Tweak: Updated `e-gallery` library to `v1.2.0`
* Tweak: Updated Dialog library to `v4.8.1`
* Tweak: Updated Google Fonts list to 07/2020
* Tweak: Added lazyload to Lightbox slideshow images
* Tweak: Removed unused `elementor-edit-area-preview` class
* Tweak: Adding support to Vimeo external links structure
* Tweak: Improved Masonry layout mechanism in Gallery widget
* Tweak: Allow using Repeater control in page settings
* Tweak: Save all Global settings to the Kit entity
* Tweak: Converted schemes to Global variables
* Tweak: Updated WeChat and Weibo social networks colors in Social Icons widget
* Tweak: Added font-family property to text-area control for better readability
* Tweak: Migrated Elementor Style settings from WP dashboard to the Global Settings menu
* Tweak: Added real-time JS handling to prevent redundant renders in Image Carousel widget and Background Slideshow
* Tweak: Moved "Stretch Section" control to the end of layout settings in Section element
* Tweak: Color tweaks to editor panel elements
* Tweak: Minor UI improvements in the editor panel
* Tweak: Added backup warning before major version plugin upgrade
* Tweak: Improved dialog a11y to support more accessible lightbox close button
* Tweak: Added infrastructure support for the new Theme Builder
* Tweak: Added an option to set the mobile browser header color in supported devices
* Tweak: Changed panel behavior when switching document to prevent laggy behavior
* Tweak: Added Improved DOM Output option in Elementor settings to allow legacy mode of HTML DOM prior to v3.0
* Tweak: Removed v2.9 reference from the Dynamic promotions
* Fix: Image block alignment broken by Elementor general figure styling ([#11906](https://github.com/elementor/elementor/issues/11906), [#9259](https://github.com/elementor/elementor/issues/9259), [#7331](https://github.com/elementor/elementor/issues/7331))
* Fix: Elementor posts aren't properly imported with WordPress Importer v0.7 ([#11466](https://github.com/elementor/elementor/issues/11466), [#10744](https://github.com/elementor/elementor/issues/10744), [#11927](https://github.com/elementor/elementor/issues/11927))
* Fix: Divider can't use `EM` font-size unit in Divider widget ([#11352](https://github.com/elementor/elementor/issues/11352))
* Fix: Theme Style overrides Icon List items typography values when list items are links in Icon List widget ([#12021](https://github.com/elementor/elementor/issues/12021))
* Fix: Responsive glitch in Range control ([#11233](https://github.com/elementor/elementor/issues/11233))
* Fix: Multiple repeater controls in the same panel causes JS errors ([#11523](https://github.com/elementor/elementor/issues/11523))
* Fix: Multiple repeater control types in same panel glitches
* Fix: Dark mode UI glitches in Display Conditions modal
* Fix: The wrong video being opened in Media Carousel widget
* Fix: Dark mode UI glitch in Display conditions modal
* Fix: Connect issues when using non-latin character domains
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v3-0-planned-deprecations/)

= 2.9.14 - 2020-07-21 =
* Tweak: Added compatibility with WordPress v5.5 ([#11820](https://github.com/elementor/elementor/issues/11820), [#11830](https://github.com/elementor/elementor/issues/11830))
* Fix: Elementor posts aren't properly imported with WordPress Importer v0.7 ([#11466](https://github.com/elementor/elementor/issues/11466), [#10744](https://github.com/elementor/elementor/issues/10744))
* Fix: Added sanitization to post titles in WordPress dashboard for better security

= 2.9.13 - 2020-06-23 =
* Fix: Duplicated Hidden type form fields inherited required attribute in Form widget ([#11578](https://github.com/elementor/elementor/issues/11578))
* Fix: Select2 control width glitch

= 2.9.12 - 2020-06-14 =
* Fix: Dynamic default value not working in Form widget ([#11578](https://github.com/elementor/elementor/issues/11578), [#11609](https://github.com/elementor/elementor/issues/11609))
* Fix: Dark mode glitches in Form Step items ([#11579](https://github.com/elementor/elementor/issues/11579))
* Fix: Unfiltered files upload not working when enabled from WordPress dashboard (Props [@jrutheiser](https://github.com/jrutheiser))
* Fix: File upload control has text cursor instead of pointer cursor
* Fix: Dark mode glitches in File upload control
* Fix: Clear icon dark mode glitch in the widgets search box

= 2.9.11 - 2020-06-02 =
* Fix: Several URI protocols removed from links ([#11518](https://github.com/elementor/elementor/issues/11518))

= 2.9.10 - 2020-06-01 =
* Tweak: Updated `eicons` library to v5.7.0
* Tweak: Added infrastructure improvements to Repeater control to support upcoming versions
* Fix: Popup action links triggers 404 error ([#11104](https://github.com/elementor/elementor/issues/11104))
* Fix: Removed source map comment so browsers don't look for source map in Swiper library ([#10764](https://github.com/elementor/elementor/issues/10764))
* Fix: Elementor posts aren't properly imported with WordPress Importer v0.7 ([#11466](https://github.com/elementor/elementor/issues/11466), [#10744](https://github.com/elementor/elementor/issues/10744))
* Fix: Theme Style overrides link settings in some cases ([#11462](https://github.com/elementor/elementor/issues/11462))
* Fix: UI glitch in Media controls
* Fix: Hardened sanitization in Custom Link Attributes and in URL control to avoid security issues

= 2.9.9 - 2020-05-24 =
* Tweak: Added 'Learn More' link to URL Custom Attributes Description
* Tweak: Added real-time JS preview handling in Image Carousel widget
* Tweak: Added File upload control UI
* Tweak: Added support for processing Envato Template Kit ZIP files
* Tweak: Allow disabling a repeater item sorting option by adding a class
* Tweak: Adjusted CSS selectors to be more generic in Forms
* Tweak: Updated content in Getting Started screen
* Tweak: Added JSON files support to files upload handler
* Tweak: Changed SVG Uploads prompt message to Unfiltered Files
* Tweak: Added an option for repeater item to disable itself
* Tweak: Removed temporary code from Page Settings
* Tweak: Added infrastructure improvements to Repeater control to support upcoming versions
* Fix: Default line-height value in Heading widget is overriding line-height setting in Theme Style ([#10501](https://github.com/elementor/elementor/issues/10501), [#10649](https://github.com/elementor/elementor/issues/10649))
* Fix: Dynamic settings are not available in frontend JS in edge cases
* Fix: Connectivity issues in Connect module
* Fix: Removed source map comment so browsers don't look for source map in Swiper library ([#10764](https://github.com/elementor/elementor/issues/10764))
* Fix: Render style on repeater item change in Page Settings
* Fix: Added sanitization to Custom Link Attributes control to avoid security issue
* Fix: Added sanitization to URL control to avoid security issue

= 2.9.8 - 2020-04-21 =
* Tweak: Added `.webp` file extension support to Elementor Lightbox
* Fix: Added missing font-family for `elementor-button` class to avoid system font ([#11166](https://github.com/elementor/elementor/issues/11166))
* Fix: Site Part without any content has no height and not accessible in some cases
* Fix: PHP notice in Connect module
* Fix: Navigator resize action is not available in edge cases
* Fix: SVG sanitizer `href` attribute for better security
* Fix: Hardened user role that is allowed to upload unfiltered files for better security

= 2.9.7 - 2020-03-25 =
* Tweak: Added compatibility with WordPress v5.4 ([#10745](https://github.com/elementor/elementor/issues/10745))
* Tweak: Show Typography section to everyone in Section, Column and Inner Section elements ([#10592](https://github.com/elementor/elementor/issues/10592))
* Fix: Custom Attributes section missing in Advanced tab ([#10881](https://github.com/elementor/elementor/issues/10881))
* Fix: Title color & Typography settings are being overridden by Theme Style in Accordion and Toggle widget ([#10900](https://github.com/elementor/elementor/issues/10900))
* Fix: Merged "Play On Mobile" string for better i18n in Video widget (Props [@pedro-mendonca](https://github.com/pedro-mendonca), [#10315](https://github.com/elementor/elementor/issues/10315))
* Fix: Whatsapp share link does not escape HTML entities ([#10746](https://github.com/elementor/elementor/issues/10746))
* Fix: Invalid request error conflict with Yoast plugin and Share Buttons widget ([#10746](https://github.com/elementor/elementor/issues/10746))
* Fix: FontAwesome Pro icons not loading in edge cases
* Fix: Missing Dynamic indication in Range control ([#10835](https://github.com/elementor/elementor/issues/10835))
* Fix: Carousel spins multiple times when loading in Image Carousel widget
* Fix: Theme style overrides Lightbox Share links color
* Fix: Loading spinner placement glitch in URL control
* Fix: Missing separator control in Icon widget
* Fix: Dynamic Tags with the character “0” are not visible on frontend
* Fix: Dashboard menu UI glitch for non-Administrator role users

= 2.9.6 - 2020-03-12 =
* Fix: Removed redundant dynamic capabilities from all of color controls inside Group controls in Theme Style
* Fix: Global widget is clickable when editing Theme Style
* Fix: Dropcap not working immediately in Text Editor widget
* Security Fix: Enable Safe Mode only for `activate_plugins` capability

= 2.9.5 - 2020-03-09 =
* Tweak: Added new parameter to Swiper wrapper to limit breakpoint intervention ([#10525](https://github.com/elementor/elementor/issues/10525))
* Tweak: Updated E-Gallery library to v1.1.2
* Tweak: Show descriptive Pro Promotions only for Administrator role
* Fix: Inconsistent responsive display issue in the Editor when using Custom Breakpoints ([#10540](https://github.com/elementor/elementor/issues/10540))
* Fix: Editor not loading when the Library launches on load ([Topic](https://wordpress.org/support/topic/editor-broken-in-2-9-4/))
* Fix: Gallery Columns does not update immediately in the preview when in Masonry layout
* Fix: Navigator does not update according to the current site part being edited
* Fix: Hidden responsive indicator missing in editor
* Fix: Tabs widget title color not working on mobile

= 2.9.4 - 2020-03-04 =
* Tweak: Added support for Elementor Pro v2.9.0
* Tweak: Updated Swiper.js library to v5.3.6
* Tweak: Added version expected release date for Theme Style Custom CSS and Dynamic Color promotions
* Fix: Warning in `/base/controls-stack.php` when a 3rd party controls doesn’t have a default dynamic value ([#10578](https://github.com/elementor/elementor/issues/10578))
* Fix: Alignment issue on drag when not set to Infinite Loop in Image Carousel widget
* Fix: Title Color change both title and link color in Accordion widget

= 2.9.3 - 2020-02-26 =
* Tweak: Don't show the "Have a look" link after successful Theme Style publish
* Tweak: Show Database update notice only for users with `update_plugins` capability
* Tweak: Added Custom CSS promotion section in Theme Style
* Tweak: Added compatibility for Pro v2.9 features
* Fix: Warning in `/base/controls-stack.php` when a control doesn't have a dynamic capability ([#10578](https://github.com/elementor/elementor/issues/10578))
* Fix: Beta Tester sign up modal pops up multiple times after signing up
* Fix: `<a>` Links appear with `underline` text-decoration
* Fix: Shortcode doesn't render when exiting inline edit
* Fix: Whatsapp not sharing the link of the page in Share buttons widget
* Fix: Whatsapp share doesn't include line breaks between the page title and the URL in Share buttons widget
* Fix: Box Shadow doesn't display properly in Toggle widget
* Fix: Custom Link Attributes are not being applied in Icon Box widget
* Fix: Added sanitization to Custom Link Attributes control to avoid security issue (Props [@yzy9951](https://github.com/yzy9951))

= 2.9.2 - 2020-02-16 =
* Fix: Responsive issue when using Slides to Show control in carousel widgets ([#10540](https://github.com/elementor/elementor/issues/10540))
* Fix: Title and Description causes JS error in Lightbox in edge cases

= 2.9.1 - 2020-02-13 =
* Tweak: Added Native WordPress Gallery support for lightbox pagination
* Tweak: Updated eicons library to v5.6.2
* Fix: Popup builder not accessible when Pro plugin is active ([#10502](https://github.com/elementor/elementor/issues/10502))
* Fix: Pagination not working when one item is visible in carousel widgets ([#10508](https://github.com/elementor/elementor/issues/10508), [Topic](https://wordpress.org/support/topic/carrousel-is-blocked-with-version-2-9/))
* Fix: Empty panel after dragging a widget in edge cases
* Fix: Theme Style button being added in Theme Builder documents
* Fix: Google fonts enqueuing issue
* Fix: Preview mode not changing when dismissing the exit prompt
* Fix: Kit showing up in Finder results
* Fix: Missing preview icon in Finder
* Fix: Missing preview icon in Theme Builder footer

= 2.9.0 - 2020-02-10 =
* New: Introducing Theme Style - set your default HTML tags styling using Elementor ([#534](https://github.com/elementor/elementor/issues/534))
* New: Added Custom Link Attributes to Link Options to allow adding custom attributes to `<a>` tags ([#5716](https://github.com/elementor/elementor/issues/5716), [#3642](https://github.com/elementor/elementor/issues/3642), [#9225](https://github.com/elementor/elementor/issues/9225), [#9079](https://github.com/elementor/elementor/issues/9079))
* Tweak: Added Title & Description to Lightbox ([#9826](https://github.com/elementor/elementor/issues/9826), [#2502](https://github.com/elementor/elementor/issues/2502))
* Tweak: Added Full Screen option in Lightbox ([#5260](https://github.com/elementor/elementor/issues/5260))
* Tweak: Added direct sharing options in Lightbox ([#2502](https://github.com/elementor/elementor/issues/2502))
* Tweak: Added Zoom option in Lightbox ([#9399](https://github.com/elementor/elementor/issues/9399))
* Tweak: Moved `social-share.js` to Core instead of Pro ([Developers Blog Post](https://developers.elementor.com/migration-of-modules-to-core-in-2-9/))
* Tweak: Moved `link-actions.js` to Core instead of Pro and changed its name to `url-actions.js` ([Developers Blog Post](https://developers.elementor.com/migration-of-modules-to-core-in-2-9/))
* Tweak: Added dynamic capabilities to Title control in Toggle widget ([#10070](https://github.com/elementor/elementor/issues/10070))
* Tweak: Disabled autocomplete feature from the Color Picker ([#10030](https://github.com/elementor/elementor/issues/10030))
* Tweak: Added Mix social network to recommended tab in Social Icons widget ([#10099](https://github.com/elementor/elementor/issues/10099))
* Tweak: Removed StumbleUpon social network from recommended tab in Social Icons widget ([#10099](https://github.com/elementor/elementor/issues/10099))
* Tweak: Added `i18n` function to translation string in DB upgrades manager (Props [@ramiy](https://github.com/ramiy))
* Tweak: Added `vh` and `em` size units to Max Width control in Image widget ([#5376](https://github.com/elementor/elementor/issues/5376))
* Tweak: Added `vh` and `em` size units to Spacer widget
* Tweak: Exposed external API for Swiper instances ([Developers Blog Post](https://developers.elementor.com/expose-swiper-in-elementor/))
* Tweak: Updated Pickr library to v1.5.0
* Tweak: Updated Dialogs Manager library to v4.7.5
* Tweak: Added URL hash routing capability to Lightbox
* Tweak: Refactored panel controls UI
* Tweak: Updated Font Awesome library to v5.12.0
* Tweak: Converted `add_render_attributes` for custom links to `add_link_attributes()`
* Tweak: Added a mixin for `absolute-center` positioning
* Tweak: Updated `eicons` library to v5.6.1
* Tweak: Changed `eicon-zoom-in` icon to `eicon-zoom-in-bold` in Template Library
* Tweak: Allow background image and color being displayed in empty column
* Tweak: Converted `editor-base.js` to ES6
* Tweak: Added `featured_image` URL to frontend config
* Tweak: Made minor UI tweaks to "Recalc" button in System Info
* Tweak: Added inline editing capability to Progress Bar widget Title
* Tweak: Removed redundant display conditions from Drop Cap section in Text Editor widget
* Tweak: Remove redundant `label_block` parameters from control instances
* Tweak: Added 'alt' property when using SVG icons in Social Icons widget
* Tweak: Added translators comment explaining the `%s` placeholder in Video Provider control
* Tweak: Added Pro features descriptive promotions to Editor panel
* Tweak: Added Dynamic capabilities to be active by default in WYSIWYG Control
* Tweak: Changed the position of all panel tooltips to appear above the controls
* Tweak: Replaced nerd icons with new Elementor emojis
* Tweak: Added descriptive message suggesting backing up the Database before upgrading to Font Awesome 5
* Tweak: Changed panel arrow icon to chevron
* Tweak: Deprecated Typography section in Section & Column elements for users who didn't place values
* Fix: `playsinline` attribute missing when setting self-hosted video to Play on Mobile in Video widget ([#9892](https://github.com/elementor/elementor/issues/9892))
* Fix: Beta sign-up modal closes unexpectedly ([#10355](https://github.com/elementor/elementor/issues/10355))
* Fix: "Slides to Show" option doesn't work on Tablet view in Image Carousel widget ([#10227](https://github.com/elementor/elementor/issues/10227))
* Fix: Previous and Next buttons are not displaying on Safari in Media Carousel's Cube mode ([#9725](https://github.com/elementor/elementor/issues/9725))
* Fix: Form style being reset adding MailChimp/Getresponse action in Form widget ([#9313](https://github.com/elementor/elementor/issues/9313))
* Fix: Post Title and Archive Excerpt wrong values in edge cases
* Fix: Wrong `<select>` field width in "Add New" dialog
* Fix: Gap created by the "Add Media" button in Text Editor widget
* Fix: "Insert Template" library button glitch
* Fix: WordPress Media 'Image Details' fields are 100% width instead of contained
* Fix: Pause on hover can't be disabled in Image Carousel widget
* Fix: Aligned Dark UI Theme gradients in panel and library tabs
* Fix: Category Select2 dropdown stays above the view on scroll in Template Library modal
* Deprecated: See all deprecations to this version in our ([Developers Deprecations Post](https://developers.elementor.com/v2-9-0-planned-deprecations/))

= 2.8.5 - 2020-01-27 =
* Fix: Handle corrupted DB log
* Fix: Template inserted upside down in edge cases
* Fix: Added data sanitization on System Info
* Fix: Error when trying to edit widgets with `inline` Icons control before Font Awesome 5 migration

= 2.8.4 - 2020-01-19 =
* Tweak: Changed default color in Color Picker
* Fix: Curved edge with background color in Toggle widget ([#5156](https://github.com/elementor/elementor/issues/5156))
* Fix: Added sanitize data on create new template
* Fix: Inconsistent panel view when deleting all content
* Fix: Empty Typography control after Reset Style
* Fix: Inline sizing delay when position is set to Absolute/Fixed in Image widget
* Fix: Wrong sizing on undo/redo to Section element columns

= 2.8.3 - 2020-01-01 =
* Tweak: Improved accessibility to "Go Pro" menu item ([#9021](https://github.com/elementor/elementor/issues/9021))
* Tweak: Added new CLI command - User Connect
* Tweak: Updated Recommended Icons tab in Toggle and Accordion widgets
* Fix: Placement glitch when dragging a widget to a new section ([#9954](https://github.com/elementor/elementor/issues/9954))
* Fix: Controls popover closes when picking a color in it
* Fix: Dark mode style glitches in edge cases
* Fix: Correct calculation of usage data
* Fix: Pasting style of one Section to other changes the structure
* Fix: Long text overflow issue in Divider widget
* Fix: Scroll bars visible when not needed in Icon Library

= 2.8.2 - 2019-12-16 =
* Tweak: Improved hidden responsive elements visibility
* Fix: Autoplay not working in Image Carousel widget ([#9872](https://github.com/elementor/elementor/issues/9872))
* Fix: Logger fatal error on upgrade ([#9872](https://github.com/elementor/elementor/issues/9872))
* Fix: Negative custom shapes in Editor ([#9850](https://github.com/elementor/elementor/issues/9850))
* Fix: Render correct widget data in Global widgets
* Fix: `0` value in Dynamic Tags causes Undefined Index error

= 2.8.1 - 2019-12-11 =
* Fix: Style pasting issue on Columns ([#9811](https://github.com/elementor/elementor/issues/9811))
* Fix: Column duplication not working for Inner Section widget ([#9824](https://github.com/elementor/elementor/issues/9824))
* Fix: Redundant renders when changing colors in Color control
* Fix: Dark Mode glitches in WordPress widgets ([#9829](https://github.com/elementor/elementor/issues/9829))
* Fix: Repeater rows doesn't have `_id` in edge cases
* Fix: Connect issue for Multisite users
* Fix: Dynamic tag fallback does not handle '0' character

= 2.8.0 - 2019-12-09 =
* New: Introducing new Color Picker control with Saved Colors built-in ([#6968](https://github.com/elementor/elementor/issues/6968), [#8982](https://github.com/elementor/elementor/issues/8982), [Developers Blog Post](https://developers.elementor.com/elementor-2-8-new-color-picker/]))
* New: Introducing Dark Mode UI theme ([#9263](https://github.com/elementor/elementor/issues/9263), [#8249](https://github.com/elementor/elementor/issues/8249), [#9125](https://github.com/elementor/elementor/issues/9125))
* New: User Preferences section in Editor Panel menu
* Tweak: Better responsive visibility indication ([#8020](https://github.com/elementor/elementor/issues/8020), [#4775](https://github.com/elementor/elementor/issues/4775))
* Tweak: Added `background-size` and `background-position` controls to Background Slideshow ([#9199](https://github.com/elementor/elementor/issues/9199))
* Tweak: Removed Google+ network from "Recommended" tab in Social Icons widget ([#9393](https://github.com/elementor/elementor/issues/9393))
* Tweak: Replaced default Google+ network icon with YouTube icon in Social Icons widget
* Tweak: Added dynamic capabilities to alert widget ([#9436](https://github.com/elementor/elementor/issues/9436))
* Tweak: Added "Play on Mobile" option to allow iOS autoplay to Video widget ([#3442](https://github.com/elementor/elementor/issues/3442))
* Tweak: Added inline skin for Icon control
* Tweak: Added `elementor/settings/controls/checkbox_list_cpt/post_type_objects` filter to control over displayed CPT in WordPress Dashboard UI (Props [@sc0ttkclark](https://github.com/sc0ttkclark))
* Tweak: Moved "Editing Handles" and "Lightbox in editor" options to the new "Preferences" section
* Tweak: Added `playsinline` attribute to Vimeo and YouTube sources to allow autoplay on iOS devices in Background Video
* Tweak: Added Documents count to System Info
* Tweak: Added global utility function for adding link attributes in Button widget
* Tweak: Re-designed responsive mode switcher control
* Tweak: Re-designed alerts and notices in the Editor Panel
* Tweak: Improved styling of checkboxes and radio inputs
* Tweak: Changed "Custom Positioning" section label to "Positioning"
* Tweak: Allow access to Templates Library for connected users only
* Tweak: Changed icon control skin to "Inline" in Toggle and Accordion widgets
* Tweak: Added Icon Manager "Recommended" tab in Toggle and Accordion widgets
* Tweak: Changed all occurrences of `text-overflow: ellipsis` to a single `mixin`
* Tweak: Added "Pause On Interaction" option to Image Carousel widget
* Tweak: Updated `e-icons` library to v5.5.0
* Tweak: Updated `e-gallery` library to v1.1.0
* Tweak: Dropped Support for WP v4.9, now supporting WP v5.0+
* Tweak: Dropped delete revision option in Revisions Panel since WordPress v5.3 ([Topic](http://core.trac.wordpress.org/ticket/43709))
* Tweak: Added compatibility with WordPress v5.3
* Fix: Use CSS Flex to align and order button icon ([#561](https://github.com/elementor/elementor/issues/561))
* Fix: Remove Ninja Forms compatibility script which causes server issues ([#8267](https://github.com/elementor/elementor/issues/8267))
* Fix: Added `title` attribute to embedded iframes ([#9374](https://github.com/elementor/elementor/issues/9374), Props [@shipley-dcc](https://github.com/shipley-dcc))
* Fix: Reset style in a button doesn't work on colors ([#8454](https://github.com/elementor/elementor/issues/8454))
* Fix: No fallback when SVG file doesn't exist ([#9481](https://github.com/elementor/elementor/issues/9481))
* Fix: Custom shapes "negative" support fix ([#9761](https://github.com/elementor/elementor/issues/9761))
* Fix : Shapes SVG include PHP Parse Error If the uploaded SVG file contain `<?xml version="1.0" encoding="UTF-8"?>` ([#9693](https://github.com/elementor/elementor/issues/9693), Props [@MarieComet](https://github.com/MarieComet))
* Fix: Added better output escaping in the attributes for checkbox lists in Admin forms (Props [@MarieComet](https://github.com/MarieComet))
* Fix: Adding a link overrides the Title and Description colors in Testimonial widget
* Fix: Removed redundant Editor Panel Spacer in Image Carousel widget
* Fix: "Delete All Content" doesn't close current widget editing options
* Fix: Pause on hover option doesn't work in Image Carousel widget
* Fix: Limit transition duration in Ken Burns CSS only to `transform` property
* Fix: Recommended tab icons don't change when there's more than one `Icons` control per widget
* Fix: Hidden sticky section generates redundant scroll offset when using Anchors
* Fix: Inserting a saved section as a template uses the same `ID`
* Fix: Double rendering of elements in Editor
* Deprecated: See all deprecations to this version in our [Developers Deprecations Post](https://developers.elementor.com/v2-8-0-planned-deprecations/)

= 2.7.6 - 2019-12-08 =
* Fix: Transition Duration option not applying to elements background
* Fix: Hide Beta Testers signup form if the user already subscribed
* Fix: Added HTML escaping to `Admin` class and to System Info

= 2.7.5 - 2019-10-28 =
* Tweak: Changed the "Finder" icon in the Editor panel
* Fix: Added official color support for `linkedin-in` icon in Social Icons widget ([#9298](https://github.com/elementor/elementor/issues/9298))
* Fix: `Ctrl/CMD + Shift + L` not opening Library modal
* Fix: Clear Log button HTML shows in System Info Copy & Paste section
* Fix: Ken burns effect not working on the 1st slide if Infinite Loop option is turned off in Background Slideshow

= 2.7.4 - 2019-10-06 =
* Tweak: Improved filtering animation behavior in Gallery widget
* Fix: Icon custom colors being overridden by styling custom colors in Social Icons widget
* Fix: Added `SVG` custom color support for Social Icons widget
* Fix: Elements not being displayed when it has the value `0`
* Fix: Pagination glitch in image carousel widget
* Fix: Select2 throws JS console error when trying to unset a dropdown value in Templates modal
* Fix: Background Slideshow overrides the Columns' border-radius
* Fix: Tabs not working after previewing a template in Templates modal

= 2.7.3 - 2019-09-24 =
* Tweak: Updated E-gallery library to v1.0.1
* Fix: Images overlap issue in Image Carousel widget ([Topic](https://wordpress.org/support/topic/elementor-image-carousel-2/#post-11943476))
* Fix: Removed extra character in Divider widget ([#9102](https://github.com/elementor/elementor/issues/9102))
* Fix: Console error after resetting "Background Slideshow" gallery
* Fix: Popup templates not visible in My Templates tab on import action
* Fix: Panel spinner issue caused by 3rd party addons when activating Safe Mode
* Fix: Lightbox won't close on background click in Image widget
* Fix: "Need Help" hover UI glitch in RTL
* Fix: SVG icon missing in Icon List widget

= 2.7.2 - 2019-09-16 =
* Fix: Data updater stuck in site with large database ([Topic](https://wordpress.org/support/topic/the-elementor-data-updater-problems-continue/), [Topic](https://wordpress.org/support/topic/v2-7-not-stable/))
* Fix: Added backward compatibility for deprecated hooks in page settings ([#9060](https://github.com/elementor/elementor/issues/9060), [#9080](https://github.com/elementor/elementor/issues/9080))
* Fix: Added support for custom document in `autosave` ([#9070](https://github.com/elementor/elementor/issues/9070))
* Fix: Changed the default `slidesPerGroup` from 2 to 1 in Image Carousel widget

= 2.7.1 - 2019-09-10 =
* Fix: Self Hosted video lightbox closing when clicking on the video ([#8931](https://github.com/elementor/elementor/issues/8931))
* Fix: Downgrade from v2.7.0 causes 500 errors
* Fix: Vertical alignment issue in Safari browser ([#9034](https://github.com/elementor/elementor/issues/9034))
* Fix: `contains` and `!contains` control conditions ([#9043](https://github.com/elementor/elementor/issues/9043))
* Fix: Use background updater for v2.7 script task ([Topic](https://wordpress.org/support/topic/v2-7-not-stable/))
* Fix: Backwards compatibility for previous versions using Divider widget

= 2.7.0 - 2019-09-08 =
* New: Added background slideshow to Section, Column and Inner Section elements ([#2898](https://github.com/elementor/elementor/issues/2898), [#4022](https://github.com/elementor/elementor/issues/4022))
* New: Editor JS API to improve capabilities and stability ([Developers Blog Post](https://developers.elementor.com/elementor-2-7-new-editor-js-api-e/))
* New: Added additional divider styles to Divider widget ([#7418](https://github.com/elementor/elementor/issues/7418))
* Tweak: Background videos now support Vimeo as a source ([#1903](https://github.com/elementor/elementor/issues/1903))
* Tweak: Added the option to add Icon and Text to Divider widget ([#7418](https://github.com/elementor/elementor/issues/7418))
* Tweak: Converted all widgets JS handlers to ES6 classes ([Developers Blog Post](https://developers.elementor.com/building-a-simple-custom-widget-with-javascript/), [Developers Documentation](https://developers.elementor.com/creating-a-new-widget/adding-javascript-to-elementor-widgets/))
* Tweak: Added option to select the version you rollback to in "Version Control" tool
* Tweak: Use `swiper.js` instead of `slick.js` in Image Carousel widget ([Developers Blog Post](https://developers.elementor.com/elementor-2-7-moving-sliders-from-slick-to-swiper/))
* Tweak: Implemented virtual list render in Icons Library to improve performance and stability
* Tweak: Added support of `wp_body_open` in Canvas template
* Tweak: Editor Panel UI tweaks
* Tweak: Updated Gallery control UI
* Tweak: Moved structure control into section panel in Section element
* Tweak: Added option to allow background video to play on mobile devices if device support it
* Tweak: Added more post statuses to show in the Finder ([#8175](https://github.com/elementor/elementor/issues/8175))
* Tweak: Added `Space-around`, `Space-between` and `Space-evenly` to Vertical Align options in Section element
* Tweak: Added filter for additional styles by 3rd party plugins `elementor/divider/styles/additional_styles` to Divider widget
* Tweak: Removed "Need Help?" button from WP widgets
* Tweak: Updated `Eicons` library to v5.4.0
* Fix: Background video plays on mobile devices in some cases (#8782, [Developers Blog Post](https://developers.elementor.com/background-video-in-mobile-mode/))
* Fix: Icon List alignment issue ([#8539](https://github.com/elementor/elementor/issues/8539))
* Fix: Two clicks needed to start Vimeo videos with image overlay
* Fix: Init navigator only once when changing page template
* Fix: Added backwards compatibility for `Controls_Manager::add_tab()` and `\Elementor\Core\Settings\Manager::add_settings_manager()`
* Fix: Carousels navigation arrows causes horizontal scroll when setting to `Outside`
* Fix: Heading widget with the character "0" is not visible on frontend
* Deprecated: Removed old deprecated aliases ([Developers Blog Post](https://developers.elementor.com/v2-7-0-planned-deprecations/))

= 2.6.8 - 2019-08-07 =
* Tweak: Added SVG import/export support to Icons control
* Fix: Empty `caps` while checking user `caps` ([#8732](https://github.com/elementor/elementor/issues/8732))
* Fix: Rotate control not working in Icon widget responsive modes ([#8753](https://github.com/elementor/elementor/issues/8753))
* Fix: Counter widget start number greater than 999
* Fix: Avoid migrating old Icon value if new one exists on import
* Fix: Editor not being loaded when using an Icon from removed Icon Set

= 2.6.7 - 2019-07-30 =
* Tweak: Run `.htaccess` inspection only when needed
* Tweak: UI glitches in `select2` control
* Tweak: `get_style_depends` styles are now loaded in editor preview
* Fix: Avoid false-positive editor loading error ([#8706](https://github.com/elementor/elementor/issues/8706))
* Fix: Disable browser suggestions on panel elements search input ([#6581](https://github.com/elementor/elementor/issues/6581))
* Fix: Contributor role can view other contributors draft and pending posts when Elementor is installed ([#8689](https://github.com/elementor/elementor/issues/8689))
* Fix: Save changed before previewing a revision

= 2.6.6 - 2019-07-23 =
* Fix: WordPress editor is open by default ([#8628](https://github.com/elementor/elementor/issues/8628), [#8647](https://github.com/elementor/elementor/issues/8647))
* Fix: Icon spacing disappeared after upgrading to Font Awesome 5 ([#8609](https://github.com/elementor/elementor/issues/8609))
* Fix: Star icon appears instead of an unset icon in Icon Box widget after upgrading to Font Awesome 5 ([#8596](https://github.com/elementor/elementor/issues/8596))
* Fix: Empty icon library by Using JS files instead of JSON for icon lists
* Fix: `Preview Could Not be Loaded` message displayed if `$_SERVER['SERVER_SOFTWARE']` is empty
* Fix: Navigator Indicators RTL UI glitches

= 2.6.5 - 2019-07-18 =
* Fix: Reverted the changes in Section and Column Vertical Alignment control

= 2.6.4 - 2019-07-17 =
* Tweak: Added CSS variable for admin font family
* Tweak: Updated dialog library
* Tweak: Improved `.htaccess` error message in `Preview Could Not Be Loaded` message
* Fix: `Condition` and `Conditions` not working together ([#8233](https://github.com/elementor/elementor/issues/8233))
* Fix: Pages are always displayed in `draft` status ([#8510](https://github.com/elementor/elementor/issues/8510))
* Fix: Incorrect Absolute position dragging values in RTL ([#7719](https://github.com/elementor/elementor/issues/7719))
* Fix: SVG icon hover color in Button widget

= 2.6.3 - 2019-07-15 =
* Tweak: Improve Icon control preview color for better visibility ([#8530](https://github.com/elementor/elementor/issues/8530))
* Tweak: Cleaning all output buffering in order to enable Gzip

= 2.6.2 - 2019-07-14 =
* Tweak: Make sure activeTab exists in Icon library
* Fix: Star rating widget alignment is not responsive ([#8444](https://github.com/elementor/elementor/issues/8444))
* Fix: Temporarily reverted `application/json` content type to support various server configurations
* Fix: Progress Bar typography backward compatibility
* Fix: Missing `fas` icons in Social Icons widget
* Fix: Navigator Indicators tooltip location
* Fix: Added Icon List backward compatibility on template import

= 2.6.1 - 2019-07-10 =
* Tweak: Added responsive controls to Icon size and Icon rotation in Icon widget
* Fix: Preview Could Not Be Loaded `.htaccess` error if permalink structure not set
* Fix: Added backward compatibility for Icon control
* Fix: UI glitches in the global lightbox modal

= 2.6.0 - 2019-07-09 =
* New: Introducing Icon Manager - the new way to add icons into your page ([#110](https://github.com/elementor/elementor/issues/110))
* New: Introducing SVG file library ([#5570](https://github.com/elementor/elementor/issues/5570))
* New: Replaced Font Awesome 4 with Font Awesome 5 ([#4430](https://github.com/elementor/elementor/issues/4430))
* New: Added Help links to elements for better support flow
* New: Added Navigator indicators for Custom Position ([#2180](https://github.com/elementor/elementor/issues/2180))
* New: Introducing Deprecated Notice control for developers ([Development Doc](https://developers.elementor.com/elementor-controls/deprecated-notice-control/))
* Tweak: Added 9:16 Aspect ratio (vertical video) support for Video widget ([#7051](https://github.com/elementor/elementor/issues/7051))
* Tweak: Set pre-selected attachment when opening the media modal ([#7937](https://github.com/elementor/elementor/issues/7937))
* Tweak: Added more style options for Progress Bar widget ([#3413](https://github.com/elementor/elementor/issues/3413))
* Tweak: Added icon alignment option to icon list ([#8219](https://github.com/elementor/elementor/issues/8219))
* Tweak: Added option to hide columns in Navigator ([#7863](https://github.com/elementor/elementor/issues/7863))
* Tweak: Added vertical alignment option to Image Carousel ([#6963](https://github.com/elementor/elementor/issues/6963))
* Tweak: Added Responsive option for the Content Align in Column ([#2554](https://github.com/elementor/elementor/issues/2554))
* Tweak: Removed `Prevent Scroll` option from Google Maps widget which now defaults in the map origin ([#8244](https://github.com/elementor/elementor/issues/8244))
* Tweak: Reduce-motion accessibility support for CSS animation library ([#7968](https://github.com/elementor/elementor/issues/7968))
* Tweak: Added start & end time for self-hosted background video ([#7941](https://github.com/elementor/elementor/issues/7941))
* Tweak: Added Play once option for background video ([#5129](https://github.com/elementor/elementor/issues/5129))
* Tweak: Added the `playsinline` attribute to autoplay background video on iOS ([#8198](https://github.com/elementor/elementor/issues/8198))
* Tweak: Added `text-shadow` option to Button widget ([#7317](https://github.com/elementor/elementor/issues/7317))
* Tweak: Set `$fonts_to_enqueue` as public to allow manual enqueue of fonts ([#7622](https://github.com/elementor/elementor/issues/7622))
* Tweak: Show dashboard notices for admin role only ([#7304](https://github.com/elementor/elementor/issues/7304))
* Tweak: Added Viber icon for Social Icons widget ([#4430](https://github.com/elementor/elementor/issues/4430))
* Tweak: Added dynamic capabilities to link control in Social Icons widget ([#8097](https://github.com/elementor/elementor/issues/8097))
* Tweak: Added option to pick custom colors per icon in Social Icons widget ([#4430](https://github.com/elementor/elementor/issues/4430))
* Tweak: Added default background color to Social Icons widget
* Tweak: Added opt-in form to Elementor updates for beta testers
* Tweak: Updated E-icons library to v5.3.2
* Tweak: Removed all Font Awesome 4 dependencies
* Tweak: Clear 3rd party cache plugins on every change in Maintenance Mode
* Tweak: Using `application/json` content type in order to avoid double zipping by the server to reduce load panel errors
* Tweak: Added control to support Exit Animations
* Tweak: Added editor notices bar for admin role only
* Tweak: Moved elements PHP handles to JS - resolves double Editing Handles in the Post Content widget
* Tweak: Added custom messages for "Preview could not be loaded" state
* Tweak: Added focus state emphasis to Dimensions control
* Tweak: Redesigned Media control
* Tweak: Use `wp_register_style` to allow widgets to enqueue icon styles
* Fix: Open the correct video when you have multiple images with the same video link in the same Media Carousel ([#8047](https://github.com/elementor/elementor/issues/8047))
* Fix: Added escaping in the template type ([#8435](https://github.com/elementor/elementor/issues/8435))
* Fix: Compression encoding for `gzip` to `brotli` conversions (Props [@pingram3541](https://github.com/pingram3541))
* Fix: Vimeo video lightbox is not playing automatically
* Fix: Backslash was removed from Custom CSS in page settings
* Fix: Set LTR direction only to `input` and `textarea` in RTL view
* Fix: Color Picker UI glitches in RTL view
* Fix: Avoid adding CSS for the first post in archive pages

= 2.5.16 - 2019-05-28 =
* Tweak: Added `reduce-motion` accessibility support for CSS animation library ([#7968](https://github.com/elementor/elementor/issues/7968), Props [@drawcard](https://github.com/drawcard))
* Fix: Tabs Widget compatibility for IE ([#8123](https://github.com/elementor/elementor/issues/8123))
* Fix: Admin dashboard dialog button UI glitch

= 2.5.15 - 2019-05-07 =
* Fix: Param type in doc block is partial ([#7988](https://github.com/elementor/elementor/issues/7988))
* Fix: Paste Style changes widget title in Navigator ([#7931](https://github.com/elementor/elementor/issues/7931))

= 2.5.14 - 2019-04-16 =
* Tweak: Remove default values on update to avoid 413 error ([#7842](https://github.com/elementor/elementor/issues/7842))
* Fix: Load minified `common-modules.js` & `editor-modules.js` instead of full version

= 2.5.13 - 2019-04-10 =
* Fix: Resize viewport not working properly ([#7795](https://github.com/elementor/elementor/issues/7795), [Topic](https://wordpress.org/support/topic/width-is-not-responding-automatically/), [Topic](https://wordpress.org/support/topic/stretch-section-problem/))
* Fix: Remove Shape Divider in the editor if set to `None`

= 2.5.12 - 2019-04-08 =
* Fix: DB updates process not running in edge cases ([#7725](https://github.com/elementor/elementor/issues/7725))
* Fix: Column width issue after Column Gap value changed ([#7718](https://github.com/elementor/elementor/issues/7718))
* Fix: Load minified `frontend-modules.js` instead of full version ([#7082](https://github.com/elementor/elementor/issues/7082))
* Fix: Removed source map reference from minified versions of JS files ([#7082](https://github.com/elementor/elementor/issues/7082))
* Fix: Missing section handles while using `overflow: hidden` option
* Fix: Removed condition from content style in Accordion widget
* Fix: Dialogs close button in RTL layout
* Fix: Self-hosted background video resize issue
* Fix: Dynamic Tag dropdown in RTL
* Fix: Color picker UI glitch

= 2.5.11 - 2019-03-31 =
* Tweak: Removed duplicated grid style from frontend.css
* Fix: Document settings not saving ([#7629](https://github.com/elementor/elementor/issues/7629))
* Fix: Date-Time control triggering unnecessary `edit` event

= 2.5.10 - 2019-03-26 =
* Fix: Preview for static homepage ([#7440](https://github.com/elementor/elementor/issues/7440))
* Fix: Added compatibility for Gutenberg plugin v5.3.0+ ([#7557](https://github.com/elementor/elementor/issues/7557))
* Fix: Slider control not setting correct position on Undo/Redo shortcuts
* Fix: Duplicated shape divider issue in editor view

= 2.5.9 - 2019-03-18 =
* Tweak: Modal style improvements
* Tweak: Added preview debug link to "Preview could not be loaded" message
* Fix: First section delete action not saving ([#7477](https://github.com/elementor/elementor/issues/7477))
* Fix: Compatibility issue with 3rd party plugins ([#7467](https://github.com/elementor/elementor/issues/7467))

= 2.5.8 - 2019-03-15 =
* Fix: Restore `wp_localize_script` for Gutenberg editor

= 2.5.7 - 2019-03-14 =
* Fix: changed `ElementorGutenbergSettings` print order ([#7457](https://github.com/elementor/elementor/issues/7457))

= 2.5.6 - 2019-03-14 =
* Tweak: Minor UI improvement in Slider Control
* Tweak: Added support for range handles in Slider Control
* Fix: Stretch Inner Section in the editor ([#7430](https://github.com/elementor/elementor/issues/7430))
* Fix: Ensure print order of Elementor config JS variables ([#7443](https://github.com/elementor/elementor/issues/7443))
* Fix: Inline elements not working on new pages ([#7433](https://github.com/elementor/elementor/issues/7433))
* Fix: Removed media `id` when attachment is missing

= 2.5.5 - 2019-03-11 =
* Tweak: Support gzipped Elementor Ajax data when GZIP is enabled
* Tweak: Added labels and scales to Slider control
* Fix: Device mode detection in IE and Edge browser ([#7356](https://github.com/elementor/elementor/issues/7356))
* Fix: Positioning issue while dragging inline text widgets
* Fix: Empty widget calculation according to the new column flexbox model

= 2.5.4 - 2019-03-10 =
* Tweak: Added flexbox notice dialog
* Fix: Backward compatibility for flexbox property ([#7366](https://github.com/elementor/elementor/issues/7366))
* Fix: Allow `None` value for Entrance Animation control ([#7355](https://github.com/elementor/elementor/issues/7355))
* Fix: Device mode detection in IE and Edge browser ([#7356](https://github.com/elementor/elementor/issues/7356))
* Fix: Revert self hosted video background fix ([#7374](https://github.com/elementor/elementor/issues/7374))
* Fix: Removed Delimiter parameter form `ucwords` for older PHP compatibility
* Fix: Slider control returns wrong value
* Fix: Slider control for RTL

= 2.5.3 - 2019-03-06 =
* Tweak: Reduced Editor memory usage by moving common controls injection to the frontend ([#7308](https://github.com/elementor/elementor/issues/7308))
* Tweak: Moved sync library from POST to GET method
* Fix: `ucwords` support for older php versions ([#7327](https://github.com/elementor/elementor/issues/7327), [#7310](https://github.com/elementor/elementor/issues/7310))
* Fix: Background self hosted videos responsive
* Fix: Group controls `conditions` support

= 2.5.2 - 2019-03-05 =
* Fix: Custom space between widgets override ([#7309](https://github.com/elementor/elementor/issues/7309))
* Fix: Changed the default width of Absolute & Fixed position widget ([#7311](https://github.com/elementor/elementor/issues/7311))
* **Note: This fix might affect previous custom positioning settings, if you have previously used position absolute or position fixed, please review your site.**

= 2.5.1 - 2019-03-04 =
* Fix: Inline Width (auto) not working ([#7293](https://github.com/elementor/elementor/issues/7293))

= 2.5.0 - 2019-03-04 =
* New: Added Position Absolute & Position Fixed for widgets ([#5568](https://github.com/elementor/elementor/issues/5568))
* New: Added inline & custom width options to place widgets side by side without columns ([#4960](https://github.com/elementor/elementor/issues/4960))
* New: Added responsive options for Column ([#2965](https://github.com/elementor/elementor/issues/2965), [#6898](https://github.com/elementor/elementor/issues/7104), Props [@wayheming](https://github.com/wayheming))
* New: Added Columns for Text Editor widget ([#7104](https://github.com/elementor/elementor/issues/7104))
* New: Added `space-between`, `space-around` & `space-evenly` for Vertical Alignment in Column ([#5083](https://github.com/elementor/elementor/issues/5083))
* New: Added Horizontal Alignment in Column for inline widget
* New: Added responsive capabilities for Entrance Animation ([#1639](https://github.com/elementor/elementor/issues/1639))
* New: Added `elementor/template/viewport_tag` filter hook to Meta Viewport tag ([#7043](https://github.com/elementor/elementor/issues/7043))
* Tweak: Improved editor loading time and performance by ~50%
* Tweak: Remember last editing tab per element in the editor panel for better workflow ([#7087](https://github.com/elementor/elementor/issues/7087))
* Tweak: Added text shadow & background color controls for caption in Image widget (Props [@ramiy](https://github.com/ramiy))
* Tweak: Added access to super admin role in Maintenance Mode (Props [@GermanKrutov](https://github.com/GermanKrutov))
* Tweak: Added vw unit support for Typography size control
* Tweak: Added vw unit support for Custom Min. Height control in section
* Tweak: Added overflow option for section
* Tweak: Renamed `Content Position` control to `Vertical Align` in column layout
* Tweak: Renamed `Scrolling Effects` section to `Motion Effects`
* Tweak: Added Dynamic capabilities to Inner Section CSS class and CSS ID ([#6779](https://github.com/elementor/elementor/issues/6779))
* Tweak: Added Dynamic capabilities to Section CSS class and CSS ID ([#6779](https://github.com/elementor/elementor/issues/6779))
* Tweak: Added Dynamic capabilities all widgets CSS class and CSS ID ([#6779](https://github.com/elementor/elementor/issues/6779))
* Tweak: Added Dynamic capabilities to Button CSS ID ([#6779](https://github.com/elementor/elementor/issues/6779))
* Fix: Separator inside Repeater control ([#6851](https://github.com/elementor/elementor/issues/6851))
* Fix: Selectors not working in dynamic tags in edge cases (Props [@crazypsycho](https://github.com/crazypsycho))
* Fix: Entrance animations glitches ([#6945](https://github.com/elementor/elementor/issues/6945))
* Fix: Library "Blocks" tab empty
* Fix: Custom shape dividers not loading in editor ([#6550](https://github.com/elementor/elementor/issues/6550))
* Fix: WP-CLI Flush CSS command glitch in multisite ([#7190](https://github.com/elementor/elementor/issues/7190))
* Fix: PHP 7.3 Compatibility when saving settings ([#6890](https://github.com/elementor/elementor/issues/6890))

= 2.4.7 - 2019-02-18 =
* Fix: Incorrect device mode detection in Safari ([#7036](https://github.com/elementor/elementor/issues/7036))
* Fix: Elementor dashboard templates URL corrupted links in edge cases
* Fix: Sticky offset handling for menu anchor
* Fix: Avoid WP admin bar handling errors

= 2.4.6 - 2019-02-11 =
* Tweak: Use `<a>` for editor panel links ([#6767](https://github.com/elementor/elementor/issues/6767), [#7025](https://github.com/elementor/elementor/issues/7025))
* Fix: FireFox glitch in the color picker ([#6968](https://github.com/elementor/elementor/issues/6968))
* Fix: Horizontal scroll in Icon List widget ([#6558](https://github.com/elementor/elementor/issues/6558))
* Fix: PHP notice when user has no capabilities ([#6936](https://github.com/elementor/elementor/issues/6936))
* Fix: "Prevent Scroll" in popup options didn't work in edge cases

= 2.4.5 - 2019-01-30 =
* Tweak: Updated Eicons v4.2.0
* Fix: Control conditions not working for toggle popover ([#6780](https://github.com/elementor/elementor/issues/6780))
* Fix: URLs with hash in admin cause JS error ([#6902](https://github.com/elementor/elementor/issues/6902))
* Fix: Wrong section structure in the panel

= 2.4.4 - 2019-01-24 =
* Tweak: Added 1:1 aspect ratio to Video widget
* Tweak: Added ability to add condition/s for tabs control wrapper ([#6830](https://github.com/elementor/elementor/issues/6830))
* Fix: Template search with no categories ([#6810](https://github.com/elementor/elementor/issues/6810))
* Fix: Make sure browser has access to `localStorage`
* Fix: Removed Finder from the Customizer
* Fix: Editor UI glitch in Firefox browser

= 2.4.3 - 2019-01-21 =
* Tweak: Update `width` control label in Image Box widget ([#6808](https://github.com/elementor/elementor/issues/6808))
* Fix: PHP warning in self hosted video `render_pain_content()`
* Fix: Responsive `background-attachment` issue

= 2.4.2 - 2019-01-20 =
* Tweak: Better duplicate Error log entries handling
* Tweak: Split and merge similar translation strings
* Fix: Missing manual caption style in Image widget ([#6791](https://github.com/elementor/elementor/issues/6791))
* Fix: JS handlers not working in editor for other templates
* Fix: Log only Elementor related errors
* Fix: Background updates total iterations

= 2.4.1 - 2019-01-15 =
* Tweak: Avoid widget rendering on `box-shadow` and `text-shadow` changes ([#6737](https://github.com/elementor/elementor/issues/6737))
* Fix: Capital letters not working in anchors
* Fix: Admin menu position incorrect format
* Fix: Avoid duplicate run of element handlers on nested documents

= 2.4.0 - 2019-01-14 =
* New: Added Safe Mode for scenarios that the editor didn't load properly
* New: Added `<!--more-->` tag support ([#462](https://github.com/elementor/elementor/issues/462))
* New: Added Read More widget aka `<!--more-->` tag
* New: Added Categories taxonomy for Template Library ([#886](https://github.com/elementor/elementor/issues/886))
* New: Added responsive capabilities for Background control ([#2355](https://github.com/elementor/elementor/issues/2355))
* New: Added custom option for `background-size` & `background-position` ([#2571](https://github.com/elementor/elementor/issues/2571))
* New: Added responsive capabilities for Border control ([#3243](https://github.com/elementor/elementor/issues/3243), [#5284](https://github.com/elementor/elementor/issues/5284))
* New: Added External URL source for Video widget
* Tweak: Added a notice when clicking on Back to WordPress editor button ([#5597](https://github.com/elementor/elementor/issues/5597))
* Tweak: Added Link & Globe for Social Icons widget ([#6405](https://github.com/elementor/elementor/issues/6405))
* Tweak: Added `elementor/controls/animations/additional_animations` hook for adding custom Entrance Animations ([#6545](https://github.com/elementor/elementor/issues/6545))
* Tweak: Added `elementor/controls/hover_animations/additional_animations` hook for adding custom Hover Animations ([#6545](https://github.com/elementor/elementor/issues/6545))
* Tweak: Reorganized admin menu for Elementor settings & Template Library
* Tweak: Exclude library CPT from Yoast SEO sitemap
* Tweak: Added attributes for column wrappers (Props [@thenovacreator](https://github.com/thenovacreator))
* Tweak: Added `autocomplete` parameter for URL control (default: true)
* Tweak: Added sanitize data on Menu Anchor ID
* Tweak: Added dynamic options for Testimonial widget
* Tweak: Added PHP error log to System Info
* Tweak: Added Quick Edit option for Template Library
* Tweak: Renamed "Content" tab in Templates Library to "Page"
* Fix: Inline editing issue for Repeater control ([#6445](https://github.com/elementor/elementor/issues/6445))
* Fix: Panel footer menu not closing on background click ([#6547](https://github.com/elementor/elementor/issues/6547))
* Fix: Typography control doesn't generate default CSS ([#6500](https://github.com/elementor/elementor/issues/6500))
* Fix: Alignment issue in Icon List widget ([#6507](https://github.com/elementor/elementor/issues/6507))

= 2.3.8 - 2018-12-20 =
* Fix: Editor not working caused by last version

= 2.3.7 - 2018-12-20 =
* Fix: Invalid type in Import templates ([#6483](https://github.com/elementor/elementor/issues/6483))
* Fix: Save global templates
* Fix: WP widgets compatibility
* Fix: Responsive device buttons always shown

= 2.3.6 - 2018-12-17 =
* Fix: `Back to WordPress Editor` not working when meta-boxes are present ([#6437](https://github.com/elementor/elementor/issues/6437))
* Fix: Register documents only if needed
* Fix: Style glitch in the responsive control ([#6227](https://github.com/elementor/elementor/issues/6227))

= 2.3.5 - 2018-12-11 =
* Tweak: Load `elementor_debug_log` option only on System Info page
* Fix: Maintenance Mode admin console issue
* Fix: Elementor stuck on loader instead of `The Content Area Was Not Found`
* Fix: Temporary compatibility workaround for Chrome v70+ dragging above nested iframe issue
* Fix: `border-radius` for Video not displayed in Safari browser
* Fix: Avoid `z-index` changes by `nanocss` in build process

= 2.3.4 - 2018-11-29 =
* Tweak: Make sure "Edit with Elementor" action link is the last link in My Templates
* Fix: CSS parsing for non-existing controls ([#6296](https://github.com/elementor/elementor/issues/6296))
* Fix: Shows only editable documents in Finder

= 2.3.3 - 2018-11-28 =
* New: Added `elementor/frontend/{$element_type}/should_render` filter
* Tweak: Added a new `render_html_attributes` method
* Tweak: Added placeholder support for `date-time` control
* Tweak: Added ability to set a default value in a control `selectors` settings ([#6241](https://github.com/elementor/elementor/issues/6241))
* Tweak: Open links in new tab on `Ctrl + Enter` ([#6258](https://github.com/elementor/elementor/issues/6258))
* Tweak: Set the correct Dynamic tag categories for Self Hosted video
* Fix: Video lightbox won't open in some configurations
* Fix: Self-hosted video lightbox width
* Fix: Removed invalid `href` attribute in Tabs widget ([#6261](https://github.com/elementor/elementor/issues/6261))
* Fix: Restored element / widget hooks priority ([#6189](https://github.com/elementor/elementor/issues/6189))
* Fix: Drag the direct inner element only
* Fix: Show unsupported templates on My Templates list
* Fix: Make sure the document type exists in documents manager
* Fix: Set default for `slides to scroll` in Image Carousel widget

= 2.3.2 - 2018-11-17 =
* Tweak: Added `vw` unit to base control
* Tweak: Added WP Plugins and Users links to Finder
* Tweak: Updated Swiper progress style to new version
* Fix: Missing "View Page" and "Exit to Dashboard" in editor menu
* Fix: Background video position in edge cases
* Fix: Incorrect keyboard shortcut in Hotkeys cheatsheet
* Fix: Fatal error for 3rd party plugins ([#6192](https://github.com/elementor/elementor/issues/6192))

= 2.3.1 - 2018-11-12 =
* Fix: Print empty elements but not empty widgets ([#6134](https://github.com/elementor/elementor/issues/6134))
* Fix: Removed `figcaption` tag when no caption is set ([#6134](https://github.com/elementor/elementor/issues/6134))

= 2.3.0 - 2018-11-12 =
* New: Introducing Finder - The Easiest Way to Switch Between Pages ([#292](https://github.com/elementor/elementor/issues/292))
* New: Added Keyboard Shortcuts modal
* New: Introducing Star Rating widget
* Tweak: Added Deviantart, freeCodeCamp and GitLab to Social Icons widget ([#5820](https://github.com/elementor/elementor/issues/5820), [#5816](https://github.com/elementor/elementor/issues/5816))
* Tweak: Show preview fonts select on scroll ([#5207](https://github.com/elementor/elementor/issues/5207), [#3499](https://github.com/elementor/elementor/issues/3499))
* Tweak: Added compatibility for WordPress v5.0 Beta ([#6019](https://github.com/elementor/elementor/issues/6019))
* Tweak: Added filter to rename external CSS files ([#5968](https://github.com/elementor/elementor/issues/5968))
* Tweak: Added dynamic option for caption in the Image widget ([#5770](https://github.com/elementor/elementor/issues/5770))
* Tweak: Updated YouTube related videos control due to YouTube's API changes ([#5984](https://github.com/elementor/elementor/issues/5984), [#5939](https://github.com/elementor/elementor/issues/5939))
* Tweak: Added a general `hook elementor/frontend/after_render` to element-base
* Tweak: Added `elementor/element/before_attribute_render` hook before attributes are rendered
* Tweak: Added `elementor/element/after_attribute_render` hook after attributes are rendered
* Tweak: New `elementor/editor/init` action
* Tweak: A new `get_post_type_title` method for document
* Tweak: Added `Ctrl/Cmd + E` shortcut to open Finder modal
* Tweak: Added `Ctrl/Cmd + ?` shortcut to open all keyboard shortcuts modal
* Tweak: Added `Esc` shortcut for opening the settings menu
* Tweak: Update Eicons v3.9.2
* Fix: Added default width for SVG image to support edge cases ([#5987](https://github.com/elementor/elementor/issues/5987))
* Fix: Better WP Widget hooks support ([#5844](https://github.com/elementor/elementor/issues/5844))
* Fix: Exclude image link from lightbox when the link has `download` attribute ([#5996](https://github.com/elementor/elementor/issues/5996))
* Fix: Background Gradient not working inside Repeater control ([#5914](https://github.com/elementor/elementor/issues/5914))
* Fix: Added support for future post status ([#5681](https://github.com/elementor/elementor/issues/5681), [#6000](https://github.com/elementor/elementor/issues/6000))
* Fix: Missing string translations ([#5989](https://github.com/elementor/elementor/issues/5989))
* Fix: Lightbox images links detection ([#6103](https://github.com/elementor/elementor/issues/6103))
* Fix: Animation class removed from Lightbox closing ([#5911](https://github.com/elementor/elementor/issues/5911))
* Fix: Allow plugins to set current page as Login Page for Maintenance Mode
* Fix: Added navigation between tabs via tab keyboard for better accessibility
* Fix: Use Thumbnail if selected size is missing
* Fix: A new method to handle with multiple image custom sizes
* Fix: Import template by WP CLI
* Fix: Height of bar without content in Progress Bar widget
* Fix: Added compatibility with a new embed API of YouTube
* Fix: Don't render wrapper of element if the content is empty
* Deprecated: `Source_Local::TYPE_META_KEY` is no longer supported
* Deprecated: `Document::save_type()` is replaced by `Document::save_template_type()`

= 2.2.7 - 2018-10-24 =
* Tweak: Update Google Fonts list with more than 20 new fonts ([Topic](https://wordpress.org/support/topic/please-update-google-fonts-library-with-the-latest-font-for-free-version/))
* Fix: Admin Notice not dismissing in some server configurations ([#5927](https://github.com/elementor/elementor/issues/5927))
* Fix: Image link not respecting the image size in the Image widget ([#5897](https://github.com/elementor/elementor/issues/5897))

= 2.2.6 - 2018-10-22 =
* Tweak: Added `get_render_attributes` method to `Element_Base` ([#5878](https://github.com/elementor/elementor/issues/5878))
* Tweak: Sets the minimum width of the content area as `300px`
* Tweak: Darken Elementor Loader icon color
* Fix: Field labels with multiple rows style glitch ([#4295](https://github.com/elementor/elementor/issues/4295))

= 2.2.5 - 2018-10-07 =
* Tweak: System Info now recognizes MariaDB versions
* Tweak: Allow document to override widgets panel settings
* Fix: Admin Notice not dismissing when JS disabled ([#5832](https://github.com/elementor/elementor/issues/5832))
* Fix: System Info Uncaught Error on edge cases

= 2.2.4 - 2018-09-20 =
* Tweak: Added Poster option for Self Hosted in Video widget
* Fix: Revert Gutenberg Image Block compatibility until stable version ([#5763](https://github.com/elementor/elementor/issues/5763))

= 2.2.3 - 2018-09-17 =
* Tweak: Improved browser compatibility with Elementor grid
* Fix: Toggle Widget first toggle always open on load
* Fix: Changed Dailymotion placeholder in Video widget

= 2.2.2 - 2018-09-16 =
* Tweak: Improved IE compatibility mechanism
* Tweak: Added `remove_all_actions( 'after_wp_tiny_mce' )` hook for better compatibility with 3rd party plugins ([#5686](https://github.com/elementor/elementor/issues/5686))
* Fix: Content styling missing when no icon for Toggle widget ([#5735](https://github.com/elementor/elementor/issues/5735))
* Fix: Redirect issue on activation via Ajax
* Fix: Excluded `Actions` label from ordering in Template library modal
* Fix: Avoid setting empty title for elements in Navigator
* Fix: Compatibility for Gutenberg Image Block

= 2.2.1 - 2018-09-03 =
* Fix: Auto scrolling when clicking on long element ([#5591](https://github.com/elementor/elementor/issues/5591))
* Fix: Increased number input width besides slider control ([#5521](https://github.com/elementor/elementor/issues/5521))
* Fix: Typography line-height default unit for all devices ([#5527](https://github.com/elementor/elementor/issues/5527))
* Fix: Added Navigator compatibility for Role Manager ([#5599](https://github.com/elementor/elementor/issues/5599))
* Fix: Clear cache via native WordPress methods to support object cache plugins ([#4179](https://github.com/elementor/elementor/issues/4179))
* Fix: E loader RTL style glitch
* Fix: History undo after adding a section
* Fix: Styling issue with Testimonial widget image
* Fix: Panel elements search error when the user is in `content only` mode
* Fix: Better Navigator support for safari versions

= 2.2.0 - 2018-08-28 =
* New: Introducing Navigator Panel ([#1165](https://github.com/elementor/elementor/issues/1165))
* New: Added Autocomplete URL functionality for Internal Linking ([#255](https://github.com/elementor/elementor/issues/255))
* New: Added `Cmd/Ctrl + I` hotkey for the Navigator
* New: Added `Cmd/Ctrl + Shift + V` hotkey for paste style
* New: Added "Getting Started" page after new installation
* New: Added reverse columns for tablet screen resolution ([#2070](https://github.com/elementor/elementor/issues/2070))
* Tweak: Changed the name of Columns widget to Inner Section to reduce confusion
* Tweak: Added option to restore Editing Handles ([#4981](https://github.com/elementor/elementor/issues/4981))
* Tweak: Remember Editor panel width in localStorage ([#2414](https://github.com/elementor/elementor/issues/2414))
* Tweak: Added official color option on hover state in Social Icons Widget ([#2032](https://github.com/elementor/elementor/issues/2032))
* Tweak: New user interface for CSS Filters control
* Tweak: Added `Hue` filter to CSS Filters control
* Tweak: Added a Lazy Load option for the Video widget ([#5189](https://github.com/elementor/elementor/issues/5189))
* Tweak: Added Mixcloud icon to Social Icons widget ([#5417](https://github.com/elementor/elementor/issues/5417))
* Tweak: Added an additional aspect ratio (21:9) in Video widget
* Tweak: Added edit capabilities in the responsive mode
* Tweak: Removed default transparency on hover button ([#4406](https://github.com/elementor/elementor/issues/4406))
* Tweak: Redesign `E` loader
* Tweak: Added `elementor/frontend/before_render` hook to `element-base`
* Fix: Reverse column bug in responsive columns ([#5421](https://github.com/elementor/elementor/issues/5421))
* Fix: Compatibility for IE11 by adding IE CSS via JS ([#5510](https://github.com/elementor/elementor/issues/5510), [#5530](https://github.com/elementor/elementor/issues/5530))
* Fix: IE11 bug in case of `min-height` mixed with `align-items: center`
* Fix: Resize the columns via the `Column Width` after moving the section ([#5393](https://github.com/elementor/elementor/issues/5393))
* Fix: Different videos with the same image showing the same video
* Fix: Inline editing in Progress Bar widget

= 2.1.8 - 2018-08-19 =
* Fix: Prevent columns from overflowing the grid area in Firefox browser ([#5442](https://github.com/elementor/elementor/issues/5442))
* Fix: Style glitch in the panel separator

= 2.1.7 - 2018-08-15 =
* Fix: Avoid copying custom CSS ID when pasting style ([#5416](https://github.com/elementor/elementor/issues/5416))
* Fix: Responsive CSS for minimal grid utility ([#5375](https://github.com/elementor/elementor/issues/5375))
* Fix: Make Elementor Canvas compatible with iPhone X landscape orientation

= 2.1.6 - 2018-07-31 =
* Fix: Removed reference to source map file in Swiper minified file
* Fix: Element handle style glitch in mobile view
* Fix: Delete element with hotkey after text editing
* Fix: Avoid auto focus on the widget panel search field after deleting an element

= 2.1.5 - 2018-07-26 =
* Tweak: Added `Ctrl/Cmd + Y` for redo action ([#5090](https://github.com/elementor/elementor/issues/5090))
* Tweak: Clear WP cache in upgrade steps
* Fix: Corrected conditions for start/end times with loop in Video widget
* Fix: Padding for first active control in section
* Fix: Loader styling for some languages
* Fix: RTL style for Choose control
* Fix: Possible memory leak by parsing and sanitizing data and settings only when it's necessary
* Fix: Fixed text selection detection in FireFox
* Fix: `on-paste` check for Choose control
* Fix: `fixed` positions for Color Picker
* Fix: Removed empty space from `elementor/document/urls/edit` hook name ([#5216](https://github.com/elementor/elementor/issues/5216))
* Fix: Dynamic tags support in Shortcode widget ([#5214](https://github.com/elementor/elementor/issues/5214))

= 2.1.4 - 2018-07-18 =
* Tweak: Show modified date instead of publish date on Dashboard widget ([#4169](https://github.com/elementor/elementor/issues/4169))
* Fix: Selector for CSS Filters control in column
* Fix: CSS Filters for self hosted video
* Fix: Avoid creating unnecessary CSS for widgets
* Fix: Added backward compatibility for deprecated controls
* Fix: Color Picker control value change detection
* Fix: Avoid multiple ready trigger in preview
* Fix: Save default page template

= 2.1.3 - 2018-07-16 =
* Tweak: Added CSS filter control for Map, Video, Section and Column
* Tweak: Added Inspector option in Tools page
* Tweak: Added Inspector log for Canvas & Full Width templates
* Tweak: The preview is now loading via plain URL to avoid errors in some server configurations
* Tweak: Added `print_elements_with_wrapper` method to allow a document to overwrite it's wrapper
* Tweak: Added action hook `elementor/template-library/after_get_source_data`
* Fix: Page template missing after import ([#4498](https://github.com/elementor/elementor/issues/4498))
* Fix: Maintenance Mode override theme template
* Fix: Removed focus and active states from Hover Animations
* Fix: Prevent submit forms in the preview
* Fix: Disable history tracking when restoring a revision
* Fix: Empty history changes after restoring a revision
* Fix: Paste style on control with groups
* Fix: Avoid overwrite `_wp_page_template` in edge cases

= 2.1.2 - 2018-07-08 =
* Tweak: Set active/inactive category by `'active' => false` (default is `true`)
* Tweak: Added forms input style compatibility for some themes
* Fix: XML Demo Import compatibility for admin based imports ([#4947](https://github.com/elementor/elementor/issues/4947))
* Fix: Set correct `post_id` when call `enqueue_scripts` from preview mode
* Fix: Conditions not working on dynamic backgrounds
* Fix: Paste of multiple content in Add Section Area
* Fix: Set lower priority to `template_include` hook to override 3rd party plugins, e.g. WooCommerce
* Fix: Hide Save Widget as Global button for old versions
* Fix: Added RTL style for section with handles inside
* Deprecated: Restore `get_page` method as hard deprecated ([#4870](https://github.com/elementor/elementor/issues/4870))

= 2.1.1 - 2018-07-03 =
* Tweak: Compatibility Gutenberg classic editor
* Fix: Compatibility for PHP < 5.6

= 2.1.0 - 2018-07-03 =
* New: Introducing Elementor Context Menu
* New: Copy/Paste elements ([#4151](https://github.com/elementor/elementor/issues/4151))
* New: Copy/Paste element style ([#1479](https://github.com/elementor/elementor/issues/1479))
* New: Copy/Paste between pages
* New: Reset element style
* New: Added `copy_all_content` option
* New: Change mobile & tablet breakpoints ([#78](https://github.com/elementor/elementor/issues/78))
* New: Introducing Elementor WP-CLI integration ([#2161](https://github.com/elementor/elementor/issues/2161))
* New: Added collapsible panel categories ([#3805](https://github.com/elementor/elementor/issues/3805))
* New: Added Self hosted videos with HTML5 for Video widget ([#3313](https://github.com/elementor/elementor/issues/3313))
* New: Added Dailymotion provider for Video widget ([#2285](https://github.com/elementor/elementor/issues/2285))
* New: Added start/end controls for Video widget ([#3565](https://github.com/elementor/elementor/issues/3565))
* New: Added new icons to Social Icons widget: Android & Thumbtack ([#4235](https://github.com/elementor/elementor/issues/4235), [#4486](https://github.com/elementor/elementor/issues/4486))
* New: Hotkey: Ctrl / Cmd + C = Copy
* New: Hotkey: Ctrl / Cmd + V = Paste
* Tweak: Added CSS Filter group control ([#3260](https://github.com/elementor/elementor/issues/3260))
* Tweak: Added CSS Filters to Image & Image Box widgets
* Tweak: Added compatibility with Gutenberg 3.0.+ ([#2631](https://github.com/elementor/elementor/issues/2631))
* Tweak: Added CSS ID for Button widget ([#4535](https://github.com/elementor/elementor/issues/4535))
* Tweak: Added keywords to all widgets for more accurate filtering
* Tweak: Replace hover section with tabs Icon & Icon Box widgets
* Tweak: Moved `delete all content` to the page level right click
* Tweak: When converting content with only a shortcode to Elementor, a shortcode widget used instead of Text Editor widget (#4616)
* Tweak: Updated Swiper library to v4.3.3 ([#4170](https://github.com/elementor/elementor/issues/4170))
* Tweak: Added `elementor/shapes/additional_shapes` filter hook to allow custom shape dividers (#4759)
* Tweak: Added video support to media control ([#4066](https://github.com/elementor/elementor/issues/4066))
* Tweak: Dropped Support for WP v4.6, now supporting WP v4.7+
* Fix: The `wpColorPicker` will not be updated after emptying the value
* Fix: Added fallback for injection position when the injection control does not exist
* Fix: Focus state behavior in the Repeater control ([#4596](https://github.com/elementor/elementor/issues/4596))
* Deprecated: `Repeater::get_fields()` is replaced by `Repeater::get_controls()`
* Deprecated: `Element_Base::get_parent()` is replaced by `Element_Base::get_data( 'parent' )`
* Deprecated: `get_class_controls` is no longer supported
* Deprecated: `\Elementor\settings\page\manager::get_page()` is now removed ([#4870](https://github.com/elementor/elementor/issues/4870))

= 2.0.16 - 2018-06-12 =
* Fix: Dynamic Tag CSS not working on columns & widgets ([#4662](https://github.com/elementor/elementor/issues/4662))
* Fix: Hide title now also hides Post Title widget
* Fix: Trigger Elementor ready only once

= 2.0.15 - 2018-06-05 =
* Fix: Background video in Chrome v67+
* Fix: CSS glitch in local library modal
* Fix: CSS glitch with dynamic buttons in a repeater
* Fix: Divider control on top of controls popover
* Fix: Conditions check for `in` & `!in`
* Fix: Prefix class when the value is numeric

= 2.0.14 - 2018-05-29 =
* Fix: Missing Dynamic Image Background properties on front-end ([#4577](https://github.com/elementor/elementor/issues/4577))
* Fix: Avoid page settings global `$post` being overwritten by 3rd party plugins ([#4563](https://github.com/elementor/elementor/issues/4563))
* Fix: Editor won’t load for users without publishing capabilities
* Fix: Only show Caption style section for Image widget if needed

= 2.0.13 - 2018-05-28 =
* New: Dynamic Image Backgrounds ([#3971](https://github.com/elementor/elementor/issues/3971))
* Tweak: Updated Dialog Manager v4.3.2
* Tweak: Better accessibility for Back to Editor button
* Tweak: Confusing error message ([#4546](https://github.com/elementor/elementor/issues/4546))
* Fix: Panel buttons style glitch in edge cases
* Fix: PHP notice in Polylang compatibility
* Fix: Editor won't load for users without publishing capabilities
* Fix: Media queries ordering in CSS file for mixed min and max break points
* Fix: Improved video background loading method

= 2.0.12 - 2018-05-15 =
* Tweak: Added new method `add_body_class`
* Tweak: Set library filter by the document
* Tweak: Allow 3rd party developers to register documents on init
* Fix: Set attach uploaded images to the current post
* Fix: Scroll not working in the library modal ([#4394](https://github.com/elementor/elementor/issues/4394))
* Fix: Blocks dropdown menu escapes container on scroll ([#3947](https://github.com/elementor/elementor/issues/3947))

= 2.0.11 - 2018-05-09 =
* Fix: Stronger selector for heading widget color, to avoid section style override ([#4266](https://github.com/elementor/elementor/issues/4266))
* Fix: style missing when role manager in content only mode

= 2.0.10 - 2018-05-08 =
* Fix: Added icon for Dynamic Tag without settings
* Fix: Added offset for anchor scroll section is sticky ([#4323](https://github.com/elementor/elementor/issues/4323))
* Fix: Title changing on auto-save for various languages
* Fix: Cannot scroll in the library modal ([#4325](https://github.com/elementor/elementor/issues/4325))

= 2.0.9 - 2018-05-01 =
* Tweak: Added ability to add Dynamic Tags from 3rd party applications
* Tweak: Added ability to add custom classes to the wrapper for extending widgets
* Tweak: Using full version of Select2.js in the admin
* Tweak: Update Eicons v3.3
* Fix: My Template tabs are not translatable ([#4221](https://github.com/elementor/elementor/issues/4221))
* Fix: Added compatibility for domain mapping with filter tag ([#4307](https://github.com/elementor/elementor/issues/4307))
* Fix: Popover closes after clicking on select2 search container ([#4310](https://github.com/elementor/elementor/issues/4310))
* Fix: Wrapping Dynamic Tag in the editor
* Fix: Removed unnecessary condition from Transition Duration control
* Fix: Removed listening to parent model on Repeater control
* Fix: Controls styling in repeater
* Fix: Ensure the frontend scripts are enqueued once in the editor preview

= 2.0.8 - 2018-04-23 =
* Tweak: Added action hook for caching plugins when clearing CSS files `do_action( 'elementor/css-file/clear_cache' );` ([#4179](https://github.com/elementor/elementor/issues/4179))
* Tweak: Added Page Templates support only for layout pages
* Fix: Smooth scrolling not working ([#4168](https://github.com/elementor/elementor/issues/4168), [#4125](https://github.com/elementor/elementor/issues/4125))
* Fix: Stretch section for archive pages
* Fix: Icon vertical alignment in icon list widget

= 2.0.7 - 2018-04-18 =
* Tweak: Rename filter `elementor/get_document/post_id` to `elementor/documents/get/post_id`
* Tweak: Added Divider control for Icon List widget on inline skin
* Tweak: Added CSS classes per document in Preview
* Tweak: Added option to avoid export data by adding `export=false` to control args
* Fix: Support for editing images in WP Media Modal ([#3062](https://github.com/elementor/elementor/issues/3062))
* Fix: Responsive inline alignment for Icon List widget
* Fix: Added higher priority to support Google Fonts in locations
* Fix: Import templates with Dynamic data
* Fix: Editing buttons missing when preview set as archive
* Fix: Export document settings for all template types

= 2.0.6 - 2018-04-15 =
* New: Image widget - added new control for Image width
* Tweak: Added support for dynamic video via ACF
* Tweak: Reorder admin columns in My Templates
* Tweak: Update Eicons v3.2.2
* Tweak: Added `elementor/get_document/post_id` filter for `get document` - compatibility for translation plugins
* Tweak: Print container classes per document
* Tweak: Added `elementor-edit-area-preview` and `elementor-edit-area-active` classes
* Tweak: Removed wrapper for dynamic data
* Fix: Per device Visibility for editor with multiple Elementor areas
* Fix: CSS glitch for URL input with dynamic
* Fix: Dynamic Show Before/After only when value is not empty

= 2.0.5 - 2018-04-11 =
* Fix: Avoid merge for incorrect page settings data
* Fix: Style tab on document settings not saving on autosave
* Fix: Duplicate icons by updated Eicons library

= 2.0.4 - 2018-04-09 =
* Tweak: Update Google Fonts list with more than 30 new fonts
* Tweak: Updated Eicons to v3.2.0
* Fix: Page template doesn't work in draft status ([#3706](https://github.com/elementor/elementor/issues/3706))
* Fix: Make sure that document settings works only on existing posts ([#3993](https://github.com/elementor/elementor/issues/3993), [Topic](https://wordpress.org/support/topic/php-fatal-error-after-last-update/))
* Fix: Removed some duplicate strings
* Fix: Undefined index when save editor using old method
* Fix: Use `&nbsp;` to add spaces before/after for tags with HTML ([#3826](https://github.com/elementor/elementor/issues/3826))

= 2.0.3 - 2018-03-29 =
* Tweak: Added Knowledge Base link to WordPress admin menu
* Tweak: Improved performance in the editor using internal caching
* Fix: Avoid WordPress override page template in auto-save status ([Topic](https://wordpress.org/support/topic/save-draft-will-revert-page-layout-into-default-instead-of-elementor-canvas/))
* Fix: Set element ID before sanitizing data

= 2.0.2 - 2018-03-28 =
* Fix: Title changing on auto-save
* Fix: Page templates reverts to default on preview ([#3933](https://github.com/elementor/elementor/issues/3933))
* Fix: Editor won't load when a custom image size is used ([#3926](https://github.com/elementor/elementor/issues/3926))
* Fix: WordPress Widgets not loading saved settings ([#3930](https://github.com/elementor/elementor/issues/3930))

= 2.0.1 - 2018-03-27 =
* Fix: Removed promotion in the Role Manager area ([#3903](https://github.com/elementor/elementor/issues/3903))
* Fix: Improved responsive style for My Template modal ([#3914](https://github.com/elementor/elementor/issues/3914))
* Fix: Issue with conditional repeater field ([#3886](https://github.com/elementor/elementor/issues/3886), [#3865](https://github.com/elementor/elementor/issues/3865))
* Fix: The notifications toast goes down when you change the page layout
* Fix: Custom delimiter char for frontend template in the Counter widget
* Fix: Import template from WP Admin

= 2.0.0 - 2018-03-26 =
* New: Added new library tab for pre-design "Blocks"
* New: Added new page template "Elementor Full Width"
* New: Document Type method to register new template types ([#3409](https://github.com/elementor/elementor/issues/3409))
* New: Added new social icons for 500px, Steam and StumbleUpon ([#3106](https://github.com/elementor/elementor/issues/3106), [#3636](https://github.com/elementor/elementor/issues/3636))
* Tweak: Dropped Support WP 4.5, now supporting WP 4.6 and higher
* Tweak: New settings interface for Role Manager
* Tweak: Reintroduce divider control for UI panel ([Developer API](https://developers.elementor.com/elementor-controls/divider-control/))
* Tweak: Added inline view for icon-list widget ([#1624](https://github.com/elementor/elementor/issues/1624))
* Tweak: Added "Featured Image" control to document settings ([#3429](https://github.com/elementor/elementor/issues/3429))
* Tweak: Added custom thousand separator in Counter widget ([#3459](https://github.com/elementor/elementor/issues/3459))
* Tweak: Added "Image Size" control to the "Video" widget overlay image ([#3488](https://github.com/elementor/elementor/issues/3488))
* Tweak: Added "Image Size" control to the "Image Box" widget ([#3251](https://github.com/elementor/elementor/issues/3251))
* Tweak: Added "Image Size" control to the "Testimonial" widget ([#3472](https://github.com/elementor/elementor/issues/3472))
* Tweak: Added "Link" control to the "Testimonial" widget ([#3480](https://github.com/elementor/elementor/issues/3480))
* Tweak: Added "Caption Spacing" control to the "Image" widget ([#3452](https://github.com/elementor/elementor/issues/3452))
* Tweak: Added responsive control to the text padding in the "Button" widget
* Tweak: Added compatibility for the future release of Elementor
* Tweak: Added `Groove` style to the "Border" control
* Tweak: Decreasing column minimum width to 2%
* Tweak: Decreasing section minimum height in edit mode
* Tweak: Match HTML tags for the section and the column ([#3471](https://github.com/elementor/elementor/issues/3471))
* Tweak: Renamed the `clear_page` section in the document settings
* Tweak: Renamed the `general-elements` widgets category to `general` ([#3729](https://github.com/elementor/elementor/issues/3729))
* Tweak: Remember the last library tab and search/filters that was open
* Tweak: Replace the select control with a switcher control in the Video widget "Play Icon" control ([#3262](https://github.com/elementor/elementor/issues/3262))
* Tweak: Video widget accessibility - added `role="button"` attribute to the Play button ([#3270](https://github.com/elementor/elementor/issues/3270))
* Tweak: Button widget accessibility - added `role="button"` attribute ([#3271](https://github.com/elementor/elementor/issues/3271))
* Tweak: Icon widget accessibility - added `aria-hidden` attribute to the `i` tag ([#3272](https://github.com/elementor/elementor/issues/3272))
* Tweak: Icon Box widget accessibility - added `aria-hidden` attribute to the `i` tag ([#3273](https://github.com/elementor/elementor/issues/3273))
* Tweak: Google Maps widget accessibility - added `aria-label` attribute to the `iframe` tag ([#3274](https://github.com/elementor/elementor/issues/3274))
* Tweak: Moved `placeholder` and `title` settings from base control settings into individual controls that use them ([#3795](https://github.com/elementor/elementor/issues/3795))
* Tweak: Updated the "Icon Box" widget to handle cases where no icon is set
* Tweak: Updated the way widget categories are registered in Elementor ([#3729](https://github.com/elementor/elementor/issues/3729))
* Tweak: Slick.js library Updated to v1.8.1 ([#3538](https://github.com/elementor/elementor/issues/3538))
* Tweak: Updated Eicons to v3.0.0
* Tweak: Updated Select2 to v4.0.5
* Fix: Document settings now saved as revisions on auto save ([#3510](https://github.com/elementor/elementor/issues/3510))
* Fix: Show preset section in the right side for RTL

= 1.9.8 - 2018-03-12 =
* Fix: Activate publish button if there is an auto-save version
* Fix: Server error message missing in edge cases
* Fix: Updated dialog library ([#3668](https://github.com/elementor/elementor/issues/3668))
* Fix: Text Decoration option in Button Widget ([#3701](https://github.com/elementor/elementor/issues/3701))
* Fix: Added `autopause=0` in order to allow multiple autoplay in Vimeo video
* Fix: Added missing translation string to control in the Image Carousel widget

= 1.9.7 - 2018-02-27 =
* Tweak: `Element_Base::get_style_depends()` method is now overwritable ([#3494](https://github.com/elementor/elementor/issues/3494))
* Fix: "Enter" key triggers Elementor mode instead of default submit action ([#3556](https://github.com/elementor/elementor/issues/3556))
* Fix: Stop showing Connection Lost notification when editing widgets
* Fix: Counter widget - Allow spacing between prefix and suffix

= 1.9.6 - 2018-02-21 =
* Fix: When selecting a color with alpha, the value is not fully visible ([#3320](https://github.com/elementor/elementor/issues/3320))
* Fix: "Create new Page" button in dashboard widget ([#3491](https://github.com/elementor/elementor/issues/3491))
* Fix: `safe_copy_elementor_meta` for editor revisions
* Fix: Clear filters when syncing library
* Fix: Background attachment fixed only on desktop

= 1.9.5 - 2018-02-14 =
* Fix: Added reset for background video when the parent has set `text-align: center;`
* Fix: Print global and post CSS files after all 3rd party plugins styles
* Fix: Avoid setting editor changed-flag on auto-saving
* Fix: Stretch section fallback to body when selector not found or the container doesn't exist

= 1.9.4 - 2018-02-07 =
* Tweak: Added draft posts to Elementor dashboard widget ([#3379](https://github.com/elementor/elementor/issues/3379))
* Tweak: Removed CodeMirror script for WP Custom HTML widget to improve performance
* Fix: Fonts not loaded in edit mode for widget template ([#3352](https://github.com/elementor/elementor/issues/3352))
* Fix: Prevent template library modal close on actions in edge cases
* Fix: Set save button as disabled if there is nothing to save
* Fix: Added publish to editor translations
* Fix: Added a flex-basis patch for better support in Firefox browser
* Fix: Image Box heading link now works without an image ([#2854](https://github.com/elementor/elementor/issues/2854))

= 1.9.3 - 2018-01-21 =
* Fix: Enqueue style/script if when not needed ([#3094](https://github.com/elementor/elementor/issues/3094))
* Fix: Added compatibility for Safari browser accessibility
* Fix: Error message in saver
* Fix: Elementor missing WP editor content ([#3139](https://github.com/elementor/elementor/issues/3139))
* Fix: Changes lost on revisions panel tab destroy ([#3242](https://github.com/elementor/elementor/issues/3242))

= 1.9.2 - 2018-01-16 =
* Tweak: Improved querying for revisions to reduce load time on posts with lots of revisions
* Tweak: Added new notifications when connection with the server is lost and on server errors
* Fix: Autosave now show only Elementor data ([#3144](https://github.com/elementor/elementor/issues/3144))
* Fix: Show correct post content in autosave
* Fix: Preview Changes button now uses `wp_preview_url()`
* Fix: Typography settings not showing correctly in some cases ([#3145](https://github.com/elementor/elementor/issues/3145))
* Fix: Group control with conditions ([#3182](https://github.com/elementor/elementor/issues/3182))
* Fix: Import template in some configurations by checking if `ZipArchive` class is exists
* Fix: Tooltip flickering in the responsive mode button ([#3151](https://github.com/elementor/elementor/issues/3151))

= 1.9.1 - 2018-01-10 =
* Fix: Conflict between responsive control and group control popup ([#3130](https://github.com/elementor/elementor/issues/3130))
* Fix: Color picker handle lag
* Fix: Native WordPress widgets stopped working

= 1.9.0 - 2018-01-09 =
* New: Added Autosave capability for editor
* New: Added save as draft option ([#2824](https://github.com/elementor/elementor/issues/2824))
* New: Brand new Template Library
* New: Added filter & sorting (new, trend & popular) to Template Library ([#1711](https://github.com/elementor/elementor/issues/1711))
* New: Added import & sync tools to Template Library ([#2402](https://github.com/elementor/elementor/issues/2402))
* New: Added search form to Template Library ([#2499](https://github.com/elementor/elementor/issues/2499))
* New: Added my favorites option to the Template Library
* New: Added sorting (name, type, author & date) to My Templates Library
* New: Added new social icons for Meetup, RSS & Skype ([#2703](https://github.com/elementor/elementor/issues/2703), [#2701](https://github.com/elementor/elementor/issues/2701), [#3090](https://github.com/elementor/elementor/issues/3090))
* New: Added Overview dashboard widget
* Tweak: Added privacy control to video widget ([#2741](https://github.com/elementor/elementor/issues/2741))
* Tweak: Added new controls for Icons, Spacing & Padding to Toggle widget ([#2836](https://github.com/elementor/elementor/issues/2836))
* Tweak: Added new controls for Icons & Padding to Accordion widget ([#2836](https://github.com/elementor/elementor/issues/2836))
* Tweak: Added responsive space between control to Icon List widget
* Tweak: Rename "Page Settings" panel to "Document Settings"
* Tweak: Moved My Library to top of admin menu ([#2843](https://github.com/elementor/elementor/issues/2843))
* Tweak: Re-design menu of editor panel
* Tweak: Replaced `jquery-simple-dtpicker` with `flatpickr` JS library ([#2095](https://github.com/elementor/elementor/issues/2095))
* Tweak: Reduced panel clutter by allowing group control to be displayed as a popup
* Tweak: Added Gutenberg compatibility - "Add New Elementor" and "Back to Classic" edit screen
* Tweak: Added excerpt control to the Document Settings if the post type supports excerpts ([#2837](https://github.com/elementor/elementor/issues/2837))
* Tweak: Added `text-decoration` to Typography control
* Tweak: All functions, action hooks and filter hooks have been documented
* Tweak: Implementing inline editing functionality in various Elementor widgets
* Tweak: Accessible widget search - add label for screen readers in the search box ([#2835](https://github.com/elementor/elementor/issues/2835))
* Tweak: Improved Editor accessibility - replace `title` attributes with `aria-label` ([#2861](https://github.com/elementor/elementor/issues/2861))
* Tweak: Progress Bar widget accessibility - set correct `role` and added new `aria` attributes ([#2861](https://github.com/elementor/elementor/issues/2861))
* Tweak: Alert widget accessibility - make the "Dismiss" button accessible ([#3108](https://github.com/elementor/elementor/issues/3108))
* Tweak: Tabs widget accessibility - added `id`, `role` and `aria` attributes ([#2913](https://github.com/elementor/elementor/issues/2913))
* Tweak: Toggle widget accessibility - added `id`, `role` and `aria` attributes ([#2836](https://github.com/elementor/elementor/issues/2836))
* Tweak: Accordion widget accessibility - added `id`, `role` and `aria` attributes ([#2836](https://github.com/elementor/elementor/issues/2836))
* Tweak: Social Icons widget accessibility - added labels for screen readers
* Tweak: Added Browser support notification for unsupported browsers
* Tweak: Depended styles, various elements can set stylesheet dependencies ([#1636](https://github.com/elementor/elementor/issues/1636))
* Tweak: Added option to set control as required by `required => true`
* Fix: Added `wptexturize` filter to match WordPress native text formatting
* Fix: Alignment issue with Icon widget

= 1.8.12 - 2018-01-03 =
* Tweak: Added style compatibility for multiple select field
* Tweak: Added trim extra spaces in WP editor
* Fix: WC session not defined in editor

= 1.8.11 - 2017-12-19 =
* Tweak: Support W3C validation when using multiple Google Fonts ([Topic](https://wordpress.org/support/topic/bad-character-while-embedding-google-fonts/))
* Tweak: Eicons v2.9.0 Updated
* Fix: TinyMCE inside repeater missing content after sort canceled ([#2952](https://github.com/elementor/elementor/issues/2952))
* Fix: Alpha color picker added `#` to default value ([#2978](https://github.com/elementor/elementor/issues/2978))
* Fix: Wrong parameter for multiple conditions with nested relations
* Fix: Init heartbeat only after preview has been loaded successfully to prevent unexpected behavior in edge cases

= 1.8.10 - 2017-12-13 =
* Tweak: Allow support for multiple conditions with relations
* Fix: Waypoint in order to support default options and trigger once
* Fix: Entrance animation near the bottom of the page ([#2114](https://github.com/elementor/elementor/issues/2114), [#2060](https://github.com/elementor/elementor/issues/2060))
* Fix: Avoid delete current revision preview, check it's a valid revision
* Fix: Change post-status in page settings

= 1.8.9 - 2017-12-06 =
* Security Fix! - Prevent potential privilege escalation in page settings & history
* Fix: Allow Contributors to preview library templates
* Fix: Font Awesome icons are italicized ([#2873](https://github.com/elementor/elementor/issues/2873))
* Fix: CSS autoprefixer for minified files (now supports last 10 versions of browsers)
* Fix: Import template in Safari browser
* Fix: Post config for none singular pages
* Fix: Re-Render WYSIWYG control inside repeater on sort changed ([#2897](https://github.com/elementor/elementor/issues/2897), [#2450](https://github.com/elementor/elementor/issues/2450), [#2324](https://github.com/elementor/elementor/issues/2324))
* Fix: Eicons CSS re-compiled ([#2878](https://github.com/elementor/elementor/issues/2878))

= 1.8.8 - 2017-11-30 =
* Tweak: Eicons v2.8.0 Updated
* Security Fix! - Prevent potential privilege escalation in template library

= 1.8.7 - 2017-11-29 =
* Tweak: Eicons v2.7.0 updated
* Fix: Added per post type "edit_post" capability mapping in the editor ([#2846](https://github.com/elementor/elementor/issues/2846))
* Fix: Bump color picker script version to v2.0 to force a browser cached version refresh ([#2550](https://github.com/elementor/elementor/issues/2550))

= 1.8.6 - 2017-11-26 =
* Fix: Changed minimum editing capability to `edit_posts` ([#2791](https://github.com/elementor/elementor/issues/2791))
* Fix: Disable `white-space: pre-wrap` in advanced editing mode ([#2776](https://github.com/elementor/elementor/issues/2776))
* Fix: Check publish capabilities in the page settings
* Fix: Fixed line break in webkit in cases when there are no active nodes.
* Fix: Import/export template in some server configurations.

= 1.8.5 - 2017-11-19 =
* Fix: Compatibility with the WordPress 4.9 native widgets ([#2763](https://github.com/elementor/elementor/issues/2763))
* Fix: Removed related videos from background video in some cases ([#2372](https://github.com/elementor/elementor/issues/2372))
* Fix: Element inside similar element conflict ([#2760](https://github.com/elementor/elementor/issues/2760))
* Fix: Responsive alignment in Icon List widget

= 1.8.4 - 2017-11-14 =
* Fix: Accordion open/close icon ([#2740](https://github.com/elementor/elementor/issues/2740))
* Fix: Color control does not allow more than 7 characters ([#2737](https://github.com/elementor/elementor/issues/2737))
* Fix: Button wrap on Safari browser
* Fix: Print duplicate styles when Print Method set to Internal Embedding

= 1.8.3 - 2017-11-11 =
* Fix: Added nonce to export template action ([Topic](https://wordpress.org/support/topic/importation-error-in-library-elementor-8-2/))
* Fix: Native WordPress widgets stopped working ([#2732](https://github.com/elementor/elementor/issues/2732))

= 1.8.2 - 2017-11-09 =
* Fix: Added nonce to export template action ([#2722](https://github.com/elementor/elementor/issues/2722))
* Fix: Changes nonce key for third party plugins

= 1.8.1 - 2017-11-09 =
* Fix: Bug loading Editor templates in some server configurations ([#2712](https://github.com/elementor/elementor/issues/2712))
* Fix: Added parameter to overwrite control recursively ([#2702](https://github.com/elementor/elementor/issues/2702))
* Fix: Patched nonce validation for all library actions
* Fix: Glitch with Tabs widget on touch screen
* Fix: Glitch with Button wrap on small screens

= 1.8.0 - 2017-11-07 =
* New: Inline Editing added to all text, textarea & TinyMCE controls ([#437](https://github.com/elementor/elementor/issues/437))
* New: Added auto complete for code editor (Custom CSS & HTML widget) ([#2416](https://github.com/elementor/elementor/issues/2416))
* New: Added more icons to Social Icons widget: Telegram & OK ([#2670](https://github.com/elementor/elementor/issues/2670))
* New: Set focus to search bar when you click on widget button in the panel ([#2333](https://github.com/elementor/elementor/issues/2333))
* Tweak: Added `soft wrap` for code editor
* Tweak: Better accessibility for Tabs, Toggle & Accordion widgets
* Tweak: Added option to disable the widget on-change rendering
* Tweak: Improved error dialogs for better handling cases when the editor fails to load.
* Tweak: Rename Global Colors & Fonts to Default Colors and Default Fonts for better clarify
* Tweak: Update Google Fonts list with more than 10 new fonts
* Tweak: Eicons v2.5.0 updated
* Tweak: Added an "Elementor" post state for post table
* Tweak: Added responsive control for Icon Box (Space & Size)
* Tweak: Added compatibility and support for WP Color in WordPress 4.9 ([#2550](https://github.com/elementor/elementor/issues/2550))
* Tweak: Better nonce handling/renewing to avoid timeouts
* Tweak: Added compatibility for the future release of Elementor
* Fix: Icon List widget alignment with RTL
* Fix: Render element on unknown control changed
* Fix: Conflict `elementor-clickable` element with editor lightbox
* Fix: Handle download media/gallery inside repeater control for export/import

= 1.7.12 - 2017-10-24 =
* Fix: Stay in the same tab settings after saving
* Fix: Refresh heartbeat token when nonce is expired
* Fix: Space for mobile in Image Box widget ([#2586](https://github.com/elementor/elementor/issues/2586))
* Fix: Added visual indication for disabled swiper nav arrow button
* Fix: Color picker alignment for extended panel ([#2548](https://github.com/elementor/elementor/issues/2548))

= 1.7.11 - 2017-10-04 =
* Fix: `options is undefined` error when using select2

= 1.7.10 - 2017-10-03 =
* Fix: Extend of Icon control for Social Icons widget

= 1.7.9 - 2017-10-03 =
* Fix: Bug loading editor in Windows server

= 1.7.8 - 2017-10-03 =
* Fix: Font and Icon controls that got effected by previous update

= 1.7.7 - 2017-10-03 =
* Tweak: If current value is not in the options show it as `Unknown value` for Select2 control
* Fix: Import all template types by WordPress Importer
* Fix: Pagination color default for carousels
* Fix: Bug loading editor in some server configurations

= 1.7.6 - 2017-09-26 =
* Tweak: Changed video lightbox width for tablet to 100%
* Tweak: Changed Vimeo placeholder for the Video widget
* Tweak: Added `vh` unit support for control base
* Fix: Prevent showing of placeholder image in video lightbox
* Fix: Show Swiper arrows navigation in the center
* Fix: Set a centered image preview for media control in the panel
* Fix: Removed `sourceMappingURL` reference to prevent an error with the Safari browser

= 1.7.5 - 2017-09-24 =
* Tweak: Clear CSS Meta after change print method
* Tweak: Set default style for UI controls in all sliders and carousels
* Fix: Added compatibility for WordPress 4.8.2 & 4.7.6
* Fix: Sync Library tool for manual updating

= 1.7.4 - 2017-09-18 =
* Tweak: Added compatibility for the future release of Elementor
* Fix: Space widgets applied on the last element ([#2331](https://github.com/elementor/elementor/issues/2331))
* Fix: Internal CSS for Template Library embed ([#2394](https://github.com/elementor/elementor/issues/2394))

= 1.7.3 - 2017-09-11 =
* Tweak: Added responsive size for icon list widget ([#2302](https://github.com/elementor/elementor/issues/2302))
* Tweak: Added keyboard control for lightbox slideshow ([#2270](https://github.com/elementor/elementor/issues/2270))
* Tweak: Updated E-Icons font v2.4.2
* Fix: Changed image carousel breakpoints ([#2341](https://github.com/elementor/elementor/issues/2341))
* Fix: Handles editor view for Divi theme ([#2342](https://github.com/elementor/elementor/issues/2342))
* Fix: Live editing to the page settings custom CSS ([#2363](https://github.com/elementor/elementor/issues/2363))

= 1.7.2 - 2017-09-05 =
* Tweak: Added compatibility for the future release of Elementor
* Tweak: Added API option for developers to insert controls between preexisting controls
* Tweak: Added compatibility with Yoast SEO plugin
* Fix: Added compatibility for Multisite installation with Domain Mapping ([#2280](https://github.com/elementor/elementor/issues/2280))
* Fix: Disappearing widgets when you undo remove section ([#2301](https://github.com/elementor/elementor/issues/2301))

= 1.7.1 - 2017-08-29 =
* Tweak: Removed some filters for better performance
* Fix: Allow import `.zip` mime type to the library for some browsers / servers
* Fix: Save checking for the history log
* Fix: Change default template in page settings

= 1.7.0 - 2017-08-28 =
* New: History actions in the editor ([#266](https://github.com/elementor/elementor/issues/266))
* New: Hotkey: Ctrl / Cmd + Z = Undo
* New: Hotkey: Ctrl / Cmd + Shift + Z = Redo
* New: Hotkey: Ctrl / Cmd + D = Duplicate
* New: Hotkey: Delete = Delete element
* New: Added more icons to Social Icons widget: Weibo & WeChat
* Tweak: Added color control for UI elements in the lightbox
* Tweak: Allow to exclude devices in responsive control
* Tweak: Added compatibility for Table of Contents Plus plugin ([#2248](https://github.com/elementor/elementor/issues/2248))
* Fix: Added compatibility for Multisite installation with Domain Mapping
* Fix: CSS Animations names no longer minified, in order to prevent unexpected conflicts
* Fix: Sometimes content in Repeater control disappears
* Fix: Conflict rollback to the last version when beta testing mode enabled
* Fix: Conflict with Polylang plugin ([#2124](https://github.com/elementor/elementor/issues/2124))
* Fix: Allow fullscreen in the editor preview for video player

= 1.6.5 - 2017-08-20 =
* Tweak: Added compatibility for the future release of Elementor Pro
* Fix: Set font size inherit for Heading widget ([#2098](https://github.com/elementor/elementor/issues/2098))
* Fix: Anchor links smooth scrolling not working when admin bar is disabled ([#2210](https://github.com/elementor/elementor/issues/2210))

= 1.6.4 - 2017-08-06 =
* Tweak: Allow to close lightbox when clicking outside of the element
* Fix: Added font family inherit for input placeholder
* Fix: Reload none-saved changes on editor reload
* Fix: Added compatibility for WPMU DEV's Domain Mapping plugin ([#2120](https://github.com/elementor/elementor/issues/2120))

= 1.6.3 - 2017-08-09 =
* Fix: Lightbox for Image Carousel ([#2135](https://github.com/elementor/elementor/issues/2135))
* Fix: Allow to dismiss message asking to share non-sensitive usage data ([#2136](https://github.com/elementor/elementor/issues/2136))
* Fix: Conflict with Advanced TinyMCE plugin ([Topic](https://wordpress.org/support/topic/tinymce-unbreakable-space-not-display/))

= 1.6.2 - 2017-08-03 =
* Fix: More compatibility for some server configuration ([#2119](https://github.com/elementor/elementor/issues/2119))
* Fix: Added lightbox control for image gallery widget ([#2121](https://github.com/elementor/elementor/issues/2121))
* Fix: Conflict with TablePress and other plugins that add buttons to TinyMCE ([Topic](https://wordpress.org/support/topic/cant-view-editor-in-1-6-after-update/))

= 1.6.1 - 2017-08-02 =
* Fix: Conflict with Contact Form module by Jetpack ([#2125](https://github.com/elementor/elementor/issues/2125))
* Fix: Conflict with Popup Maker plugin
* Fix: Updated previous stable version for rollback
* Fix: Checking if the content area not found before anything else
* Fix: Condition hidden for section control didn't work well
* Fix: Reset border for iFrame in video lightbox ([#2121](https://github.com/elementor/elementor/issues/2121))

= 1.6.0 - 2017-08-02 =
* [Read more in the release post](https://elementor.com/v160-image-lightbox/)
* New: Added native Lightbox for images and galleries ([#218](https://github.com/elementor/elementor/issues/218))
* New: Added Text Shadow control ([#1696](https://github.com/elementor/elementor/issues/1696)) ([Developer API](https://github.com/elementor/elementor/blob/master/docs/content/controls/_text-shadow.md))
* New: Added Text Shadow option for Heading widget ([#1940](https://github.com/elementor/elementor/issues/1940))
* New: Added bulk import / export for template library ([#1241](https://github.com/elementor/elementor/issues/1241))
* New: Added `elementor/widgets/wordpress/widget_args` filter to customize WP widget markup ([#2052](https://github.com/elementor/elementor/issues/2052))
* Tweak: The editor loading now in the wp-admin area instead of the front-end
* Tweak: Replaced editor icons from FontAwesome to Eicons for beautiful sharpness
* Tweak: Added library access to all users with `edit_pages` capability
* Tweak: Loading scripts from the preview for more accurate front-end simulation and fix some issues
* Tweak: Replaced textarea input with WYSIWYG control for Testimonial widget ([#1321](https://github.com/elementor/elementor/issues/1321))
* Tweak: Changed default play icon for Video widget
* Tweak: First step to move Autoloader classes method
* Tweak: Switcher control `label_on` and `label_off` is not required anymore ([Developer API](https://github.com/elementor/elementor/blob/master/docs/content/controls/_switcher.md))
* Tweak: Style settings now also in the editor (under > menu > general settings)
* Tweak: Changed the colors of editor loading screen to positive design
* Tweak: Added Mute option for YouTube in Video widget ([#1897](https://github.com/elementor/elementor/issues/1897))
* Twaek! - Added compatibility for Polylang plugin ([#1959](https://github.com/elementor/elementor/issues/1959))
* Fix: Unknown text input in Group Controls ([#1926](https://github.com/elementor/elementor/issues/1926))
* Fix: Show edit with Elementor button on admin bar in regular posts
* Fix: Inherit style for select field from field
* Fix: Conflict with ManageWP on Multisite ([#1600](https://github.com/elementor/elementor/issues/1600), [#1456](https://github.com/elementor/elementor/issues/1456))
* Deprecated: `elementor/controls/get_available_tabs_controls` filter, please use `\Elementor\Controls_Manager::add_tab( $tab_name, $tab_title )` instead of

= 1.5.5 - 2017-07-18 =
* Fix: Page Settings data gets corrupted on revision save ([#2031](https://github.com/elementor/elementor/issues/2031))

= 1.5.4 - 2017-07-17 =
* Tweak: Re-organized SCSS files for the editor panel
* Tweak: Added example how to remove some styles from front-end ([#1992](https://github.com/elementor/elementor/issues/1992))
* Tweak: Added `do_action( 'elementor/preview/enqueue_scripts' );` for loading scripts in the preview only
* Tweak: Added `panel/widgets/{WIDGET_TYPE}/controls/wp_widget/loaded` JS action to handle WP widget controls ([#1886](https://github.com/elementor/elementor/issues/1886))
* Tweak: Changed Image placeholder
* Tweak: Return `checkbox` control as deprecated in v1.5 ([#2003](https://github.com/elementor/elementor/issues/2003))
* Fix: Changed carousel breakpoints for better responsiveness (Based on [#1785](https://github.com/elementor/elementor/issues/2003))
* Fix: After v1.5 some default unit in mobile editing set as pixel instead of the original default
* Fix: Removed `wptexturize` from Elementor content to avoid some plugins wrong texturize

= 1.5.3 - 2017-07-09 =
* Tweak: Google Fonts list updated with 17 new fonts
* Tweak: Added responsive control for min-height in Column widget
* Tweak: Added default value for HTML Tags
* Tweak: Added editor compatibility for some themes
* Fix: Added `latin-ext` subset in Font control for Polish language
* Fix: Updated control condition on Skin
* Fix: Glitch CSS in video iFrame
* Fix: Hover effect opacity transition
* Fix: Column background overlay condition in front-end
* Fix: Shape divider width units for tablet and mobile ([#1817](https://github.com/elementor/elementor/issues/1817))
* Fix: Video Background position center of section ([#1925](https://github.com/elementor/elementor/issues/1925))
* Fix: Toggle & Accordion glitch in the editor
* Fix: Hide errors for control without selector ([#1923](https://github.com/elementor/elementor/issues/1923))

= 1.5.2 - 2017-07-02 =
* Fix: Library dialog not loading in different languages
* Fix: Removed duplicate data from editor loading
* Fix: Navigation condition in Image Carousel widget ([#1920](https://github.com/elementor/elementor/issues/1920))
* Fix: Added `latin-ext` subset in Font control for Romanian language ([#1915](https://github.com/elementor/elementor/issues/1915))

= 1.5.1 - 2017-06-29 =
* Tweak: Show the fallback image until video playback starts ([#1901](https://github.com/elementor/elementor/issues/1901))
* Tweak: Set Animation delay value in milliseconds
* Fix: Buttons size for different languages ([Topic](https://wordpress.org/support/topic/cosmetic-for-french-interface/))
* Fix: Destroy the Waypoint after one running ([#1906](https://github.com/elementor/elementor/issues/1906))
* Fix: Image stretch condition in Image Carousel widget
* Fix: Restore value builder for rollback compatibility ([#1911](https://github.com/elementor/elementor/issues/1911))

= 1.5.0 - 2017-06-28 =
* New: A big UI makeover to the editor for the 1st birthday of Elementor ([#493](https://github.com/elementor/elementor/issues/493), [#335](https://github.com/elementor/elementor/issues/335), [#692](https://github.com/elementor/elementor/issues/692))
* New: Added responsive mode for Column Width control, including Tablet! ([#418](https://github.com/elementor/elementor/issues/418))
* New: Added option to set the column width trough the panel ([#847](https://github.com/elementor/elementor/issues/847))
* New: Added Element Hover for Background, BG Overlay, Border & Box Shadow
* New: Added `nofollow` option for all link controls ([Topic](https://wordpress.org/support/topic/elementor-nofollow-links-is-there-a-way-to-specify-links-nofollow-attribute/), [#953](https://github.com/elementor/elementor/issues/953), [#1695](https://github.com/elementor/elementor/issues/1695))
* New: Added HTML Tag for Section & Column ([#1619](https://github.com/elementor/elementor/issues/1619))
* New: Added Inset option for Box shadow control ([#1623](https://github.com/elementor/elementor/issues/1623))
* New: Added option to rollback to a previous version of Elementor & Pro
* New: Added option to get update notifications for beta versions of Elementor & Pro
* New: Added Space Between Widgets option under global settings and per column ([#1221](https://github.com/elementor/elementor/issues/1221))
* New: Added Z-Index option for all elements and removed original default `z-index` from `widgets-wrap` ([#1743](https://github.com/elementor/elementor/issues/1743))
* New: Import template get the page settings also if available
* Tweak: Added Tabs UI for admin setting pages
* Tweak: Added Layout tab for column for better workflow
* Tweak: Elementor not active when WP version doesn't meet minimum requirements
* Tweak: Added Animation Delay to Entrance Animation for all elements ([#558](https://github.com/elementor/elementor/issues/558))
* Tweak: Improved panel accessibility by adding `for` to the labels
* Tweak: Added responsive control for min-height in Section ([#630](https://github.com/elementor/elementor/issues/630))
* Tweak: Added responsive control for image size in Image / Image Box widgets
* Tweak: Added PX unit & responsive control for border width in Divider widget
* Tweak: Added responsive control for gap in Divider widget
* Tweak: Added responsive control for spacing in Image Box widget
* Tweak: Added responsive control for slides to show control in Image Carousel
* Tweak: Added responsive control for border radius in Image widget
* Tweak: Changed video embed method for better performance and fix loading bug
* Tweak: Changed `the_content` priority for better integration with 3rd party plugins (e.g WooCommerce Membership)
* Tweak: Added WP filter to get available image sizes
* Tweak: Updated Swiper library to 3.4.2
* Tweak: Limit up to 100 revisions to display in the panel for better performance
* Fix: YouTube link detection regex for some situations
* Fix: Content flashes before entrance animation ([#1672](https://github.com/elementor/elementor/issues/1672))
* Fix: Added `latin-ext` subset in Font control for Czech language ([#1630](https://github.com/elementor/elementor/issues/1630))
* Fix: Restore to post version without Elementor in Revision History
* Fix: Removed `margin: 0` setting from figure inside text editor widget
* Fix: Content flashes before entrance animation ([#1672](https://github.com/elementor/elementor/issues/1672))
* Fix: Bug Image Carousel widget in RTL direction
* Fix: `show_label` affected inner label in the repeater control ([#1707](https://github.com/elementor/elementor/issues/1707))

= 1.4.10 - 2017-06-25 =
* Fix: CSS `autoprefixer` for minified files (last 5 versions of browsers support)

= 1.4.9 - 2017-06-19 =
* Tweak: Compatibility with the new WordPress 4.8 widgets (Rich Text, Image, Video and Audio)
* Tweak: Disable Elementor editor in the default Blog page
* Fix: Bug post archive when first post set as Canvas template

= 1.4.8 - 2017-05-28 =
* New: Added new option: Set the CSS Print Method as Internal Embedding or External Files
* Tweak: CSS `autoprefixer` now supports last 5 versions of browsers
* Tweak: Nested all Swiper style under `elementor` class
* Fix: Open the first section when switching tabs not working
* Fix: Video widget cover image not displayed properly when lightbox on ([#1763](https://github.com/elementor/elementor/issues/1763))

= 1.4.7 - 2017-05-18 =
* Tweak: Added WP favicon support on Editor mode
* Fix: Do not return empty `<img>` tag when not found image source on Image Size group control

= 1.4.6 - 2017-05-09 =
* New: Added new Google font `Arsenal` for typography control
* Fix: An issue with custom size in Image Widget ([#1688](https://github.com/elementor/elementor/issues/1688))
* Fix: Tilt shape glitch in wide screens
* Fix: Increasing `z-index` for overlay settings in the editor ([#1209](https://github.com/elementor/elementor/issues/1209))
* Fix: The layout is reset when you drag the section
* Fix: Register events on different skins to register multiple handles in one widget
* Fix: Parse controls default settings by PHP
* Fix: Advanced style apply in the element inside element

= 1.4.5 - 2017-04-30 =
* Tweak: Use `update_metadata` instead of `update_post_meta` for revision history
* Fix: If Image Carousel caption is set to none, don't print the markup for the `figcaption`
* Fix: Don’t run buttons arrangement when TinyMCE has custom configuration
* Fix: Apply default value to desktop only for mobile editing
* Fix: Double rendering in the editor
* Fix: Prevent Elementor video autoplay in WordPress backend editor

= 1.4.4 - 2017-04-20 =
* Fix: Customizer is not loading in some cases ([Topic](https://wordpress.org/support/topic/cant-load-wp-customizer-because-of-get_css-error/))

= 1.4.3 - 2017-04-19 =
* Tweak: Avoid enqueue `post.css` for `the_excerpt`
* Tweak: Updated E-Icons font v2.1.0
* Fix: Conflict between different skins sharing a control with same prefix-class
* Fix: Added compatibility for more WP widgets ([Topic](https://wordpress.org/support/topic/cant-change-event-calendar-widget/))

= 1.4.2 - 2017-04-04 =
* Fix: Page Settings not reload on the page template ([#1586](https://github.com/elementor/elementor/issues/1586))
* Fix: Full width default value in preview mode
* Fix: Responsive switcher in repeater control

= 1.4.1 - 2017-04-03 =
* New: `Added action elementor/frontend/after_register_scripts` - runs after register scripts in the frontend
* New: `Added action elementor/editor/after_enqueue_styles` - runs after enqueue styles in the Editor
* New: `Added action elementor/editor/before_enqueue_styles` - runs before enqueue styles in the Editor
* New: `Added action elementor/editor/after_enqueue_scripts` - runs after enqueue scripts in Editor
* New: `Added action elementor/post-css-file/parse` to change Post CSS output
* Tweak: Added new tool for editor loader method
* Fix: Autosave settings on each change after 3 seconds ([#1546](https://github.com/elementor/elementor/issues/1546))
* Fix: Reset column resize after section sorting
* Fix: Incorrect wrapper height in some cases in Video lightbox
* Fix: ACE editor lines gutter overflows over panel footer menu ([#1575](https://github.com/elementor/elementor/issues/1575))

= 1.4.0 - 2017-03-28 =
* New: Canvas: Native Blank Page Template. No header, no footer, just Elementor
* New: Maintenance Mode for Under Contraction and Coming Soon page
* New: Page Settings: Choose Page Template, Change Status, Edit / Hide Page Title ([#632](https://github.com/elementor/elementor/issues/632), [#447](https://github.com/elementor/elementor/issues/447))
* New: Page Style: Padding and Background Color, Image or Gradient
* New: Drop Cap option for Text Editor widget
* New: Added Debug box in the System Info screen, to keep a record of recent error messages in the editor
* New: Added more icons to Social Icons widget: Yelp, Xing, Email, Shopping Cart and Whatsapp ([#1462](https://github.com/elementor/elementor/issues/1462), [#1463](https://github.com/elementor/elementor/issues/1463), [#1471](https://github.com/elementor/elementor/issues/1471), [#1481](https://github.com/elementor/elementor/issues/1481))
* Tweak: Added hover style and animation for Social Icons widget ([#426](https://github.com/elementor/elementor/issues/426), [#1472](https://github.com/elementor/elementor/issues/1472))
* Tweak: Removed unnecessary data settings from frontend output
* Fix: Duplicate repeater field with switcher control ([#1442](https://github.com/elementor/elementor/issues/1442), [#1472](https://github.com/elementor/elementor/issues/1472))
* Fix: Google Font family with spacing
* Fix: Custom image size in some situations ([Topic](https://wordpress.org/support/topic/insider-elementor-editor-error-500-admin-ajax/))
* Fix: Smooth scrolling for anchor links, now limited only to links with `.elementor-element` or `.elementor-menu-anchor` classes ([#1478](https://github.com/elementor/elementor/issues/1478))

= 1.3.5 - 2017-03-20 =
* Tweak: Minimum WordPress version is now v4.5
* Fix: Shape divider glitch on some screen widths
* Fix: Shape divider flip bug in safari browser
* Fix: Conflict with jQuery FitVids plugin ([Topic](https://wordpress.org/support/topic/video-lightbox-4/))
* Fix: Generated CSS-file breakpoint for tablet - changed from `1023px` to `1024px` ([#1454](https://github.com/elementor/elementor/issues/1454))
* Fix: Close HTML `div` for Alert widget

= 1.3.4 - 2017-03-14 =
* Tweak: Added more hooks for handling styles & scripts ([Topic](https://wordpress.org/support/topic/is-there-way-to-remove-google-font-link/))
* Tweak: Added Swiper library for future widgets
* Fix: Added Revision History for all Elementor-enabled CPTs
* Fix: Shapes for RTL direction
* Fix: Issue with images not loading in some situations
* Fix: Click on arrow icon in select field

= 1.3.3 - 2017-03-08 =
* Fix: Negative shape rotation in front-end ([#1438](https://github.com/elementor/elementor/issues/1438))
* Fix: Error with anchor links with an invalid target ([Topic](https://wordpress.org/support/topic/popup-maker-not-working-on-elementor-1-3-2-pages/))
* Fix: Alignment issue in Icon List widget

= 1.3.2 - 2017-03-07 =
* Tweak: Added Bring to Front option for shape divider
* Fix: Normalize template data in some situations ([#1432](https://github.com/elementor/elementor/issues/1432))
* Fix: Removed shapes handler JS from front-end
* Fix: Added support for shapes on Edge browser ([#1427](https://github.com/elementor/elementor/issues/1427))
* Fix: Shapes glitch on some screen width

= 1.3.1 - 2017-03-07 =
* Fix: Insert or embed template in the editor ([#1426](https://github.com/elementor/elementor/issues/1426), [#1425](https://github.com/elementor/elementor/issues/1425))
* Fix: Imported templates were not saved correctly ([Topic](https://wordpress.org/support/topic/new-version-not-loading-2/))
* Fix: Comparing default values for array or multiple controls

= 1.3.0 - 2017-03-06 =
* New: Added Shape Divider to sections
* New: Added Lightbox for video widget ([#741](https://github.com/elementor/elementor/issues/741))
* New: Added new social icons for Slideshare, Vkontakte & Tripadvisor
* New: Print JS file just when is needed, by new method `Widget::get_script_depends()` ([Code Reference](https://github.com/elementor/elementor-hello-world/blob/5d37a45a9419ecb825e1706eb83689dfa0b252f8/widgets/hello-world.php#L34-L42))
* Tweak: Improved Icon List widget by adding: line-up, divider and space between options ([#822](https://github.com/elementor/elementor/issues/822))
* Tweak: Added box shadow control for Button widget ([#1357](https://github.com/elementor/elementor/issues/1357))
* Tweak: Don't use `html_entity_decode` on json posted data (Improving JSON format for saving Data by deprecated `html_entity_decode`)
* Tweak: Element-ID as anchor has no smooth scroll - Added ability to pass element id to selectors ([#1333](https://github.com/elementor/elementor/issues/1333))
* Fix: Added margin top property in `.elementor-inner` class for better handling with fixed headers

= 1.2.4 - 2017-02-28 =
* Tweak: Improved embed google fonts in the front-end
* Tweak: Added selector in Button widget to override custom style in some themes ([#1285](https://github.com/elementor/elementor/issues/1285))
* Tweak: Load unminified color-picker.js file when `SCRIPT_DEBUG` is `true` ([#1364](https://github.com/elementor/elementor/issues/1364))
* Fix: Bug when dragging a column from a single-column section to another section ([#1346](https://github.com/elementor/elementor/issues/1346))
* Fix: Reduced padding for Progress Bar widget in mobile ([#1358](https://github.com/elementor/elementor/issues/1358))
* Fix: Descriptor field style in the repeater field
* Fix: Alt key for some keyboard input sources ([#1328](https://github.com/elementor/elementor/issues/1328))

= 1.2.3 - 2017-02-14 =
* Fix: Typography group data render ([Topic](https://wordpress.org/support/topic/erro-after-upgrading-to-version-1-2-2/))

= 1.2.2 - 2017-02-14 =
* Tweak: Added filter to change the menu anchor distance
* Tweak: Regenerate CSS run after editor saved for quicker saving
* Tweak: Major performance improvement by optimizing backend method calls
* Fix: Selector `elementor:after` added spacing after the Elementor content
* Fix: Removed inline CSS from default section in frontend

= 1.2.1 - 2017-02-08 =
* Fix: Change on Repeater control is not effect in the preview
* Fix: When you add a widget base on remote only is render twice

= 1.2.0 - 2017-02-01 =
* New: Added Gradient Background for Section and Column ([#71](https://github.com/elementor/elementor/issues/71))
* New: Added full caption functionality to Image Carousel widget ([#1212](https://github.com/elementor/elementor/issues/1212), [#1124](https://github.com/elementor/elementor/issues/1124), [#1197](https://github.com/elementor/elementor/issues/1197))
* New: Added Custom Element ID control for all elements ([#939](https://github.com/elementor/elementor/issues/939))
* New: Added Vertical layout option for Tabs widget ([#449](https://github.com/elementor/elementor/issues/449))
* Tweak: Elementor now generates an external CSS file instead of inline CSS for global fonts, colors and settings (Part 2 of [#325](https://github.com/elementor/elementor/issues/325))
* Tweak: Added Content Position option for each column
* Tweak: Regenerate CSS after running Replace URL
* Fix: Tabs on mobile now work like an Accordion widget to improve responsive design ([#443](https://github.com/elementor/elementor/issues/443))
* Fix: Improve query in Replace URL for some configurations ([#1191](https://github.com/elementor/elementor/issues/1191))
* Fix: Added `none` option for Text Transform typography control
* Fix: Bug when some controls left empty in Testimonial widget

= 1.1.7 - 2017-01-25 =
* Tweak: Change `box-shadow` type control from select to switcher
* Fix: On-loading glitch for Image Carousel widget
* Fix: Background attachment for Section on mobile devices ([#890](https://github.com/elementor/elementor/issues/890))
* Fix: Default WordPress widget checkboxes not holding values ([#1210](https://github.com/elementor/elementor/issues/1210))
* Fix: Heading with link custom color gone
* Fix: Jumping of panel when the `wp-color-picker` is active
* Deprecated: `Plugin::get_version()` method removed

= 1.1.6 - 2017-01-18 =
* Fix: Improving enqueue assets if Elementor exists on the page
* Fix: Inner Tabs in the section panel is broken

= 1.1.5 - 2017-01-17 =
* Fix: Cannot open editor panel on WP widgets

= 1.1.4 - 2017-01-17 =
* Tweak: Added compatibility for edit description tab with Elementor in WC product page
* Tweak: Performance improvement in the editor. Faster response time when interacting with elements.
* Fix: Broken responsive in Editor mode ([#1169](https://github.com/elementor/elementor/issues/1169))
* Fix: Animation happens twice above the fold ([#281](https://github.com/elementor/elementor/issues/281))

= 1.1.3 - 2017-01-15 =
* Tweak: Embed YouTube API just when is needed
* Tweak: Added post revisions support as default to any CPT Elementor is active on
* Fix: E-Icons font library correct files
* Fix: Prevent click event on parent elements when clicking inner element handle buttons
* Fix: Error message for import template

= 1.1.2 - 2017-01-12 =
* Fix: Clear browser caching from last new JS files

= 1.1.1 - 2017-01-11 =
* Fix: Revision history panel title
* Fix: Renamed content tab in Column to style
* Fix: Regenerate new Waypoints JS lib

= 1.1.0 - 2017-01-11 =
* New: Revision History (Based on WP Revisions)
* New: Added Tool for Replace Site URL in Elementor data
* New: Hotkey: Ctrl / Cmd + S = Save
* New: Hotkey: Ctrl / Cmd + P = Preview Mode
* New: Hotkey: Ctrl / Cmd + Shift + L = Open Library Modal
* New: Hotkey: Ctrl / Cmd + Shift + H = Go to Revision History
* New: Hotkey: Ctrl / Cmd + Shift + M = Mobile Editing Preview
* New: Added Background Overlay Settings for Column ([#810](https://github.com/elementor/elementor/issues/810))
* Tweak: Enqueue assets only if Elementor exists on the page
* Tweak: Move all element ID's in editor mode to classes (Start working on [#939](https://github.com/elementor/elementor/issues/939))
* Fix: Added missing translation strings for media modal ([#1126](https://github.com/elementor/elementor/issues/1126))

= 1.0.12 - 2017-01-05 =
* Fix: Library modal 'Insert' button

= 1.0.11 - 2017-01-04 =
* Fix: Some breaks from previous release

= 1.0.10 - 2017-01-04 =
* New: Added API docs for developers
* Tweak: Changed 'Prevent Scroll' control type to switcher in Map widget
* Tweak: Updated Dialog Manager v3.0.2
* Fix: Library modal 'Go Pro' button in FireFox
* Fix: Bug load Elementor in front page in some server configuration
* Fix: Ninja Forms widget conflict
* Deprecated: `elementor/frontend/enqueue_scripts/after` is no longer available
* Deprecated: `elementor/elements/print_template` is replaced by `elementor/element/print_template`
* Deprecated: `elementor/element_css/parse_css` is replaced by `elementor/element/parse_css`

= 1.0.9 - 2016-12-27 =
* Fix: Elementor library import remote template

= 1.0.8 - 2016-12-27 =
* Tweak: Added raw css support for stylesheet class ([#1086](https://github.com/elementor/elementor/issues/1086))
* Tweak: Improve memory used in the editor mode ([Topic](https://wordpress.org/support/topic/need-update-folks/))
* Fix: Issue with post-css-file in HTTPS ([#1077](https://github.com/elementor/elementor/issues/1077))

= 1.0.7 - 2016-12-25 =
* Fix: Validate export import for Elementor template
* Fix: Excluded `elementor_library` custom post type from the sitemap by Yoast SEO
* Fix: Prevent Scroll BUG in Google Map widget
* Fix: Added unique name for repeater control

= 1.0.6 - 2016-12-20 =
* New: Added social icons for Apple & Spotify
* New: Added Thousand Separator control for counter widget
* Tweak: Added Filter template types in library backend
* Tweak: Updated jQuery Numerator Plugin v0.2.1
* Fix: Added support for floating numbers in counter widget
* Fix: Removed limit from counter widget
* Deprecated: Removed `#elementor-section-wrap` ID from frontend output
* Deprecated: Removed `#elementor-inner` ID from frontend output

= 1.0.5 - 2016-12-18 =
* Tweak: Added VH unit in min-height control for section ([#764](https://github.com/elementor/elementor/issues/764))
* Fix: Bug with section-content-position in Columns widget
* Fix: Bug with stretched section
* Fix: Avoid fatal error for invalid widgets
* Deprecated: Removed `#elementor` ID from frontend output

= 1.0.4 - 2016-12-12 =
* Tweak: Added mobile editing options for social icons widget
* Fix: Icon alignment CSS issue (Icon and Icon-Box widget)
* Fix: Conflict panel with Safari browser
* Fix: Responsive grid bug with tablet screen

= 1.0.3 - 2016-12-11 =
* New: Ready for Elementor Pro
* Tweak: Added template type validation
* Fix: Select2 control fix empty state

= 1.0.2 - 2016-12-08 =
* Fix: Adjusting Text Editor widget when visual editor is disabled ([Topic](https://wordpress.org/support/topic/text-editing-menu-is-blank/))
* Fix: Conflict with Advanced TinyMCE plugin ([Topic](https://wordpress.org/support/topic/1-0-1-disabled-the-text-edit-widget/))

= 1.0.1 - 2016-12-08 =
* Fix: Waypoint script for multiple versions ([#933](https://github.com/elementor/elementor/issues/933), [#1001](https://github.com/elementor/elementor/issues/1001))
* Fix: Text Editor widget compatible for WordPress 4.7
* Fix: Label form compatible for TwentySeventy theme

= 1.0.0 - 2016-12-06 =
* New: Added code editor for HTML widget
* New: Added social icon for Houzz
* New: Added Code control base on ACE
* New: Added Tabs control for editor panel
* New: Forms style for future widgets
* Tweak: Update Waypoints library to v4.0.1
* Tweak: Added support for multiple icon fonts in Choose control
* Fix: Progress Bar "glitch" on page refresh ([#909](https://github.com/elementor/elementor/issues/909))
* Fix: post-css-file don't enqueue if isn't Elementor post ([#902](https://github.com/elementor/elementor/issues/902))
* Fix: Columns widget not render on search ([#862](https://github.com/elementor/elementor/issues/862))
* Fix: Conflict with Bootstrap 3 Shortcodes plugin ([#924](https://github.com/elementor/elementor/issues/924))
* Fix: Don't generate or enqueue CSS file if post not built with elementor
* Fix: Icon button RTL bug in tools page
* Fix: Color Picker control bug on FireFox
* Fix: Video background for section works again
* Fix: Draggable line for columns widget

= 0.11.2 - 2016-11-21 =
* Fix: Don't Generate CSS if it's not built with Elementor
* Fix: Global font goes wrong after changing weight ([#888](https://github.com/elementor/elementor/issues/888))

= 0.11.1 - 2016-11-17 =
* Fix: Columns widget bug with edit options
* Tweak: Changed breakpoint for tablet from 1023px to 1024px ([#860](https://github.com/elementor/elementor/issues/860))

= 0.11.0 - 2016-11-16 =
* New: Elementor now generates an external CSS file for each page instead of inline CSS ([#325](https://github.com/elementor/elementor/issues/325))
* New: Added a tool to regenerate Elementor pages CSS files
* New: Added TinyMCE editor for Repeater control, such as Tabs, Accordion and Toggle widgets ([#176](https://github.com/elementor/elementor/issues/176))
* New: Added Time Picker control for future widgets
* Tweak: 'Edit with Elementor' button color now uses the Admin Color Scheme
* Tweak: Improved style for Multiple Select2 control
* Tweak: Removed development mode flag
* Tweak: Elementor Icon library updated with new icons
* Tweak: Align button sizes as a new forms style
* Tweak: Panel width expanded for better comfortable editing
* Tweak: TinyMCE editor toolbar reduced to basic toolbar, for comfortable editing

= 0.10.7 - 2016-11-07 =
* Fix: Image widget template for link and caption ([Topic](https://wordpress.org/support/topic/image-not-aligning-when-linked/))
* Fix: Exclude Library from search ([Topic](https://wordpress.org/support/topic/custom-templates-accidentally-show-up-in-search-results/))
* Fix: HTML of text widget not being parsed in the preview ([Topic](https://wordpress.org/support/topic/html-of-text-widget-not-being-parsed-in-the-preview/))
* Fix: Add new section button in RTL
* Fix: Stretched section in RTL
* Fix: TinyMCE editor buttons in RTL
* Fix: Error messages for template library

= 0.10.6 - 2016-11-02 =
* Fix: Some breaks layouts from previous release

= 0.10.5 - 2016-11-02 =
* Tweak: Added Compatibility for Autoptimize Plugin ([Topic](https://wordpress.org/support/topic/open-link-in-new-tab-doesnt-work/))
* Tweak: Now `elementor_library` custom post type excluded from the sitemap by Yoast SEO ([Topic](https://wordpress.org/support/topic/disable-elementor_library-sitemap-xml/))
* Tweak: Added Compatibility for Zerif Pro theme
* Tweak: Added Compatibility for themes by Bluchic
* Tweak: Added Compatibility for jQuery Masonry Image Gallery plugin ([#762](https://github.com/elementor/elementor/issues/762))
* Tweak: Added support for "multiple value" condition in Repeater control
* Fix: Bug corrupting JSON post meta on import Elementor pages ([Topic](https://wordpress.org/support/topic/pages-importexport-not-working-properly/))
* Fix: `.elementor-slick-slider` class now apply on all Elementor custom slick theme ([#424](https://github.com/elementor/elementor/issues/424))
* Fix: Added subset support for Google Fonts to fix issues with some browsers

= 0.10.4 - 2016-10-26 =
* Tweak: Updated Font Awesome v4.7.0 (41+ icons)
* Tweak: Added type attribute for text input control
* Tweak: Improved import template from library
* Tweak: Removed admin email from system info copied
* Fix: Columns overlay layer for negative top margin scenario
* Fix: Added i18n strings to Delete All Content dialog
* Fix: Target blank link in heading widget - ([Topic](https://wordpress.org/support/topic/open-link-in-new-tab-doesnt-work/))

= 0.10.3 - 2016-10-13 =
* Fix: Hover animation classes for Image widget ([Topic](https://wordpress.org/support/topic/update-issue-animation/), [Topic](https://wordpress.org/support/topic/hover-animation-2/))
* Fix: Columns gap for all devices
* Fix: Added trigger for changes on Apply button
* Fix: Inner section class on front-end

= 0.10.2 - 2016-10-10 =
* Tweak: Added Apply button for Shortcode widget to trigger loading of external scripts
* Tweak: Restore action `elementor/widgets/widgets_registered`
* Fix: Conflict with old versions of Bfi_Thumb plugin
* Fix: Conflict with other plugins ([Topic](https://wordpress.org/support/topic/0-10-1-fatal-error-with-html-editor-syntax-highlighter/))

= 0.10.1 - 2016-10-09 =
* Fix: Image size default

= 0.10.0 - 2016-10-09 =
* New: Choose which colors appear in the editor's color picker. This makes accessing the colors you choose for the site much easier
* New: Clear the entire page content in one click ([#607](https://github.com/elementor/elementor/issues/607))
* New: Added image size control for Image widget ([#537](https://github.com/elementor/elementor/issues/537))
* New: Added social icon for Twitch.tv ([#694](https://github.com/elementor/elementor/issues/694))
* Tweak: Data stored in a JSON format instead of serialize, for smoother migration ([#403](https://github.com/elementor/elementor/issues/403))
* Tweak: Added Apply button for all WP widgets to trigger loading of external scripts
* Tweak: Adding a preview icon for repeater control ([#609](https://github.com/elementor/elementor/issues/609))
* Tweak: All widgets code rewritten to allow for better API flexibility
* Fix: Icon under 20px size now looks good
* Fix: Improve CSS for Icon list alignment ([#620](https://github.com/elementor/elementor/issues/620))
* Fix: Ninja Forms widget conflict ([#546](https://github.com/elementor/elementor/issues/546))
* Fix: Global Fonts Don't Show Up on Live Preview Screen ([#606](https://github.com/elementor/elementor/issues/606))

= 0.9.3 - 2016-09-26 =
* Tweak: New method for import template library
* Tweak: Changed priority for 3rd party plugins
* Fix: Conflict with WooCommerce Memberships
* Fix: Conflict with Lifter LMS ([#612](https://github.com/elementor/elementor/issues/612))
* Fix: Icon list correct alignment ([#620](https://github.com/elementor/elementor/issues/620))
* Fix: Reload the iframe causes the panel to stop working

= 0.9.2 - 2016-09-21 =
* Fix: Added color default for Progress Bar when schemes color is disabled
* Fix: Stretched section in RTL bug

= 0.9.1 - 2016-09-20 =
* Fix: Changed again "fit to screen" to height for "content position" support

= 0.9.0 - 2016-09-20 =
* New: Added stretch section control for all parent sections
* New: Added content width option to set the default width of the content area
* Tweak: Changed Fit to Screen to `min-height` for better behavior on mobile and content overflow scenarios.
* Tweak: Added Switcher control for editor panel ([#143](https://github.com/elementor/elementor/issues/143))
* Tweak: Improved Integration with 3rd party plugins and themes
* Fix: Hidden templates library items from nav menu
* Fix: Added color default for Button and Icon when schemes color is disabled
* Fix: Added default size for the Heading widget - ([#533](https://github.com/elementor/elementor/issues/533))
* Fix: Button border color hover control - only shown when border is set

= 0.8.1 - 2016-09-11 =
* Tweak: Changed prevent scroll default to `Yes` for Maps widget
* Tweak: Removed `asp_tags` requirements
* Tweak: Added alignment responsive support for Image Box and Icon Box widgets
* Tweak: Added mobile editing for Spacer widget
* Tweak: Added title spacing for Image Box and Icon Box
* Tweak: Removed quick link to Edit with Elementor for non-Elementor pages ([#539](https://github.com/elementor/elementor/issues/539))
* Fix: Correcting title link color in Image Box widget ([#531](https://github.com/elementor/elementor/issues/531))
* Fix: Resolved responsive bug in Image Gallery widget
* Fix: Resolved bug in editor for Safari ([#530](https://github.com/elementor/elementor/issues/530), [#540](https://github.com/elementor/elementor/issues/540))

= 0.8.0 - 2016-09-07 =
* New: Added columns ordering on mobile normal / reverse
* New: Now you can edit section and column in tablet or mobile mode
* New: Set font-size per device in Typography control
* New: Set line-height per device in Typography control
* New: Set letter-spacing per device in Typography control
* New: Set padding per device in Advanced tab
* New: Set margin per device in Advanced tab
* New: Heading widget - Set alignment per device
* New: Button widget - Set alignment per device
* New: Image widget - Set alignment per device
* New: Icon widget - Set alignment per device
* New: Divider widget - Set alignment per device
* New: Icon List Widget - Set alignment per device
* Tweak: Elementor Library connection status indicator added in System Info
* Tweak: Default content-width is now set by css and not by the panel
* Tweak: Added shortcode and oEmbed support for Tabs / Accordion / Toggle / Text Editor widgets
* Tweak: Removed Default font size in custom option
* Tweak: Hide update notice from outside Elementor
* Tweak: Removed mobile-landscape and laptop sizes from preview mode
* Tweak: Rearrange TinyMCE buttons ([#444](https://github.com/elementor/elementor/issues/444))
* Tweak: Changed range for font size control
* Tweak: Compatibility with `asp_tags`
* Tweak: Improved compatibility for old webkit browsers ([#484](https://github.com/elementor/elementor/issues/484), [#403](https://github.com/elementor/elementor/issues/403), [#370](https://github.com/elementor/elementor/issues/370))
* Fix: Removed style from gallery caption
* Fix: Wrong index in section sorting
* Fix: Column indication on inner section dragging
* Fix: Gap for Columns Widget (nested section)
* Fix: Preview mode with real sizes for mobile and tablet
* Fix: Sortable inner section not showing placeholder
* Fix: Network plugins included in System Info
* Deprecated: Column width option for mobile portrait is deprecated. Use Mobile Width option instead

= 0.7.4 - 2016-08-24 =
* New: Added Shortcode widget
* Tweak: Now compatible with password protected pages
* Fix: Library RTL bug in template preview
* Fix: Bug with `p` tag in Icon Box widget
* Fix: Style in Icon widget
* Fix: HTML widget now is working

= 0.7.3 - 2016-08-19 =
* Fix: Allow columns gap on mobile
* Fix: Content position for section

= 0.7.2 - 2016-08-18 =
* Tweak: Hide add section area for mobile device
* Tweak: Optimization assets files
* Fix: Intrusive Slick Carousel CSS ([#424](https://github.com/elementor/elementor/issues/424))
* Fix: Display content position for custom height section option

= 0.7.1 - 2016-08-17 =
* Tweak: Added column/content position for inner section
* Tweak: Block template library in the frontend
* Fix: Export template ([topic](https://wordpress.org/support/topic/template-export-not-working-in-070))
* Fix: Fatal Error during update plugin ([#412](https://github.com/elementor/elementor/issues/412))
* Fix: Separating link attributes from classes attributes in Icon Box widget ([#414](https://github.com/elementor/elementor/issues/414))
* Fix: Allow column and content position for mobile
* Fix: Removed `overflow: hidden` from `#elementor-inner` ([#415](https://github.com/elementor/elementor/issues/415))
* Fix: Aspect ratio issue for Video widget ([topic](https://wordpress.org/support/topic/video-embeds-have-black-bars-top-and-bottom))
* Fix: Image Carousel "slides to show" on tablet ([#372](https://github.com/elementor/elementor/issues/372))
* Fix: RTL style for library
* Fix: Handle errors on wrong export action

= 0.7.0 - 2016-08-16 =
* New: Template Library ([Release Post](https://elementor.com/introducing-template-library/?utm_source=wp-repo&utm_medium=link&utm_campaign=readme))
* New: Save your pages and sections to template library
* New: 20+ beautifully pre-designed templates
* New: Export / Import templates
* Fix: Changed code structure in Icon/Icon-Box/Social-Icons widget templates
* Fix: Screen size for hidden tablet class
* Fix: Blank page detection in empty pages for editing

= 0.6.6 - 2016-08-11 =
* Tweak: Improved Drag & Drop area to new section
* Tweak: Make changes in new section
* Fix: Resize columns with Widgets using iframe (YouTube, Google Maps, etc)
* Fix: Adding columns widget in 'Add new Section' area
* Fix: Allow blank target link for social-icons widget

= 0.6.5 - 2016-08-08 =
* Tweak: Added border and box shadow controls for Image widget ([#224](https://github.com/elementor/elementor/issues/244))
* Tweak: In new page don't create empty section
* Tweak: Embed Roboto font from Google Fonts in editor mode
* Fix: Reset structure or remove column after resize

= 0.6.4 - 2016-08-04 =
* Tweak: Google Fonts list updated ([#371](https://github.com/elementor/elementor/pull/371))
* Fix: Content overflow in "fit to screen" sections for mobile ([#369](https://github.com/elementor/elementor/issues/369))
* Fix: Icon Box Widget: Better support for Safari browser
* Fix: Text Editor Widget: Added listening to undo & redo events ([#340](https://github.com/elementor/elementor/issues/340))

= 0.6.3 - 2016-08-01 =
* Tweak: Show element title in delete dialog ([#337](https://github.com/elementor/elementor/pull/337))
* Tweak: Added responsive support for Image Gallery widget ([topic](https://wordpress.org/support/topic/galery-not-responsive))
* Tweak: CSS files for entrance animation and hover animations combine to `animations.min.css` file
* Fix: Columns resize cannot be repeated ([#349](https://github.com/elementor/elementor/issues/349))
* Fix: Anchor Menu widget in Safari browser
* Fix: Bug when global font set as a default ([topic](https://wordpress.org/support/topic/font-family-bug))

= 0.6.2 - 2016-07-27 =
* Tweak: Improve the way using attachment details
* Tweak: Verifies the AJAX request to prevent processing requests external of the editor
* Fix: Hover animation classes in Image widget
* Fix: Icon spacing on mobile in Icon Box widget
* Fix: Image spacing on mobile in Image Box widget

= 0.6.1 - 2016-07-26 =
* Tweak: Improved editor UI for blank pages
* Fix: Hide empty widgets in preview mode
* Fix: Bug with Section background overlay and background video ([#323](https://github.com/elementor/elementor/issues/323))
* Fix: Minor security issue

= 0.6.0 - 2016-07-25 =
* New: Added 27 Hover Animations for Image / Icon / Button widgets
* New: Editor panel now is resizable
* New: Added smooth scrolling for anchors in Anchor Menu widget
* Tweak: Improved performance
* Tweak: Improved speed in live editing
* Tweak: Optimization for memory usage (30% less!)
* Tweak: Improved UI for preview editor
* Tweak: Add `contain` background image CSS option ([#297](https://github.com/elementor/elementor/pull/297))
* Tweak: Added compatibility for NextGen Gallery plugin ([#296](https://github.com/elementor/elementor/issues/296))
* Tweak: Print JS scripts in the footer
* Tweak: Set color picker control `alpha` for all widgets
* Fix: Remove `maxlength` attribute from color picker input ([#298](https://github.com/elementor/elementor/issues/298))
* Fix: Background video: Set object-fit only HTML5 player (solved Safari YouTube issue)
* Fix: Bug with target blank link in Icon List widget
* Fix: Bug with alignment in Icon List widget ([#283](https://github.com/elementor/elementor/issues/283))
* Fix: Improved responsive in Tabs widget ([#279](https://github.com/elementor/elementor/issues/279))
* Fix: RTL bug in Alert widget ([#287](https://github.com/elementor/elementor/issues/287))

= 0.5.2 - 2016-07-17 =
* New: Added Animation Duration control for all elements
* New: Added Prevent Scroll control for Google Maps widget

= 0.5.1 - 2016-07-14 =
* Tweak: Added details image sizes in Image Sizes control
* Fix: Chrome bug with flex mode in Image Box widget
* Fix: Textarea control: set `label_block` to `true` by default
* Fix: Textarea control style for Icon Box Widget
* Fix: Removed translations from system-info for better understanding support
* Fix: Design fixes for Icon List widget

= 0.5.0 - 2016-07-13 =
* New: Added more than 35 Entrance Animations to all elements
* New: Added Box Shadow control to all elements
* Tweak: Added option to disable colors palettes in the settings
* Tweak: Added option to disable fonts default in the settings
* Tweak: Added hidden title to alert & image box widgets
* Tweak: Added group posts widget from Pojo themes
* Tweak: Remove extra margin bottom on Textarea ([#182](https://github.com/elementor/elementor/issues/182))
* Tweak: Applying border-radius on background overlay ([#249](https://github.com/elementor/elementor/issues/249))
* Tweak: Fixed toggle widget RTL style
* Tweak: Updated Font Awesome v4.6.3
* Tweak: Changed Textarea control markup according to the general concept
* Tweak: Fixed modal RTL style
* Fix: Fade effect in Image Carousel widget ([#245](https://github.com/elementor/elementor/issues/245), [#253](https://github.com/elementor/elementor/issues/253))
* Fix: Don't print anything when no have link in Video widget

= 0.4.1 - 2016-07-05 =
* Tweak: Default structure presets changed for new section
* Tweak: Added more strings to translate
* Fix: Fixed alignment for Image widget
* Fix: Some compatible to underscore 1.6.0 (Bundle from WordPress v4.4)

= 0.4.0 - 2016-07-04 =
* New: Section: Background Overlay for image and video background
* New: Added Social Icons widget
* New: Added Testimonial widget
* New: Added SoundCloud (Audio) widget
* Tweak: Fixed Bug with background in Image widget ([#180](https://github.com/elementor/elementor/issues/180))
* Tweak: Assign a field to be used as the item title for Repeater control
* Tweak: Rearrange column settings panel
* Tweak: Add link to carousel widget
* Fix: Bug carousel widget with `dir="rtl"`

= 0.3.2 - 2016-06-27 =
* Tweak: Added fully compatible for Cache plugins
* Tweak: Image widget: `<img>` alt and title fetch from Media library
* Tweak: Image widget: Added link to file media
* Tweak: Image-Box widget: `<img>` alt and title fetch from Media library
* Tweak: Carousel widget: `<img>` alt fetch from Media library
* Tweak: Add more columns options for responsive mode
* Tweak: Add `<title>` in Elementor editor
* Tweak: Rearranged section settings
* Tweak: Add more gap size (Wider)
* Tweak: Add more color schemes
* Tweak: Add negative value for letter spacing

= 0.3.1 - 2016-06-22 =
* Tweak: Added a quick tour video for Elementor
* Tweak: Enqueue YouTube API from JS (Better compatible for more themes)
* Tweak: Added compatible to WooCommerce widgets
* Tweak: Fixes re-render elements performance
* Fix: Border & Radius options for Image Carousel widget

= 0.3.0 - 2016-06-21 =
* New: Added Image Gallery widget
* New: Added Image Carousel widget
* Tweak: Fixed schemes area
* Tweak: Added spacing icon in Button widget
* Fix: Themes without `the_content()` stop the Editor
* Fix: Height option in Columns widget

= 0.2.5 - 2016-06-16 =
* Tweak: Added Vimeo support for widget Video
* Tweak: Improved UI in admin area and panel
* Tweak: Added body class `elementor-page` in frontend ([#58](https://github.com/elementor/elementor/issues/58))
* Fix: Elementor seems to get stuck while loading ([#77](https://github.com/elementor/elementor/issues/77))
* Fix: WP Widgets with multiple checkboxes ([#90](https://github.com/elementor/elementor/issues/90))

= 0.2.4 - 2016-06-13 =
* Tweak: Added some compatible to Cache plugins
* Tweak: Added shortcode support in Text Editor widget
* Tweak: Added title attribute for Image and Image Box widgets
* Fix: Replace icon for Icon Box widget with the right icon
* Fix: Scroll issue in Safari browser ([topic](https://wordpress.org/support/topic/scroll-issue-in-safri))
* Fix: Text alignment justify ([topic](https://wordpress.org/support/topic/on-text-aligning))

= 0.2.3 - 2016-06-08 =
* Tweak: Added HTML tag control for icon box title

= 0.2.2 - 2016-06-08 =
* Tweak: Minor change from the last release

= 0.2.1 - 2016-06-08 =
* New: Icon Box widget

= 0.2.0 - 2016-06-07 =
* New: Image Box widget
* Tweak: Added reset preset section structure
* Tweak: Fixed visibility mobile for small-screen

= 0.1.7 - 2016-06-06 =
* Tweak: Changed default image size control
* Fix: Custom CSS Classes in frontend

= 0.1.6 - 2016-06-05 =
* Tweak: Improved speed live edit
* Tweak: Background Control: Removed default image
* Tweak: Add responsive tab on section element
* Tweak: Responsive mode: fixed included visibility style
* Fix: Tabs & Accordion Widgets: indexes detection fixed

= 0.1.5 - 2016-06-03 =
* Tweak: Add check for `asp_tags` php config

= 0.1.4 - 2016-06-02 =
* New: Added new widget: Spacer
* Tweak: Added default category for widgets
* Tweak: Added size and opacity controls for Image widget
* Tweak: Added fallback font for custom fonts
* Tweak: Added development mode flag (filter `elementor/utils/is_development_mode`)
* Fix: Issue with duplicate after element sort

= 0.1.3 - 2016-06-01 =
* New: Added background video for all formats
* Tweak: Pojo Framework & Themes supported
* Tweak: Updated Elementor Icons v1.1
* Tweak: Added elements categories in the panel
* Fix: UI bug with WordPress widgets (panel)
* Fix: Remove fallback image placeholder in background video

= 0.1.2 - 2016-05-31 =
* Tweak: Added structure control for section element
* Tweak: Elementor Icons moved to lib (standalone)
* Fix: Better check if YouTube API is loaded
* Fix: Fixed applying value in dimensions
* Fix: Bug in resize columns

= 0.1.1 - 2016-05-30 =
* Tweak: Added alpha for background color (Button Widget)
* Tweak: Added string context and moved to WP translate repo
* Tweak: Icon Box: removed alt text control
* Fix: content position in full height section

= 0.1.0 - 2016-05-30 =
* Initial Public Beta Release
