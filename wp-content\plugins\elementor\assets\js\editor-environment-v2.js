/*! elementor - v3.31.0 - 27-08-2025 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/*!************************************************************!*\
  !*** ../core/editor/loader/v2/js/editor-environment-v2.js ***!
  \************************************************************/


var _window$elementorV;
if (!((_window$elementorV = window.elementorV2) !== null && _window$elementorV !== void 0 && _window$elementorV.env)) {
  throw new Error('The "@elementor/env" package was not loaded.');
}
window.elementorV2.env.initEnv(window.elementorEditorV2Env);
/******/ })()
;
//# sourceMappingURL=editor-environment-v2.js.map