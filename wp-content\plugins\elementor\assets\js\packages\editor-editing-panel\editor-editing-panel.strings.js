__( 'Clear', 'elementor' );
__( 'Style', 'elementor' );
// translators: %s$1 is the style label, %s$2 is the name of the style property being edited
__( `%s$1 %s$2 edited`, 'elementor' );
// translators: %s is the name of the style property being edited
__( `%s edited`, 'elementor' );
// translators: %s is the name of the property that was edited.
__( '%s edited', 'elementor' );
__( 'Layout', 'elementor' );
__( 'Spacing', 'elementor' );
__( 'Size', 'elementor' );
__( 'Position', 'elementor' );
__( 'Typography', 'elementor' );
__( 'Background', 'elementor' );
__( 'Border', 'elementor' );
__( 'Effects', 'elementor' );
/* translators: %s: Element type title. */
__( 'Edit %s', 'elementor' );
__( 'General', 'elementor' );
__( 'Style', 'elementor' );
__( 'Show less', 'elementor' );
__( 'Show more', 'elementor' );
__( 'Linear Gradient', 'elementor' );
__( 'Radial Gradient', 'elementor' );
__( 'Base', 'elementor' );
__( 'Has effective styles', 'elementor' );
__( 'Has overridden styles', 'elementor' );
__( 'Has styles', 'elementor' );
__( 'Style origin', 'elementor' );
// translators: %s is the display label of the inheritance item
__( 'Inheritance item: %s', 'elementor' );
__( 'Style origin', 'elementor' );
__( 'This is the final value', 'elementor' );
__( 'This value is overridden by another style', 'elementor' );
__( 'This has value from another style', 'elementor' );
__( 'Dynamic tags', 'elementor' );
__( 'Dynamic tags', 'elementor' );
__( 'Search dynamic tags…', 'elementor' );
__( 'Sorry, nothing matched', 'elementor' );
__( 'Try something else.', 'elementor' );
__( 'Clear & try again', 'elementor' );
__( 'Streamline your workflow with dynamic tags', 'elementor' );
__( "You'll need Elementor Pro to use this feature.", 'elementor' );
__( 'Remove dynamic value', 'elementor' );
__( 'Settings', 'elementor' );
/* translators: %s is the class name. */
__( `class %s applied`, 'elementor' );
/* translators: %s is the class name. */
__( `class %s removed`, 'elementor' );
__( 'Class', 'elementor' );
/* translators: %s is the class name. */
__( `%s created`, 'elementor' );
__( 'local', 'elementor' );
__( 'Classes', 'elementor' );
__( 'Type class name', 'elementor' );
__( 'With your current role, you can use existing classes but can’t modify them.', 'elementor' );
__( 'Sorry, nothing matched', 'elementor' );
__( 'With your current role,', 'elementor' );
__( 'you can only use existing classes.', 'elementor' );
__( 'Clear & try again', 'elementor' );
__(
					'You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.',
					'elementor'
				);
__( 'States', 'elementor' );
__( 'With your current role, you can only use existing states.', 'elementor' );
__( 'Has style', 'elementor' );
__( 'Remove', 'elementor' );
__(
					'With your current role, you can use existing classes but can’t modify them.',
					'elementor'
				);
__( 'Rename', 'elementor' );
__( 'Open CSS Class Menu', 'elementor' );
__( 'Inherited from base styles', 'elementor' );
__( 'Word spacing', 'elementor' );
__( 'Text transform', 'elementor' );
__( 'None', 'elementor' );
__( 'Capitalize', 'elementor' );
__( 'Uppercase', 'elementor' );
__( 'Lowercase', 'elementor' );
__( 'Text stroke', 'elementor' );
__( 'Direction', 'elementor' );
__( 'Left to right', 'elementor' );
__( 'Right to left', 'elementor' );
__( 'Line decoration', 'elementor' );
__( 'None', 'elementor' );
__( 'Underline', 'elementor' );
__( 'Line-through', 'elementor' );
__( 'Overline', 'elementor' );
__( 'Text color', 'elementor' );
__( 'Text align', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Justify', 'elementor' );
__( 'Line height', 'elementor' );
__( 'Letter spacing', 'elementor' );
__( 'Font weight', 'elementor' );
__( '100 - Thin', 'elementor' );
__( '200 - Extra light', 'elementor' );
__( '300 - Light', 'elementor' );
__( '400 - Normal', 'elementor' );
__( '500 - Medium', 'elementor' );
__( '600 - Semi bold', 'elementor' );
__( '700 - Bold', 'elementor' );
__( '800 - Extra bold', 'elementor' );
__( '900 - Black', 'elementor' );
__( 'Font style', 'elementor' );
__( 'Normal', 'elementor' );
__( 'Italic', 'elementor' );
__( 'Font size', 'elementor' );
__( 'Font family', 'elementor' );
__( 'Column gap', 'elementor' );
__( 'Columns', 'elementor' );
__( 'Margin', 'elementor' );
__( 'Padding', 'elementor' );
__( 'Width', 'elementor' );
__( 'Height', 'elementor' );
__( 'Min width', 'elementor' );
__( 'Min height', 'elementor' );
__( 'Max width', 'elementor' );
__( 'Max height', 'elementor' );
__( 'Aspect Ratio', 'elementor' );
__( 'Object position', 'elementor' );
__( 'Overflow', 'elementor' );
__( 'Visible', 'elementor' );
__( 'Hidden', 'elementor' );
__( 'Auto', 'elementor' );
__( 'Object fit', 'elementor' );
__( 'Fill', 'elementor' );
__( 'Cover', 'elementor' );
__( 'Contain', 'elementor' );
__( 'None', 'elementor' );
__( 'Scale down', 'elementor' );
__( 'Z-index', 'elementor' );
__( 'Position', 'elementor' );
__( 'Dimensions', 'elementor' );
__( 'Position', 'elementor' );
__( 'Static', 'elementor' );
__( 'Relative', 'elementor' );
__( 'Absolute', 'elementor' );
__( 'Fixed', 'elementor' );
__( 'Sticky', 'elementor' );
__( 'Anchor offset', 'elementor' );
__( 'Right', 'elementor' );
__( 'Left', 'elementor' );
__( 'Left', 'elementor' );
__( 'Right', 'elementor' );
__( 'Top', 'elementor' );
__( 'Bottom', 'elementor' );
__( 'Wrap', 'elementor' );
__( 'No wrap', 'elementor' );
__( 'Wrap', 'elementor' );
__( 'Reversed wrap', 'elementor' );
__( 'Display', 'elementor' );
__( 'Flex wrap', 'elementor' );
__( 'Flex child', 'elementor' );
__( 'Justify content', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Space between', 'elementor' );
__( 'Space around', 'elementor' );
__( 'Space evenly', 'elementor' );
__( 'Gaps', 'elementor' );
__( 'Flex Size', 'elementor' );
__( 'Grow', 'elementor' );
__( 'Shrink', 'elementor' );
__( 'Custom', 'elementor' );
__( 'Grow', 'elementor' );
__( 'Shrink', 'elementor' );
__( 'Basis', 'elementor' );
__( 'Order', 'elementor' );
__( 'First', 'elementor' );
__( 'Last', 'elementor' );
__( 'Custom', 'elementor' );
__( 'Custom order', 'elementor' );
__( 'Direction', 'elementor' );
__( 'Row', 'elementor' );
__( 'Column', 'elementor' );
__( 'Reversed row', 'elementor' );
__( 'Reversed column', 'elementor' );
__( 'Display', 'elementor' );
__( 'Block', 'elementor' );
__( 'Block', 'elementor' );
__( 'Flex', 'elementor' );
__( 'Flex', 'elementor' );
__( 'In-blk', 'elementor' );
__( 'Inline-block', 'elementor' );
__( 'None', 'elementor' );
__( 'None', 'elementor' );
__( 'In-flx', 'elementor' );
__( 'Inline-flex', 'elementor' );
__( 'Align self', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Stretch', 'elementor' );
__( 'Align items', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Stretch', 'elementor' );
__( 'Align content', 'elementor' );
__( 'Start', 'elementor' );
__( 'Center', 'elementor' );
__( 'End', 'elementor' );
__( 'Space between', 'elementor' );
__( 'Space around', 'elementor' );
__( 'Space evenly', 'elementor' );
__( 'Opacity', 'elementor' );
__( 'Box shadow', 'elementor' );
__( 'Filters', 'elementor' );
__( 'Backdrop filters', 'elementor' );
__( 'Border width', 'elementor' );
__( 'Top', 'elementor' );
__( 'Left', 'elementor' );
__( 'Right', 'elementor' );
__( 'Bottom', 'elementor' );
__( 'Right', 'elementor' );
__( 'Left', 'elementor' );
__( 'Adjust borders', 'elementor' );
__( 'Border type', 'elementor' );
__( 'None', 'elementor' );
__( 'Solid', 'elementor' );
__( 'Dashed', 'elementor' );
__( 'Dotted', 'elementor' );
__( 'Double', 'elementor' );
__( 'Groove', 'elementor' );
__( 'Ridge', 'elementor' );
__( 'Inset', 'elementor' );
__( 'Outset', 'elementor' );
__( 'Border radius', 'elementor' );
__( 'Top right', 'elementor' );
__( 'Top left', 'elementor' );
__( 'Top left', 'elementor' );
__( 'Top right', 'elementor' );
__( 'Bottom right', 'elementor' );
__( 'Bottom left', 'elementor' );
__( 'Bottom left', 'elementor' );
__( 'Bottom right', 'elementor' );
__( 'Adjust corners', 'elementor' );
__( 'Border', 'elementor' );
__( 'Border color', 'elementor' );
__( 'Background', 'elementor' );
__( 'System', 'elementor' );
__( 'Custom Fonts', 'elementor' );
__( 'Google Fonts', 'elementor' );
__( 'Flex direction', 'elementor' );