!function(){"use strict";var e={d:function(t,n){for(var l in n)e.o(n,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:n[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{PopoverBody:function(){return Po},controlActionsMenu:function(){return xe},init:function(){return Pa},injectIntoClassSelectorActions:function(){return fe},registerControlReplacement:function(){return l},registerStyleProviderToColors:function(){return i},stylesInheritanceTransformersRegistry:function(){return Pt},useBoundProp:function(){return n.useBoundProp},useFontFamilies:function(){return Or},usePanelActions:function(){return To},usePanelStatus:function(){return Io},useSectionWidth:function(){return Je}});var n=window.elementorV2.editorControls;const{registerControlReplacement:l,getControlReplacements:r}=(0,n.createControlReplacementsRegistry)(),o={name:"default",getThemeColor:null},a=new Map,i=(e,t)=>{a.set(e,t)},s=e=>a.get(e)??o;var c=window.React,u=window.elementorV2.editorElements,m=window.elementorV2.editorStylesRepository,p=window.elementorV2.editorUi,d=window.elementorV2.icons,f=window.elementorV2.locations,E=window.elementorV2.ui,b=window.wp.i18n;const y=(0,c.createContext)(null);function g({children:e,prop:t}){return c.createElement(y.Provider,{value:{prop:t}},e)}function v(){const e=(0,c.useContext)(y);if(!e)throw new Error("useClassesProp must be used within a ClassesPropProvider");return e.prop}const h=(0,c.createContext)(null);function _({children:e,element:t,elementType:n}){return c.createElement(h.Provider,{value:{element:t,elementType:n}},e)}function w(){const e=(0,c.useContext)(h);if(!e)throw new Error("useElement must be used within a ElementProvider");return e}var S=window.elementorV2.utils;const C=(0,S.createError)({code:"control_type_not_found",message:"Control type not found."}),x=(0,S.createError)({code:"provider_not_found",message:"Styles provider not found."}),T=(0,S.createError)({code:"provider_cannot_update_props",message:"Styles provider doesn't support updating props."}),I=(0,S.createError)({code:"style_not_found_under_provider",message:"Style not found under the provider."}),k=(0,c.createContext)(null);function P({children:e,...t}){const n=null===t.id?null:D(t.id),{userCan:l}=(0,m.useUserStylesCapability)();if(t.id&&!n)throw new x({context:{styleId:t.id}});const r=l(n?.getKey()??"").updateProps;return c.createElement(k.Provider,{value:{...t,provider:n,canEdit:r}},e)}function z(){const e=(0,c.useContext)(k);if(!e)throw new Error("useStyle must be used within a StyleProvider");return e}function D(e){return m.stylesRepository.getProviders().find(t=>t.actions.all().find(t=>t.id===e))??null}const R=e=>e&&e!==m.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,m.isElementsStylesProvider)(e)?"accent":s(e).name:"default",O=e=>e&&e!==m.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,m.isElementsStylesProvider)(e)?e=>e.palette.accent.main:s(e).getThemeColor:null;function L(e){return(0,m.isElementsStylesProvider)(e)?e=>e.palette.primary.main:O(e)}function B(e){const{_group:t,_action:n,...l}=e;return l}function V(){return V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},V.apply(null,arguments)}const N=c.forwardRef(j);function j({selected:e,options:t,entityName:n,onSelect:l,placeholder:r,onCreate:o,validate:a,renderEmptyState:i,...s},u){const{inputValue:m,setInputValue:p,error:d,setError:f,inputHandlers:b}=function(e){const[t,n]=(0,c.useState)(""),[l,r]=(0,c.useState)(null);return{inputValue:t,setInputValue:n,error:l,setError:r,inputHandlers:{onChange:t=>{const{value:l}=t.target;if(n(l),!e)return;if(!l)return void r(null);const{isValid:o,errorMessage:a}=e(l,"inputChange");r(o?null:a)},onBlur:()=>{n(""),r(null)}}}}(a),{open:y,openDropdown:g,closeDropdown:v}=function(e=!1){const[t,n]=(0,c.useState)(e);return{open:t,openDropdown:()=>n(!0),closeDropdown:()=>n(!1)}}(s.open),{createOption:h,loading:_}=function(e){const{onCreate:t,validate:n,setInputValue:l,setError:r,closeDropdown:o}=e,[a,i]=(0,c.useState)(!1);return t?{createOption:async e=>{if(i(!0),n){const{isValid:t,errorMessage:l}=n(e,"create");if(!t)return r(l),void i(!1)}try{l(""),o(),await t(e)}catch{}finally{i(!1)}},loading:a}:{createOption:null,loading:!1}}({onCreate:o,validate:a,setInputValue:p,setError:f,closeDropdown:v}),[w,S]=(0,c.useMemo)(()=>[t,e].map(e=>function(e,t){return e.map(e=>({...e,_group:`Existing ${t??"options"}`}))}(e,n?.plural)),[t,e,n?.plural]),C=function(e){const{options:t,onSelect:n,createOption:l,setInputValue:r,closeDropdown:o}=e;if(n||l)return async(e,n,i,s)=>{const c=s?.option;if(!c||"object"==typeof c&&c.fixed)return;const u=n.filter(e=>"string"!=typeof e);switch(i){case"removeOption":a(u,"removeOption",c);break;case"selectOption":{const e=c;if("create"===e._action){const t=e.value;return l?.(t)}a(u,"selectOption",e);break}case"createOption":{const e=c,n=t.find(t=>t.label.toLocaleLowerCase()===e.toLocaleLowerCase());if(!n)return l?.(e);u.push(n),a(u,"selectOption",n);break}}r(""),o()};function a(e,t,l){n?.(e.map(e=>B(e)),t,B(l))}}({options:w,onSelect:l,createOption:h,setInputValue:p,closeDropdown:v}),x=function(e){const{options:t,selected:n,onCreate:l,entityName:r}=e,o=(0,E.createFilterOptions)();return(e,a)=>{const i=n.map(e=>e.value),s=o(e.filter(e=>!i.includes(e.value)),a),c=t.some(e=>a.inputValue===e.label);return Boolean(l)&&""!==a.inputValue&&!i.includes(a.inputValue)&&!c&&s.unshift({label:`Create "${a.inputValue}"`,value:a.inputValue,_group:`Create a new ${r?.singular??"option"}`,key:`create-${a.inputValue}`,_action:"create"}),s}}({options:t,selected:e,onCreate:o,entityName:n}),T=Boolean(o)||m.length<2||void 0;return c.createElement(E.Autocomplete,V({renderTags:(e,t)=>e.map((e,n)=>c.createElement(E.Chip,V({size:"tiny"},t({index:n}),{key:e.key??e.value??e.label,label:e.label})))},s,{ref:u,freeSolo:T,forcePopupIcon:!1,multiple:!0,clearOnBlur:!0,selectOnFocus:!0,disableClearable:!0,handleHomeEndKeys:!0,disabled:_,open:y,onOpen:g,onClose:v,disableCloseOnSelect:!0,value:S,options:w,ListboxComponent:d?c.forwardRef((e,t)=>c.createElement(A,{ref:t,error:d})):void 0,renderGroup:e=>c.createElement(M,e),inputValue:m,renderInput:e=>c.createElement(E.TextField,V({},e,{error:Boolean(d),placeholder:r},b,{sx:e=>({".MuiAutocomplete-inputRoot.MuiInputBase-adornedStart":{paddingLeft:e.spacing(.25),paddingRight:e.spacing(.25)}})})),onChange:C,getOptionLabel:e=>"string"==typeof e?e:e.label,getOptionKey:e=>"string"==typeof e?e:e.key??e.value??e.label,filterOptions:x,groupBy:e=>e._group??"",renderOption:(e,t)=>{const{_group:n,label:l}=t;return c.createElement("li",V({},e,{style:{display:"block",textOverflow:"ellipsis"},"data-group":n}),l)},noOptionsText:i?.({searchValue:m,onClear:()=>{p(""),v()}}),isOptionEqualToValue:(e,t)=>"string"==typeof e?e===t:e.value===t.value}))}const M=e=>{const t=`combobox-group-${(0,c.useId)().replace(/:/g,"_")}`;return c.createElement(F,{role:"group","aria-labelledby":t},c.createElement($,{id:t}," ",e.group),c.createElement(U,{role:"listbox"},e.children))},A=c.forwardRef(({error:e="error"},t)=>c.createElement(E.Box,{ref:t,sx:e=>({padding:e.spacing(2)})},c.createElement(E.Typography,{variant:"caption",sx:{color:"error.main",display:"inline-block"}},e))),F=(0,E.styled)("li")`
	&:not( :last-of-type ) {
		border-bottom: 1px solid ${({theme:e})=>e.palette.divider};
	}
`,$=(0,E.styled)(E.Box)(({theme:e})=>({position:"sticky",top:"-8px",padding:e.spacing(1,2),color:e.palette.text.tertiary,backgroundColor:e.palette.primary.contrastText})),U=(0,E.styled)("ul")`
	padding: 0;
`,G=(0,c.createContext)(null),W=()=>{const e=(0,c.useContext)(G);if(!e)throw new Error("useCssClass must be used within a CssClassProvider");return e};function K({children:e,...t}){return c.createElement(G.Provider,{value:t},e)}const H=(0,E.styled)("div",{shouldForwardProp:e=>!["isOverridden","getColor"].includes(e)})`
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: ${({theme:e,isOverridden:t,getColor:n})=>{if(t)return e.palette.warning.light;const l=n?.(e);return l??e.palette.text.disabled}};
`;var J=window.elementorV2.editorDocuments,Y=window.elementorV2.editorProps,q=window.elementorV2.editorV1Adapters;function X(){const{id:e,setId:t}=z(),{element:n}=w(),l=Z(),r=Q();return(0,c.useMemo)(()=>(0,q.undoable)({do:({classId:t})=>{const n=e;return r(t),n},undo:({classId:e},n)=>{l(e),t(n)}},{title:(0,u.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,b.__)("class %s removed","elementor").replace("%s",e)}),[e,l,n.id,r,t])}function Z(){const{element:e}=w(),{setId:t}=z(),{setClasses:n,getAppliedClasses:l}=ee();return(0,c.useCallback)(r=>{const o=l();if(o.includes(r))throw new Error(`Class ${r} is already applied to element ${e.id}, cannot re-apply.`);const a=[...o,r];n(a),t(r)},[e.id,l,t,n])}function Q(){const{element:e}=w(),{id:t,setId:n}=z(),{setClasses:l,getAppliedClasses:r}=ee();return(0,c.useCallback)(o=>{const a=r();if(!a.includes(o))throw new Error(`Class ${o} is not applied to element ${e.id}, cannot unapply it.`);const i=a.filter(e=>e!==o);l(i),t===o&&n(i[0]??null)},[t,e.id,r,n,l])}function ee(){const{element:e}=w(),t=v();return(0,c.useMemo)(()=>({setClasses:n=>{(0,u.updateElementSettings)({id:e.id,props:{[t]:Y.classesPropTypeUtil.create(n)},withHistory:!1}),(0,J.setDocumentModifiedStatus)(!0)},getAppliedClasses:()=>(0,u.getElementSetting)(e.id,t)?.value||[]}),[t,e.id])}function te(){return te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},te.apply(null,arguments)}const ne=[{key:"normal",value:null},{key:"hover",value:"hover"},{key:"focus",value:"focus"},{key:"active",value:"active"}];function le({popupState:e,anchorEl:t,fixed:n}){const{provider:l}=W();return c.createElement(E.Menu,te({MenuListProps:{dense:!0,sx:{minWidth:"160px"}}},(0,E.bindMenu)(e),{anchorEl:t,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{horizontal:"left",vertical:-4},onKeyDown:e=>{e.stopPropagation()},disableAutoFocusItem:!0}),function({provider:e,closeMenu:t,fixed:n}){if(!e)return[];const l=m.stylesRepository.getProviderByKey(e),r=l?.actions,o=r?.update,a=!n,i=[o&&c.createElement(ae,{key:"rename-class",closeMenu:t}),a&&c.createElement(oe,{key:"unapply-class",closeMenu:t})].filter(Boolean);return i.length&&(i.unshift(c.createElement(E.MenuSubheader,{key:"provider-label",sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1,textTransform:"capitalize"}},l?.labels?.singular)),i.push(c.createElement(E.Divider,{key:"provider-actions-divider"}))),i}({provider:l,closeMenu:e.close,fixed:n}),c.createElement(E.MenuSubheader,{sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1}},(0,b.__)("States","elementor")),ne.map(t=>c.createElement(re,{key:t.key,state:t.value,closeMenu:e.close})))}function re({state:e,closeMenu:t,...n}){const{id:l,provider:r}=W(),{id:o,setId:a,setMetaState:i,meta:s}=z(),{state:u}=s,{userCan:d}=(0,m.useUserStylesCapability)(),f=function(e){const{meta:t}=z(),n=m.stylesRepository.all().find(t=>t.id===e);return Object.fromEntries(n?.variants.filter(e=>t.breakpoint===e.meta.breakpoint).map(e=>[e.meta.state??"normal",!0])??[])}(l),y=!e||d(r??"").updateProps,g=f[e??"normal"]??!1,v=!y&&!g,h=l===o,_=e===u&&h;return c.createElement(p.MenuListItem,te({},n,{selected:_,disabled:v,sx:{textTransform:"capitalize"},onClick:()=>{h||a(l),i(e),t()}}),c.createElement(p.MenuItemInfotip,{showInfoTip:v,content:(0,b.__)("With your current role, you can only use existing states.","elementor")},c.createElement(E.Stack,{gap:.75,direction:"row",alignItems:"center"},g&&c.createElement(H,{"aria-label":(0,b.__)("Has style","elementor"),getColor:L(r??"")}),e??"normal")))}function oe({closeMenu:e,...t}){const{id:n,label:l}=W(),r=X();return n?c.createElement(p.MenuListItem,te({},t,{onClick:()=>{r({classId:n,classLabel:l}),e()}}),(0,b.__)("Remove","elementor")):null}function ae({closeMenu:e}){const{handleRename:t,provider:n}=W(),{userCan:l}=(0,m.useUserStylesCapability)();if(!n)return null;const r=l(n).update;return c.createElement(p.MenuListItem,{disabled:!r,onClick:()=>{e(),t()}},c.createElement(p.MenuItemInfotip,{showInfoTip:!r,content:(0,b.__)("With your current role, you can use existing classes but can’t modify them.","elementor")},(0,b.__)("Rename","elementor")))}function ie(){return ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},ie.apply(null,arguments)}const se="tiny";function ce(e){const{chipProps:t,icon:n,color:l,fixed:r,...o}=e,{id:a,provider:i,label:s,isActive:u,onClickActive:f,renameLabel:y,setError:g}=o,{meta:v,setMetaState:h}=z(),_=(0,E.usePopupState)({variant:"popover"}),[w,S]=(0,c.useState)(null),{onDelete:C,...x}=t,{userCan:T}=(0,m.useUserStylesCapability)(),{ref:I,isEditing:k,openEditMode:P,error:D,getProps:R}=(0,p.useEditable)({value:s,onSubmit:y,validation:ue,onError:g}),O=D?"error":l,L=i?m.stylesRepository.getProviderByKey(i)?.actions:null,B=Boolean(L?.update)&&T(i??"")?.update,V=u&&v.state;return c.createElement(E.ThemeProvider,{palette:"default"},c.createElement(E.UnstableChipGroup,ie({ref:S},x,{"aria-label":`Edit ${s}`,role:"group",sx:e=>({"&.MuiChipGroup-root.MuiAutocomplete-tag":{margin:e.spacing(.125)}})}),c.createElement(E.Chip,{size:se,label:k?c.createElement(p.EditableField,ie({ref:I},R())):c.createElement(p.EllipsisWithTooltip,{maxWidth:"10ch",title:s,as:"div"}),variant:!u||v.state||k?"standard":"filled",shape:"rounded",icon:n,color:O,onClick:()=>{V?h(null):B&&u?P():f(a)},"aria-pressed":u,sx:e=>({lineHeight:1,cursor:u&&B&&!V?"text":"pointer",borderRadius:.75*e.shape.borderRadius+"px","&.Mui-focusVisible":{boxShadow:"none !important"}})}),!k&&c.createElement(E.Chip,ie({icon:V?void 0:c.createElement(d.DotsVerticalIcon,{fontSize:"tiny"}),size:se,label:V?c.createElement(E.Stack,{direction:"row",gap:.5,alignItems:"center"},c.createElement(E.Typography,{variant:"inherit"},v.state),c.createElement(d.DotsVerticalIcon,{fontSize:"tiny"})):void 0,variant:"filled",shape:"rounded",color:O},(0,E.bindTrigger)(_),{"aria-label":(0,b.__)("Open CSS Class Menu","elementor"),sx:e=>({borderRadius:.75*e.shape.borderRadius+"px",paddingRight:0,...V?{}:{paddingLeft:0},".MuiChip-label":V?{paddingRight:0}:{padding:0}})}))),c.createElement(K,ie({},o,{handleRename:P}),c.createElement(le,{popupState:_,anchorEl:w,fixed:r})))}const ue=e=>{const t=(0,m.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},me="elementor-css-class-selector",pe={label:(0,b.__)("local","elementor"),value:null,fixed:!0,color:ge("accent"),icon:c.createElement(d.MapPinIcon,null),provider:null},{Slot:de,inject:fe}=(0,f.createLocation)();function Ee(){const e=function(){const{element:e}=w();return(0,m.useProviders)().filter(e=>!!e.actions.updateProps).flatMap(t=>{const n=(0,m.isElementsStylesProvider)(t.getKey()),l=t.actions.all({elementId:e.id});return n&&0===l.length?[pe]:l.map(e=>({label:e.label,value:e.id,fixed:n,color:ge(R(t.getKey())),icon:n?c.createElement(d.MapPinIcon,null):null,provider:t.getKey()}))})}(),{id:t,setId:n}=z(),l=(0,c.useRef)(null),[r,o]=(0,c.useState)(null),a=function(){const e=function(){const{id:e,setId:t}=z(),{element:n}=w(),l=Z(),r=Q();return(0,c.useMemo)(()=>(0,q.undoable)({do:({classId:t})=>{const n=e;return l(t),n},undo:({classId:e},n)=>{r(e),t(n)}},{title:(0,u.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,b.__)("class %s applied","elementor").replace("%s",e)}),[e,l,n.id,r,t])}(),t=X();return(n,l,r)=>{if(r.value)switch(l){case"selectOption":e({classId:r.value,classLabel:r.label});break;case"removeOption":t({classId:r.value,classLabel:r.label})}}}(),{create:i,validate:s,entityName:f}=function(){const[e,t]=function(){const{id:e,setId:t}=z(),[n,l]=(0,m.useGetStylesRepositoryCreateAction)()??[null,null],r=n?.actions.delete,o=Z(),a=Q(),i=(0,c.useMemo)(()=>{if(n&&l)return(0,q.undoable)({do:({classLabel:t})=>{const n=e,r=l(t);return o(r),{prevActiveId:n,createdId:r}},undo:(e,{prevActiveId:n,createdId:l})=>{a(l),r?.(l),t(n)}},{title:(0,b.__)("Class","elementor"),subtitle:({classLabel:e})=>(0,b.__)("%s created","elementor").replace("%s",e)})},[e,o,l,r,n,t,a]);return n&&i?[n,i]:[null,null]}();if(!e||!t)return{};return{create:e=>{t({classLabel:e})},validate:(t,n)=>function(e){return e.actions.all().length>=e.limit}(e)?{isValid:!1,errorMessage:(0,b.__)("You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.","elementor")}:(0,m.validateStyleLabel)(t,n),entityName:e.labels.singular&&e.labels.plural?e.labels:void 0}}(),y=function(e){const{element:t}=w(),n=v(),l=(0,u.useElementSetting)(t.id,n)?.value||[],r=e.filter(e=>e.value&&l.includes(e.value));return r.some(e=>e.provider&&(0,m.isElementsStylesProvider)(e.provider))||r.unshift(pe),r}(e),g=y.find(e=>e.value===t)??pe,h=y.every(({fixed:e})=>e),{userCan:_}=(0,m.useUserStylesCapability)(),S=!g.provider||_(g.provider).updateProps;return c.createElement(E.Stack,{p:2},c.createElement(E.Stack,{direction:"row",gap:1,alignItems:"center",justifyContent:"space-between"},c.createElement(E.FormLabel,{htmlFor:me,size:"small"},(0,b.__)("Classes","elementor")),c.createElement(E.Stack,{direction:"row",gap:1},c.createElement(de,null))),c.createElement(p.WarningInfotip,{open:Boolean(r),text:r??"",placement:"bottom",width:l.current?.getBoundingClientRect().width,offset:[0,-15]},c.createElement(N,{id:me,ref:l,size:"tiny",placeholder:h?(0,b.__)("Type class name","elementor"):void 0,options:e,selected:y,entityName:f,onSelect:a,onCreate:i??void 0,validate:s??void 0,limitTags:50,renderEmptyState:be,getLimitTagsText:e=>c.createElement(E.Chip,{size:"tiny",variant:"standard",label:`+${e}`,clickable:!0}),renderTags:(e,t)=>e.map((e,l)=>{const r=t({index:l}),a=e.value===g?.value;return c.createElement(ce,{key:r.key,fixed:e.fixed,label:e.label,provider:e.provider,id:e.value,isActive:a,color:a&&e.color?e.color:"default",icon:e.icon,chipProps:r,onClickActive:()=>n(e.value),renameLabel:t=>{if(!e.value)throw new Error("Cannot rename a class without style id");return ye(e.provider,{label:t,id:e.value})},setError:o})})})),!S&&c.createElement(p.InfoAlert,{sx:{mt:1}},(0,b.__)("With your current role, you can use existing classes but can’t modify them.","elementor")))}const be=({searchValue:e,onClear:t})=>c.createElement(E.Box,{sx:{py:4}},c.createElement(E.Stack,{gap:1,alignItems:"center",color:"text.secondary",justifyContent:"center",sx:{px:2,m:"auto",maxWidth:"236px"}},c.createElement(d.ColorSwatchIcon,{sx:{transform:"rotate(90deg)"},fontSize:"large"}),c.createElement(E.Typography,{align:"center",variant:"subtitle2"},(0,b.__)("Sorry, nothing matched","elementor"),c.createElement("br",null),"“",e,"”."),c.createElement(E.Typography,{align:"center",variant:"caption",sx:{mb:2}},(0,b.__)("With your current role,","elementor"),c.createElement("br",null),(0,b.__)("you can only use existing classes.","elementor")),c.createElement(E.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,b.__)("Clear & try again","elementor")))),ye=(e,t)=>{if(!e)return;const n=m.stylesRepository.getProviderByKey(e);return n?n.actions.update?.(t):void 0};function ge(e){return"accent"===e?"primary":e}var ve=window.elementorV2.editorPanels,he=window.elementorV2.session,_e=window.elementorV2.menus;const we="tiny";function Se(){return Se=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},Se.apply(null,arguments)}const Ce="tiny",xe=(0,_e.createMenu)({components:{Action:function({title:e,visible:t=!0,icon:n,onClick:l}){return t?c.createElement(E.Tooltip,{placement:"top",title:e,arrow:!0},c.createElement(E.IconButton,{"aria-label":e,size:we,onClick:l},c.createElement(n,{fontSize:we}))):null},PopoverAction:function({title:e,visible:t=!0,icon:l,content:r}){const{popupState:o,triggerProps:a,popoverProps:i}=function(){const{setOpen:e}=(0,n.useFloatingActionsBar)(),t=(0,E.usePopupState)({variant:"popover"}),l=(0,E.bindTrigger)(t),r=(0,E.bindPopover)(t);return{popupState:{...t,close:()=>{t.close(),e(!1)}},triggerProps:{...l,onClick:t=>{l.onClick(t),e(!0)}},popoverProps:{...r,onClose:()=>{r.onClose(),e(!1)}}}}();return t?c.createElement(c.Fragment,null,c.createElement(E.Tooltip,{placement:"top",title:e},c.createElement(E.IconButton,Se({"aria-label":e,size:Ce},a),c.createElement(l,{fontSize:Ce}))),c.createElement(E.Popover,Se({disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:2.5}}},i),c.createElement(r,{close:o.close}))):null}}});function Te(){return c.createElement(E.Box,{role:"alert",sx:{minHeight:"100%",p:2}},c.createElement(E.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},c.createElement("strong",null,"Something went wrong")))}const Ie=(0,c.createContext)(void 0),ke=(0,E.styled)("div")`
	height: 100%;
	overflow-y: auto;
`;function Pe({children:e}){const[t,n]=(0,c.useState)("up"),l=(0,c.useRef)(null),r=(0,c.useRef)(0);return(0,c.useEffect)(()=>{const e=l.current;if(!e)return;const t=()=>{const{scrollTop:t}=e;t>r.current?n("down"):t<r.current&&n("up"),r.current=t};return e.addEventListener("scroll",t),()=>{e.removeEventListener("scroll",t)}}),c.createElement(Ie.Provider,{value:{direction:t}},c.createElement(ke,{ref:l},e))}const ze={defaultSectionsExpanded:{settings:["Content","Settings"],style:[]},defaultTab:"settings"},De=(0,c.createContext)({"e-div-block":{defaultSectionsExpanded:ze.defaultSectionsExpanded,defaultTab:"style"},"e-flexbox":{defaultSectionsExpanded:ze.defaultSectionsExpanded,defaultTab:"style"},"e-divider":{defaultSectionsExpanded:ze.defaultSectionsExpanded,defaultTab:"style"}}),Re=()=>{const{element:e}=w();return(0,c.useContext)(De)[e.type]||ze},Oe=(e,t)=>{const{element:n}=w(),l=`elementor/editor-state/${n.id}/${e}`,r=(0,he.getSessionStorageItem)(l),[o,a]=(0,c.useState)(r??t);return[o,e=>{(0,he.setSessionStorageItem)(l,e),a(e)}]},Le={image:{component:n.ImageControl,layout:"full",propTypeUtil:Y.imagePropTypeUtil},"svg-media":{component:n.SvgMediaControl,layout:"full",propTypeUtil:Y.imageSrcPropTypeUtil},text:{component:n.TextControl,layout:"full",propTypeUtil:Y.stringPropTypeUtil},textarea:{component:n.TextAreaControl,layout:"full",propTypeUtil:Y.stringPropTypeUtil},size:{component:n.SizeControl,layout:"two-columns",propTypeUtil:Y.sizePropTypeUtil},select:{component:n.SelectControl,layout:"two-columns",propTypeUtil:Y.stringPropTypeUtil},link:{component:n.LinkControl,layout:"custom",propTypeUtil:Y.linkPropTypeUtil},url:{component:n.UrlControl,layout:"full",propTypeUtil:Y.stringPropTypeUtil},switch:{component:n.SwitchControl,layout:"two-columns",propTypeUtil:Y.booleanPropTypeUtil},repeatable:{component:n.RepeatableControl,layout:"full",propTypeUtil:void 0},"key-value":{component:n.KeyValueControl,layout:"full",propTypeUtil:Y.keyValuePropTypeUtil}},Be=e=>Le[e]?.component;function Ve(){return Ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},Ve.apply(null,arguments)}const Ne=({props:e,type:t})=>{const n=Be(t),{element:l}=w();if(!n)throw new C({context:{controlType:t}});return c.createElement(n,Ve({},e,{context:{elementId:l.id}}))},je=({children:e,layout:t})=>"custom"===t?e:c.createElement(Me,{layout:t},e),Me=(0,E.styled)(E.Box,{shouldForwardProp:e=>!["layout"].includes(e)})(({layout:e,theme:t})=>({display:"grid",gridGap:t.spacing(1),...Ae(e)})),Ae=e=>({justifyContent:"space-between",gridTemplateColumns:{full:"minmax(0, 1fr)","two-columns":"repeat(2, minmax(0, 1fr))"}[e]});function Fe(e,t,n,l){const r=Ue(t,n,e.split("."));if(!r)return[];const o=[];"object"===r.kind&&o.push(...Object.keys(r.shape).map(t=>e+"."+t));const a=$e(e,l);return o.length?o.reduce((e,r)=>[...e,...Fe(r,t,n,l)],a):a}function $e(e,t){return t?.[e]?.length?t[e].reduce((e,n)=>[...e,n,...$e(n,t)],[]):[]}function Ue(e,t,n){if(!n.length)return null;const[l,...r]=n,o=e[l];return o?r.reduce((e,r,o)=>{if(!e?.kind)return null;if("union"===e.kind){const r=(0,Y.extractValue)(n.slice(0,o+1),t),a=r?.$$type??null;return Ue({[l]:e.prop_types?.[a]},t,n.slice(0,o+2))}return"array"===e.kind?e.item_prop_type:"object"===e.kind?e.shape[r]:e[r]},o):null}function Ge(e,t,n){const l=e[0],r={...n};return e.reduce((n,l,r)=>n?r===e.length-1?(n[l]=null!==t?{...n[l]??{},value:t}:null,n[l]?.value??n.value):n[l]?.value??n.value:null,r),{[l]:r[l]??null}}const We=({schema:e})=>({key:"",kind:"object",meta:{},settings:{},default:null,shape:e}),Ke=({bind:e,children:t,propDisplayName:l})=>{const{element:{id:r},elementType:{propsSchema:o,dependenciesPerTargetMapping:a={}}}=w(),i=(0,u.useElementSettings)(r,Object.keys(o)),s={[e]:i?.[e]??null},m=We({schema:o}),p=function({elementId:e,propDisplayName:t}){return(0,c.useMemo)(()=>(0,q.undoable)({do:t=>{const n=(0,u.getElementSettings)(e,Object.keys(t));return(0,u.updateElementSettings)({id:e,props:t,withHistory:!1}),(0,J.setDocumentModifiedStatus)(!0),n},undo:({},t)=>{(0,u.updateElementSettings)({id:e,props:t,withHistory:!1})}},{title:(0,u.getElementLabel)(e),subtitle:(0,b.__)("%s edited","elementor").replace("%s",t),debounce:{wait:800}}),[e,t])}({elementId:r,propDisplayName:l});return c.createElement(n.PropProvider,{propType:m,value:s,setValue:t=>{const n=function(e,t,n,l){return t.length?t.reduce((e,t)=>{const r=t.split("."),o=Ue(n,l,r),a={...l,...e};return o?(0,Y.isDependencyMet)(o?.dependencies,a)?e:{...e,...Ge(r,null,a)}:e},{...e}):e}(t,Fe(e,o,i,a),o,i);p(n)},isDisabled:e=>!(0,Y.isDependencyMet)(e?.dependencies,i)},c.createElement(n.PropKeyProvider,{bind:e},t))},He=(0,c.createContext)(null),Je=()=>{const e=(0,c.useContext)(He);return e?.current?.offsetWidth??320},Ye=(0,E.styled)(d.ChevronDownIcon,{shouldForwardProp:e=>"open"!==e})(({theme:e,open:t})=>({transform:t?"rotate(180deg)":"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})})),qe=(0,E.styled)("div")`
	position: absolute;
	top: 0;
	right: ${({theme:e})=>e.spacing(3)};
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
`,Xe=({children:e,defaultOpen:t=!1,titleEnd:n=null})=>{const[l,r]=(0,c.useState)(t);return c.createElement(E.Stack,null,c.createElement(E.Stack,{sx:{position:"relative"}},c.createElement(E.Button,{fullWidth:!0,size:"small",color:"secondary",variant:"outlined",onClick:()=>{r(e=>!e)},endIcon:c.createElement(Ye,{open:l}),sx:{my:.5}},l?(0,b.__)("Show less","elementor"):(0,b.__)("Show more","elementor")),n&&c.createElement(qe,null,Ze(n,l))),c.createElement(E.Collapse,{in:l,timeout:"auto",unmountOnExit:!0},e))};function Ze(e,t){return"function"==typeof e?e(t):e}function Qe({title:e,children:t,defaultExpanded:n=!1,titleEnd:l}){const[r,o]=Oe(e,!!n),a=(0,c.useRef)(null),i=(0,c.useId)(),s=`label-${i}`,u=`content-${i}`;return c.createElement(c.Fragment,null,c.createElement(E.ListItemButton,{id:s,"aria-controls":u,onClick:()=>{o(!r)},sx:{"&:hover":{backgroundColor:"transparent"}}},c.createElement(E.Stack,{direction:"row",alignItems:"center",justifyItems:"start",flexGrow:1,gap:.5},c.createElement(E.ListItemText,{secondary:e,secondaryTypographyProps:{color:"text.primary",variant:"caption",fontWeight:"bold"},sx:{flexGrow:0,flexShrink:1,marginInlineEnd:1}}),Ze(l,r)),c.createElement(Ye,{open:r,color:"secondary",fontSize:"tiny"})),c.createElement(E.Collapse,{id:u,"aria-labelledby":s,in:r,timeout:"auto",unmountOnExit:!0},c.createElement(He.Provider,{value:a},c.createElement(E.Stack,{ref:a,gap:2.5,p:2},t))),c.createElement(E.Divider,null))}function et(){return et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},et.apply(null,arguments)}function tt(e){return c.createElement(E.List,et({disablePadding:!0,component:"div"},e))}const nt=()=>{const{elementType:e,element:t}=w(),n=Re();return c.createElement(he.SessionStorageProvider,{prefix:t.id},c.createElement(tt,null,e.controls.map(({type:e,value:t},l)=>{return"control"===e?c.createElement(lt,{key:t.bind,control:t}):"section"===e?c.createElement(Qe,{title:t.label,key:e+"."+l,defaultExpanded:(r=t.label,n.defaultSectionsExpanded.settings?.includes(r))},t.items?.map(e=>"control"===e.type?c.createElement(lt,{key:e.value.bind,control:e.value}):null)):null;var r})))},lt=({control:e})=>{if(!Be(e.type))return null;const t=e.meta?.layout||(l=e.type,Le[l].layout);var l;const r=function(e){if(e.childControlType){const t=Be(e.childControlType),n=(e=>Le[e]?.propTypeUtil)(e.childControlType);e={...e,childControlConfig:{component:t,props:e.childControlProps||{},propTypeUtil:n}}}return e}(e.props);return"custom"===t&&(r.label=e.label),c.createElement(Ke,{bind:e.bind,propDisplayName:e.label||e.bind},e.meta?.topDivider&&c.createElement(E.Divider,null),c.createElement(je,{layout:t},e.label&&"custom"!==t?c.createElement(n.ControlFormLabel,null,e.label):null,c.createElement(Ne,{type:e.type,props:r})))};var rt=window.elementorV2.editorResponsive,ot=window.elementorV2.editorStyles;const at=()=>{const{provider:e}=z(),[,t]=(0,c.useReducer)(e=>!e,!1);(0,c.useEffect)(()=>e?.subscribe(t),[e])},it="normal",st=e=>e??it,ct=e=>e??"desktop";function ut(e,t){const n=function(e){const t={},n=(e,l)=>{const{id:r,children:o}=e;t[r]=l?[...l]:[],o?.forEach(e=>{n(e,[...t[r]??[],r])})};return n(e),t}(t),l={};return t=>{const{breakpoint:r,state:o}=t,a=st(o),i=ct(r);if(l[i]?.[a])return l[i][a].snapshot;const s=[...n[i],r];return s.forEach((t,n)=>{const r=n>0?s[n-1]:null;((t,n,r)=>{const o=ct(t),a=st(r);l[o]||(l[o]={[it]:mt(e({breakpoint:t,state:null}),n,{},null)}),r&&!l[o][a]&&(l[o][a]=mt(e({breakpoint:t,state:r}),n,l[o],r))})(t,r?l[r]:void 0,o)}),l[i]?.[a]?.snapshot}}function mt(e,t,n,l){const r=function(e){const t={};return e.forEach(e=>{const{variant:{props:n}}=e;Object.entries(n).forEach(([n,l])=>{const r=(0,Y.filterEmptyValues)(l);if(null===r)return;t[n]||(t[n]=[]);const o={...e,value:r};t[n].push(o)})}),{snapshot:t,stateSpecificSnapshot:t}}(e);return l?{snapshot:pt([r.snapshot,t?.[l]?.stateSpecificSnapshot,n[it]?.snapshot]),stateSpecificSnapshot:pt([r.stateSpecificSnapshot,t?.[l]?.stateSpecificSnapshot])}:{snapshot:pt([r.snapshot,t?.[it]?.snapshot]),stateSpecificSnapshot:void 0}}function pt(e){const t={};return e.filter(Boolean).forEach(e=>Object.entries(e).forEach(([e,n])=>{t[e]||(t[e]=[]),t[e]=t[e].concat(n)})),t}function dt(e,t,n){return e&&"object"==typeof e?function(e,t){return!!e&&(0,Y.isTransformable)(t)&&e.key!==t.$$type}(n,e)?e:t.reduce((e,t)=>e?(0,Y.isTransformable)(e)?e.value?.[t]??null:"object"==typeof e?e[t]??null:null:null,e):null}const ft=(e,t)=>e&&"union"===e.kind?Object.values(e.prop_types).find(e=>!!t.reduce((e,t)=>{if("object"!==e?.kind)return null;const{shape:n}=e;return n[t]?n[t]:null},e))??null:null,Et=(0,c.createContext)(null);function bt({children:e}){const t=vt(),n=(0,rt.getBreakpointsTree)(),{getSnapshot:l,getInheritanceChain:r}=function(e,t){const n=function(e){const t={};return e.forEach(e=>{const n=D(e.id)?.getKey()??null;e.variants.forEach(l=>{const{meta:r}=l,{state:o,breakpoint:a}=r,i=ct(a),s=st(o);t[i]||(t[i]={});const c=t[i];c[s]||(c[s]=[]),c[s].push({style:e,variant:l,provider:n})})}),t}(e);return{getSnapshot:ut(({breakpoint:e,state:t})=>n?.[ct(e)]?.[st(t)]??[],t),getInheritanceChain:(e,t,n)=>{const[l,...r]=t;let o=e[l]??[];if(r.length>0){const e=ft(n,r);o=o.map(({value:t,...n})=>({...n,value:dt(t,r,e)})).filter(({value:e})=>!(0,Y.isEmpty)(e))}return o}}}(t,n);return c.createElement(Et.Provider,{value:{getSnapshot:l,getInheritanceChain:r}},e)}function yt(){const e=(0,c.useContext)(Et),{meta:t}=z();if(!e)throw new Error("useStylesInheritanceSnapshot must be used within a StyleInheritanceProvider");return t?e.getSnapshot(t)??null:null}function gt(e){const t=(0,c.useContext)(Et);if(!t)throw new Error("useStylesInheritanceChain must be used within a StyleInheritanceProvider");const n=(0,ot.getStylesSchema)(),l=n?.[e[0]],r=yt();return r?t.getInheritanceChain(r,e,l):[]}const vt=()=>{const{element:e}=w(),t=v(),n=ht();at();const l=(0,u.useElementSetting)(e.id,t),r=Y.classesPropTypeUtil.extract(l)??[];return m.stylesRepository.all().filter(e=>[...n,...r].includes(e.id))},ht=()=>{const{elementType:e}=w(),t=(0,u.getWidgetsCache)(),n=t?.[e.key];return Object.keys(n?.base_styles??{})};function _t(e){const{element:{id:t}}=w(),{id:n,meta:l,provider:r,canEdit:o}=z(),a=function({elementId:e,meta:{breakpoint:t,state:n}}){const l=v();return(0,c.useMemo)(()=>{const r={breakpoint:t,state:n},o={elementId:e,classesProp:l,meta:r,label:m.ELEMENTS_STYLES_RESERVED_LABEL};return(0,q.undoable)({do:e=>a(e)?i(e):s(e),undo:(t,n)=>a(t)?function(t,{createdStyleId:n}){(0,u.deleteElementStyle)(e,n)}(0,n):function(t,{styleId:n,provider:l,prevProps:o}){l.actions.updateProps?.({id:n,meta:r,props:o},{elementId:e})}(0,n),redo:(e,t)=>a(e)?i(e,t):s(e)},{title:({provider:t,styleId:n})=>{let l;return l=Ct(t,n)?St.title({elementId:e}):wt.title({provider:t}),l},subtitle:({provider:t,styleId:n,propDisplayName:l})=>{let r;return r=Ct(t,n)?St.subtitle({propDisplayName:l}):wt.subtitle({provider:t,styleId:n,elementId:e,propDisplayName:l}),r},debounce:{wait:800}});function a(e){return!e.styleId&&!e.provider}function i({props:e},t){return{createdStyleId:(0,u.createElementStyle)({...o,props:e,styleId:t?.createdStyleId})}}function s({provider:t,styleId:n,props:l}){if(!t.actions.updateProps)throw new T({context:{providerKey:t.getKey()}});const o=function(e,t){if(!e)return{};const n=(0,ot.getVariantByMeta)(e,t);return structuredClone(n?.props??{})}(t.actions.get(n,{elementId:e}),r);return t.actions.updateProps({id:n,meta:r,props:l},{elementId:e}),{styleId:n,provider:t,prevProps:o}}},[e,t,n,l])}({elementId:t,meta:l});at();const i=function({styleId:e,elementId:t,provider:n,meta:l,propNames:r}){if(!n||!e)return null;const o=n.actions.get(e,{elementId:t});if(!o)throw new I({context:{styleId:e,providerKey:n.getKey()}});const a=(0,ot.getVariantByMeta)(o,l);return Object.fromEntries(r.map(e=>[e,a?.props[e]??null]))}({elementId:t,styleId:n,provider:r,meta:l,propNames:e});return{values:i,setValues:(e,{history:{propDisplayName:t}})=>{a(n?{styleId:n,provider:r,props:e,propDisplayName:t}:{styleId:null,provider:null,props:e,propDisplayName:t})},canEdit:o}}const wt={title:({provider:e})=>{const t=e.labels?.singular;return t?(n=t).charAt(0).toUpperCase()+n.slice(1):(0,b.__)("Style","elementor");var n},subtitle:({provider:e,styleId:t,elementId:n,propDisplayName:l})=>{const r=e.actions.get(t,{elementId:n})?.label;if(!r)throw new Error(`Style ${t} not found`);return(0,b.__)("%s$1 %s$2 edited","elementor").replace("%s$1",r).replace("%s$2",l)}},St={title:({elementId:e})=>(0,u.getElementLabel)(e),subtitle:({propDisplayName:e})=>(0,b.__)("%s edited","elementor").replace("%s",e)},Ct=(e,t)=>!e||!t||(0,m.isElementsStylesProvider)(e.getKey());function xt(e,t){const{values:n,setValues:l,canEdit:r}=_t([e]);return{value:n?.[e]??null,setValue:n=>{l({[e]:n},t)},canEdit:r}}var Tt=window.elementorV2.editorCanvas;function It(){const e="rtl"===(0,E.useTheme)().direction;return{isSiteRtl:!!(()=>{const e=window;return e.elementorFrontend?.config??{}})()?.is_rtl,isUiRtl:e}}const kt=async(e,t,n)=>{try{const l=await n({props:{[t]:e.value}}),r=l?.[t]??l;return(0,c.isValidElement)(r)?r:"object"==typeof r?JSON.stringify(r):String(r)}catch{return""}},Pt=(0,Tt.createTransformersRegistry)(),zt={widescreen:d.WidescreenIcon,desktop:d.DesktopIcon,laptop:d.LaptopIcon,tablet_extra:d.TabletLandscapeIcon,tablet:d.TabletPortraitIcon,mobile_extra:d.MobileLandscapeIcon,mobile:d.MobilePortraitIcon},Dt=({breakpoint:e})=>{const t=(0,rt.useBreakpoints)(),n=e||"desktop",l=zt[n];if(!l)return null;const r=t.find(e=>e.id===n)?.label;return c.createElement(E.Tooltip,{title:r,placement:"top"},c.createElement(l,{fontSize:"tiny",sx:{mt:"2px"}}))},Rt="tiny",Ot=({displayLabel:e,provider:t})=>{const n=t===m.ELEMENTS_BASE_STYLES_PROVIDER_KEY?c.createElement(E.Tooltip,{title:(0,b.__)("Inherited from base styles","elementor"),placement:"top"},c.createElement(d.InfoCircleIcon,{fontSize:Rt})):void 0;return c.createElement(E.Chip,{label:e,size:Rt,color:R(t),variant:"standard",state:"enabled",icon:n,sx:e=>({lineHeight:1,flexWrap:"nowrap",alignItems:"center",borderRadius:.75*e.shape.borderRadius+"px",flexDirection:"row-reverse",".MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}})})},Lt=({index:e,value:t})=>c.createElement(E.Typography,{variant:"caption",color:"text.tertiary",sx:{mt:"1px",textDecoration:0===e?"none":"line-through",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},t),Bt=()=>c.createElement(E.Box,{display:"flex",gap:.5,alignItems:"center"}),Vt=({inheritanceChain:e,propType:t,path:n,label:l,children:r,isDisabled:o})=>{const[a,i]=(0,c.useState)(!1),s=()=>{o||i(!1)},u=n.join("."),d=Je(),f=(0,c.useMemo)(()=>(0,Tt.createPropsResolver)({transformers:Pt,schema:{[u]:t}}),[u,t]),y=((e,t,n)=>{const[l,r]=(0,c.useState)([]);return(0,c.useEffect)(()=>{(async()=>{const l=(await Promise.all(e.filter(({style:e})=>e).map((e,l)=>(async(e,t,n,l)=>{const{variant:{meta:{state:r,breakpoint:o}},style:{label:a,id:i}}=e,s=`${a}${r?":"+r:""}`;return{id:i?i+(r??""):t,provider:e.provider||"",breakpoint:o??"desktop",displayLabel:s,value:await kt(e,n,l)}})(e,l,t,n)))).map(e=>({...e,displayLabel:m.ELEMENTS_BASE_STYLES_PROVIDER_KEY!==e.provider?e.displayLabel:(0,b.__)("Base","elementor")})).filter(e=>!e.value||""!==e.displayLabel).slice(0,2);r(l)})()},[e,t,n]),l})(e,u,f),g=c.createElement(E.ClickAwayListener,{onClickAway:s},c.createElement(E.Card,{elevation:0,sx:{width:d-32+"px",maxWidth:496,overflowX:"hidden"}},c.createElement(E.CardContent,{sx:{display:"flex",gap:.5,flexDirection:"column",p:0,"&:last-child":{pb:0}}},c.createElement(p.PopoverHeader,{title:(0,b.__)("Style origin","elementor"),onClose:s}),c.createElement(E.Stack,{gap:1.5,sx:{pl:2,pr:1,pb:2,overflowX:"hidden",overflowY:"auto"},role:"list"},y.map((e,t)=>c.createElement(E.Box,{key:e.id,display:"flex",gap:.5,role:"listitem","aria-label":(0,b.__)("Inheritance item: %s","elementor").replace("%s",e.displayLabel)},c.createElement(E.Box,{display:"flex",gap:.5,sx:{flexWrap:"wrap",width:"100%"}},c.createElement(Dt,{breakpoint:e.breakpoint}),c.createElement(Ot,{displayLabel:e.displayLabel,provider:e.provider}),c.createElement(Lt,{index:t,value:e.value})),c.createElement(Bt,null)))))));return o?c.createElement(E.Box,{sx:{display:"inline-flex"}},r):c.createElement(Nt,{showInfotip:a,onClose:s,infotipContent:g,isDisabled:o},c.createElement(E.IconButton,{onClick:()=>{o||i(e=>!e)},"aria-label":l,sx:{my:"-1px"},disabled:o},r))};function Nt({children:e,showInfotip:t,onClose:n,infotipContent:l,isDisabled:r}){const o=It().isSiteRtl?9999999:-9999999;return r?c.createElement(E.Box,{sx:{display:"inline-flex"}},e):t?c.createElement(c.Fragment,null,c.createElement(E.Backdrop,{open:t,onClick:n,sx:{backgroundColor:"transparent",zIndex:e=>e.zIndex.modal-1}}),c.createElement(E.Infotip,{placement:"top",content:l,open:t,onClose:n,disableHoverListener:!0,componentsProps:{tooltip:{sx:{mx:2}}},slotProps:{popper:{modifiers:[{name:"offset",options:{offset:[o,0]}}]}}},e)):c.createElement(E.Tooltip,{title:(0,b.__)("Style origin","elementor"),placement:"top"},e)}const jt=["box-shadow","background-overlay","filter","backdrop-filter","transform"],Mt=()=>{const{path:e,propType:t}=(0,n.useBoundProp)(),l=gt(e);if(!e||!l.length)return null;const r=e.some(e=>jt.includes(e));return c.createElement(At,{inheritanceChain:l,path:e,propType:t,isDisabled:r})},At=({inheritanceChain:e,path:t,propType:n,isDisabled:l})=>{const{id:r,provider:o,meta:a}=z(),i=r?((e,t,n)=>e.find(({style:e,variant:{meta:{breakpoint:l,state:r}}})=>e.id===t&&l===n.breakpoint&&r===n.state))(e,r,a):null,s=!(0,Y.isEmpty)(i?.value),[u]=e;if(u.provider===m.ELEMENTS_BASE_STYLES_PROVIDER_KEY)return null;const p=i===u,d=Ft({isFinalValue:p,hasValue:s}),f={getColor:p&&o?O(o.getKey()):void 0,isOverridden:!(!s||p)||void 0};return c.createElement(Vt,{inheritanceChain:e,path:t,propType:n,label:d,isDisabled:l},c.createElement(H,f))},Ft=({isFinalValue:e,hasValue:t})=>e?(0,b.__)("This is the final value","elementor"):t?(0,b.__)("This value is overridden by another style","elementor"):(0,b.__)("This has value from another style","elementor"),$t=({children:e})=>{const{propType:t}=(0,n.useBoundProp)(),l=function(e){return e?.dependencies?.terms.length?e.dependencies.terms.flatMap(e=>(0,Y.isDependency)(e)?[]:e.path):[]}(t),{values:r}=_t(l);return(0,Y.isDependencyMet)(t?.dependencies,r)?e:null},Ut=({bind:e,propDisplayName:t,children:l})=>{const r=(0,ot.getStylesSchema)(),o=gt([e]),{value:a,canEdit:i,...s}=xt(e,{history:{propDisplayName:t}}),u=We({schema:r}),[m]=o,p={[e]:m?.value};return c.createElement(n.ControlAdornmentsProvider,{items:[{id:"styles-inheritance",Adornment:Mt}]},c.createElement(n.PropProvider,{propType:u,value:{[e]:a},setValue:t=>{s.setValue(t[e])},placeholder:p,isDisabled:()=>!i},c.createElement(n.PropKeyProvider,{bind:e},c.createElement($t,null,l))))},Gt=({gap:e=2,sx:t,children:n})=>c.createElement(E.Stack,{gap:e,sx:{...t}},n),Wt=(0,b.__)("Background","elementor"),Kt=()=>c.createElement(Gt,null,c.createElement(Ut,{bind:"background",propDisplayName:Wt},c.createElement(n.BackgroundControl,null))),Ht=()=>c.createElement(E.Divider,{sx:{my:.5}}),Jt="tiny",Yt=({isAdded:e,onAdd:t,onRemove:n,children:l,disabled:r,renderLabel:o})=>c.createElement(Gt,null,c.createElement(E.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},o(),e?c.createElement(E.IconButton,{size:Jt,onClick:n,"aria-label":"Remove",disabled:r},c.createElement(d.MinusIcon,{fontSize:Jt})):c.createElement(E.IconButton,{size:Jt,onClick:t,"aria-label":"Add",disabled:r},c.createElement(d.PlusIcon,{fontSize:Jt}))),c.createElement(E.Collapse,{in:e,unmountOnExit:!0},c.createElement(Gt,null,l))),qt=({children:e})=>c.createElement(E.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:.25},c.createElement(n.ControlFormLabel,null,e),c.createElement(n.ControlAdornments,null)),Xt=c.forwardRef((e,t)=>{const{direction:n="row",children:l,label:r}=e,o="row"===n?Zt:Qt;return c.createElement(o,{label:r,ref:t,children:l})}),Zt=c.forwardRef(({label:e,children:t},n)=>c.createElement(E.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:n},c.createElement(E.Grid,{item:!0,xs:6},c.createElement(qt,null,e)),c.createElement(E.Grid,{item:!0,xs:6,sx:e=>({width:`calc(50% - ${e.spacing(2)})`})},t))),Qt=c.forwardRef(({label:e,children:t},n)=>c.createElement(E.Stack,{gap:.75,ref:n},c.createElement(qt,null,e),t)),en=(0,b.__)("Border color","elementor"),tn=()=>c.createElement(Ut,{bind:"border-color",propDisplayName:en},c.createElement(Xt,{label:en},c.createElement(n.ColorControl,null))),nn=(0,b.__)("Border type","elementor"),ln=[{value:"none",label:(0,b.__)("None","elementor")},{value:"solid",label:(0,b.__)("Solid","elementor")},{value:"dashed",label:(0,b.__)("Dashed","elementor")},{value:"dotted",label:(0,b.__)("Dotted","elementor")},{value:"double",label:(0,b.__)("Double","elementor")},{value:"groove",label:(0,b.__)("Groove","elementor")},{value:"ridge",label:(0,b.__)("Ridge","elementor")},{value:"inset",label:(0,b.__)("Inset","elementor")},{value:"outset",label:(0,b.__)("Outset","elementor")}],rn=()=>c.createElement(Ut,{bind:"border-style",propDisplayName:nn},c.createElement(Xt,{label:nn},c.createElement(n.SelectControl,{options:ln}))),on=(0,b.__)("Border width","elementor"),an=(0,E.withDirection)(d.SideRightIcon),sn=(0,E.withDirection)(d.SideLeftIcon),cn=e=>[{label:(0,b.__)("Top","elementor"),icon:c.createElement(d.SideTopIcon,{fontSize:"tiny"}),bind:"block-start"},{label:e?(0,b.__)("Left","elementor"):(0,b.__)("Right","elementor"),icon:c.createElement(an,{fontSize:"tiny"}),bind:"inline-end"},{label:(0,b.__)("Bottom","elementor"),icon:c.createElement(d.SideBottomIcon,{fontSize:"tiny"}),bind:"block-end"},{label:e?(0,b.__)("Right","elementor"):(0,b.__)("Left","elementor"),icon:c.createElement(sn,{fontSize:"tiny"}),bind:"inline-start"}],un=()=>{const{isSiteRtl:e}=It();return c.createElement(Ut,{bind:"border-width",propDisplayName:on},c.createElement(n.EqualUnequalSizesControl,{items:cn(e),label:on,icon:c.createElement(d.SideAllIcon,{fontSize:"tiny"}),tooltipLabel:(0,b.__)("Adjust borders","elementor"),multiSizePropTypeUtil:Y.borderWidthPropTypeUtil}))},mn=(0,b.__)("Border","elementor"),pn={"border-width":{$$type:"size",value:{size:1,unit:"px"}},"border-color":{$$type:"color",value:"#000000"},"border-style":{$$type:"string",value:"solid"}},dn=()=>{const{values:e,setValues:t,canEdit:l}=_t(Object.keys(pn)),r={history:{propDisplayName:mn}},o=Object.values(e??{}).some(Boolean);return c.createElement(Yt,{isAdded:o,onAdd:()=>{t(pn,r)},onRemove:()=>{t({"border-width":null,"border-color":null,"border-style":null},r)},disabled:!l,renderLabel:()=>c.createElement(n.ControlFormLabel,null,mn)},c.createElement(un,null),c.createElement(tn,null),c.createElement(rn,null))},fn=({children:e})=>{const{isSiteRtl:t}=It();return c.createElement(E.DirectionProvider,{rtl:t},c.createElement(E.ThemeProvider,null,e))},En=(0,b.__)("Border radius","elementor"),bn=(0,E.withDirection)(d.RadiusTopLeftIcon),yn=(0,E.withDirection)(d.RadiusTopRightIcon),gn=(0,E.withDirection)(d.RadiusBottomLeftIcon),vn=(0,E.withDirection)(d.RadiusBottomRightIcon),hn=e=>e?(0,b.__)("Top right","elementor"):(0,b.__)("Top left","elementor"),_n=e=>e?(0,b.__)("Top left","elementor"):(0,b.__)("Top right","elementor"),wn=e=>e?(0,b.__)("Bottom right","elementor"):(0,b.__)("Bottom left","elementor"),Sn=e=>e?(0,b.__)("Bottom left","elementor"):(0,b.__)("Bottom right","elementor"),Cn=e=>[{label:hn(e),icon:c.createElement(bn,{fontSize:"tiny"}),bind:"start-start"},{label:_n(e),icon:c.createElement(yn,{fontSize:"tiny"}),bind:"start-end"},{label:wn(e),icon:c.createElement(gn,{fontSize:"tiny"}),bind:"end-start"},{label:Sn(e),icon:c.createElement(vn,{fontSize:"tiny"}),bind:"end-end"}],xn=()=>{const{isSiteRtl:e}=It();return c.createElement(fn,null,c.createElement(Ut,{bind:"border-radius",propDisplayName:En},c.createElement(n.EqualUnequalSizesControl,{items:Cn(e),label:En,icon:c.createElement(d.BorderCornersIcon,{fontSize:"tiny"}),tooltipLabel:(0,b.__)("Adjust corners","elementor"),multiSizePropTypeUtil:Y.borderRadiusPropTypeUtil})))},Tn=()=>c.createElement(Gt,null,c.createElement(xn,null),c.createElement(Ht,null),c.createElement(dn,null)),In=(0,b.__)("Opacity","elementor"),kn=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"opacity",propDisplayName:In},c.createElement(Xt,{ref:e,label:In},c.createElement(n.SizeControl,{units:["%"],anchorRef:e,defaultUnit:"%"})))},Pn=(0,b.__)("Box shadow","elementor"),zn=(0,b.__)("Filters","elementor"),Dn=(0,b.__)("Backdrop filters","elementor"),Rn=()=>c.createElement(Gt,null,c.createElement(kn,null),c.createElement(Ht,null),c.createElement(Ut,{bind:"box-shadow",propDisplayName:Pn},c.createElement(n.BoxShadowRepeaterControl,null)),c.createElement(Ht,null),c.createElement(Ut,{bind:"filter",propDisplayName:zn},c.createElement(n.FilterRepeaterControl,null)),c.createElement(Ht,null),c.createElement(Ut,{bind:"backdrop-filter",propDisplayName:Dn},c.createElement(n.FilterRepeaterControl,{filterPropName:"backdrop-filter"}))),On=(0,b.__)("Flex direction","elementor"),Ln={row:0,column:90,"row-reverse":180,"column-reverse":270},Bn={row:0,column:-90,"row-reverse":-180,"column-reverse":-270},Vn=({icon:e,size:t,isClockwise:n=!0,offset:l=0,disableRotationForReversed:r=!1})=>{const o=(0,c.useRef)(Nn(n,l,r));return o.current=Nn(n,l,r,o),c.createElement(e,{fontSize:t,sx:{transition:".3s",rotate:`${o.current}deg`}})},Nn=(e,t,n,l)=>{const{value:r}=xt("flex-direction",{history:{propDisplayName:On}}),o="rtl"===(0,E.useTheme)().direction?-1:1,a=e?Ln:Bn,i=r?.value||"row",s=l?l.current*o:a[i]+t,c=((a[i]+t-s+360)%360+180)%360-180;return n&&["row-reverse","column-reverse"].includes(i)?0:(s+c)*o};function jn(){return jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},jn.apply(null,arguments)}const Mn=(0,b.__)("Align content","elementor"),An=(0,E.withDirection)(d.JustifyTopIcon),Fn=(0,E.withDirection)(d.JustifyBottomIcon),$n={isClockwise:!1,offset:0,disableRotationForReversed:!0},Un=[{value:"start",label:(0,b.__)("Start","elementor"),renderContent:({size:e})=>c.createElement(Vn,jn({icon:An,size:e},$n)),showTooltip:!0},{value:"center",label:(0,b.__)("Center","elementor"),renderContent:({size:e})=>c.createElement(Vn,jn({icon:d.JustifyCenterIcon,size:e},$n)),showTooltip:!0},{value:"end",label:(0,b.__)("End","elementor"),renderContent:({size:e})=>c.createElement(Vn,jn({icon:Fn,size:e},$n)),showTooltip:!0},{value:"space-between",label:(0,b.__)("Space between","elementor"),renderContent:({size:e})=>c.createElement(Vn,jn({icon:d.JustifySpaceBetweenVerticalIcon,size:e},$n)),showTooltip:!0},{value:"space-around",label:(0,b.__)("Space around","elementor"),renderContent:({size:e})=>c.createElement(Vn,jn({icon:d.JustifySpaceAroundVerticalIcon,size:e},$n)),showTooltip:!0},{value:"space-evenly",label:(0,b.__)("Space evenly","elementor"),renderContent:({size:e})=>c.createElement(Vn,jn({icon:d.JustifyDistributeVerticalIcon,size:e},$n)),showTooltip:!0}],Gn=()=>c.createElement(Ut,{bind:"align-content",propDisplayName:Mn},c.createElement(fn,null,c.createElement(Xt,{label:Mn,direction:"column"},c.createElement(n.ToggleControl,{options:Un,fullWidth:!0}))));function Wn(){return Wn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},Wn.apply(null,arguments)}const Kn=(0,b.__)("Align items","elementor"),Hn=(0,E.withDirection)(d.LayoutAlignLeftIcon),Jn=(0,E.withDirection)(d.LayoutAlignRightIcon),Yn={isClockwise:!1,offset:90},qn=[{value:"start",label:(0,b.__)("Start","elementor"),renderContent:({size:e})=>c.createElement(Vn,Wn({icon:Hn,size:e},Yn)),showTooltip:!0},{value:"center",label:(0,b.__)("Center","elementor"),renderContent:({size:e})=>c.createElement(Vn,Wn({icon:d.LayoutAlignCenterIcon,size:e},Yn)),showTooltip:!0},{value:"end",label:(0,b.__)("End","elementor"),renderContent:({size:e})=>c.createElement(Vn,Wn({icon:Jn,size:e},Yn)),showTooltip:!0},{value:"stretch",label:(0,b.__)("Stretch","elementor"),renderContent:({size:e})=>c.createElement(Vn,Wn({icon:d.LayoutDistributeVerticalIcon,size:e},Yn)),showTooltip:!0}],Xn=()=>c.createElement(fn,null,c.createElement(Ut,{bind:"align-items",propDisplayName:Kn},c.createElement(Xt,{label:Kn},c.createElement(n.ToggleControl,{options:qn}))));function Zn(){return Zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},Zn.apply(null,arguments)}const Qn=(0,b.__)("Align self","elementor"),el={row:90,"row-reverse":90,column:0,"column-reverse":0},tl=(0,E.withDirection)(d.LayoutAlignLeftIcon),nl=(0,E.withDirection)(d.LayoutAlignRightIcon),ll={isClockwise:!1},rl=e=>[{value:"start",label:(0,b.__)("Start","elementor"),renderContent:({size:t})=>c.createElement(Vn,Zn({icon:tl,size:t,offset:el[e]},ll)),showTooltip:!0},{value:"center",label:(0,b.__)("Center","elementor"),renderContent:({size:t})=>c.createElement(Vn,Zn({icon:d.LayoutAlignCenterIcon,size:t,offset:el[e]},ll)),showTooltip:!0},{value:"end",label:(0,b.__)("End","elementor"),renderContent:({size:t})=>c.createElement(Vn,Zn({icon:nl,size:t,offset:el[e]},ll)),showTooltip:!0},{value:"stretch",label:(0,b.__)("Stretch","elementor"),renderContent:({size:t})=>c.createElement(Vn,Zn({icon:d.LayoutDistributeVerticalIcon,size:t,offset:el[e]},ll)),showTooltip:!0}],ol=({parentStyleDirection:e})=>c.createElement(Ut,{bind:"align-self",propDisplayName:Qn},c.createElement(fn,null,c.createElement(Xt,{label:Qn},c.createElement(n.ToggleControl,{options:rl(e)})))),al=(0,b.__)("Display","elementor"),il=[{value:"block",renderContent:()=>(0,b.__)("Block","elementor"),label:(0,b.__)("Block","elementor"),showTooltip:!0},{value:"flex",renderContent:()=>(0,b.__)("Flex","elementor"),label:(0,b.__)("Flex","elementor"),showTooltip:!0},{value:"inline-block",renderContent:()=>(0,b.__)("In-blk","elementor"),label:(0,b.__)("Inline-block","elementor"),showTooltip:!0},{value:"none",renderContent:()=>(0,b.__)("None","elementor"),label:(0,b.__)("None","elementor"),showTooltip:!0},{value:"inline-flex",renderContent:()=>(0,b.__)("In-flx","elementor"),label:(0,b.__)("Inline-flex","elementor"),showTooltip:!0}],sl=()=>{const e=cl();return c.createElement(Ut,{bind:"display",propDisplayName:al,placeholder:e},c.createElement(Xt,{label:al,direction:"column"},c.createElement(n.ToggleControl,{options:il,maxItems:4,fullWidth:!0})))},cl=()=>gt(["display"])[0]?.value??void 0,ul=(0,b.__)("Direction","elementor"),ml=[{value:"row",label:(0,b.__)("Row","elementor"),renderContent:({size:e})=>{const t=(0,E.withDirection)(d.ArrowRightIcon);return c.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column",label:(0,b.__)("Column","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:"row-reverse",label:(0,b.__)("Reversed row","elementor"),renderContent:({size:e})=>{const t=(0,E.withDirection)(d.ArrowLeftIcon);return c.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column-reverse",label:(0,b.__)("Reversed column","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0}],pl=()=>c.createElement(Ut,{bind:"flex-direction",propDisplayName:ul},c.createElement(fn,null,c.createElement(Xt,{label:ul},c.createElement(n.ToggleControl,{options:ml})))),dl=(0,b.__)("Order","elementor"),fl=-99999,El="first",bl="last",yl="custom",gl={[El]:fl,[bl]:99999},vl=[{value:El,label:(0,b.__)("First","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0},{value:bl,label:(0,b.__)("Last","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:yl,label:(0,b.__)("Custom","elementor"),renderContent:({size:e})=>c.createElement(d.PencilIcon,{fontSize:e}),showTooltip:!0}],hl=()=>{const{value:e,setValue:t,canEdit:l}=xt("order",{history:{propDisplayName:dl}}),[r,o]=(0,c.useState)(_l(e?.value??null));return(0,c.useEffect)(()=>{const t=_l(e?.value??null);o(t)},[e?.value]),c.createElement(Ut,{bind:"order",propDisplayName:dl},c.createElement(fn,null,c.createElement(Gt,null,c.createElement(Xt,{label:dl},c.createElement(n.ControlToggleButtonGroup,{items:vl,value:r,onChange:e=>{o(e),t(e&&e!==yl?{$$type:"number",value:gl[e]}:null)},exclusive:!0,disabled:!l})),yl===r&&c.createElement(E.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},c.createElement(E.Grid,{item:!0,xs:6},c.createElement(qt,null,(0,b.__)("Custom order","elementor"))),c.createElement(E.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},c.createElement(n.NumberControl,{min:-99998,max:99998,shouldForceInt:!0}))))))},_l=e=>99999===e?bl:fl===e?El:0===e||e?yl:null,wl=(0,b.__)("Flex Size","elementor"),Sl=[{value:"flex-grow",label:(0,b.__)("Grow","elementor"),renderContent:({size:e})=>c.createElement(d.ExpandIcon,{fontSize:e}),showTooltip:!0},{value:"flex-shrink",label:(0,b.__)("Shrink","elementor"),renderContent:({size:e})=>c.createElement(d.ShrinkIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,b.__)("Custom","elementor"),renderContent:({size:e})=>c.createElement(d.PencilIcon,{fontSize:e}),showTooltip:!0}],Cl=()=>{const{value:e,setValue:t,canEdit:l}=xt("flex",{history:{propDisplayName:wl}}),r=e,o=r?.value?.flexGrow?.value||null,a=r?.value?.flexShrink?.value||null,i=r?.value?.flexBasis?.value||null,s=(0,c.useMemo)(()=>Il({grow:o,shrink:a,basis:i}),[o,a,i]),[u,m]=(0,c.useState)(s),[p,d]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{p||m(s)},[s,p]),(0,c.useEffect)(()=>{null===e&&d(!1)},[e]),c.createElement(fn,null,c.createElement(Gt,null,c.createElement(Ut,{bind:"flex",propDisplayName:wl},c.createElement(Xt,{label:wl},c.createElement(n.ControlToggleButtonGroup,{value:u,onChange:(e=null)=>{m(e),d("custom"===e);const n=xl(e,r);t(n)},disabled:!l,items:Sl,exclusive:!0})),"custom"===u&&c.createElement(Tl,null))))},xl=(e,t)=>e?"flex-grow"===e?Y.flexPropTypeUtil.create({flexGrow:Y.numberPropTypeUtil.create(1),flexShrink:null,flexBasis:null}):"flex-shrink"===e?Y.flexPropTypeUtil.create({flexGrow:null,flexShrink:Y.numberPropTypeUtil.create(1),flexBasis:null}):"custom"===e?t||Y.flexPropTypeUtil.create({flexGrow:null,flexShrink:null,flexBasis:null}):null:null,Tl=()=>{const e=(0,c.useRef)(null),t=(0,n.useBoundProp)(Y.flexPropTypeUtil);return c.createElement(n.PropProvider,t,c.createElement(c.Fragment,null,c.createElement(Xt,{label:(0,b.__)("Grow","elementor")},c.createElement(n.PropKeyProvider,{bind:"flexGrow"},c.createElement(n.NumberControl,{min:0,shouldForceInt:!0}))),c.createElement(Xt,{label:(0,b.__)("Shrink","elementor")},c.createElement(n.PropKeyProvider,{bind:"flexShrink"},c.createElement(n.NumberControl,{min:0,shouldForceInt:!0}))),c.createElement(Xt,{label:(0,b.__)("Basis","elementor"),ref:e},c.createElement(n.PropKeyProvider,{bind:"flexBasis"},c.createElement(n.SizeControl,{extendedOptions:["auto"],anchorRef:e})))))},Il=({grow:e,shrink:t,basis:n})=>null!==e||null!==t||n?t&&e||n?"custom":1===e?"flex-grow":1===t?"flex-shrink":"custom":null,kl=(0,b.__)("Gaps","elementor"),Pl=()=>c.createElement(Ut,{bind:"gap",propDisplayName:kl},c.createElement(n.GapControl,{label:kl}));function zl(){return zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},zl.apply(null,arguments)}const Dl=(0,b.__)("Justify content","elementor"),Rl=(0,E.withDirection)(d.JustifyTopIcon),Ol=(0,E.withDirection)(d.JustifyBottomIcon),Ll={isClockwise:!0,offset:-90},Bl=[{value:"flex-start",label:(0,b.__)("Start","elementor"),renderContent:({size:e})=>c.createElement(Vn,zl({icon:Rl,size:e},Ll)),showTooltip:!0},{value:"center",label:(0,b.__)("Center","elementor"),renderContent:({size:e})=>c.createElement(Vn,zl({icon:d.JustifyCenterIcon,size:e},Ll)),showTooltip:!0},{value:"flex-end",label:(0,b.__)("End","elementor"),renderContent:({size:e})=>c.createElement(Vn,zl({icon:Ol,size:e},Ll)),showTooltip:!0},{value:"space-between",label:(0,b.__)("Space between","elementor"),renderContent:({size:e})=>c.createElement(Vn,zl({icon:d.JustifySpaceBetweenVerticalIcon,size:e},Ll)),showTooltip:!0},{value:"space-around",label:(0,b.__)("Space around","elementor"),renderContent:({size:e})=>c.createElement(Vn,zl({icon:d.JustifySpaceAroundVerticalIcon,size:e},Ll)),showTooltip:!0},{value:"space-evenly",label:(0,b.__)("Space evenly","elementor"),renderContent:({size:e})=>c.createElement(Vn,zl({icon:d.JustifyDistributeVerticalIcon,size:e},Ll)),showTooltip:!0}],Vl=()=>c.createElement(Ut,{bind:"justify-content",propDisplayName:Dl},c.createElement(fn,null,c.createElement(Xt,{label:Dl,direction:"column"},c.createElement(n.ToggleControl,{options:Bl,fullWidth:!0})))),Nl=(0,b.__)("Wrap","elementor"),jl=[{value:"nowrap",label:(0,b.__)("No wrap","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowRightIcon,{fontSize:e}),showTooltip:!0},{value:"wrap",label:(0,b.__)("Wrap","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowBackIcon,{fontSize:e}),showTooltip:!0},{value:"wrap-reverse",label:(0,b.__)("Reversed wrap","elementor"),renderContent:({size:e})=>c.createElement(d.ArrowForwardIcon,{fontSize:e}),showTooltip:!0}],Ml=()=>c.createElement(Ut,{bind:"flex-wrap",propDisplayName:Nl},c.createElement(fn,null,c.createElement(Xt,{label:Nl},c.createElement(n.ToggleControl,{options:jl})))),Al=(0,b.__)("Display","elementor"),Fl=(0,b.__)("Flex wrap","elementor"),$l=()=>{const{value:e}=xt("display",{history:{propDisplayName:Al}}),t=cl(),n=Wl(e,t),{element:l}=w(),r=(0,u.useParentElement)(l.id),o=(a=r?.id||null,(0,q.__privateUseListenTo)([(0,q.windowEvent)("elementor/device-mode/change"),(0,q.commandEndEvent)("document/elements/reset-style"),(0,q.commandEndEvent)("document/elements/settings"),(0,q.commandEndEvent)("document/elements/paste-style")],()=>{if(!a)return null;const e=window,t=e.elementor?.getContainer?.(a);return t?.view?.el?window.getComputedStyle(t.view.el):null}));var a;const i=o?.flexDirection??"row";return c.createElement(Gt,null,c.createElement(sl,null),n&&c.createElement(Ul,null),"flex"===o?.display&&c.createElement(Gl,{parentStyleDirection:i}))},Ul=()=>{const{value:e}=xt("flex-wrap",{history:{propDisplayName:Fl}});return c.createElement(c.Fragment,null,c.createElement(pl,null),c.createElement(Vl,null),c.createElement(Xn,null),c.createElement(Ht,null),c.createElement(Pl,null),c.createElement(Ml,null),["wrap","wrap-reverse"].includes(e?.value)&&c.createElement(Gn,null))},Gl=({parentStyleDirection:e})=>c.createElement(c.Fragment,null,c.createElement(Ht,null),c.createElement(n.ControlFormLabel,null,(0,b.__)("Flex child","elementor")),c.createElement(ol,{parentStyleDirection:e}),c.createElement(hl,null),c.createElement(Cl,null)),Wl=(e,t)=>{const n=e?.value??t?.value;return!!n&&("flex"===n||"inline-flex"===n)},Kl=(0,E.withDirection)(d.SideLeftIcon),Hl=(0,E.withDirection)(d.SideRightIcon),Jl={"inset-block-start":c.createElement(d.SideTopIcon,{fontSize:"tiny"}),"inset-block-end":c.createElement(d.SideBottomIcon,{fontSize:"tiny"}),"inset-inline-start":c.createElement(Vn,{icon:Kl,size:"tiny"}),"inset-inline-end":c.createElement(Vn,{icon:Hl,size:"tiny"})},Yl=e=>e?(0,b.__)("Right","elementor"):(0,b.__)("Left","elementor"),ql=e=>e?(0,b.__)("Left","elementor"):(0,b.__)("Right","elementor"),Xl=()=>{const{isSiteRtl:e}=It(),t=[(0,c.useRef)(null),(0,c.useRef)(null)];return c.createElement(fn,null,c.createElement(E.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:t[0]},c.createElement(Zl,{side:"inset-block-start",label:(0,b.__)("Top","elementor"),rowRef:t[0]}),c.createElement(Zl,{side:"inset-inline-end",label:ql(e),rowRef:t[0]})),c.createElement(E.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:t[1]},c.createElement(Zl,{side:"inset-block-end",label:(0,b.__)("Bottom","elementor"),rowRef:t[1]}),c.createElement(Zl,{side:"inset-inline-start",label:Yl(e),rowRef:t[1]})))},Zl=({side:e,label:t,rowRef:l})=>c.createElement(Ut,{bind:e,propDisplayName:t},c.createElement(E.Grid,{container:!0,gap:.75,alignItems:"center"},c.createElement(E.Grid,{item:!0,xs:12},c.createElement(qt,null,t)),c.createElement(E.Grid,{item:!0,xs:12},c.createElement(n.SizeControl,{startIcon:Jl[e],extendedOptions:["auto"],anchorRef:l})))),Ql=(0,b.__)("Anchor offset","elementor"),er=["px","em","rem","vw","vh"],tr=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"scroll-margin-top",propDisplayName:Ql},c.createElement(Xt,{label:Ql,ref:e},c.createElement(n.SizeControl,{units:er,anchorRef:e})))},nr=(0,b.__)("Position","elementor"),lr=[{label:(0,b.__)("Static","elementor"),value:"static"},{label:(0,b.__)("Relative","elementor"),value:"relative"},{label:(0,b.__)("Absolute","elementor"),value:"absolute"},{label:(0,b.__)("Fixed","elementor"),value:"fixed"},{label:(0,b.__)("Sticky","elementor"),value:"sticky"}],rr=({onChange:e})=>c.createElement(Ut,{bind:"position",propDisplayName:nr},c.createElement(Xt,{label:nr},c.createElement(n.SelectControl,{options:lr,onChange:e}))),or=(0,b.__)("Z-index","elementor"),ar=()=>c.createElement(Ut,{bind:"z-index",propDisplayName:or},c.createElement(Xt,{label:or},c.createElement(n.NumberControl,null))),ir=(0,b.__)("Position","elementor"),sr=(0,b.__)("Dimensions","elementor"),cr=()=>{const{value:e}=xt("position",{history:{propDisplayName:ir}}),{values:t,setValues:n}=_t(["inset-block-start","inset-block-end","inset-inline-start","inset-inline-end"]),[l,r,o]=ur(),a=e&&"static"!==e?.value;return c.createElement(Gt,null,c.createElement(rr,{onChange:(e,a)=>{const i={history:{propDisplayName:sr}};"static"===e?t&&(r(t),n({"inset-block-start":void 0,"inset-block-end":void 0,"inset-inline-start":void 0,"inset-inline-end":void 0},i)):"static"===a&&l&&(n(l,i),o())}}),a?c.createElement(c.Fragment,null,c.createElement(Xl,null),c.createElement(ar,null)):null,c.createElement(Ht,null),c.createElement(tr,null))},ur=()=>{const{id:e,meta:t}=z(),n=`styles/${e}/${t.breakpoint||"desktop"}/${t.state||"null"}/dimensions`;return(0,he.useSessionStorage)(n)},mr=({fields:e})=>{const{id:t,meta:n,provider:l}=z(),r=yt(),o=Object.fromEntries(Object.entries(r??{}).filter(([t])=>e.includes(t))),{hasValues:a,hasOverrides:i}=function(e,t,n){let l=!1,r=!1;return Object.values(e).forEach(e=>{const o=function(e,t,n){return e.find(({style:{id:e},variant:{meta:{breakpoint:l,state:r}}})=>e===t&&l===n.breakpoint&&r===n.state)}(e,t,n);if(!o)return;const[a]=e;o===a?l=!0:r=!0}),{hasValues:l,hasOverrides:r}}(o,t??"",n);if(!a&&!i)return null;const s=(0,b.__)("Has effective styles","elementor"),u=(0,b.__)("Has overridden styles","elementor");return c.createElement(E.Tooltip,{title:(0,b.__)("Has styles","elementor"),placement:"top"},c.createElement(E.Stack,{direction:"row",sx:{"& > *":{marginInlineStart:-.25}},role:"list"},a&&l&&c.createElement(H,{getColor:O(l.getKey()),"data-variant":(0,m.isElementsStylesProvider)(l.getKey())?"local":"global",role:"listitem","aria-label":s}),i&&c.createElement(H,{isOverridden:!0,"data-variant":"overridden",role:"listitem","aria-label":u})))},pr=({fields:e=[],children:t})=>c.createElement(Xe,{titleEnd:dr(e)},t);function dr(e){return 0===e.length?null:t=>t?null:c.createElement(mr,{fields:e})}const fr=(0,b.__)("Object fit","elementor"),Er=[{label:(0,b.__)("Fill","elementor"),value:"fill"},{label:(0,b.__)("Cover","elementor"),value:"cover"},{label:(0,b.__)("Contain","elementor"),value:"contain"},{label:(0,b.__)("None","elementor"),value:"none"},{label:(0,b.__)("Scale down","elementor"),value:"scale-down"}],br=()=>c.createElement(Ut,{bind:"object-fit",propDisplayName:fr},c.createElement(Xt,{label:fr},c.createElement(n.SelectControl,{options:Er}))),yr=(0,b.__)("Overflow","elementor"),gr=[{value:"visible",label:(0,b.__)("Visible","elementor"),renderContent:({size:e})=>c.createElement(d.EyeIcon,{fontSize:e}),showTooltip:!0},{value:"hidden",label:(0,b.__)("Hidden","elementor"),renderContent:({size:e})=>c.createElement(d.EyeOffIcon,{fontSize:e}),showTooltip:!0},{value:"auto",label:(0,b.__)("Auto","elementor"),renderContent:({size:e})=>c.createElement(d.LetterAIcon,{fontSize:e}),showTooltip:!0}],vr=()=>c.createElement(Ut,{bind:"overflow",propDisplayName:yr},c.createElement(Xt,{label:yr},c.createElement(n.ToggleControl,{options:gr})));function hr(){return hr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},hr.apply(null,arguments)}const _r=[[{bind:"width",label:(0,b.__)("Width","elementor")},{bind:"height",label:(0,b.__)("Height","elementor")}],[{bind:"min-width",label:(0,b.__)("Min width","elementor")},{bind:"min-height",label:(0,b.__)("Min height","elementor")}],[{bind:"max-width",label:(0,b.__)("Max width","elementor")},{bind:"max-height",label:(0,b.__)("Max height","elementor")}]],wr=(0,b.__)("Aspect Ratio","elementor"),Sr=()=>{const e=[(0,c.useRef)(null),(0,c.useRef)(null),(0,c.useRef)(null)];return c.createElement(Gt,null,_r.map((t,n)=>c.createElement(E.Grid,{key:n,container:!0,gap:2,flexWrap:"nowrap",ref:e[n]},t.map(t=>c.createElement(E.Grid,{item:!0,xs:6,key:t.bind},c.createElement(Cr,hr({},t,{rowRef:e[n],extendedOptions:["auto"]})))))),c.createElement(Ht,null),c.createElement(E.Stack,null,c.createElement(vr,null)),c.createElement(pr,{fields:["aspect-ratio","object-fit"]},c.createElement(E.Stack,{gap:2,pt:2},c.createElement(Ut,{bind:"aspect-ratio",propDisplayName:wr},c.createElement(n.AspectRatioControl,{label:wr})),c.createElement(Ht,null),c.createElement(br,null),c.createElement(Ut,{bind:"object-position",propDisplayName:(0,b.__)("Object position","elementor")},c.createElement(E.Grid,{item:!0,xs:6},c.createElement(n.PositionControl,null))))))},Cr=({label:e,bind:t,rowRef:l,extendedOptions:r})=>c.createElement(Ut,{bind:t,propDisplayName:e},c.createElement(E.Grid,{container:!0,gap:.75,alignItems:"center"},c.createElement(E.Grid,{item:!0,xs:12},c.createElement(qt,null,e)),c.createElement(E.Grid,{item:!0,xs:12},c.createElement(n.SizeControl,{extendedOptions:r,anchorRef:l})))),xr=(0,b.__)("Margin","elementor"),Tr=(0,b.__)("Padding","elementor"),Ir=()=>{const{isSiteRtl:e}=It();return c.createElement(Gt,null,c.createElement(Ut,{bind:"margin",propDisplayName:xr},c.createElement(n.LinkedDimensionsControl,{label:xr,isSiteRtl:e,extendedOptions:["auto"]})),c.createElement(Ht,null),c.createElement(Ut,{bind:"padding",propDisplayName:Tr},c.createElement(n.LinkedDimensionsControl,{label:Tr,isSiteRtl:e})))},kr=(0,b.__)("Columns","elementor"),Pr=()=>c.createElement(Ut,{bind:"column-count",propDisplayName:kr},c.createElement(Xt,{label:kr},c.createElement(n.NumberControl,{shouldForceInt:!0,min:0,step:1}))),zr=(0,b.__)("Column gap","elementor"),Dr=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"column-gap",propDisplayName:zr},c.createElement(Xt,{label:zr,ref:e},c.createElement(n.SizeControl,{anchorRef:e})))},Rr={system:(0,b.__)("System","elementor"),custom:(0,b.__)("Custom Fonts","elementor"),googlefonts:(0,b.__)("Google Fonts","elementor")},Or=()=>{const e=(()=>{const{controls:e}=(()=>{const e=window;return e.elementor?.config??{}})(),t=e?.font?.options;return t||null})();return(0,c.useMemo)(()=>{const t=["system","custom","googlefonts"];return Object.entries(e||{}).reduce((e,[n,l])=>{if(!Rr[l])return e;const r=t.indexOf(l);return e[r]||(e[r]={label:Rr[l],fonts:[]}),e[r].fonts.push(n),e},[]).filter(Boolean)},[e])},Lr=(0,b.__)("Font family","elementor"),Br=()=>{const e=Or(),t=Je();return 0===e.length?null:c.createElement(Ut,{bind:"font-family",propDisplayName:Lr},c.createElement(Xt,{label:Lr},c.createElement(n.FontFamilyControl,{fontFamilies:e,sectionWidth:t})))},Vr=(0,b.__)("Font size","elementor"),Nr=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"font-size",propDisplayName:Vr},c.createElement(Xt,{label:Vr,ref:e},c.createElement(n.SizeControl,{anchorRef:e})))},jr=(0,b.__)("Font style","elementor"),Mr=[{value:"normal",label:(0,b.__)("Normal","elementor"),renderContent:({size:e})=>c.createElement(d.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"italic",label:(0,b.__)("Italic","elementor"),renderContent:({size:e})=>c.createElement(d.ItalicIcon,{fontSize:e}),showTooltip:!0}],Ar=()=>c.createElement(Ut,{bind:"font-style",propDisplayName:jr},c.createElement(Xt,{label:jr},c.createElement(n.ToggleControl,{options:Mr}))),Fr=(0,b.__)("Font weight","elementor"),$r=[{value:"100",label:(0,b.__)("100 - Thin","elementor")},{value:"200",label:(0,b.__)("200 - Extra light","elementor")},{value:"300",label:(0,b.__)("300 - Light","elementor")},{value:"400",label:(0,b.__)("400 - Normal","elementor")},{value:"500",label:(0,b.__)("500 - Medium","elementor")},{value:"600",label:(0,b.__)("600 - Semi bold","elementor")},{value:"700",label:(0,b.__)("700 - Bold","elementor")},{value:"800",label:(0,b.__)("800 - Extra bold","elementor")},{value:"900",label:(0,b.__)("900 - Black","elementor")}],Ur=()=>c.createElement(Ut,{bind:"font-weight",propDisplayName:Fr},c.createElement(Xt,{label:Fr},c.createElement(n.SelectControl,{options:$r}))),Gr=(0,b.__)("Letter spacing","elementor"),Wr=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"letter-spacing",propDisplayName:Gr},c.createElement(Xt,{label:Gr,ref:e},c.createElement(n.SizeControl,{anchorRef:e})))},Kr=(0,b.__)("Line height","elementor"),Hr=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"line-height",propDisplayName:Kr},c.createElement(Xt,{label:Kr,ref:e},c.createElement(n.SizeControl,{anchorRef:e})))},Jr=(0,b.__)("Text align","elementor"),Yr=(0,E.withDirection)(d.AlignLeftIcon),qr=(0,E.withDirection)(d.AlignRightIcon),Xr=[{value:"start",label:(0,b.__)("Start","elementor"),renderContent:({size:e})=>c.createElement(Yr,{fontSize:e}),showTooltip:!0},{value:"center",label:(0,b.__)("Center","elementor"),renderContent:({size:e})=>c.createElement(d.AlignCenterIcon,{fontSize:e}),showTooltip:!0},{value:"end",label:(0,b.__)("End","elementor"),renderContent:({size:e})=>c.createElement(qr,{fontSize:e}),showTooltip:!0},{value:"justify",label:(0,b.__)("Justify","elementor"),renderContent:({size:e})=>c.createElement(d.AlignJustifiedIcon,{fontSize:e}),showTooltip:!0}],Zr=()=>c.createElement(Ut,{bind:"text-align",propDisplayName:Jr},c.createElement(fn,null,c.createElement(Xt,{label:Jr},c.createElement(n.ToggleControl,{options:Xr})))),Qr=(0,b.__)("Text color","elementor"),eo=()=>c.createElement(Ut,{bind:"color",propDisplayName:Qr},c.createElement(Xt,{label:Qr},c.createElement(n.ColorControl,null))),to=(0,b.__)("Line decoration","elementor"),no=[{value:"none",label:(0,b.__)("None","elementor"),renderContent:({size:e})=>c.createElement(d.MinusIcon,{fontSize:e}),showTooltip:!0,exclusive:!0},{value:"underline",label:(0,b.__)("Underline","elementor"),renderContent:({size:e})=>c.createElement(d.UnderlineIcon,{fontSize:e}),showTooltip:!0},{value:"line-through",label:(0,b.__)("Line-through","elementor"),renderContent:({size:e})=>c.createElement(d.StrikethroughIcon,{fontSize:e}),showTooltip:!0},{value:"overline",label:(0,b.__)("Overline","elementor"),renderContent:({size:e})=>c.createElement(d.OverlineIcon,{fontSize:e}),showTooltip:!0}],lo=()=>c.createElement(Ut,{bind:"text-decoration",propDisplayName:to},c.createElement(Xt,{label:to},c.createElement(n.ToggleControl,{options:no,exclusive:!1}))),ro=(0,b.__)("Direction","elementor"),oo=[{value:"ltr",label:(0,b.__)("Left to right","elementor"),renderContent:({size:e})=>c.createElement(d.TextDirectionLtrIcon,{fontSize:e}),showTooltip:!0},{value:"rtl",label:(0,b.__)("Right to left","elementor"),renderContent:({size:e})=>c.createElement(d.TextDirectionRtlIcon,{fontSize:e}),showTooltip:!0}],ao=()=>c.createElement(Ut,{bind:"direction",propDisplayName:ro},c.createElement(Xt,{label:ro},c.createElement(n.ToggleControl,{options:oo}))),io={$$type:"stroke",value:{color:{$$type:"color",value:"#000000"},width:{$$type:"size",value:{unit:"px",size:1}}}},so=(0,b.__)("Text stroke","elementor"),co=()=>{const{value:e,setValue:t,canEdit:l}=xt("stroke",{history:{propDisplayName:so}}),r=Boolean(e);return c.createElement(Ut,{bind:"stroke",propDisplayName:so},c.createElement(Yt,{isAdded:r,onAdd:()=>{t(io)},onRemove:()=>{t(null)},disabled:!l,renderLabel:()=>c.createElement(qt,null,so)},c.createElement(n.StrokeControl,null)))},uo=(0,b.__)("Text transform","elementor"),mo=[{value:"none",label:(0,b.__)("None","elementor"),renderContent:({size:e})=>c.createElement(d.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"capitalize",label:(0,b.__)("Capitalize","elementor"),renderContent:({size:e})=>c.createElement(d.LetterCaseIcon,{fontSize:e}),showTooltip:!0},{value:"uppercase",label:(0,b.__)("Uppercase","elementor"),renderContent:({size:e})=>c.createElement(d.LetterCaseUpperIcon,{fontSize:e}),showTooltip:!0},{value:"lowercase",label:(0,b.__)("Lowercase","elementor"),renderContent:({size:e})=>c.createElement(d.LetterCaseLowerIcon,{fontSize:e}),showTooltip:!0}],po=()=>c.createElement(Ut,{bind:"text-transform",propDisplayName:uo},c.createElement(Xt,{label:uo},c.createElement(n.ToggleControl,{options:mo}))),fo=(0,b.__)("Word spacing","elementor"),Eo=()=>{const e=(0,c.useRef)(null);return c.createElement(Ut,{bind:"word-spacing",propDisplayName:fo},c.createElement(Xt,{label:fo,ref:e},c.createElement(n.SizeControl,{anchorRef:e})))},bo=()=>c.createElement(Gt,null,c.createElement(Br,null),c.createElement(Ur,null),c.createElement(Nr,null),c.createElement(Ht,null),c.createElement(Zr,null),c.createElement(eo,null),c.createElement(pr,{fields:["line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]},c.createElement(Gt,{sx:{pt:2}},c.createElement(Hr,null),c.createElement(Wr,null),c.createElement(Eo,null),c.createElement(Pr,null),c.createElement(Dr,null),c.createElement(Ht,null),c.createElement(lo,null),c.createElement(po,null),c.createElement(ao,null),c.createElement(Ar,null),c.createElement(co,null)))),yo=({section:e,fields:t=[]})=>{const{component:n,name:l,title:r}=e,o=Re(),a=n,i=o.defaultSectionsExpanded.style?.includes(l);return c.createElement(Qe,{title:r,defaultExpanded:i,titleEnd:dr(t)},c.createElement(a,null))},go={position:"sticky",zIndex:1100,opacity:1,backgroundColor:"background.default",transition:"top 300ms ease"},vo=()=>{const e=function(){const{elementType:e}=w(),t=Object.entries(e.propsSchema).find(([,e])=>"plain"===e.kind&&e.key===Y.CLASSES_PROP_KEY);if(!t)throw new Error("Element does not have a classes prop");return t[0]}(),[t,n]=function(e){const[t,n]=Oe("active-style-id",null),l=function(e){const{element:t}=w();return(0,u.useElementSetting)(t.id,e)}(e)?.value||[],r=function(e){const{element:t}=w(),n=(0,u.getElementStyles)(t.id)??{};return Object.values(n).find(t=>e.includes(t.id))}(l);return[function(e,t){return e&&t.includes(e)?e:null}(t,l)||r?.id||null,n]}(e),[l,r]=(0,c.useState)(null),o=(0,rt.useActiveBreakpoint)();return c.createElement(g,{prop:e},c.createElement(P,{meta:{breakpoint:o,state:l},id:t,setId:e=>{n(e),r(null)},setMetaState:r},c.createElement(he.SessionStorageProvider,{prefix:t??""},c.createElement(bt,null,c.createElement(ho,null,c.createElement(Ee,null),c.createElement(E.Divider,null)),c.createElement(tt,null,c.createElement(yo,{section:{component:$l,name:"Layout",title:(0,b.__)("Layout","elementor")},fields:["display","flex-direction","flex-wrap","justify-content","align-items","align-content","align-self","gap"]}),c.createElement(yo,{section:{component:Ir,name:"Spacing",title:(0,b.__)("Spacing","elementor")},fields:["margin","padding"]}),c.createElement(yo,{section:{component:Sr,name:"Size",title:(0,b.__)("Size","elementor")},fields:["width","min-width","max-width","height","min-height","max-height","overflow","aspect-ratio","object-fit"]}),c.createElement(yo,{section:{component:cr,name:"Position",title:(0,b.__)("Position","elementor")},fields:["position","z-index","scroll-margin-top"]}),c.createElement(yo,{section:{component:bo,name:"Typography",title:(0,b.__)("Typography","elementor")},fields:["font-family","font-weight","font-size","text-align","color","line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]}),c.createElement(yo,{section:{component:Kt,name:"Background",title:(0,b.__)("Background","elementor")},fields:["background"]}),c.createElement(yo,{section:{component:Tn,name:"Border",title:(0,b.__)("Border","elementor")},fields:["border-radius","border-width","border-color","border-style"]}),c.createElement(yo,{section:{component:Rn,name:"Effects",title:(0,b.__)("Effects","elementor")},fields:["box-shadow","opacity","transform","filter","backdrop-filter"]})),c.createElement(E.Box,{sx:{height:"150px"}})))))};function ho({children:e}){const t=(0,c.useContext)(Ie)?.direction??"up";return c.createElement(E.Stack,{sx:{...go,top:"up"===t?"37px":0}},e)}function _o(){return _o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},_o.apply(null,arguments)}const wo=()=>{const{element:e}=w();return c.createElement(c.Fragment,{key:e.id},c.createElement(So,null))},So=()=>{const e=Re().defaultTab,[t,n]=Oe("tab",e),{getTabProps:l,getTabPanelProps:r,getTabsProps:o}=(0,E.useTabs)(t);return c.createElement(Pe,null,c.createElement(E.Stack,{direction:"column",sx:{width:"100%"}},c.createElement(E.Stack,{sx:{...go,top:0}},c.createElement(E.Tabs,_o({variant:"fullWidth",size:"small",sx:{mt:.5}},o(),{onChange:(e,t)=>{o().onChange(e,t),n(t)}}),c.createElement(E.Tab,_o({label:(0,b.__)("General","elementor")},l("settings"))),c.createElement(E.Tab,_o({label:(0,b.__)("Style","elementor")},l("style")))),c.createElement(E.Divider,null)),c.createElement(E.TabPanel,_o({},r("settings"),{disablePadding:!0}),c.createElement(nt,null)),c.createElement(E.TabPanel,_o({},r("style"),{disablePadding:!0}),c.createElement(vo,null))))},{useMenuItems:Co}=xe,{panel:xo,usePanelActions:To,usePanelStatus:Io}=(0,ve.__createPanel)({id:"editing-panel",component:()=>{const{element:e,elementType:t}=(0,u.useSelectedElement)(),l=r(),o=Co().default;if(!e||!t)return null;const a=(0,b.__)("Edit %s","elementor").replace("%s",t.title);return c.createElement(E.ErrorBoundary,{fallback:c.createElement(Te,null)},c.createElement(he.SessionStorageProvider,{prefix:"elementor"},c.createElement(p.ThemeProvider,null,c.createElement(ve.Panel,null,c.createElement(ve.PanelHeader,null,c.createElement(ve.PanelHeaderTitle,null,a),c.createElement(d.AtomIcon,{fontSize:"small",sx:{color:"text.tertiary"}})),c.createElement(ve.PanelBody,null,c.createElement(n.ControlActionsProvider,{items:o},c.createElement(n.ControlReplacementsProvider,{replacements:l},c.createElement(_,{element:e,elementType:t},c.createElement(wo,null)))))))))}});function ko(){return ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},ko.apply(null,arguments)}const Po=e=>{const t=Je();return c.createElement(p.PopoverBody,ko({},e,{width:t}))};var zo=window.elementorV2.editor;const Do=()=>{const e=(0,u.getSelectedElements)(),t=(0,u.getWidgetsCache)();return 1===e.length&&!!t?.[e[0].type]?.atomic_controls},Ro=()=>((()=>{const{open:e}=To();(0,c.useEffect)(()=>(0,q.__privateListenTo)((0,q.commandStartEvent)("panel/editor/open"),()=>{Do()&&e()}),[])})(),null),Oo=()=>{const{atomicDynamicTags:e}=(()=>{const e=window;return e.elementor?.config??{}})();return e?{tags:e.tags,groups:e.groups}:null};var Lo=window.elementorV2.schema;const Bo="dynamic",Vo=e=>{const t="union"===e.kind&&e.prop_types[Bo];return t&&t.key===Bo?t:null},No=e=>(0,Y.isTransformable)(e)&&e.$$type===Bo,jo=(0,Y.createPropUtils)(Bo,Lo.z.strictObject({name:Lo.z.string(),settings:Lo.z.any().optional()})),Mo=()=>{let e=[];const{propType:t}=(0,n.useBoundProp)();if(t){const n=Vo(t);e=n?.settings.categories||[]}return(0,c.useMemo)(()=>Ao(e),[e.join()])},Ao=e=>{const t=Oo();if(!e.length||!t?.tags)return[];const n=new Set(e);return Object.values(t.tags).filter(e=>e.categories.some(e=>n.has(e)))},Fo=e=>{const t=Mo();return(0,c.useMemo)(()=>t.find(t=>t.name===e)??null,[t,e])};function $o(){return $o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},$o.apply(null,arguments)}const Uo=()=>c.createElement(d.DatabaseIcon,{fontSize:"tiny"}),Go=({value:e})=>{const t=(0,n.useBoundProp)(Y.backgroundImageOverlayPropTypeUtil);return c.createElement(n.PropProvider,$o({},t,{value:e.value}),c.createElement(n.PropKeyProvider,{bind:"image"},c.createElement(Wo,{rawValue:e.value})))},Wo=({rawValue:e})=>{const{propType:t}=(0,n.useBoundProp)(),l=t.prop_types["background-image-overlay"];return c.createElement(n.PropProvider,{propType:l.shape.image,value:e,setValue:()=>{}},c.createElement(n.PropKeyProvider,{bind:"src"},c.createElement(Ko,{rawValue:e.image})))},Ko=({rawValue:e})=>{const t=e.value.src,n=Fo(t.value.name||"");return c.createElement(c.Fragment,null,n?.label)},Ho=e=>{const{element:t}=w(),n=`dynamic/non-dynamic-values-history/${t.id}/${e}`;return(0,he.useSessionStorage)(n)},Jo=({bind:e,children:t})=>{const{value:l,setValue:r}=(0,n.useBoundProp)(jo),{name:o="",settings:a}=l??{},i=Fo(o);if(!i)throw new Error(`Dynamic tag ${o} not found`);const s=i.props_schema[e],u=s?.default,m=a?.[e]??u,p=We({schema:i.props_schema});return c.createElement(n.PropProvider,{propType:p,setValue:e=>{r({name:o,settings:{...a,...e}})},value:{[e]:m}},c.createElement(n.PropKeyProvider,{bind:e},t))},Yo=({close:e})=>{const[t,l]=(0,c.useState)(""),{groups:r}=Oo()||{},o=(0,E.useTheme)(),{value:a}=(0,n.useBoundProp)(),{bind:i,value:s,setValue:u}=(0,n.useBoundProp)(jo),[,m]=Ho(i),f=!!s,y=Zo(t),g=!y.length&&!t.trim(),v=y.flatMap(([e,t])=>[{type:"category",value:e,label:r?.[e]?.title||e},...t.map(e=>({type:"item",value:e.value,label:e.label}))]);return c.createElement(Po,null,c.createElement(p.PopoverHeader,{title:(0,b.__)("Dynamic tags","elementor"),onClose:e,icon:c.createElement(d.DatabaseIcon,{fontSize:"tiny"})}),g?c.createElement(Xo,null):c.createElement(c.Fragment,null,c.createElement(p.PopoverSearch,{value:t,onSearch:e=>{l(e)},placeholder:(0,b.__)("Search dynamic tags…","elementor")}),c.createElement(E.Divider,null),c.createElement(p.PopoverMenuList,{items:v,onSelect:t=>{f||m(a);const n=y.flatMap(([,e])=>e).find(e=>e.value===t);u({name:t,settings:{label:n?.label}}),e()},onClose:e,selectedValue:s?.name,itemStyle:e=>"item"===e.type?{paddingInlineStart:o.spacing(3.5)}:{},noResultsComponent:c.createElement(qo,{searchValue:t,onClear:()=>l("")})})))},qo=({searchValue:e,onClear:t})=>c.createElement(E.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},c.createElement(d.DatabaseIcon,{fontSize:"large"}),c.createElement(E.Typography,{align:"center",variant:"subtitle2"},(0,b.__)("Sorry, nothing matched","elementor"),c.createElement("br",null),"“",e,"”."),c.createElement(E.Typography,{align:"center",variant:"caption",sx:{display:"flex",flexDirection:"column"}},(0,b.__)("Try something else.","elementor"),c.createElement(E.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,b.__)("Clear & try again","elementor")))),Xo=()=>c.createElement(c.Fragment,null,c.createElement(E.Divider,null),c.createElement(E.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},c.createElement(d.DatabaseIcon,{fontSize:"large"}),c.createElement(E.Typography,{align:"center",variant:"subtitle2"},(0,b.__)("Streamline your workflow with dynamic tags","elementor")),c.createElement(E.Typography,{align:"center",variant:"caption"},(0,b.__)("You'll need Elementor Pro to use this feature.","elementor")))),Zo=e=>[...Mo().reduce((t,{name:n,label:l,group:r})=>l.toLowerCase().includes(e.trim().toLowerCase())?(t.has(r)||t.set(r,[]),t.get(r)?.push({label:l,value:n}),t):t,new Map)];function Qo(){return Qo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)({}).hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},Qo.apply(null,arguments)}const ea="tiny",ta=()=>{const{setValue:e}=(0,n.useBoundProp)(),{bind:t,value:l}=(0,n.useBoundProp)(jo),[r]=Ho(t),o=(0,E.usePopupState)({variant:"popover"}),{name:a=""}=l,i=Fo(a);if(!i)throw new Error(`Dynamic tag ${a} not found`);return c.createElement(E.Box,null,c.createElement(E.UnstableTag,Qo({fullWidth:!0,showActionsOnHover:!0,label:i.label,startIcon:c.createElement(d.DatabaseIcon,{fontSize:ea})},(0,E.bindTrigger)(o),{actions:c.createElement(c.Fragment,null,c.createElement(na,{dynamicTag:i}),c.createElement(E.IconButton,{size:ea,onClick:()=>{e(r??null)},"aria-label":(0,b.__)("Remove dynamic value","elementor")},c.createElement(d.XIcon,{fontSize:ea})))})),c.createElement(E.Popover,Qo({disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:1}}},(0,E.bindPopover)(o)),c.createElement(Po,null,c.createElement(Yo,{close:o.close}))))},na=({dynamicTag:e})=>{const t=(0,E.usePopupState)({variant:"popover"});return e.atomic_controls.length?c.createElement(c.Fragment,null,c.createElement(E.IconButton,Qo({size:ea},(0,E.bindTrigger)(t),{"aria-label":(0,b.__)("Settings","elementor")}),c.createElement(d.SettingsIcon,{fontSize:ea})),c.createElement(E.Popover,Qo({disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:1}}},(0,E.bindPopover)(t)),c.createElement(Po,null,c.createElement(p.PopoverHeader,{title:e.label,onClose:t.close,icon:c.createElement(d.DatabaseIcon,{fontSize:ea})}),c.createElement(la,{controls:e.atomic_controls})))):null},la=({controls:e})=>{const t=e.filter(({type:e})=>"section"===e),{getTabsProps:n,getTabProps:l,getTabPanelProps:r}=(0,E.useTabs)(0);return t.length?c.createElement(c.Fragment,null,c.createElement(E.Tabs,Qo({size:"small",variant:"fullWidth"},n()),t.map(({value:e},t)=>c.createElement(E.Tab,Qo({key:t,label:e.label,sx:{px:1,py:.5}},l(t))))),c.createElement(E.Divider,null),t.map(({value:e},t)=>c.createElement(E.TabPanel,Qo({key:t,sx:{flexGrow:1,py:0,overflowY:"auto"}},r(t)),c.createElement(E.Stack,{p:2,gap:2},e.items.map(e=>"control"===e.type?c.createElement(ra,{key:e.value.bind,control:e.value}):null))))):null},ra=({control:e})=>Be(e.type)?c.createElement(Jo,{bind:e.bind},c.createElement(E.Grid,{container:!0,gap:.75},e.label?c.createElement(E.Grid,{item:!0,xs:12},c.createElement(n.ControlFormLabel,null,e.label)):null,c.createElement(E.Grid,{item:!0,xs:12},c.createElement(Ne,{type:e.type,props:e.props})))):null,oa=(0,S.createError)({code:"dynamic_tags_manager_not_found",message:"Dynamic tags manager not found"}),aa=(0,Tt.createTransformer)(e=>e.name?function(e,t){const n=window,{dynamicTags:l}=n.elementor??{};if(!l)throw new oa;const r=()=>{const n=l.createTag("v4-dynamic-tag",e,t);return n?l.loadTagDataFromCache(n)??null:null},o=r();return null!==o?o:new Promise(e=>{l.refreshCacheFromServer(()=>{e(r())})})}(e.name,function(e){const t=Object.entries(e).map(([e,t])=>[e,(0,Y.isTransformable)(t)?t.value:t]);return Object.fromEntries(t)}(e.settings??{})):null),ia=()=>{const{propType:e}=(0,n.useBoundProp)(),t=!!e&&(e=>!!Vo(e))(e);return{visible:t,icon:d.DatabaseIcon,title:(0,b.__)("Dynamic tags","elementor"),content:({close:e})=>c.createElement(Yo,{close:e})}},{registerPopoverAction:sa}=xe,ca=()=>{l({component:ta,condition:({value:e})=>No(e)}),(0,n.injectIntoRepeaterItemLabel)({id:"dynamic-background-image",condition:({value:e})=>No(e.value?.image?.value?.src),component:Go}),(0,n.injectIntoRepeaterItemIcon)({id:"dynamic-background-image",condition:({value:e})=>No(e.value?.image?.value?.src),component:Uo}),sa({id:"dynamic-tags",useProps:ia}),Tt.styleTransformersRegistry.register("dynamic",aa),Tt.settingsTransformersRegistry.register("dynamic",aa)},{registerAction:ua}=xe;function ma(){const e=!!(0,c.useContext)(k),{value:t,setValue:l,path:r}=(0,n.useBoundProp)();return{visible:e&&null!=t&&r.length<=2,title:(0,b.__)("Clear","elementor"),icon:d.BrushBigIcon,onClick:()=>l(null)}}const pa=new Set(["background-color-overlay","background-image-overlay","background-gradient-overlay","gradient-color-stop","color-stop","background-image-position-offset","background-image-size-scale","image-src","image","background-overlay"]),da=(0,Tt.createTransformer)(e=>c.createElement(E.Stack,{direction:"row",gap:10},c.createElement(fa,{value:e}),c.createElement(Ea,{value:e}))),fa=({value:e})=>{const{color:t}=e;return c.createElement(ba,{size:"inherit",component:"span",value:t})},Ea=({value:{color:e}})=>c.createElement("span",null,e),ba=(0,E.styled)(E.UnstableColorIndicator)(({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"})),ya=(0,Tt.createTransformer)(e=>c.createElement(E.Stack,{direction:"row",gap:10},c.createElement(ga,{value:e}),c.createElement(va,{value:e}))),ga=({value:e})=>{const t=ha(e);return c.createElement(ba,{size:"inherit",component:"span",value:t})},va=({value:e})=>"linear"===e.type?c.createElement("span",null,(0,b.__)("Linear Gradient","elementor")):c.createElement("span",null,(0,b.__)("Radial Gradient","elementor")),ha=e=>{const t=e.stops?.map(({color:e,offset:t})=>`${e} ${t??0}%`)?.join(",");return"linear"===e.type?`linear-gradient(${e.angle}deg, ${t})`:`radial-gradient(circle at ${e.positions}, ${t})`};var _a=window.elementorV2.wpMedia;const wa=(0,Tt.createTransformer)(e=>c.createElement(E.Stack,{direction:"row",gap:10},c.createElement(Sa,{value:e}),c.createElement(Ca,{value:e}))),Sa=({value:e})=>{const{imageUrl:t}=xa(e);return c.createElement(E.CardMedia,{image:t,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},Ca=({value:e})=>{const{imageTitle:t}=xa(e);return c.createElement(p.EllipsisWithTooltip,{title:t},c.createElement("span",null,t))},xa=e=>{let t,n=null;const l=e?.image.src,{data:r}=(0,_a.useWpMediaAttachment)(l.id||null);if(l.id){const e=Ta(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},Ta=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",Ia=(0,Tt.createTransformer)(e=>e&&0!==e.length?c.createElement(E.Stack,{direction:"column"},e.map((e,t)=>c.createElement(E.Stack,{key:t},e))):null);const ka=()=>{!function(){const e=Tt.styleTransformersRegistry.all();Object.entries(e).forEach(([e,t])=>{pa.has(e)||Pt.register(e,t)}),Pt.registerFallback((0,Tt.createTransformer)(e=>e)),Pt.register("background-color-overlay",da),Pt.register("background-gradient-overlay",ya),Pt.register("background-image-overlay",wa),Pt.register("background-overlay",Ia)}()};function Pa(){(0,ve.__registerPanel)(xo),za(),(0,zo.injectIntoLogic)({id:"editing-panel-hooks",component:Ro}),ca(),ka(),ua({id:"reset-style-value",useProps:ma})}const za=()=>{(0,q.blockCommand)({command:"panel/editor/open",condition:Do})};(window.elementorV2=window.elementorV2||{}).editorEditingPanel=t}(),window.elementorV2.editorEditingPanel?.init?.();