/*! elementor - v3.31.0 - 27-08-2025 */
(()=>{var e={9923:(e,t,o)=>{"use strict";var n=o(12470).__,r=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var l=r(o(39805)),a=r(o(40989)),i=r(o(15118)),u=r(o(29402)),s=r(o(41621)),d=r(o(87861)),c=r(o(68520)),f=r(o(86045));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){return(0,l.default)(this,_default),function _callSuper(e,t,o){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,u.default)(e).constructor):t.apply(e,o))}(this,_default,arguments)}return(0,d.default)(_default,e),(0,a.default)(_default,[{key:"getModalOptions",value:function getModalOptions(){return{id:"elementor-new-floating-elements-modal"}}},{key:"getLogoOptions",value:function getLogoOptions(){return{title:n("New Floating Elements","elementor")}}},{key:"initialize",value:function initialize(){!function _superPropGet(e,t,o,n){var r=(0,s.default)((0,u.default)(1&n?e.prototype:e),t,o);return 2&n&&"function"==typeof r?function(e){return r.apply(o,e)}:r}(_default,"initialize",this,3)([]),this.showLogo(),this.showContentView()}},{key:"showContentView",value:function showContentView(){this.modalContent.show(new c.default)}}])}(f.default)},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,o)=>{var n=o(10564).default;e.exports=function toPrimitive(e,t){if("object"!=n(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,t||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},14718:(e,t,o)=>{var n=o(29402);e.exports=function _superPropBase(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=n(e)););return e},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,o)=>{var n=o(10564).default,r=o(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},22575:(e,t,o)=>{"use strict";var n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o(39805)),l=n(o(40989)),a=n(o(15118)),i=n(o(29402)),u=n(o(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){return(0,r.default)(this,_default),function _callSuper(e,t,o){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,i.default)(e).constructor):t.apply(e,o))}(this,_default,arguments)}return(0,u.default)(_default,e),(0,l.default)(_default,[{key:"id",value:function id(){return"elementor-template-library-loading"}},{key:"getTemplate",value:function getTemplate(){return"#tmpl-elementor-template-library-loading"}}])}(Marionette.ItemView)},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},32384:(e,t,o)=>{"use strict";var n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o(39805)),l=n(o(40989)),a=n(o(15118)),i=n(o(29402)),u=n(o(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){return(0,r.default)(this,_default),function _callSuper(e,t,o){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,i.default)(e).constructor):t.apply(e,o))}(this,_default,arguments)}return(0,u.default)(_default,e),(0,l.default)(_default,[{key:"getTemplate",value:function getTemplate(){return"#tmpl-elementor-templates-modal__header__logo"}},{key:"className",value:function className(){return"elementor-templates-modal__header__logo"}},{key:"events",value:function events(){return{click:"onClick"}}},{key:"templateHelpers",value:function templateHelpers(){return{title:this.getOption("title")}}},{key:"onClick",value:function onClick(){var e=this.getOption("click");e&&e()}}])}(Marionette.ItemView)},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,o)=>{var n=o(45498);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,n(r.key),r)}}e.exports=function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},41621:(e,t,o)=>{var n=o(14718);function _get(){return e.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,o){var r=n(e,t);if(r){var l=Object.getOwnPropertyDescriptor(r,t);return l.get?l.get.call(arguments.length<3?e:o):l.value}},e.exports.__esModule=!0,e.exports.default=e.exports,_get.apply(null,arguments)}e.exports=_get,e.exports.__esModule=!0,e.exports.default=e.exports},42242:(e,t,o)=>{"use strict";var n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o(39805)),l=n(o(40989)),a=n(o(15118)),i=n(o(29402)),u=n(o(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){return(0,r.default)(this,_default),function _callSuper(e,t,o){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,i.default)(e).constructor):t.apply(e,o))}(this,_default,arguments)}return(0,u.default)(_default,e),(0,l.default)(_default,[{key:"className",value:function className(){return"elementor-templates-modal__header"}},{key:"getTemplate",value:function getTemplate(){return"#tmpl-elementor-templates-modal__header"}},{key:"regions",value:function regions(){return{logoArea:".elementor-templates-modal__header__logo-area",tools:"#elementor-template-library-header-tools",menuArea:".elementor-templates-modal__header__menu-area"}}},{key:"ui",value:function ui(){return{closeModal:".elementor-templates-modal__header__close"}}},{key:"events",value:function events(){return{"click @ui.closeModal":"onCloseModalClick"}}},{key:"templateHelpers",value:function templateHelpers(){return{closeType:this.getOption("closeType")}}},{key:"onCloseModalClick",value:function onCloseModalClick(){var e,t;this._parent._parent._parent.hideModal();var o=null!==(e=null===(t=elementor.config)||void 0===t||null===(t=t.document)||void 0===t?void 0:t.type)&&void 0!==e?e:"default",n=new CustomEvent("core/modal/close/".concat(o));window.dispatchEvent(n),this.isFloatingButtonLibraryClose()&&($e.internal("document/save/set-is-modified",{status:!1}),window.location.href=elementor.config.admin_floating_button_admin_url)}},{key:"isFloatingButtonLibraryClose",value:function isFloatingButtonLibraryClose(){var e,t;return window.elementor&&(null===(e=elementor.config)||void 0===e?void 0:e.admin_floating_button_admin_url)&&"floating-buttons"===(null===(t=elementor.config)||void 0===t||null===(t=t.document)||void 0===t?void 0:t.type)&&(this.$el.closest(".dialog-lightbox-widget-content").find(".elementor-template-library-template-floating_button").length||this.$el.closest(".dialog-lightbox-widget-content").find("#elementor-template-library-preview").length||this.$el.closest(".dialog-lightbox-widget-content").find("#elementor-template-library-templates-empty").length)}}])}(Marionette.LayoutView)},45498:(e,t,o)=>{var n=o(10564).default,r=o(11327);e.exports=function toPropertyKey(e){var t=r(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},68520:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=Marionette.ItemView.extend({id:"elementor-new-floating-elements-dialog-content",template:"#tmpl-elementor-new-floating-elements",ui:{},events:{},onRender:function onRender(){}})},86045:(e,t,o)=>{"use strict";var n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(o(39805)),l=n(o(40989)),a=n(o(15118)),i=n(o(29402)),u=n(o(87861)),s=n(o(42242)),d=n(o(32384)),c=n(o(22575));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){return(0,r.default)(this,_default),function _callSuper(e,t,o){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,i.default)(e).constructor):t.apply(e,o))}(this,_default,arguments)}return(0,u.default)(_default,e),(0,l.default)(_default,[{key:"el",value:function el(){return this.getModal().getElements("widget")}},{key:"regions",value:function regions(){return{modalHeader:".dialog-header",modalContent:".dialog-lightbox-content",modalLoading:".dialog-lightbox-loading"}}},{key:"initialize",value:function initialize(){this.modalHeader.show(new s.default(this.getHeaderOptions()))}},{key:"getModal",value:function getModal(){return this.modal||this.initModal(),this.modal}},{key:"initModal",value:function initModal(){var e={className:"elementor-templates-modal",closeButton:!1,draggable:!1,hide:{onOutsideClick:!1,onEscKeyPress:!1}};jQuery.extend(!0,e,this.getModalOptions()),this.modal=elementorCommon.dialogsManager.createWidget("lightbox",e),this.modal.getElements("message").append(this.modal.addElement("content"),this.modal.addElement("loading")),e.draggable&&this.draggableModal()}},{key:"showModal",value:function showModal(){this.getModal().show()}},{key:"hideModal",value:function hideModal(){this.getModal().hide()}},{key:"draggableModal",value:function draggableModal(){var e=this.getModal().getElements("widgetContent");e.draggable({containment:"parent",stop:function stop(){e.height("")}}),e.css("position","absolute")}},{key:"getModalOptions",value:function getModalOptions(){return{}}},{key:"getLogoOptions",value:function getLogoOptions(){return{}}},{key:"getHeaderOptions",value:function getHeaderOptions(){return{closeType:"normal"}}},{key:"getHeaderView",value:function getHeaderView(){return this.modalHeader.currentView}},{key:"showLoadingView",value:function showLoadingView(){this.modalLoading.show(new c.default),this.modalLoading.$el.show(),this.modalContent.$el.hide()}},{key:"hideLoadingView",value:function hideLoadingView(){this.modalContent.$el.show(),this.modalLoading.$el.hide()}},{key:"showLogo",value:function showLogo(){this.getHeaderView().logoArea.show(new d.default(this.getLogoOptions()))}}])}(Marionette.LayoutView)},87861:(e,t,o)=>{var n=o(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,o){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,o)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(o){var n=t[o];if(void 0!==n)return n.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,__webpack_require__),r.exports}(()=>{"use strict";var e=__webpack_require__(96784)(__webpack_require__(9923)),t=elementorModules.ViewModule.extend({getDefaultSettings:function getDefaultSettings(){return{selectors:{addButtonTopBar:".page-title-action",addButtonAdminBar:"#wp-admin-bar-new-e-floating-buttons a",addButtonEmptyTemplate:"#elementor-template-library-add-new"}}},getDefaultElements:function getDefaultElements(){var e=this.getSettings("selectors");return{addButtonTopBar:document.querySelector(e.addButtonTopBar),addButtonAdminBar:document.querySelector(e.addButtonAdminBar),addButtonEmptyTemplate:document.querySelector(e.addButtonEmptyTemplate)}},bindEvents:function bindEvents(){null!==this.elements.addButtonTopBar&&this.elements.addButtonTopBar.addEventListener("click",this.onAddButtonClick),null!==this.elements.addButtonAdminBar&&this.elements.addButtonAdminBar.addEventListener("click",this.onAddButtonClick),null!==this.elements.addButtonEmptyTemplate&&this.elements.addButtonEmptyTemplate.addEventListener("click",this.onAddButtonClick)},onInit:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),this.layout=new e.default},onAddButtonClick:function onAddButtonClick(e){e.preventDefault(),this.layout.showModal()}});document.addEventListener("DOMContentLoaded",function(){window.elementorNewFloatingElements=new t})})()})();