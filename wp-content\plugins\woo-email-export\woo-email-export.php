<?php
/**
 * Plugin Name: WooCommerce Email Export
 * Plugin URI: 
 * Description: Export customer email addresses from WooCommerce orders to CSV based on date range and order status criteria.
 * Version: 1.0.0
 * Author: 
 * Text Domain: woo-email-export
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 * WC requires at least: 6.0
 * WC tested up to: 8.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>' . 
             __('WooCommerce Email Export requires WooCommerce to be installed and active.', 'woo-email-export') . 
             '</p></div>';
    });
    return;
}

// Define plugin constants
define('WEE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WEE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WEE_VERSION', '1.0.0');
define('WEE_TEXT_DOMAIN', 'woo-email-export');

// Load plugin classes
require_once WEE_PLUGIN_DIR . 'includes/class-wee-admin.php';

/**
 * Initialize the plugin
 */
function wee_init() {
    // Load text domain for translations
    load_plugin_textdomain('woo-email-export', false, dirname(plugin_basename(__FILE__)) . '/languages');
    
    // Initialize admin functionality
    if (is_admin()) {
        new WEE_Admin();
    }
}

// Hook into WordPress
add_action('plugins_loaded', 'wee_init');

/**
 * Plugin activation hook
 */
function wee_activate() {
    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        deactivate_plugins(plugin_basename(__FILE__));
        wp_die(__('WooCommerce Email Export requires WooCommerce to be installed and active.', 'woo-email-export'));
    }
}
register_activation_hook(__FILE__, 'wee_activate');

/**
 * Plugin deactivation hook
 */
function wee_deactivate() {
    // Clean up any temporary files or scheduled events if needed
    // Currently no cleanup needed
}
register_deactivation_hook(__FILE__, 'wee_deactivate');
