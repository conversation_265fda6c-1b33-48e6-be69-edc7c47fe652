2025-09-04T15:25:16+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_order_number() in C:\laragon\www\test\wp-content\plugins\woo-email-ordini\test-plugin.php:86 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-email-ordini\test-plugin.php","line":86},"remote-logging":true,"backtrace":["#0 {main}n  thrown"]}
2025-09-04T15:26:31+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_order_number() in C:\laragon\www\test\wp-content\plugins\woo-email-ordini\debug-simple.php:52 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-email-ordini\debug-simple.php","line":52},"remote-logging":true,"backtrace":["#0 {main}n  thrown"]}
2025-09-04T15:26:53+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_order_number() in C:\laragon\www\test\wp-content\plugins\woo-email-ordini\debug-simple.php:52 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-email-ordini\debug-simple.php","line":52},"remote-logging":true,"backtrace":["#0 {main}n  thrown"]}
2025-09-04T15:31:20+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_email() in C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-csv-exporter.php:92 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-csv-exporter.php","line":92},"remote-logging":true,"backtrace":["#0 C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-csv-exporter.php(36): Woo_Email_Ordini_CSV_Exporter->extract_emails(Array)n#1 C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-admin.php(133): Woo_Email_Ordini_CSV_Exporter->export('2025-07-01', '2025-09-01', Array)n#2 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): Woo_Email_Ordini_Admin->handle_csv_export('')n#3 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)n#4 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#5 C:\laragon\www\test\wp-admin\admin.php(176): do_action('admin_init')n#6 {main}n  thrown"]}
2025-09-04T15:33:18+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_email() in C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-csv-exporter.php:92 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-csv-exporter.php","line":92},"remote-logging":true,"backtrace":["#0 C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-csv-exporter.php(36): Woo_Email_Ordini_CSV_Exporter->extract_emails(Array)n#1 C:\laragon\www\test\wp-content\plugins\woo-email-ordini\includes\class-admin.php(133): Woo_Email_Ordini_CSV_Exporter->export('2025-05-29', '2025-09-08', Array)n#2 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): Woo_Email_Ordini_Admin->handle_csv_export('')n#3 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)n#4 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#5 C:\laragon\www\test\wp-admin\admin.php(176): do_action('admin_init')n#6 {main}n  thrown"]}
2025-09-04T15:44:08+00:00 CRITICAL Uncaught Error: Call to undefined method Automattic\WooCommerce\Admin\Overrides\OrderRefund::get_billing_email() in C:\laragon\www\test\wp-content\plugins\woo-email-export\includes\class-wee-admin.php:194 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www\test\wp-content\plugins\woo-email-export\includes\class-wee-admin.php","line":194},"remote-logging":true,"backtrace":["#0 C:\laragon\www\test\wp-content\plugins\woo-email-export\includes\class-wee-admin.php(176): WEE_Admin->generate_csv('2025-07-01', '2025-09-04', Array)n#1 C:\laragon\www\test\wp-includes\class-wp-hook.php(324): WEE_Admin->handle_export_emails('')n#2 C:\laragon\www\test\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)n#3 C:\laragon\www\test\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#4 C:\laragon\www\test\wp-admin\admin-post.php(82): do_action('admin_post_wee_...')n#5 {main}n  thrown"]}
