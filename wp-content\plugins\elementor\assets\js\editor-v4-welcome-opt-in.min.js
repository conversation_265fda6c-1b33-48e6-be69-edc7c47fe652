/*! elementor - v3.31.0 - 27-08-2025 */
(()=>{var e={6903:(e,t,r)=>{"use strict";var o=r(62688),n=r(96784),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.WelcomeDialog=void 0;var i=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,o=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var n,i,u={__proto__:null,default:e};if(null===e||"object"!=a(e)&&"function"!=typeof e)return u;if(n=t?o:r){if(n.has(e))return n.get(e);n.set(e,u)}for(var l in e)"default"!==l&&{}.hasOwnProperty.call(e,l)&&((i=(n=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,l))&&(i.get||i.set)?n(u,l,i):u[l]=e[l]);return u}(e,t)}(r(41594)),u=n(r(18821)),l=r(12470),s=r(86956);var c={heading:(0,l.__)("Say hello to a new experience!","elementor"),introduction:(0,l.__)("You're now using Editor V4, a new generation of web creation.","elementor"),listItems:[(0,l.__)("Try out Editor V4 elements such as Div, SVG and Paragraph.","elementor"),(0,l.__)("Set up a new Class and apply it site-wide for perfect consistency.","elementor"),(0,l.__)("Customize any style element per screen size by switching between responsive views.","elementor")],footerText:(0,l.__)("Need help getting started?","elementor"),helpCenter:(0,l.__)("Learn more","elementor"),closeButton:(0,l.__)("Let's Go","elementor")},p="https://go.elementor.com/wp-dash-opt-in-v4-help-center/";(t.WelcomeDialog=function WelcomeDialog(e){var t=e.doClose,r=(0,i.useRef)(null),o=(0,i.useState)(!1),n=(0,u.default)(o,2),a=n[0],l=n[1];return(0,i.useEffect)(function(){r.current=document.body,l(!0)},[]),a&&r.current?i.default.createElement(s.Dialog,{open:Boolean(r.current),onClose:t,maxWidth:"sm"},i.default.createElement(s.Box,{sx:{aspectRatio:"2",backgroundImage:"url(https://assets.elementor.com/v4-promotion/v1/images/v4_opt_in.png)",backgroundSize:"cover",backgroundPosition:"center"}}),i.default.createElement(s.Stack,{pt:3,pb:1.5,px:3,gap:3},i.default.createElement(s.Typography,{variant:"h6",color:"text.primary"},c.heading),i.default.createElement(s.Box,null,i.default.createElement(s.Typography,{variant:"body1",color:"text.secondary"},c.introduction),i.default.createElement(s.List,{sx:{pl:2}},c.listItems.map(function(e,t){return i.default.createElement(s.ListItem,{key:t,sx:{listStyle:"disc",display:"list-item",color:"text.secondary",p:0}},i.default.createElement(s.Typography,{variant:"body1"},e))}))),i.default.createElement(s.Stack,{direction:"row",alignItems:"center",gap:1.5},i.default.createElement(s.Typography,{variant:"body1",color:"text.secondary"},c.footerText),i.default.createElement(s.Link,{href:p,target:"_blank",variant:"body1",color:"info.main",sx:{textDecoration:"none"}},c.helpCenter))),i.default.createElement(s.Divider,null),i.default.createElement(s.Stack,{py:2,px:3},i.default.createElement(s.Button,{variant:"contained",color:"accent",onClick:t,sx:{ml:"auto"}},c.closeButton))):null}).propTypes={doClose:o.func}},7470:(e,t,r)=>{"use strict";var o=r(75206);t.createRoot=o.createRoot,t.hydrateRoot=o.hydrateRoot},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},12470:e=>{"use strict";e.exports=wp.i18n},18791:(e,t,r)=>{"use strict";var o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var n=_interopRequireWildcard(r(75206)),a=r(7470);function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var r=new WeakMap,n=new WeakMap;return(_interopRequireWildcard=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,u={__proto__:null,default:e};if(null===e||"object"!=o(e)&&"function"!=typeof e)return u;if(a=t?n:r){if(a.has(e))return a.get(e);a.set(e,u)}for(var l in e)"default"!==l&&{}.hasOwnProperty.call(e,l)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,l))&&(i.get||i.set)?a(u,l,i):u[l]=e[l]);return u})(e,t)}t.default={render:function render(e,t){var r;try{var o=(0,a.createRoot)(t);o.render(e),r=function unmountFunction(){o.unmount()}}catch(o){n.render(e,t),r=function unmountFunction(){n.unmountComponentAtNode(t)}}return{unmount:r}}}},18821:(e,t,r)=>{var o=r(70569),n=r(65474),a=r(37744),i=r(11018);e.exports=function _slicedToArray(e,t){return o(e)||n(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var o=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},40362:(e,t,r)=>{"use strict";var o=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,n,a,i){if(i!==o){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},41594:e=>{"use strict";e.exports=React},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},62688:(e,t,r)=>{e.exports=r(40362)()},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,n,a,i,u=[],l=!0,s=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(o=a.call(r)).done)&&(u.push(o.value),u.length!==t);l=!0);}catch(e){s=!0,n=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(s)throw n}}return u}},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},75206:e=>{"use strict";e.exports=ReactDOM},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o},e.exports.__esModule=!0,e.exports.default=e.exports},86956:e=>{"use strict";e.exports=elementorV2.ui},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(62688),t=__webpack_require__(96784),r=__webpack_require__(10564),o=t(__webpack_require__(18821)),n=function _interopRequireWildcard(e,t){if("function"==typeof WeakMap)var o=new WeakMap,n=new WeakMap;return function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;var a,i,u={__proto__:null,default:e};if(null===e||"object"!=r(e)&&"function"!=typeof e)return u;if(a=t?n:o){if(a.has(e))return a.get(e);a.set(e,u)}for(var l in e)"default"!==l&&{}.hasOwnProperty.call(e,l)&&((i=(a=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,l))&&(i.get||i.set)?a(u,l,i):u[l]=e[l]);return u}(e,t)}(__webpack_require__(41594)),a=t(__webpack_require__(18791)),i=__webpack_require__(86956),u=__webpack_require__(6903);var l=function App(e){var t=(0,n.useState)(!0),r=(0,o.default)(t,2),a=r[0],l=r[1];return n.default.createElement(i.DirectionProvider,{rtl:e.isRTL},n.default.createElement(i.LocalizationProvider,null,n.default.createElement(i.ThemeProvider,{colorScheme:"light",palette:"unstable"},a&&n.default.createElement(u.WelcomeDialog,{doClose:function handleClose(){l(!1)}}))))};l.propTypes={isRTL:e.bool.isRequired};!function init(){if(!document.body.classList.contains("elementor-editor-active"))return null;a.default.render(n.default.createElement(l,{isRTL:!!elementorCommon.config.isRTL}),function getRootElement(){var e=document.querySelector("#e-v4-opt-in-welcome");return e||((e=document.createElement("div")).id="e-v4-opt-in-welcome",document.body.appendChild(e)),e}())}()})()})();