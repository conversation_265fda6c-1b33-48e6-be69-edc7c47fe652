/*! For license information please see editor-editing-panel.js.LICENSE.txt */
!function(){"use strict";var e={"./packages/packages/core/editor-editing-panel/src/action.tsx":function(e,t,n){n.r(t),n.d(t,{default:function(){return Action}});var o=n("react"),r=n("@elementor/ui");const s="tiny";function Action({title:e,visible:t=!0,icon:n,onClick:a}){return t?o.createElement(r.<PERSON><PERSON><PERSON>,{placement:"top",title:e,arrow:!0},o.createElement(r.<PERSON><PERSON><PERSON>,{"aria-label":e,size:s,onClick:a},o.createElement(n,{fontSize:s}))):null}},"./packages/packages/core/editor-editing-panel/src/components/add-or-remove-content.tsx":function(e,t,n){n.r(t),n.d(t,{AddOrRemoveContent:function(){return AddOrRemoveContent}});var o=n("react"),r=n("@elementor/icons"),s=n("@elementor/ui"),a=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx");const i="tiny",AddOrRemoveContent=({isAdded:e,onAdd:t,onRemove:n,children:l,disabled:c,renderLabel:p})=>o.createElement(a.SectionContent,null,o.createElement(s.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},p(),e?o.createElement(s.IconButton,{size:i,onClick:n,"aria-label":"Remove",disabled:c},o.createElement(r.MinusIcon,{fontSize:i})):o.createElement(s.IconButton,{size:i,onClick:t,"aria-label":"Add",disabled:c},o.createElement(r.PlusIcon,{fontSize:i}))),o.createElement(s.Collapse,{in:e,unmountOnExit:!0},o.createElement(a.SectionContent,null,l)))},"./packages/packages/core/editor-editing-panel/src/components/collapse-icon.tsx":function(e,t,n){n.r(t),n.d(t,{CollapseIcon:function(){return s}});var o=n("@elementor/icons"),r=n("@elementor/ui");const s=(0,r.styled)(o.ChevronDownIcon,{shouldForwardProp:e=>"open"!==e})(({theme:e,open:t})=>({transform:t?"rotate(180deg)":"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})}))},"./packages/packages/core/editor-editing-panel/src/components/collapsible-content.tsx":function(e,t,n){n.r(t),n.d(t,{CollapsibleContent:function(){return CollapsibleContent},getCollapsibleValue:function(){return getCollapsibleValue}});var o=n("react"),r=n("@elementor/ui"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/components/collapse-icon.tsx");const i=(0,r.styled)("div")`
	position: absolute;
	top: 0;
	right: ${({theme:e})=>e.spacing(3)};
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
`,CollapsibleContent=({children:e,defaultOpen:t=!1,titleEnd:n=null})=>{const[l,c]=(0,o.useState)(t);return o.createElement(r.Stack,null,o.createElement(r.Stack,{sx:{position:"relative"}},o.createElement(r.Button,{fullWidth:!0,size:"small",color:"secondary",variant:"outlined",onClick:()=>{c(e=>!e)},endIcon:o.createElement(a.CollapseIcon,{open:l}),sx:{my:.5}},l?(0,s.__)("Show less","elementor"):(0,s.__)("Show more","elementor")),n&&o.createElement(i,null,getCollapsibleValue(n,l))),o.createElement(r.Collapse,{in:l,timeout:"auto",unmountOnExit:!0},e))};function getCollapsibleValue(e,t){return"function"==typeof e?e(t):e}},"./packages/packages/core/editor-editing-panel/src/components/control-label.tsx":function(e,t,n){n.r(t),n.d(t,{ControlLabel:function(){return ControlLabel}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/ui");const ControlLabel=({children:e})=>o.createElement(s.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:.25},o.createElement(r.ControlFormLabel,null,e),o.createElement(r.ControlAdornments,null))},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/autocomplete-option-internal-properties.ts":function(e,t,n){function addGroupToOptions(e,t){return e.map(e=>({...e,_group:`Existing ${t??"options"}`}))}function removeInternalKeys(e){const{_group:t,_action:n,...o}=e;return o}n.r(t),n.d(t,{addGroupToOptions:function(){return addGroupToOptions},removeInternalKeys:function(){return removeInternalKeys}})},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/creatable-autocomplete.tsx":function(e,t,n){n.r(t),n.d(t,{CreatableAutocomplete:function(){return p}});var o=n("react"),r=n("@elementor/ui"),s=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/autocomplete-option-internal-properties.ts"),a=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-autocomplete-change.ts"),i=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-autocomplete-states.ts"),l=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-create-option.ts"),c=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-filter-options.ts");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const p=o.forwardRef(CreatableAutocompleteInner);function CreatableAutocompleteInner({selected:e,options:t,entityName:n,onSelect:p,placeholder:u,onCreate:g,validate:m,renderEmptyState:y,...f},k){const{inputValue:x,setInputValue:v,error:h,setError:b,inputHandlers:E}=(0,i.useInputState)(m),{open:S,openDropdown:_,closeDropdown:C}=(0,i.useOpenState)(f.open),{createOption:w,loading:P}=(0,l.useCreateOption)({onCreate:g,validate:m,setInputValue:v,setError:b,closeDropdown:C}),[T,I]=(0,o.useMemo)(()=>[t,e].map(e=>(0,s.addGroupToOptions)(e,n?.plural)),[t,e,n?.plural]),F=(0,a.useAutocompleteChange)({options:T,onSelect:p,createOption:w,setInputValue:v,closeDropdown:C}),D=(0,c.useFilterOptions)({options:t,selected:e,onCreate:g,entityName:n}),z=Boolean(g)||x.length<2||void 0;return o.createElement(r.Autocomplete,_extends({renderTags:(e,t)=>e.map((e,n)=>o.createElement(r.Chip,_extends({size:"tiny"},t({index:n}),{key:e.key??e.value??e.label,label:e.label})))},f,{ref:k,freeSolo:z,forcePopupIcon:!1,multiple:!0,clearOnBlur:!0,selectOnFocus:!0,disableClearable:!0,handleHomeEndKeys:!0,disabled:P,open:S,onOpen:_,onClose:C,disableCloseOnSelect:!0,value:I,options:T,ListboxComponent:h?o.forwardRef((e,t)=>o.createElement(d,{ref:t,error:h})):void 0,renderGroup:e=>o.createElement(Group,e),inputValue:x,renderInput:e=>o.createElement(r.TextField,_extends({},e,{error:Boolean(h),placeholder:u},E,{sx:e=>({".MuiAutocomplete-inputRoot.MuiInputBase-adornedStart":{paddingLeft:e.spacing(.25),paddingRight:e.spacing(.25)}})})),onChange:F,getOptionLabel:e=>"string"==typeof e?e:e.label,getOptionKey:e=>"string"==typeof e?e:e.key??e.value??e.label,filterOptions:D,groupBy:e=>e._group??"",renderOption:(e,t)=>{const{_group:n,label:r}=t;return o.createElement("li",_extends({},e,{style:{display:"block",textOverflow:"ellipsis"},"data-group":n}),r)},noOptionsText:y?.({searchValue:x,onClear:()=>{v(""),C()}}),isOptionEqualToValue:(e,t)=>"string"==typeof e?e===t:e.value===t.value}))}const Group=e=>{const t=`combobox-group-${(0,o.useId)().replace(/:/g,"_")}`;return o.createElement(u,{role:"group","aria-labelledby":t},o.createElement(g,{id:t}," ",e.group),o.createElement(m,{role:"listbox"},e.children))},d=o.forwardRef(({error:e="error"},t)=>o.createElement(r.Box,{ref:t,sx:e=>({padding:e.spacing(2)})},o.createElement(r.Typography,{variant:"caption",sx:{color:"error.main",display:"inline-block"}},e))),u=(0,r.styled)("li")`
	&:not( :last-of-type ) {
		border-bottom: 1px solid ${({theme:e})=>e.palette.divider};
	}
`,g=(0,r.styled)(r.Box)(({theme:e})=>({position:"sticky",top:"-8px",padding:e.spacing(1,2),color:e.palette.text.tertiary,backgroundColor:e.palette.primary.contrastText})),m=(0,r.styled)("ul")`
	padding: 0;
`},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/index.ts":function(e,t,n){n.r(t),n.d(t,{CreatableAutocomplete:function(){return o.CreatableAutocomplete}});var o=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/creatable-autocomplete.tsx")},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-autocomplete-change.ts":function(e,t,n){n.r(t),n.d(t,{useAutocompleteChange:function(){return useAutocompleteChange}});var o=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/autocomplete-option-internal-properties.ts");function useAutocompleteChange(e){const{options:t,onSelect:n,createOption:r,setInputValue:s,closeDropdown:a}=e;if(!n&&!r)return;return async(e,n,o,i)=>{const l=i?.option;if(!l||"object"==typeof l&&l.fixed)return;const c=n.filter(e=>"string"!=typeof e);switch(o){case"removeOption":updateSelectedOptions(c,"removeOption",l);break;case"selectOption":{const e=l;if("create"===e._action){const t=e.value;return r?.(t)}updateSelectedOptions(c,"selectOption",e);break}case"createOption":{const e=l,n=t.find(t=>t.label.toLocaleLowerCase()===e.toLocaleLowerCase());if(!n)return r?.(e);c.push(n),updateSelectedOptions(c,"selectOption",n);break}}s(""),a()};function updateSelectedOptions(e,t,r){n?.(e.map(e=>(0,o.removeInternalKeys)(e)),t,(0,o.removeInternalKeys)(r))}}},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-autocomplete-states.ts":function(e,t,n){n.r(t),n.d(t,{useInputState:function(){return useInputState},useOpenState:function(){return useOpenState}});var o=n("react");function useInputState(e){const[t,n]=(0,o.useState)(""),[r,s]=(0,o.useState)(null);return{inputValue:t,setInputValue:n,error:r,setError:s,inputHandlers:{onChange:t=>{const{value:o}=t.target;if(n(o),!e)return;if(!o)return void s(null);const{isValid:r,errorMessage:a}=e(o,"inputChange");s(r?null:a)},onBlur:()=>{n(""),s(null)}}}}function useOpenState(e=!1){const[t,n]=(0,o.useState)(e);return{open:t,openDropdown:()=>n(!0),closeDropdown:()=>n(!1)}}},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-create-option.ts":function(e,t,n){n.r(t),n.d(t,{useCreateOption:function(){return useCreateOption}});var o=n("react");function useCreateOption(e){const{onCreate:t,validate:n,setInputValue:r,setError:s,closeDropdown:a}=e,[i,l]=(0,o.useState)(!1);if(!t)return{createOption:null,loading:!1};return{createOption:async e=>{if(l(!0),n){const{isValid:t,errorMessage:o}=n(e,"create");if(!t)return s(o),void l(!1)}try{r(""),a(),await t(e)}catch{}finally{l(!1)}},loading:i}}},"./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/use-filter-options.ts":function(e,t,n){n.r(t),n.d(t,{useFilterOptions:function(){return useFilterOptions}});var o=n("@elementor/ui");function useFilterOptions(e){const{options:t,selected:n,onCreate:r,entityName:s}=e,a=(0,o.createFilterOptions)();return(e,o)=>{const i=n.map(e=>e.value),l=a(e.filter(e=>!i.includes(e.value)),o),c=t.some(e=>o.inputValue===e.label);return Boolean(r)&&""!==o.inputValue&&!i.includes(o.inputValue)&&!c&&l.unshift({label:`Create "${o.inputValue}"`,value:o.inputValue,_group:`Create a new ${s?.singular??"option"}`,key:`create-${o.inputValue}`,_action:"create"}),l}}},"./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-context.tsx":function(e,t,n){n.r(t),n.d(t,{CssClassProvider:function(){return CssClassProvider},useCssClass:function(){return useCssClass}});var o=n("react");const r=(0,o.createContext)(null),useCssClass=()=>{const e=(0,o.useContext)(r);if(!e)throw new Error("useCssClass must be used within a CssClassProvider");return e};function CssClassProvider({children:e,...t}){return o.createElement(r.Provider,{value:t},e)}},"./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-item.tsx":function(e,t,n){n.r(t),n.d(t,{CssClassItem:function(){return CssClassItem}});var o=n("react"),r=n("@elementor/editor-styles-repository"),s=n("@elementor/editor-ui"),a=n("@elementor/icons"),i=n("@elementor/ui"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-context.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-menu.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const u="tiny";function CssClassItem(e){const{chipProps:t,icon:n,color:g,fixed:m,...y}=e,{id:f,provider:k,label:x,isActive:v,onClickActive:h,renameLabel:b,setError:E}=y,{meta:S,setMetaState:_}=(0,c.useStyle)(),C=(0,i.usePopupState)({variant:"popover"}),[w,P]=(0,o.useState)(null),{onDelete:T,...I}=t,{userCan:F}=(0,r.useUserStylesCapability)(),{ref:D,isEditing:z,openEditMode:L,error:R,getProps:O}=(0,s.useEditable)({value:x,onSubmit:b,validation:validateLabel,onError:E}),A=R?"error":g,B=k?r.stylesRepository.getProviderByKey(k)?.actions:null,V=Boolean(B?.update)&&F(k??"")?.update,N=v&&S.state;return o.createElement(i.ThemeProvider,{palette:"default"},o.createElement(i.UnstableChipGroup,_extends({ref:P},I,{"aria-label":`Edit ${x}`,role:"group",sx:e=>({"&.MuiChipGroup-root.MuiAutocomplete-tag":{margin:e.spacing(.125)}})}),o.createElement(i.Chip,{size:u,label:z?o.createElement(s.EditableField,_extends({ref:D},O())):o.createElement(s.EllipsisWithTooltip,{maxWidth:"10ch",title:x,as:"div"}),variant:!v||S.state||z?"standard":"filled",shape:"rounded",icon:n,color:A,onClick:()=>{N?_(null):V&&v?L():h(f)},"aria-pressed":v,sx:e=>({lineHeight:1,cursor:v&&V&&!N?"text":"pointer",borderRadius:.75*e.shape.borderRadius+"px","&.Mui-focusVisible":{boxShadow:"none !important"}})}),!z&&o.createElement(i.Chip,_extends({icon:N?void 0:o.createElement(a.DotsVerticalIcon,{fontSize:"tiny"}),size:u,label:N?o.createElement(i.Stack,{direction:"row",gap:.5,alignItems:"center"},o.createElement(i.Typography,{variant:"inherit"},S.state),o.createElement(a.DotsVerticalIcon,{fontSize:"tiny"})):void 0,variant:"filled",shape:"rounded",color:A},(0,i.bindTrigger)(C),{"aria-label":(0,l.__)("Open CSS Class Menu","elementor"),sx:e=>({borderRadius:.75*e.shape.borderRadius+"px",paddingRight:0,...N?{}:{paddingLeft:0},".MuiChip-label":N?{paddingRight:0}:{padding:0}})}))),o.createElement(p.CssClassProvider,_extends({},y,{handleRename:L}),o.createElement(d.CssClassMenu,{popupState:C,anchorEl:w,fixed:m})))}const validateLabel=e=>{const t=(0,r.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage}},"./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-menu.tsx":function(e,t,n){n.r(t),n.d(t,{CssClassMenu:function(){return CssClassMenu}});var o=n("react"),r=n("@elementor/editor-styles-repository"),s=n("@elementor/editor-ui"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/utils/get-styles-provider-color.ts"),p=n("./packages/packages/core/editor-editing-panel/src/components/style-indicator.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-context.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/use-apply-and-unapply-class.ts");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const g=[{key:"normal",value:null},{key:"hover",value:"hover"},{key:"focus",value:"focus"},{key:"active",value:"active"}];function CssClassMenu({popupState:e,anchorEl:t,fixed:n}){const{provider:s}=(0,d.useCssClass)();return o.createElement(a.Menu,_extends({MenuListProps:{dense:!0,sx:{minWidth:"160px"}}},(0,a.bindMenu)(e),{anchorEl:t,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{horizontal:"left",vertical:-4},onKeyDown:e=>{e.stopPropagation()},disableAutoFocusItem:!0}),function getMenuItemsByProvider({provider:e,closeMenu:t,fixed:n}){if(!e)return[];const s=r.stylesRepository.getProviderByKey(e),i=s?.actions,l=i?.update,c=!n,p=[l&&o.createElement(RenameClassMenuItem,{key:"rename-class",closeMenu:t}),c&&o.createElement(UnapplyClassMenuItem,{key:"unapply-class",closeMenu:t})].filter(Boolean);p.length&&(p.unshift(o.createElement(a.MenuSubheader,{key:"provider-label",sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1,textTransform:"capitalize"}},s?.labels?.singular)),p.push(o.createElement(a.Divider,{key:"provider-actions-divider"})));return p}({provider:s,closeMenu:e.close,fixed:n}),o.createElement(a.MenuSubheader,{sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1}},(0,i.__)("States","elementor")),g.map(t=>o.createElement(StateMenuItem,{key:t.key,state:t.value,closeMenu:e.close})))}function StateMenuItem({state:e,closeMenu:t,...n}){const{id:u,provider:g}=(0,d.useCssClass)(),{id:m,setId:y,setMetaState:f,meta:k}=(0,l.useStyle)(),{state:x}=k,{userCan:v}=(0,r.useUserStylesCapability)(),h=function useModifiedStates(e){const{meta:t}=(0,l.useStyle)(),n=r.stylesRepository.all().find(t=>t.id===e);return Object.fromEntries(n?.variants.filter(e=>t.breakpoint===e.meta.breakpoint).map(e=>[e.meta.state??"normal",!0])??[])}(u),b=!e||v(g??"").updateProps,E=h[e??"normal"]??!1,S=!b&&!E,_=u===m,C=e===x&&_;return o.createElement(s.MenuListItem,_extends({},n,{selected:C,disabled:S,sx:{textTransform:"capitalize"},onClick:()=>{_||y(u),f(e),t()}}),o.createElement(s.MenuItemInfotip,{showInfoTip:S,content:(0,i.__)("With your current role, you can only use existing states.","elementor")},o.createElement(a.Stack,{gap:.75,direction:"row",alignItems:"center"},E&&o.createElement(p.StyleIndicator,{"aria-label":(0,i.__)("Has style","elementor"),getColor:(0,c.getTempStylesProviderThemeColor)(g??"")}),e??"normal")))}function UnapplyClassMenuItem({closeMenu:e,...t}){const{id:n,label:r}=(0,d.useCssClass)(),a=(0,u.useUnapplyClass)();return n?o.createElement(s.MenuListItem,_extends({},t,{onClick:()=>{a({classId:n,classLabel:r}),e()}}),(0,i.__)("Remove","elementor")):null}function RenameClassMenuItem({closeMenu:e}){const{handleRename:t,provider:n}=(0,d.useCssClass)(),{userCan:a}=(0,r.useUserStylesCapability)();if(!n)return null;const l=a(n).update;return o.createElement(s.MenuListItem,{disabled:!l,onClick:()=>{e(),t()}},o.createElement(s.MenuItemInfotip,{showInfoTip:!l,content:(0,i.__)("With your current role, you can use existing classes but can’t modify them.","elementor")},(0,i.__)("Rename","elementor")))}},"./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-selector.tsx":function(e,t,n){n.r(t),n.d(t,{ClassSelectorActionsSlot:function(){return b},CssClassSelector:function(){return CssClassSelector},injectIntoClassSelectorActions:function(){return E}});var o=n("react"),r=n("@elementor/editor-elements"),s=n("@elementor/editor-styles-repository"),a=n("@elementor/editor-ui"),i=n("@elementor/icons"),l=n("@elementor/locations"),c=n("@elementor/ui"),p=n("@wordpress/i18n"),d=n("./packages/packages/core/editor-editing-panel/src/contexts/classes-prop-context.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/utils/get-styles-provider-color.ts"),y=n("./packages/packages/core/editor-editing-panel/src/components/creatable-autocomplete/index.ts"),f=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-item.tsx"),k=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/use-apply-and-unapply-class.ts");const x="elementor-css-class-selector",v=50,h={label:(0,p.__)("local","elementor"),value:null,fixed:!0,color:getTempStylesProviderColorName("accent"),icon:o.createElement(i.MapPinIcon,null),provider:null},{Slot:b,inject:E}=(0,l.createLocation)();function CssClassSelector(){const e=function useOptions(){const{element:e}=(0,u.useElement)(),isProviderEditable=e=>!!e.actions.updateProps;return(0,s.useProviders)().filter(isProviderEditable).flatMap(t=>{const n=(0,s.isElementsStylesProvider)(t.getKey()),r=t.actions.all({elementId:e.id});return n&&0===r.length?[h]:r.map(e=>({label:e.label,value:e.id,fixed:n,color:getTempStylesProviderColorName((0,m.getStylesProviderColorName)(t.getKey())),icon:n?o.createElement(i.MapPinIcon,null):null,provider:t.getKey()}))})}(),{id:t,setId:n}=(0,g.useStyle)(),l=(0,o.useRef)(null),[E,S]=(0,o.useState)(null),_=function useHandleSelect(){const e=(0,k.useApplyClass)(),t=(0,k.useUnapplyClass)();return(n,o,r)=>{if(r.value)switch(o){case"selectOption":e({classId:r.value,classLabel:r.label});break;case"removeOption":t({classId:r.value,classLabel:r.label})}}}(),{create:C,validate:w,entityName:P}=function useCreateAction(){const[e,t]=(0,k.useCreateAndApplyClass)();if(!e||!t)return{};const create=e=>{t({classLabel:e})},validate=(t,n)=>function hasReachedLimit(e){return e.actions.all().length>=e.limit}(e)?{isValid:!1,errorMessage:(0,p.__)("You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.","elementor")}:(0,s.validateStyleLabel)(t,n),n=e.labels.singular&&e.labels.plural?e.labels:void 0;return{create:create,validate:validate,entityName:n}}(),T=function useAppliedOptions(e){const{element:t}=(0,u.useElement)(),n=(0,d.useClassesProp)(),o=(0,r.useElementSetting)(t.id,n)?.value||[],a=e.filter(e=>e.value&&o.includes(e.value)),i=a.some(e=>e.provider&&(0,s.isElementsStylesProvider)(e.provider));i||a.unshift(h);return a}(e),I=T.find(e=>e.value===t)??h,F=T.every(({fixed:e})=>e),{userCan:D}=(0,s.useUserStylesCapability)(),z=!I.provider||D(I.provider).updateProps;return o.createElement(c.Stack,{p:2},o.createElement(c.Stack,{direction:"row",gap:1,alignItems:"center",justifyContent:"space-between"},o.createElement(c.FormLabel,{htmlFor:x,size:"small"},(0,p.__)("Classes","elementor")),o.createElement(c.Stack,{direction:"row",gap:1},o.createElement(b,null))),o.createElement(a.WarningInfotip,{open:Boolean(E),text:E??"",placement:"bottom",width:l.current?.getBoundingClientRect().width,offset:[0,-15]},o.createElement(y.CreatableAutocomplete,{id:x,ref:l,size:"tiny",placeholder:F?(0,p.__)("Type class name","elementor"):void 0,options:e,selected:T,entityName:P,onSelect:_,onCreate:C??void 0,validate:w??void 0,limitTags:v,renderEmptyState:EmptyState,getLimitTagsText:e=>o.createElement(c.Chip,{size:"tiny",variant:"standard",label:`+${e}`,clickable:!0}),renderTags:(e,t)=>e.map((e,r)=>{const s=t({index:r}),a=e.value===I?.value;return o.createElement(f.CssClassItem,{key:s.key,fixed:e.fixed,label:e.label,provider:e.provider,id:e.value,isActive:a,color:a&&e.color?e.color:"default",icon:e.icon,chipProps:s,onClickActive:()=>n(e.value),renameLabel:t=>{if(!e.value)throw new Error("Cannot rename a class without style id");return updateClassByProvider(e.provider,{label:t,id:e.value})},setError:S})})})),!z&&o.createElement(a.InfoAlert,{sx:{mt:1}},(0,p.__)("With your current role, you can use existing classes but can’t modify them.","elementor")))}const EmptyState=({searchValue:e,onClear:t})=>o.createElement(c.Box,{sx:{py:4}},o.createElement(c.Stack,{gap:1,alignItems:"center",color:"text.secondary",justifyContent:"center",sx:{px:2,m:"auto",maxWidth:"236px"}},o.createElement(i.ColorSwatchIcon,{sx:{transform:"rotate(90deg)"},fontSize:"large"}),o.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,p.__)("Sorry, nothing matched","elementor"),o.createElement("br",null),"“",e,"”."),o.createElement(c.Typography,{align:"center",variant:"caption",sx:{mb:2}},(0,p.__)("With your current role,","elementor"),o.createElement("br",null),(0,p.__)("you can only use existing classes.","elementor")),o.createElement(c.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,p.__)("Clear & try again","elementor")))),updateClassByProvider=(e,t)=>{if(!e)return;const n=s.stylesRepository.getProviderByKey(e);return n?n.actions.update?.(t):void 0};function getTempStylesProviderColorName(e){return"accent"===e?"primary":e}},"./packages/packages/core/editor-editing-panel/src/components/css-classes/use-apply-and-unapply-class.ts":function(e,t,n){n.r(t),n.d(t,{useApplyClass:function(){return useApplyClass},useCreateAndApplyClass:function(){return useCreateAndApplyClass},useUnapplyClass:function(){return useUnapplyClass}});var o=n("react"),r=n("@elementor/editor-documents"),s=n("@elementor/editor-elements"),a=n("@elementor/editor-props"),i=n("@elementor/editor-styles-repository"),l=n("@elementor/editor-v1-adapters"),c=n("@wordpress/i18n"),p=n("./packages/packages/core/editor-editing-panel/src/contexts/classes-prop-context.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx");function useApplyClass(){const{id:e,setId:t}=(0,u.useStyle)(),{element:n}=(0,d.useElement)(),r=useApply(),a=useUnapply();return(0,o.useMemo)(()=>(0,l.undoable)({do:({classId:t})=>{const n=e;return r(t),n},undo:({classId:e},n)=>{a(e),t(n)}},{title:(0,s.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,c.__)("class %s applied","elementor").replace("%s",e)}),[e,r,n.id,a,t])}function useUnapplyClass(){const{id:e,setId:t}=(0,u.useStyle)(),{element:n}=(0,d.useElement)(),r=useApply(),a=useUnapply();return(0,o.useMemo)(()=>(0,l.undoable)({do:({classId:t})=>{const n=e;return a(t),n},undo:({classId:e},n)=>{r(e),t(n)}},{title:(0,s.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,c.__)("class %s removed","elementor").replace("%s",e)}),[e,r,n.id,a,t])}function useCreateAndApplyClass(){const{id:e,setId:t}=(0,u.useStyle)(),[n,r]=(0,i.useGetStylesRepositoryCreateAction)()??[null,null],s=n?.actions.delete,a=useApply(),p=useUnapply(),d=(0,o.useMemo)(()=>{if(n&&r)return(0,l.undoable)({do:({classLabel:t})=>{const n=e,o=r(t);return a(o),{prevActiveId:n,createdId:o}},undo:(e,{prevActiveId:n,createdId:o})=>{p(o),s?.(o),t(n)}},{title:(0,c.__)("Class","elementor"),subtitle:({classLabel:e})=>(0,c.__)("%s created","elementor").replace("%s",e)})},[e,a,r,s,n,t,p]);return n&&d?[n,d]:[null,null]}function useApply(){const{element:e}=(0,d.useElement)(),{setId:t}=(0,u.useStyle)(),{setClasses:n,getAppliedClasses:r}=useClasses();return(0,o.useCallback)(o=>{const s=r();if(s.includes(o))throw new Error(`Class ${o} is already applied to element ${e.id}, cannot re-apply.`);const a=[...s,o];n(a),t(o)},[e.id,r,t,n])}function useUnapply(){const{element:e}=(0,d.useElement)(),{id:t,setId:n}=(0,u.useStyle)(),{setClasses:r,getAppliedClasses:s}=useClasses();return(0,o.useCallback)(o=>{const a=s();if(!a.includes(o))throw new Error(`Class ${o} is not applied to element ${e.id}, cannot unapply it.`);const i=a.filter(e=>e!==o);r(i),t===o&&n(i[0]??null)},[t,e.id,s,n,r])}function useClasses(){const{element:e}=(0,d.useElement)(),t=(0,p.useClassesProp)();return(0,o.useMemo)(()=>({setClasses:n=>{(0,s.updateElementSettings)({id:e.id,props:{[t]:a.classesPropTypeUtil.create(n)},withHistory:!1}),(0,r.setDocumentModifiedStatus)(!0)},getAppliedClasses:()=>(0,s.getElementSetting)(e.id,t)?.value||[]}),[t,e.id])}},"./packages/packages/core/editor-editing-panel/src/components/editing-panel-error-fallback.tsx":function(e,t,n){n.r(t),n.d(t,{EditorPanelErrorFallback:function(){return EditorPanelErrorFallback}});var o=n("react"),r=n("@elementor/ui");function EditorPanelErrorFallback(){return o.createElement(r.Box,{role:"alert",sx:{minHeight:"100%",p:2}},o.createElement(r.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},o.createElement("strong",null,"Something went wrong")))}},"./packages/packages/core/editor-editing-panel/src/components/editing-panel-hooks.tsx":function(e,t,n){n.r(t),n.d(t,{EditingPanelHooks:function(){return EditingPanelHooks}});var o=n("./packages/packages/core/editor-editing-panel/src/hooks/use-open-editor-panel.ts");const EditingPanelHooks=()=>((0,o.useOpenEditorPanel)(),null)},"./packages/packages/core/editor-editing-panel/src/components/editing-panel-tabs.tsx":function(e,t,n){n.r(t),n.d(t,{EditingPanelTabs:function(){return EditingPanelTabs}});var o=n("react"),r=n("@elementor/ui"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/contexts/scroll-context.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/hooks/use-default-panel-settings.ts"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-state-by-element.ts"),p=n("./packages/packages/core/editor-editing-panel/src/components/settings-tab.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-tab.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const EditingPanelTabs=()=>{const{element:e}=(0,a.useElement)();return o.createElement(o.Fragment,{key:e.id},o.createElement(PanelTabContent,null))},PanelTabContent=()=>{const e=(0,l.useDefaultPanelSettings)().defaultTab,[t,n]=(0,c.useStateByElement)("tab",e),{getTabProps:a,getTabPanelProps:u,getTabsProps:g}=(0,r.useTabs)(t);return o.createElement(i.ScrollProvider,null,o.createElement(r.Stack,{direction:"column",sx:{width:"100%"}},o.createElement(r.Stack,{sx:{...d.stickyHeaderStyles,top:0}},o.createElement(r.Tabs,_extends({variant:"fullWidth",size:"small",sx:{mt:.5}},g(),{onChange:(e,t)=>{g().onChange(e,t),n(t)}}),o.createElement(r.Tab,_extends({label:(0,s.__)("General","elementor")},a("settings"))),o.createElement(r.Tab,_extends({label:(0,s.__)("Style","elementor")},a("style")))),o.createElement(r.Divider,null)),o.createElement(r.TabPanel,_extends({},u("settings"),{disablePadding:!0}),o.createElement(p.SettingsTab,null)),o.createElement(r.TabPanel,_extends({},u("style"),{disablePadding:!0}),o.createElement(d.StyleTab,null))))}},"./packages/packages/core/editor-editing-panel/src/components/editing-panel.tsx":function(e,t,n){n.r(t),n.d(t,{EditingPanel:function(){return EditingPanel}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-elements"),a=n("@elementor/editor-panels"),i=n("@elementor/editor-ui"),l=n("@elementor/icons"),c=n("@elementor/session"),p=n("@elementor/ui"),d=n("@wordpress/i18n"),u=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/control-replacement.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/controls-actions.ts"),y=n("./packages/packages/core/editor-editing-panel/src/components/editing-panel-error-fallback.tsx"),f=n("./packages/packages/core/editor-editing-panel/src/components/editing-panel-tabs.tsx");const{useMenuItems:k}=m.controlActionsMenu,EditingPanel=()=>{const{element:e,elementType:t}=(0,s.useSelectedElement)(),n=(0,g.getControlReplacements)(),m=k().default;if(!e||!t)return null;const x=(0,d.__)("Edit %s","elementor").replace("%s",t.title);return o.createElement(p.ErrorBoundary,{fallback:o.createElement(y.EditorPanelErrorFallback,null)},o.createElement(c.SessionStorageProvider,{prefix:"elementor"},o.createElement(i.ThemeProvider,null,o.createElement(a.Panel,null,o.createElement(a.PanelHeader,null,o.createElement(a.PanelHeaderTitle,null,x),o.createElement(l.AtomIcon,{fontSize:"small",sx:{color:"text.tertiary"}})),o.createElement(a.PanelBody,null,o.createElement(r.ControlActionsProvider,{items:m},o.createElement(r.ControlReplacementsProvider,{replacements:n},o.createElement(u.ElementProvider,{element:e,elementType:t},o.createElement(f.EditingPanelTabs,null)))))))))}},"./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx":function(e,t,n){n.r(t),n.d(t,{PanelDivider:function(){return PanelDivider}});var o=n("react"),r=n("@elementor/ui");const PanelDivider=()=>o.createElement(r.Divider,{sx:{my:.5}})},"./packages/packages/core/editor-editing-panel/src/components/popover-body.tsx":function(e,t,n){n.r(t),n.d(t,{PopoverBody:function(){return PopoverBody}});var o=n("react"),r=n("@elementor/editor-ui"),s=n("./packages/packages/core/editor-editing-panel/src/contexts/section-context.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const PopoverBody=e=>{const t=(0,s.useSectionWidth)();return o.createElement(r.PopoverBody,_extends({},e,{width:t}))}},"./packages/packages/core/editor-editing-panel/src/components/section-content.tsx":function(e,t,n){n.r(t),n.d(t,{SectionContent:function(){return SectionContent}});var o=n("react"),r=n("@elementor/ui");const SectionContent=({gap:e=2,sx:t,children:n})=>o.createElement(r.Stack,{gap:e,sx:{...t}},n)},"./packages/packages/core/editor-editing-panel/src/components/section.tsx":function(e,t,n){n.r(t),n.d(t,{Section:function(){return Section}});var o=n("react"),r=n("@elementor/ui"),s=n("./packages/packages/core/editor-editing-panel/src/contexts/section-context.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/hooks/use-state-by-element.ts"),i=n("./packages/packages/core/editor-editing-panel/src/components/collapse-icon.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/collapsible-content.tsx");function Section({title:e,children:t,defaultExpanded:n=!1,titleEnd:c}){const[p,d]=(0,a.useStateByElement)(e,!!n),u=(0,o.useRef)(null),g=(0,o.useId)(),m=`label-${g}`,y=`content-${g}`;return o.createElement(o.Fragment,null,o.createElement(r.ListItemButton,{id:m,"aria-controls":y,onClick:()=>{d(!p)},sx:{"&:hover":{backgroundColor:"transparent"}}},o.createElement(r.Stack,{direction:"row",alignItems:"center",justifyItems:"start",flexGrow:1,gap:.5},o.createElement(r.ListItemText,{secondary:e,secondaryTypographyProps:{color:"text.primary",variant:"caption",fontWeight:"bold"},sx:{flexGrow:0,flexShrink:1,marginInlineEnd:1}}),(0,l.getCollapsibleValue)(c,p)),o.createElement(i.CollapseIcon,{open:p,color:"secondary",fontSize:"tiny"})),o.createElement(r.Collapse,{id:y,"aria-labelledby":m,in:p,timeout:"auto",unmountOnExit:!0},o.createElement(s.SectionRefContext.Provider,{value:u},o.createElement(r.Stack,{ref:u,gap:2.5,p:2},t))),o.createElement(r.Divider,null))}},"./packages/packages/core/editor-editing-panel/src/components/sections-list.tsx":function(e,t,n){n.r(t),n.d(t,{SectionsList:function(){return SectionsList}});var o=n("react"),r=n("@elementor/ui");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}function SectionsList(e){return o.createElement(r.List,_extends({disablePadding:!0,component:"div"},e))}},"./packages/packages/core/editor-editing-panel/src/components/settings-tab.tsx":function(e,t,n){n.r(t),n.d(t,{SettingsTab:function(){return SettingsTab}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/session"),a=n("@elementor/ui"),i=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/control.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/controls-registry/control-type-container.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/controls-registry/controls-registry.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/controls-registry/settings-field.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/hooks/use-default-panel-settings.ts"),g=n("./packages/packages/core/editor-editing-panel/src/components/section.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/components/sections-list.tsx");const SettingsTab=()=>{const{elementType:e,element:t}=(0,i.useElement)(),n=(0,u.useDefaultPanelSettings)();return o.createElement(s.SessionStorageProvider,{prefix:t.id},o.createElement(m.SectionsList,null,e.controls.map(({type:e,value:t},r)=>{return"control"===e?o.createElement(Control,{key:t.bind,control:t}):"section"===e?o.createElement(g.Section,{title:t.label,key:e+"."+r,defaultExpanded:(s=t.label,n.defaultSectionsExpanded.settings?.includes(s))},t.items?.map(e=>"control"===e.type?o.createElement(Control,{key:e.value.bind,control:e.value}):null)):null;var s})))},Control=({control:e})=>{if(!(0,p.getControl)(e.type))return null;const t=e.meta?.layout||(0,p.getDefaultLayout)(e.type),n=function populateChildControlProps(e){if(e.childControlType){const t=(0,p.getControl)(e.childControlType),n=(0,p.getPropTypeUtil)(e.childControlType);e={...e,childControlConfig:{component:t,props:e.childControlProps||{},propTypeUtil:n}}}return e}(e.props);return"custom"===t&&(n.label=e.label),o.createElement(d.SettingsField,{bind:e.bind,propDisplayName:e.label||e.bind},e.meta?.topDivider&&o.createElement(a.Divider,null),o.createElement(c.ControlTypeContainer,{layout:t},e.label&&"custom"!==t?o.createElement(r.ControlFormLabel,null,e.label):null,o.createElement(l.Control,{type:e.type,props:n})))}},"./packages/packages/core/editor-editing-panel/src/components/style-indicator.tsx":function(e,t,n){n.r(t),n.d(t,{StyleIndicator:function(){return r}});var o=n("@elementor/ui");const r=(0,o.styled)("div",{shouldForwardProp:e=>!["isOverridden","getColor"].includes(e)})`
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: ${({theme:e,isOverridden:t,getColor:n})=>{if(t)return e.palette.warning.light;const o=n?.(e);return o??e.palette.text.disabled}};
`},"./packages/packages/core/editor-editing-panel/src/components/style-sections/background-section/background-section.tsx":function(e,t,n){n.r(t),n.d(t,{BackgroundSection:function(){return BackgroundSection}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx");const l=(0,s.__)("Background","elementor"),BackgroundSection=()=>o.createElement(i.SectionContent,null,o.createElement(a.StylesField,{bind:"background",propDisplayName:l},o.createElement(r.BackgroundControl,null)))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-color-field.tsx":function(e,t,n){n.r(t),n.d(t,{BorderColorField:function(){return BorderColorField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Border color","elementor"),BorderColorField=()=>o.createElement(a.StylesField,{bind:"border-color",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.ColorControl,null)))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-field.tsx":function(e,t,n){n.r(t),n.d(t,{BorderField:function(){return BorderField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-fields.ts"),i=n("./packages/packages/core/editor-editing-panel/src/components/add-or-remove-content.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-color-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-style-field.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-width-field.tsx");const d=(0,s.__)("Border","elementor"),u={"border-width":{$$type:"size",value:{size:1,unit:"px"}},"border-color":{$$type:"color",value:"#000000"},"border-style":{$$type:"string",value:"solid"}},BorderField=()=>{const{values:e,setValues:t,canEdit:n}=(0,a.useStylesFields)(Object.keys(u)),s={history:{propDisplayName:d}},g=Object.values(e??{}).some(Boolean);return o.createElement(i.AddOrRemoveContent,{isAdded:g,onAdd:()=>{t(u,s)},onRemove:()=>{t({"border-width":null,"border-color":null,"border-style":null},s)},disabled:!n,renderLabel:()=>o.createElement(r.ControlFormLabel,null,d)},o.createElement(p.BorderWidthField,null),o.createElement(l.BorderColorField,null),o.createElement(c.BorderStyleField,null))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-radius-field.tsx":function(e,t,n){n.r(t),n.d(t,{BorderRadiusField:function(){return BorderRadiusField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-props"),a=n("@elementor/icons"),i=n("@elementor/ui"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts"),d=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx");const u=(0,l.__)("Border radius","elementor"),g=(0,i.withDirection)(a.RadiusTopLeftIcon),m=(0,i.withDirection)(a.RadiusTopRightIcon),y=(0,i.withDirection)(a.RadiusBottomLeftIcon),f=(0,i.withDirection)(a.RadiusBottomRightIcon),getStartStartLabel=e=>e?(0,l.__)("Top right","elementor"):(0,l.__)("Top left","elementor"),getStartEndLabel=e=>e?(0,l.__)("Top left","elementor"):(0,l.__)("Top right","elementor"),getEndStartLabel=e=>e?(0,l.__)("Bottom right","elementor"):(0,l.__)("Bottom left","elementor"),getEndEndLabel=e=>e?(0,l.__)("Bottom left","elementor"):(0,l.__)("Bottom right","elementor"),getCorners=e=>[{label:getStartStartLabel(e),icon:o.createElement(g,{fontSize:"tiny"}),bind:"start-start"},{label:getStartEndLabel(e),icon:o.createElement(m,{fontSize:"tiny"}),bind:"start-end"},{label:getEndStartLabel(e),icon:o.createElement(y,{fontSize:"tiny"}),bind:"end-start"},{label:getEndEndLabel(e),icon:o.createElement(f,{fontSize:"tiny"}),bind:"end-end"}],BorderRadiusField=()=>{const{isSiteRtl:e}=(0,p.useDirection)();return o.createElement(d.UiProviders,null,o.createElement(c.StylesField,{bind:"border-radius",propDisplayName:u},o.createElement(r.EqualUnequalSizesControl,{items:getCorners(e),label:u,icon:o.createElement(a.BorderCornersIcon,{fontSize:"tiny"}),tooltipLabel:(0,l.__)("Adjust corners","elementor"),multiSizePropTypeUtil:s.borderRadiusPropTypeUtil})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-section.tsx":function(e,t,n){n.r(t),n.d(t,{BorderSection:function(){return BorderSection}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-radius-field.tsx");const BorderSection=()=>o.createElement(s.SectionContent,null,o.createElement(i.BorderRadiusField,null),o.createElement(r.PanelDivider,null),o.createElement(a.BorderField,null))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-style-field.tsx":function(e,t,n){n.r(t),n.d(t,{BorderStyleField:function(){return BorderStyleField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Border type","elementor"),c=[{value:"none",label:(0,s.__)("None","elementor")},{value:"solid",label:(0,s.__)("Solid","elementor")},{value:"dashed",label:(0,s.__)("Dashed","elementor")},{value:"dotted",label:(0,s.__)("Dotted","elementor")},{value:"double",label:(0,s.__)("Double","elementor")},{value:"groove",label:(0,s.__)("Groove","elementor")},{value:"ridge",label:(0,s.__)("Ridge","elementor")},{value:"inset",label:(0,s.__)("Inset","elementor")},{value:"outset",label:(0,s.__)("Outset","elementor")}],BorderStyleField=()=>o.createElement(a.StylesField,{bind:"border-style",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.SelectControl,{options:c})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-width-field.tsx":function(e,t,n){n.r(t),n.d(t,{BorderWidthField:function(){return BorderWidthField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-props"),a=n("@elementor/icons"),i=n("@elementor/ui"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts");const d=(0,l.__)("Border width","elementor"),u=(0,i.withDirection)(a.SideRightIcon),g=(0,i.withDirection)(a.SideLeftIcon),getEdges=e=>[{label:(0,l.__)("Top","elementor"),icon:o.createElement(a.SideTopIcon,{fontSize:"tiny"}),bind:"block-start"},{label:e?(0,l.__)("Left","elementor"):(0,l.__)("Right","elementor"),icon:o.createElement(u,{fontSize:"tiny"}),bind:"inline-end"},{label:(0,l.__)("Bottom","elementor"),icon:o.createElement(a.SideBottomIcon,{fontSize:"tiny"}),bind:"block-end"},{label:e?(0,l.__)("Right","elementor"):(0,l.__)("Left","elementor"),icon:o.createElement(g,{fontSize:"tiny"}),bind:"inline-start"}],BorderWidthField=()=>{const{isSiteRtl:e}=(0,p.useDirection)();return o.createElement(c.StylesField,{bind:"border-width",propDisplayName:d},o.createElement(r.EqualUnequalSizesControl,{items:getEdges(e),label:d,icon:o.createElement(a.SideAllIcon,{fontSize:"tiny"}),tooltipLabel:(0,l.__)("Adjust borders","elementor"),multiSizePropTypeUtil:s.borderWidthPropTypeUtil}))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/effects-section/effects-section.tsx":function(e,t,n){n.r(t),n.d(t,{EffectsSection:function(){return EffectsSection}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/effects-section/opacity-control-field.tsx");const p=(0,s.__)("Box shadow","elementor"),d=(0,s.__)("Filters","elementor"),u=(0,s.__)("Backdrop filters","elementor"),EffectsSection=()=>o.createElement(l.SectionContent,null,o.createElement(c.OpacityControlField,null),o.createElement(i.PanelDivider,null),o.createElement(a.StylesField,{bind:"box-shadow",propDisplayName:p},o.createElement(r.BoxShadowRepeaterControl,null)),o.createElement(i.PanelDivider,null),o.createElement(a.StylesField,{bind:"filter",propDisplayName:d},o.createElement(r.FilterRepeaterControl,null)),o.createElement(i.PanelDivider,null),o.createElement(a.StylesField,{bind:"backdrop-filter",propDisplayName:u},o.createElement(r.FilterRepeaterControl,{filterPropName:"backdrop-filter"})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/effects-section/opacity-control-field.tsx":function(e,t,n){n.r(t),n.d(t,{OpacityControlField:function(){return OpacityControlField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Opacity","elementor"),OpacityControlField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"opacity",propDisplayName:l},o.createElement(i.StylesFieldLayout,{ref:e,label:l},o.createElement(r.SizeControl,{units:["%"],anchorRef:e,defaultUnit:"%"})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/align-content-field.tsx":function(e,t,n){n.r(t),n.d(t,{AlignContentField:function(){return AlignContentField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/utils/rotated-icon.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const u=(0,i.__)("Align content","elementor"),g=(0,a.withDirection)(s.JustifyTopIcon),m=(0,a.withDirection)(s.JustifyBottomIcon),y={isClockwise:!1,offset:0,disableRotationForReversed:!0},f=[{value:"start",label:(0,i.__)("Start","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:g,size:e},y)),showTooltip:!0},{value:"center",label:(0,i.__)("Center","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifyCenterIcon,size:e},y)),showTooltip:!0},{value:"end",label:(0,i.__)("End","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:m,size:e},y)),showTooltip:!0},{value:"space-between",label:(0,i.__)("Space between","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifySpaceBetweenVerticalIcon,size:e},y)),showTooltip:!0},{value:"space-around",label:(0,i.__)("Space around","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifySpaceAroundVerticalIcon,size:e},y)),showTooltip:!0},{value:"space-evenly",label:(0,i.__)("Space evenly","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifyDistributeVerticalIcon,size:e},y)),showTooltip:!0}],AlignContentField=()=>o.createElement(l.StylesField,{bind:"align-content",propDisplayName:u},o.createElement(c.UiProviders,null,o.createElement(p.StylesFieldLayout,{label:u,direction:"column"},o.createElement(r.ToggleControl,{options:f,fullWidth:!0}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/align-items-field.tsx":function(e,t,n){n.r(t),n.d(t,{AlignItemsField:function(){return AlignItemsField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/utils/rotated-icon.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const u=(0,i.__)("Align items","elementor"),g=(0,a.withDirection)(s.LayoutAlignLeftIcon),m=(0,a.withDirection)(s.LayoutAlignRightIcon),y={isClockwise:!1,offset:90},f=[{value:"start",label:(0,i.__)("Start","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:g,size:e},y)),showTooltip:!0},{value:"center",label:(0,i.__)("Center","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.LayoutAlignCenterIcon,size:e},y)),showTooltip:!0},{value:"end",label:(0,i.__)("End","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:m,size:e},y)),showTooltip:!0},{value:"stretch",label:(0,i.__)("Stretch","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.LayoutDistributeVerticalIcon,size:e},y)),showTooltip:!0}],AlignItemsField=()=>o.createElement(c.UiProviders,null,o.createElement(l.StylesField,{bind:"align-items",propDisplayName:u},o.createElement(p.StylesFieldLayout,{label:u},o.createElement(r.ToggleControl,{options:f}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/align-self-child-field.tsx":function(e,t,n){n.r(t),n.d(t,{AlignSelfChild:function(){return AlignSelfChild}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/utils/rotated-icon.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const u=(0,i.__)("Align self","elementor"),g={row:90,"row-reverse":90,column:0,"column-reverse":0},m=(0,a.withDirection)(s.LayoutAlignLeftIcon),y=(0,a.withDirection)(s.LayoutAlignRightIcon),f={isClockwise:!1},getOptions=e=>[{value:"start",label:(0,i.__)("Start","elementor"),renderContent:({size:t})=>o.createElement(d.RotatedIcon,_extends({icon:m,size:t,offset:g[e]},f)),showTooltip:!0},{value:"center",label:(0,i.__)("Center","elementor"),renderContent:({size:t})=>o.createElement(d.RotatedIcon,_extends({icon:s.LayoutAlignCenterIcon,size:t,offset:g[e]},f)),showTooltip:!0},{value:"end",label:(0,i.__)("End","elementor"),renderContent:({size:t})=>o.createElement(d.RotatedIcon,_extends({icon:y,size:t,offset:g[e]},f)),showTooltip:!0},{value:"stretch",label:(0,i.__)("Stretch","elementor"),renderContent:({size:t})=>o.createElement(d.RotatedIcon,_extends({icon:s.LayoutDistributeVerticalIcon,size:t,offset:g[e]},f)),showTooltip:!0}],AlignSelfChild=({parentStyleDirection:e})=>o.createElement(l.StylesField,{bind:"align-self",propDisplayName:u},o.createElement(c.UiProviders,null,o.createElement(p.StylesFieldLayout,{label:u},o.createElement(r.ToggleControl,{options:getOptions(e)}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/display-field.tsx":function(e,t,n){n.r(t),n.d(t,{DisplayField:function(){return DisplayField},useDisplayPlaceholderValue:function(){return useDisplayPlaceholderValue}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/contexts/styles-inheritance-context.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const c=(0,s.__)("Display","elementor"),p=[{value:"block",renderContent:()=>(0,s.__)("Block","elementor"),label:(0,s.__)("Block","elementor"),showTooltip:!0},{value:"flex",renderContent:()=>(0,s.__)("Flex","elementor"),label:(0,s.__)("Flex","elementor"),showTooltip:!0},{value:"inline-block",renderContent:()=>(0,s.__)("In-blk","elementor"),label:(0,s.__)("Inline-block","elementor"),showTooltip:!0},{value:"none",renderContent:()=>(0,s.__)("None","elementor"),label:(0,s.__)("None","elementor"),showTooltip:!0},{value:"inline-flex",renderContent:()=>(0,s.__)("In-flx","elementor"),label:(0,s.__)("Inline-flex","elementor"),showTooltip:!0}],DisplayField=()=>{const e=useDisplayPlaceholderValue();return o.createElement(i.StylesField,{bind:"display",propDisplayName:c,placeholder:e},o.createElement(l.StylesFieldLayout,{label:c,direction:"column"},o.createElement(r.ToggleControl,{options:p,maxItems:4,fullWidth:!0})))},useDisplayPlaceholderValue=()=>(0,a.useStylesInheritanceChain)(["display"])[0]?.value??void 0},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/flex-direction-field.tsx":function(e,t,n){n.r(t),n.d(t,{FlexDirectionField:function(){return FlexDirectionField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const d=(0,i.__)("Direction","elementor"),u=[{value:"row",label:(0,i.__)("Row","elementor"),renderContent:({size:e})=>{const t=(0,a.withDirection)(s.ArrowRightIcon);return o.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column",label:(0,i.__)("Column","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:"row-reverse",label:(0,i.__)("Reversed row","elementor"),renderContent:({size:e})=>{const t=(0,a.withDirection)(s.ArrowLeftIcon);return o.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column-reverse",label:(0,i.__)("Reversed column","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0}],FlexDirectionField=()=>o.createElement(l.StylesField,{bind:"flex-direction",propDisplayName:d},o.createElement(c.UiProviders,null,o.createElement(p.StylesFieldLayout,{label:d},o.createElement(r.ToggleControl,{options:u}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/flex-order-field.tsx":function(e,t,n){n.r(t),n.d(t,{FIRST_DEFAULT_VALUE:function(){return y},FlexOrderField:function(){return FlexOrderField},LAST_DEFAULT_VALUE:function(){return f}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts"),p=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/control-label.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const m=(0,i.__)("Order","elementor"),y=-99999,f=99999,k="first",x="last",v="custom",h={[k]:y,[x]:f},b=[{value:k,label:(0,i.__)("First","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0},{value:x,label:(0,i.__)("Last","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:v,label:(0,i.__)("Custom","elementor"),renderContent:({size:e})=>o.createElement(s.PencilIcon,{fontSize:e}),showTooltip:!0}],FlexOrderField=()=>{const{value:e,setValue:t,canEdit:n}=(0,c.useStylesField)("order",{history:{propDisplayName:m}}),[s,k]=(0,o.useState)(getGroupControlValue(e?.value??null));(0,o.useEffect)(()=>{const t=getGroupControlValue(e?.value??null);k(t)},[e?.value]);return o.createElement(l.StylesField,{bind:"order",propDisplayName:m},o.createElement(p.UiProviders,null,o.createElement(u.SectionContent,null,o.createElement(g.StylesFieldLayout,{label:m},o.createElement(r.ControlToggleButtonGroup,{items:b,value:s,onChange:e=>{k(e),t(e&&e!==v?{$$type:"number",value:h[e]}:null)},exclusive:!0,disabled:!n})),v===s&&o.createElement(a.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},o.createElement(a.Grid,{item:!0,xs:6},o.createElement(d.ControlLabel,null,(0,i.__)("Custom order","elementor"))),o.createElement(a.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},o.createElement(r.NumberControl,{min:y+1,max:f-1,shouldForceInt:!0}))))))},getGroupControlValue=e=>f===e?x:y===e?k:0===e||e?v:null},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/flex-size-field.tsx":function(e,t,n){n.r(t),n.d(t,{FlexSizeField:function(){return FlexSizeField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-props"),a=n("@elementor/icons"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts"),p=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const g=(0,i.__)("Flex Size","elementor"),m=[{value:"flex-grow",label:(0,i.__)("Grow","elementor"),renderContent:({size:e})=>o.createElement(a.ExpandIcon,{fontSize:e}),showTooltip:!0},{value:"flex-shrink",label:(0,i.__)("Shrink","elementor"),renderContent:({size:e})=>o.createElement(a.ShrinkIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,i.__)("Custom","elementor"),renderContent:({size:e})=>o.createElement(a.PencilIcon,{fontSize:e}),showTooltip:!0}],FlexSizeField=()=>{const{value:e,setValue:t,canEdit:n}=(0,c.useStylesField)("flex",{history:{propDisplayName:g}}),s=e,a=s?.value?.flexGrow?.value||null,i=s?.value?.flexShrink?.value||null,y=s?.value?.flexBasis?.value||null,f=(0,o.useMemo)(()=>getActiveGroup({grow:a,shrink:i,basis:y}),[a,i,y]),[k,x]=(0,o.useState)(f),[v,h]=(0,o.useState)(!1);(0,o.useEffect)(()=>{v||x(f)},[f,v]),(0,o.useEffect)(()=>{null===e&&h(!1)},[e]);return o.createElement(p.UiProviders,null,o.createElement(d.SectionContent,null,o.createElement(l.StylesField,{bind:"flex",propDisplayName:g},o.createElement(u.StylesFieldLayout,{label:g},o.createElement(r.ControlToggleButtonGroup,{value:k,onChange:(e=null)=>{x(e),h("custom"===e);const n=createFlexValueForGroup(e,s);t(n)},disabled:!n,items:m,exclusive:!0})),"custom"===k&&o.createElement(FlexCustomField,null))))},createFlexValueForGroup=(e,t)=>e?"flex-grow"===e?s.flexPropTypeUtil.create({flexGrow:s.numberPropTypeUtil.create(1),flexShrink:null,flexBasis:null}):"flex-shrink"===e?s.flexPropTypeUtil.create({flexGrow:null,flexShrink:s.numberPropTypeUtil.create(1),flexBasis:null}):"custom"===e?t||s.flexPropTypeUtil.create({flexGrow:null,flexShrink:null,flexBasis:null}):null:null,FlexCustomField=()=>{const e=(0,o.useRef)(null),t=(0,r.useBoundProp)(s.flexPropTypeUtil);return o.createElement(r.PropProvider,t,o.createElement(o.Fragment,null,o.createElement(u.StylesFieldLayout,{label:(0,i.__)("Grow","elementor")},o.createElement(r.PropKeyProvider,{bind:"flexGrow"},o.createElement(r.NumberControl,{min:0,shouldForceInt:!0}))),o.createElement(u.StylesFieldLayout,{label:(0,i.__)("Shrink","elementor")},o.createElement(r.PropKeyProvider,{bind:"flexShrink"},o.createElement(r.NumberControl,{min:0,shouldForceInt:!0}))),o.createElement(u.StylesFieldLayout,{label:(0,i.__)("Basis","elementor"),ref:e},o.createElement(r.PropKeyProvider,{bind:"flexBasis"},o.createElement(r.SizeControl,{extendedOptions:["auto"],anchorRef:e})))))},getActiveGroup=({grow:e,shrink:t,basis:n})=>null!==e||null!==t||n?t&&e||n?"custom":1===e?"flex-grow":1===t?"flex-shrink":"custom":null},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/gap-control-field.tsx":function(e,t,n){n.r(t),n.d(t,{GapControlField:function(){return GapControlField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx");const i=(0,s.__)("Gaps","elementor"),GapControlField=()=>o.createElement(a.StylesField,{bind:"gap",propDisplayName:i},o.createElement(r.GapControl,{label:i}))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/justify-content-field.tsx":function(e,t,n){n.r(t),n.d(t,{JustifyContentField:function(){return JustifyContentField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/utils/rotated-icon.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const u=(0,i.__)("Justify content","elementor"),g=(0,a.withDirection)(s.JustifyTopIcon),m=(0,a.withDirection)(s.JustifyBottomIcon),y={isClockwise:!0,offset:-90},f=[{value:"flex-start",label:(0,i.__)("Start","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:g,size:e},y)),showTooltip:!0},{value:"center",label:(0,i.__)("Center","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifyCenterIcon,size:e},y)),showTooltip:!0},{value:"flex-end",label:(0,i.__)("End","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:m,size:e},y)),showTooltip:!0},{value:"space-between",label:(0,i.__)("Space between","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifySpaceBetweenVerticalIcon,size:e},y)),showTooltip:!0},{value:"space-around",label:(0,i.__)("Space around","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifySpaceAroundVerticalIcon,size:e},y)),showTooltip:!0},{value:"space-evenly",label:(0,i.__)("Space evenly","elementor"),renderContent:({size:e})=>o.createElement(d.RotatedIcon,_extends({icon:s.JustifyDistributeVerticalIcon,size:e},y)),showTooltip:!0}],JustifyContentField=()=>o.createElement(l.StylesField,{bind:"justify-content",propDisplayName:u},o.createElement(c.UiProviders,null,o.createElement(p.StylesFieldLayout,{label:u,direction:"column"},o.createElement(r.ToggleControl,{options:f,fullWidth:!0}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/layout-section.tsx":function(e,t,n){n.r(t),n.d(t,{LayoutSection:function(){return LayoutSection}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-elements"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/hooks/use-computed-style.ts"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts"),p=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/align-content-field.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/align-items-field.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/align-self-child-field.tsx"),y=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/display-field.tsx"),f=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/flex-direction-field.tsx"),k=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/flex-order-field.tsx"),x=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/flex-size-field.tsx"),v=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/gap-control-field.tsx"),h=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/justify-content-field.tsx"),b=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/wrap-field.tsx");const E=(0,a.__)("Display","elementor"),S=(0,a.__)("Flex wrap","elementor"),LayoutSection=()=>{const{value:e}=(0,c.useStylesField)("display",{history:{propDisplayName:E}}),t=(0,y.useDisplayPlaceholderValue)(),n=shouldDisplayFlexFields(e,t),{element:r}=(0,i.useElement)(),a=(0,s.useParentElement)(r.id),p=(0,l.useComputedStyle)(a?.id||null),u=p?.flexDirection??"row";return o.createElement(d.SectionContent,null,o.createElement(y.DisplayField,null),n&&o.createElement(FlexFields,null),"flex"===p?.display&&o.createElement(FlexChildFields,{parentStyleDirection:u}))},FlexFields=()=>{const{value:e}=(0,c.useStylesField)("flex-wrap",{history:{propDisplayName:S}});return o.createElement(o.Fragment,null,o.createElement(f.FlexDirectionField,null),o.createElement(h.JustifyContentField,null),o.createElement(g.AlignItemsField,null),o.createElement(p.PanelDivider,null),o.createElement(v.GapControlField,null),o.createElement(b.WrapField,null),["wrap","wrap-reverse"].includes(e?.value)&&o.createElement(u.AlignContentField,null))},FlexChildFields=({parentStyleDirection:e})=>o.createElement(o.Fragment,null,o.createElement(p.PanelDivider,null),o.createElement(r.ControlFormLabel,null,(0,a.__)("Flex child","elementor")),o.createElement(m.AlignSelfChild,{parentStyleDirection:e}),o.createElement(k.FlexOrderField,null),o.createElement(x.FlexSizeField,null)),shouldDisplayFlexFields=(e,t)=>{const n=e?.value??t?.value;return!!n&&("flex"===n||"inline-flex"===n)}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/utils/rotated-icon.tsx":function(e,t,n){n.r(t),n.d(t,{RotatedIcon:function(){return RotatedIcon}});var o=n("react"),r=n("@elementor/ui"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts");const i=(0,s.__)("Flex direction","elementor"),l={row:0,column:90,"row-reverse":180,"column-reverse":270},c={row:0,column:-90,"row-reverse":-180,"column-reverse":-270},RotatedIcon=({icon:e,size:t,isClockwise:n=!0,offset:r=0,disableRotationForReversed:s=!1})=>{const a=(0,o.useRef)(useGetTargetAngle(n,r,s));return a.current=useGetTargetAngle(n,r,s,a),o.createElement(e,{fontSize:t,sx:{transition:".3s",rotate:`${a.current}deg`}})},useGetTargetAngle=(e,t,n,o)=>{const{value:s}=(0,a.useStylesField)("flex-direction",{history:{propDisplayName:i}}),p="rtl"===(0,r.useTheme)().direction?-1:1,d=e?l:c,u=s?.value||"row",g=o?o.current*p:d[u]+t,m=((d[u]+t-g+360)%360+180)%360-180;return n&&["row-reverse","column-reverse"].includes(u)?0:(g+m)*p}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/wrap-field.tsx":function(e,t,n){n.r(t),n.d(t,{WrapField:function(){return WrapField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const p=(0,a.__)("Wrap","elementor"),d=[{value:"nowrap",label:(0,a.__)("No wrap","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowRightIcon,{fontSize:e}),showTooltip:!0},{value:"wrap",label:(0,a.__)("Wrap","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowBackIcon,{fontSize:e}),showTooltip:!0},{value:"wrap-reverse",label:(0,a.__)("Reversed wrap","elementor"),renderContent:({size:e})=>o.createElement(s.ArrowForwardIcon,{fontSize:e}),showTooltip:!0}],WrapField=()=>o.createElement(i.StylesField,{bind:"flex-wrap",propDisplayName:p},o.createElement(l.UiProviders,null,o.createElement(c.StylesFieldLayout,{label:p},o.createElement(r.ToggleControl,{options:d}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/dimensions-field.tsx":function(e,t,n){n.r(t),n.d(t,{DimensionsField:function(){return DimensionsField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts"),p=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/control-label.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/utils/rotated-icon.tsx");const g=(0,a.withDirection)(s.SideLeftIcon),m=(0,a.withDirection)(s.SideRightIcon),y={"inset-block-start":o.createElement(s.SideTopIcon,{fontSize:"tiny"}),"inset-block-end":o.createElement(s.SideBottomIcon,{fontSize:"tiny"}),"inset-inline-start":o.createElement(u.RotatedIcon,{icon:g,size:"tiny"}),"inset-inline-end":o.createElement(u.RotatedIcon,{icon:m,size:"tiny"})},getInlineStartLabel=e=>e?(0,i.__)("Right","elementor"):(0,i.__)("Left","elementor"),getInlineEndLabel=e=>e?(0,i.__)("Left","elementor"):(0,i.__)("Right","elementor"),DimensionsField=()=>{const{isSiteRtl:e}=(0,c.useDirection)(),t=[(0,o.useRef)(null),(0,o.useRef)(null)];return o.createElement(p.UiProviders,null,o.createElement(a.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:t[0]},o.createElement(DimensionField,{side:"inset-block-start",label:(0,i.__)("Top","elementor"),rowRef:t[0]}),o.createElement(DimensionField,{side:"inset-inline-end",label:getInlineEndLabel(e),rowRef:t[0]})),o.createElement(a.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:t[1]},o.createElement(DimensionField,{side:"inset-block-end",label:(0,i.__)("Bottom","elementor"),rowRef:t[1]}),o.createElement(DimensionField,{side:"inset-inline-start",label:getInlineStartLabel(e),rowRef:t[1]})))},DimensionField=({side:e,label:t,rowRef:n})=>o.createElement(l.StylesField,{bind:e,propDisplayName:t},o.createElement(a.Grid,{container:!0,gap:.75,alignItems:"center"},o.createElement(a.Grid,{item:!0,xs:12},o.createElement(d.ControlLabel,null,t)),o.createElement(a.Grid,{item:!0,xs:12},o.createElement(r.SizeControl,{startIcon:y[e],extendedOptions:["auto"],anchorRef:n}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/offset-field.tsx":function(e,t,n){n.r(t),n.d(t,{OffsetField:function(){return OffsetField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Anchor offset","elementor"),c=["px","em","rem","vw","vh"],OffsetField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"scroll-margin-top",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l,ref:e},o.createElement(r.SizeControl,{units:c,anchorRef:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/position-field.tsx":function(e,t,n){n.r(t),n.d(t,{PositionField:function(){return PositionField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Position","elementor"),c=[{label:(0,s.__)("Static","elementor"),value:"static"},{label:(0,s.__)("Relative","elementor"),value:"relative"},{label:(0,s.__)("Absolute","elementor"),value:"absolute"},{label:(0,s.__)("Fixed","elementor"),value:"fixed"},{label:(0,s.__)("Sticky","elementor"),value:"sticky"}],PositionField=({onChange:e})=>o.createElement(a.StylesField,{bind:"position",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.SelectControl,{options:c,onChange:e})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/position-section.tsx":function(e,t,n){n.r(t),n.d(t,{PositionSection:function(){return PositionSection}});var o=n("react"),r=n("@elementor/session"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts"),l=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-fields.ts"),c=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/dimensions-field.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/offset-field.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/position-field.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/z-index-field.tsx");const y=(0,s.__)("Position","elementor"),f=(0,s.__)("Dimensions","elementor"),PositionSection=()=>{const{value:e}=(0,i.useStylesField)("position",{history:{propDisplayName:y}}),{values:t,setValues:n}=(0,l.useStylesFields)(["inset-block-start","inset-block-end","inset-inline-start","inset-inline-end"]),[r,s,a]=usePersistDimensions(),k=e&&"static"!==e?.value;return o.createElement(p.SectionContent,null,o.createElement(g.PositionField,{onChange:(e,o)=>{const i={history:{propDisplayName:f}};"static"===e?t&&(s(t),n({"inset-block-start":void 0,"inset-block-end":void 0,"inset-inline-start":void 0,"inset-inline-end":void 0},i)):"static"===o&&r&&(n(r,i),a())}}),k?o.createElement(o.Fragment,null,o.createElement(d.DimensionsField,null),o.createElement(m.ZIndexField,null)):null,o.createElement(c.PanelDivider,null),o.createElement(u.OffsetField,null))},usePersistDimensions=()=>{const{id:e,meta:t}=(0,a.useStyle)(),n=`${`styles/${e}/${t.breakpoint||"desktop"}/${t.state||"null"}`}/dimensions`;return(0,r.useSessionStorage)(n)}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/z-index-field.tsx":function(e,t,n){n.r(t),n.d(t,{ZIndexField:function(){return ZIndexField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Z-index","elementor"),ZIndexField=()=>o.createElement(a.StylesField,{bind:"z-index",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.NumberControl,null)))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/size-section/object-fit-field.tsx":function(e,t,n){n.r(t),n.d(t,{ObjectFitField:function(){return ObjectFitField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Object fit","elementor"),c=[{label:(0,s.__)("Fill","elementor"),value:"fill"},{label:(0,s.__)("Cover","elementor"),value:"cover"},{label:(0,s.__)("Contain","elementor"),value:"contain"},{label:(0,s.__)("None","elementor"),value:"none"},{label:(0,s.__)("Scale down","elementor"),value:"scale-down"}],ObjectFitField=()=>o.createElement(a.StylesField,{bind:"object-fit",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.SelectControl,{options:c})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/size-section/overflow-field.tsx":function(e,t,n){n.r(t),n.d(t,{OverflowField:function(){return OverflowField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const c=(0,a.__)("Overflow","elementor"),p=[{value:"visible",label:(0,a.__)("Visible","elementor"),renderContent:({size:e})=>o.createElement(s.EyeIcon,{fontSize:e}),showTooltip:!0},{value:"hidden",label:(0,a.__)("Hidden","elementor"),renderContent:({size:e})=>o.createElement(s.EyeOffIcon,{fontSize:e}),showTooltip:!0},{value:"auto",label:(0,a.__)("Auto","elementor"),renderContent:({size:e})=>o.createElement(s.LetterAIcon,{fontSize:e}),showTooltip:!0}],OverflowField=()=>o.createElement(i.StylesField,{bind:"overflow",propDisplayName:c},o.createElement(l.StylesFieldLayout,{label:c},o.createElement(r.ToggleControl,{options:p})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/size-section/size-section.tsx":function(e,t,n){n.r(t),n.d(t,{SizeSection:function(){return SizeSection}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/ui"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/control-label.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-tab-collapsible-content.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/size-section/object-fit-field.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/size-section/overflow-field.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const m=[[{bind:"width",label:(0,a.__)("Width","elementor")},{bind:"height",label:(0,a.__)("Height","elementor")}],[{bind:"min-width",label:(0,a.__)("Min width","elementor")},{bind:"min-height",label:(0,a.__)("Min height","elementor")}],[{bind:"max-width",label:(0,a.__)("Max width","elementor")},{bind:"max-height",label:(0,a.__)("Max height","elementor")}]],y=(0,a.__)("Aspect Ratio","elementor"),SizeSection=()=>{const e=[(0,o.useRef)(null),(0,o.useRef)(null),(0,o.useRef)(null)];return o.createElement(p.SectionContent,null,m.map((t,n)=>o.createElement(s.Grid,{key:n,container:!0,gap:2,flexWrap:"nowrap",ref:e[n]},t.map(t=>o.createElement(s.Grid,{item:!0,xs:6,key:t.bind},o.createElement(SizeField,_extends({},t,{rowRef:e[n],extendedOptions:["auto"]})))))),o.createElement(c.PanelDivider,null),o.createElement(s.Stack,null,o.createElement(g.OverflowField,null)),o.createElement(d.StyleTabCollapsibleContent,{fields:["aspect-ratio","object-fit"]},o.createElement(s.Stack,{gap:2,pt:2},o.createElement(i.StylesField,{bind:"aspect-ratio",propDisplayName:y},o.createElement(r.AspectRatioControl,{label:y})),o.createElement(c.PanelDivider,null),o.createElement(u.ObjectFitField,null),o.createElement(i.StylesField,{bind:"object-position",propDisplayName:(0,a.__)("Object position","elementor")},o.createElement(s.Grid,{item:!0,xs:6},o.createElement(r.PositionControl,null))))))},SizeField=({label:e,bind:t,rowRef:n,extendedOptions:a})=>o.createElement(i.StylesField,{bind:t,propDisplayName:e},o.createElement(s.Grid,{container:!0,gap:.75,alignItems:"center"},o.createElement(s.Grid,{item:!0,xs:12},o.createElement(l.ControlLabel,null,e)),o.createElement(s.Grid,{item:!0,xs:12},o.createElement(r.SizeControl,{extendedOptions:a,anchorRef:n}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/spacing-section/spacing-section.tsx":function(e,t,n){n.r(t),n.d(t,{SpacingSection:function(){return SpacingSection}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts"),l=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx");const p=(0,s.__)("Margin","elementor"),d=(0,s.__)("Padding","elementor"),SpacingSection=()=>{const{isSiteRtl:e}=(0,i.useDirection)();return o.createElement(c.SectionContent,null,o.createElement(a.StylesField,{bind:"margin",propDisplayName:p},o.createElement(r.LinkedDimensionsControl,{label:p,isSiteRtl:e,extendedOptions:["auto"]})),o.createElement(l.PanelDivider,null),o.createElement(a.StylesField,{bind:"padding",propDisplayName:d},o.createElement(r.LinkedDimensionsControl,{label:d,isSiteRtl:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/column-count-field.tsx":function(e,t,n){n.r(t),n.d(t,{ColumnCountField:function(){return ColumnCountField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Columns","elementor"),ColumnCountField=()=>o.createElement(a.StylesField,{bind:"column-count",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.NumberControl,{shouldForceInt:!0,min:0,step:1})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/column-gap-field.tsx":function(e,t,n){n.r(t),n.d(t,{ColumnGapField:function(){return ColumnGapField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Column gap","elementor"),ColumnGapField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"column-gap",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l,ref:e},o.createElement(r.SizeControl,{anchorRef:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-family-field.tsx":function(e,t,n){n.r(t),n.d(t,{FontFamilyField:function(){return FontFamilyField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/contexts/section-context.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/hooks/use-font-families.ts");const p=(0,s.__)("Font family","elementor"),FontFamilyField=()=>{const e=(0,c.useFontFamilies)(),t=(0,a.useSectionWidth)();return 0===e.length?null:o.createElement(i.StylesField,{bind:"font-family",propDisplayName:p},o.createElement(l.StylesFieldLayout,{label:p},o.createElement(r.FontFamilyControl,{fontFamilies:e,sectionWidth:t})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-size-field.tsx":function(e,t,n){n.r(t),n.d(t,{FontSizeField:function(){return FontSizeField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Font size","elementor"),FontSizeField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"font-size",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l,ref:e},o.createElement(r.SizeControl,{anchorRef:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-style-field.tsx":function(e,t,n){n.r(t),n.d(t,{FontStyleField:function(){return FontStyleField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const c=(0,a.__)("Font style","elementor"),p=[{value:"normal",label:(0,a.__)("Normal","elementor"),renderContent:({size:e})=>o.createElement(s.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"italic",label:(0,a.__)("Italic","elementor"),renderContent:({size:e})=>o.createElement(s.ItalicIcon,{fontSize:e}),showTooltip:!0}],FontStyleField=()=>o.createElement(i.StylesField,{bind:"font-style",propDisplayName:c},o.createElement(l.StylesFieldLayout,{label:c},o.createElement(r.ToggleControl,{options:p})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-weight-field.tsx":function(e,t,n){n.r(t),n.d(t,{FontWeightField:function(){return FontWeightField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Font weight","elementor"),c=[{value:"100",label:(0,s.__)("100 - Thin","elementor")},{value:"200",label:(0,s.__)("200 - Extra light","elementor")},{value:"300",label:(0,s.__)("300 - Light","elementor")},{value:"400",label:(0,s.__)("400 - Normal","elementor")},{value:"500",label:(0,s.__)("500 - Medium","elementor")},{value:"600",label:(0,s.__)("600 - Semi bold","elementor")},{value:"700",label:(0,s.__)("700 - Bold","elementor")},{value:"800",label:(0,s.__)("800 - Extra bold","elementor")},{value:"900",label:(0,s.__)("900 - Black","elementor")}],FontWeightField=()=>o.createElement(a.StylesField,{bind:"font-weight",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.SelectControl,{options:c})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/hooks/use-font-families.ts":function(e,t,n){n.r(t),n.d(t,{useFontFamilies:function(){return useFontFamilies}});var o=n("react"),r=n("@wordpress/i18n"),s=n("./packages/packages/core/editor-editing-panel/src/sync/get-elementor-globals.ts");const a={system:(0,r.__)("System","elementor"),custom:(0,r.__)("Custom Fonts","elementor"),googlefonts:(0,r.__)("Google Fonts","elementor")},useFontFamilies=()=>{const e=(()=>{const{controls:e}=(0,s.getElementorConfig)(),t=e?.font?.options;return t||null})();return(0,o.useMemo)(()=>{const t=["system","custom","googlefonts"];return Object.entries(e||{}).reduce((e,[n,o])=>{if(!a[o])return e;const r=t.indexOf(o);return e[r]||(e[r]={label:a[o],fonts:[]}),e[r].fonts.push(n),e},[]).filter(Boolean)},[e])}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/letter-spacing-field.tsx":function(e,t,n){n.r(t),n.d(t,{LetterSpacingField:function(){return LetterSpacingField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Letter spacing","elementor"),LetterSpacingField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"letter-spacing",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l,ref:e},o.createElement(r.SizeControl,{anchorRef:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/line-height-field.tsx":function(e,t,n){n.r(t),n.d(t,{LineHeightField:function(){return LineHeightField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Line height","elementor"),LineHeightField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"line-height",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l,ref:e},o.createElement(r.SizeControl,{anchorRef:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-alignment-field.tsx":function(e,t,n){n.r(t),n.d(t,{TextAlignmentField:function(){return TextAlignmentField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const d=(0,i.__)("Text align","elementor"),u=(0,a.withDirection)(s.AlignLeftIcon),g=(0,a.withDirection)(s.AlignRightIcon),m=[{value:"start",label:(0,i.__)("Start","elementor"),renderContent:({size:e})=>o.createElement(u,{fontSize:e}),showTooltip:!0},{value:"center",label:(0,i.__)("Center","elementor"),renderContent:({size:e})=>o.createElement(s.AlignCenterIcon,{fontSize:e}),showTooltip:!0},{value:"end",label:(0,i.__)("End","elementor"),renderContent:({size:e})=>o.createElement(g,{fontSize:e}),showTooltip:!0},{value:"justify",label:(0,i.__)("Justify","elementor"),renderContent:({size:e})=>o.createElement(s.AlignJustifiedIcon,{fontSize:e}),showTooltip:!0}],TextAlignmentField=()=>o.createElement(l.StylesField,{bind:"text-align",propDisplayName:d},o.createElement(c.UiProviders,null,o.createElement(p.StylesFieldLayout,{label:d},o.createElement(r.ToggleControl,{options:m}))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-color-field.tsx":function(e,t,n){n.r(t),n.d(t,{TextColorField:function(){return TextColorField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Text color","elementor"),TextColorField=()=>o.createElement(a.StylesField,{bind:"color",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l},o.createElement(r.ColorControl,null)))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-decoration-field.tsx":function(e,t,n){n.r(t),n.d(t,{TextDecorationField:function(){return TextDecorationField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const c=(0,a.__)("Line decoration","elementor"),p=[{value:"none",label:(0,a.__)("None","elementor"),renderContent:({size:e})=>o.createElement(s.MinusIcon,{fontSize:e}),showTooltip:!0,exclusive:!0},{value:"underline",label:(0,a.__)("Underline","elementor"),renderContent:({size:e})=>o.createElement(s.UnderlineIcon,{fontSize:e}),showTooltip:!0},{value:"line-through",label:(0,a.__)("Line-through","elementor"),renderContent:({size:e})=>o.createElement(s.StrikethroughIcon,{fontSize:e}),showTooltip:!0},{value:"overline",label:(0,a.__)("Overline","elementor"),renderContent:({size:e})=>o.createElement(s.OverlineIcon,{fontSize:e}),showTooltip:!0}],TextDecorationField=()=>o.createElement(i.StylesField,{bind:"text-decoration",propDisplayName:c},o.createElement(l.StylesFieldLayout,{label:c},o.createElement(r.ToggleControl,{options:p,exclusive:!1})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-direction-field.tsx":function(e,t,n){n.r(t),n.d(t,{TextDirectionField:function(){return TextDirectionField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const c=(0,a.__)("Direction","elementor"),p=[{value:"ltr",label:(0,a.__)("Left to right","elementor"),renderContent:({size:e})=>o.createElement(s.TextDirectionLtrIcon,{fontSize:e}),showTooltip:!0},{value:"rtl",label:(0,a.__)("Right to left","elementor"),renderContent:({size:e})=>o.createElement(s.TextDirectionRtlIcon,{fontSize:e}),showTooltip:!0}],TextDirectionField=()=>o.createElement(i.StylesField,{bind:"direction",propDisplayName:c},o.createElement(l.StylesFieldLayout,{label:c},o.createElement(r.ToggleControl,{options:p})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-stroke-field.tsx":function(e,t,n){n.r(t),n.d(t,{TextStrokeField:function(){return TextStrokeField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts"),l=n("./packages/packages/core/editor-editing-panel/src/components/add-or-remove-content.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/control-label.tsx");const p={$$type:"stroke",value:{color:{$$type:"color",value:"#000000"},width:{$$type:"size",value:{unit:"px",size:1}}}},d=(0,s.__)("Text stroke","elementor"),TextStrokeField=()=>{const{value:e,setValue:t,canEdit:n}=(0,i.useStylesField)("stroke",{history:{propDisplayName:d}}),s=Boolean(e);return o.createElement(a.StylesField,{bind:"stroke",propDisplayName:d},o.createElement(l.AddOrRemoveContent,{isAdded:s,onAdd:()=>{t(p)},onRemove:()=>{t(null)},disabled:!n,renderLabel:()=>o.createElement(c.ControlLabel,null,d)},o.createElement(r.StrokeControl,null)))}},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/transform-field.tsx":function(e,t,n){n.r(t),n.d(t,{TransformField:function(){return TransformField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const c=(0,a.__)("Text transform","elementor"),p=[{value:"none",label:(0,a.__)("None","elementor"),renderContent:({size:e})=>o.createElement(s.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"capitalize",label:(0,a.__)("Capitalize","elementor"),renderContent:({size:e})=>o.createElement(s.LetterCaseIcon,{fontSize:e}),showTooltip:!0},{value:"uppercase",label:(0,a.__)("Uppercase","elementor"),renderContent:({size:e})=>o.createElement(s.LetterCaseUpperIcon,{fontSize:e}),showTooltip:!0},{value:"lowercase",label:(0,a.__)("Lowercase","elementor"),renderContent:({size:e})=>o.createElement(s.LetterCaseLowerIcon,{fontSize:e}),showTooltip:!0}],TransformField=()=>o.createElement(i.StylesField,{bind:"text-transform",propDisplayName:c},o.createElement(l.StylesFieldLayout,{label:c},o.createElement(r.ToggleControl,{options:p})))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/typography-section.tsx":function(e,t,n){n.r(t),n.d(t,{TypographySection:function(){return TypographySection}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/components/panel-divider.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/components/section-content.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/components/style-tab-collapsible-content.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/column-count-field.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/column-gap-field.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-family-field.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-size-field.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-style-field.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/font-weight-field.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/letter-spacing-field.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/line-height-field.tsx"),y=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-alignment-field.tsx"),f=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-color-field.tsx"),k=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-decoration-field.tsx"),x=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-direction-field.tsx"),v=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/text-stroke-field.tsx"),h=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/transform-field.tsx"),b=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/word-spacing-field.tsx");const TypographySection=()=>o.createElement(s.SectionContent,null,o.createElement(c.FontFamilyField,null),o.createElement(u.FontWeightField,null),o.createElement(p.FontSizeField,null),o.createElement(r.PanelDivider,null),o.createElement(y.TextAlignmentField,null),o.createElement(f.TextColorField,null),o.createElement(a.StyleTabCollapsibleContent,{fields:["line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]},o.createElement(s.SectionContent,{sx:{pt:2}},o.createElement(m.LineHeightField,null),o.createElement(g.LetterSpacingField,null),o.createElement(b.WordSpacingField,null),o.createElement(i.ColumnCountField,null),o.createElement(l.ColumnGapField,null),o.createElement(r.PanelDivider,null),o.createElement(k.TextDecorationField,null),o.createElement(h.TransformField,null),o.createElement(x.TextDirectionField,null),o.createElement(d.FontStyleField,null),o.createElement(v.TextStrokeField,null))))},"./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/word-spacing-field.tsx":function(e,t,n){n.r(t),n.d(t,{WordSpacingField:function(){return WordSpacingField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx");const l=(0,s.__)("Word spacing","elementor"),WordSpacingField=()=>{const e=(0,o.useRef)(null);return o.createElement(a.StylesField,{bind:"word-spacing",propDisplayName:l},o.createElement(i.StylesFieldLayout,{label:l,ref:e},o.createElement(r.SizeControl,{anchorRef:e})))}},"./packages/packages/core/editor-editing-panel/src/components/style-tab-collapsible-content.tsx":function(e,t,n){n.r(t),n.d(t,{StyleTabCollapsibleContent:function(){return StyleTabCollapsibleContent},getStylesInheritanceIndicators:function(){return getStylesInheritanceIndicators}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/styles-inheritance-section-indicators.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/components/collapsible-content.tsx");const StyleTabCollapsibleContent=({fields:e=[],children:t})=>o.createElement(s.CollapsibleContent,{titleEnd:getStylesInheritanceIndicators(e)},t);function getStylesInheritanceIndicators(e){return 0===e.length?null:t=>t?null:o.createElement(r.StylesInheritanceSectionIndicators,{fields:e})}},"./packages/packages/core/editor-editing-panel/src/components/style-tab-section.tsx":function(e,t,n){n.r(t),n.d(t,{StyleTabSection:function(){return StyleTabSection}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/hooks/use-default-panel-settings.ts"),s=n("./packages/packages/core/editor-editing-panel/src/components/section.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/components/style-tab-collapsible-content.tsx");const StyleTabSection=({section:e,fields:t=[]})=>{const{component:n,name:i,title:l}=e,c=(0,r.useDefaultPanelSettings)(),p=n,d=c.defaultSectionsExpanded.style?.includes(i);return o.createElement(s.Section,{title:l,defaultExpanded:d,titleEnd:(0,a.getStylesInheritanceIndicators)(t)},o.createElement(p,null))}},"./packages/packages/core/editor-editing-panel/src/components/style-tab.tsx":function(e,t,n){n.r(t),n.d(t,{StyleTab:function(){return StyleTab},stickyHeaderStyles:function(){return w}});var o=n("react"),r=n("@elementor/editor-props"),s=n("@elementor/editor-responsive"),a=n("@elementor/session"),i=n("@elementor/ui"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/contexts/classes-prop-context.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/contexts/scroll-context.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/contexts/styles-inheritance-context.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/hooks/use-active-style-def-id.ts"),y=n("./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-selector.tsx"),f=n("./packages/packages/core/editor-editing-panel/src/components/sections-list.tsx"),k=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/background-section/background-section.tsx"),x=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/border-section/border-section.tsx"),v=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/effects-section/effects-section.tsx"),h=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/layout-section/layout-section.tsx"),b=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/position-section/position-section.tsx"),E=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/size-section/size-section.tsx"),S=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/spacing-section/spacing-section.tsx"),_=n("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/typography-section.tsx"),C=n("./packages/packages/core/editor-editing-panel/src/components/style-tab-section.tsx");const w={position:"sticky",zIndex:1100,opacity:1,backgroundColor:"background.default",transition:"top 300ms ease"},StyleTab=()=>{const e=function useCurrentClassesProp(){const{elementType:e}=(0,p.useElement)(),t=Object.entries(e.propsSchema).find(([,e])=>"plain"===e.kind&&e.key===r.CLASSES_PROP_KEY);if(!t)throw new Error("Element does not have a classes prop");return t[0]}(),[t,n]=(0,m.useActiveStyleDefId)(e),[d,w]=(0,o.useState)(null),P=(0,s.useActiveBreakpoint)();return o.createElement(c.ClassesPropProvider,{prop:e},o.createElement(u.StyleProvider,{meta:{breakpoint:P,state:d},id:t,setId:e=>{n(e),w(null)},setMetaState:w},o.createElement(a.SessionStorageProvider,{prefix:t??""},o.createElement(g.StyleInheritanceProvider,null,o.createElement(ClassesHeader,null,o.createElement(y.CssClassSelector,null),o.createElement(i.Divider,null)),o.createElement(f.SectionsList,null,o.createElement(C.StyleTabSection,{section:{component:h.LayoutSection,name:"Layout",title:(0,l.__)("Layout","elementor")},fields:["display","flex-direction","flex-wrap","justify-content","align-items","align-content","align-self","gap"]}),o.createElement(C.StyleTabSection,{section:{component:S.SpacingSection,name:"Spacing",title:(0,l.__)("Spacing","elementor")},fields:["margin","padding"]}),o.createElement(C.StyleTabSection,{section:{component:E.SizeSection,name:"Size",title:(0,l.__)("Size","elementor")},fields:["width","min-width","max-width","height","min-height","max-height","overflow","aspect-ratio","object-fit"]}),o.createElement(C.StyleTabSection,{section:{component:b.PositionSection,name:"Position",title:(0,l.__)("Position","elementor")},fields:["position","z-index","scroll-margin-top"]}),o.createElement(C.StyleTabSection,{section:{component:_.TypographySection,name:"Typography",title:(0,l.__)("Typography","elementor")},fields:["font-family","font-weight","font-size","text-align","color","line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]}),o.createElement(C.StyleTabSection,{section:{component:k.BackgroundSection,name:"Background",title:(0,l.__)("Background","elementor")},fields:["background"]}),o.createElement(C.StyleTabSection,{section:{component:x.BorderSection,name:"Border",title:(0,l.__)("Border","elementor")},fields:["border-radius","border-width","border-color","border-style"]}),o.createElement(C.StyleTabSection,{section:{component:v.EffectsSection,name:"Effects",title:(0,l.__)("Effects","elementor")},fields:["box-shadow","opacity","transform","filter","backdrop-filter"]})),o.createElement(i.Box,{sx:{height:"150px"}})))))};function ClassesHeader({children:e}){const t=(0,d.useScrollDirection)();return o.createElement(i.Stack,{sx:{...w,top:"up"===t?"37px":0}},e)}},"./packages/packages/core/editor-editing-panel/src/components/styles-field-layout.tsx":function(e,t,n){n.r(t),n.d(t,{StylesFieldLayout:function(){return a}});var o=n("react"),r=n("@elementor/ui"),s=n("./packages/packages/core/editor-editing-panel/src/components/control-label.tsx");const a=o.forwardRef((e,t)=>{const{direction:n="row",children:r,label:s}=e,a="row"===n?i:l;return o.createElement(a,{label:s,ref:t,children:r})}),i=o.forwardRef(({label:e,children:t},n)=>o.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:n},o.createElement(r.Grid,{item:!0,xs:6},o.createElement(s.ControlLabel,null,e)),o.createElement(r.Grid,{item:!0,xs:6,sx:e=>({width:`calc(50% - ${e.spacing(2)})`})},t))),l=o.forwardRef(({label:e,children:t},n)=>o.createElement(r.Stack,{gap:.75,ref:n},o.createElement(s.ControlLabel,null,e),t))},"./packages/packages/core/editor-editing-panel/src/contexts/classes-prop-context.tsx":function(e,t,n){n.r(t),n.d(t,{ClassesPropProvider:function(){return ClassesPropProvider},useClassesProp:function(){return useClassesProp}});var o=n("react");const r=(0,o.createContext)(null);function ClassesPropProvider({children:e,prop:t}){return o.createElement(r.Provider,{value:{prop:t}},e)}function useClassesProp(){const e=(0,o.useContext)(r);if(!e)throw new Error("useClassesProp must be used within a ClassesPropProvider");return e.prop}},"./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx":function(e,t,n){n.r(t),n.d(t,{ElementProvider:function(){return ElementProvider},useElement:function(){return useElement}});var o=n("react");const r=(0,o.createContext)(null);function ElementProvider({children:e,element:t,elementType:n}){return o.createElement(r.Provider,{value:{element:t,elementType:n}},e)}function useElement(){const e=(0,o.useContext)(r);if(!e)throw new Error("useElement must be used within a ElementProvider");return e}},"./packages/packages/core/editor-editing-panel/src/contexts/scroll-context.tsx":function(e,t,n){n.r(t),n.d(t,{ScrollProvider:function(){return ScrollProvider},useScrollDirection:function(){return useScrollDirection}});var o=n("react"),r=n("@elementor/ui");const s=(0,o.createContext)(void 0),a=(0,r.styled)("div")`
	height: 100%;
	overflow-y: auto;
`,i="up";function ScrollProvider({children:e}){const[t,n]=(0,o.useState)(i),r=(0,o.useRef)(null),l=(0,o.useRef)(0);return(0,o.useEffect)(()=>{const e=r.current;if(!e)return;const handleScroll=()=>{const{scrollTop:t}=e;t>l.current?n("down"):t<l.current&&n("up"),l.current=t};return e.addEventListener("scroll",handleScroll),()=>{e.removeEventListener("scroll",handleScroll)}}),o.createElement(s.Provider,{value:{direction:t}},o.createElement(a,{ref:r},e))}function useScrollDirection(){return(0,o.useContext)(s)?.direction??i}},"./packages/packages/core/editor-editing-panel/src/contexts/section-context.tsx":function(e,t,n){n.r(t),n.d(t,{SectionRefContext:function(){return r},useSectionWidth:function(){return useSectionWidth}});var o=n("react");const r=(0,o.createContext)(null),useSectionWidth=()=>{const e=(0,o.useContext)(r);return e?.current?.offsetWidth??320}},"./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx":function(e,t,n){n.r(t),n.d(t,{StyleProvider:function(){return StyleProvider},getProviderByStyleId:function(){return getProviderByStyleId},useIsStyle:function(){return useIsStyle},useStyle:function(){return useStyle}});var o=n("react"),r=n("@elementor/editor-styles-repository"),s=n("./packages/packages/core/editor-editing-panel/src/errors.ts");const a=(0,o.createContext)(null);function StyleProvider({children:e,...t}){const n=null===t.id?null:getProviderByStyleId(t.id),{userCan:i}=(0,r.useUserStylesCapability)();if(t.id&&!n)throw new s.StylesProviderNotFoundError({context:{styleId:t.id}});const l=i(n?.getKey()??"").updateProps;return o.createElement(a.Provider,{value:{...t,provider:n,canEdit:l}},e)}function useStyle(){const e=(0,o.useContext)(a);if(!e)throw new Error("useStyle must be used within a StyleProvider");return e}function getProviderByStyleId(e){return r.stylesRepository.getProviders().find(t=>t.actions.all().find(t=>t.id===e))??null}function useIsStyle(){return!!(0,o.useContext)(a)}},"./packages/packages/core/editor-editing-panel/src/contexts/styles-inheritance-context.tsx":function(e,t,n){n.r(t),n.d(t,{StyleInheritanceProvider:function(){return StyleInheritanceProvider},useStylesInheritanceChain:function(){return useStylesInheritanceChain},useStylesInheritanceSnapshot:function(){return useStylesInheritanceSnapshot}});var o=n("react"),r=n("@elementor/editor-elements"),s=n("@elementor/editor-props"),a=n("@elementor/editor-responsive"),i=n("@elementor/editor-styles"),l=n("@elementor/editor-styles-repository"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-rerender.ts"),p=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/create-styles-inheritance.ts"),d=n("./packages/packages/core/editor-editing-panel/src/contexts/classes-prop-context.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),g=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx");const m=(0,o.createContext)(null);function StyleInheritanceProvider({children:e}){const t=useAppliedStyles(),n=(0,a.getBreakpointsTree)(),{getSnapshot:r,getInheritanceChain:s}=(0,p.createStylesInheritance)(t,n);return o.createElement(m.Provider,{value:{getSnapshot:r,getInheritanceChain:s}},e)}function useStylesInheritanceSnapshot(){const e=(0,o.useContext)(m),{meta:t}=(0,g.useStyle)();if(!e)throw new Error("useStylesInheritanceSnapshot must be used within a StyleInheritanceProvider");return t?e.getSnapshot(t)??null:null}function useStylesInheritanceChain(e){const t=(0,o.useContext)(m);if(!t)throw new Error("useStylesInheritanceChain must be used within a StyleInheritanceProvider");const n=(0,i.getStylesSchema)(),r=n?.[e[0]],s=useStylesInheritanceSnapshot();return s?t.getInheritanceChain(s,e,r):[]}const useAppliedStyles=()=>{const{element:e}=(0,u.useElement)(),t=(0,d.useClassesProp)(),n=useBaseStyles();(0,c.useStylesRerender)();const o=(0,r.useElementSetting)(e.id,t),a=s.classesPropTypeUtil.extract(o)??[];return l.stylesRepository.all().filter(e=>[...n,...a].includes(e.id))},useBaseStyles=()=>{const{elementType:e}=(0,u.useElement)(),t=(0,r.getWidgetsCache)(),n=t?.[e.key];return Object.keys(n?.base_styles??{})}},"./packages/packages/core/editor-editing-panel/src/control-replacement.tsx":function(e,t,n){n.r(t),n.d(t,{getControlReplacements:function(){return s},registerControlReplacement:function(){return r}});var o=n("@elementor/editor-controls");const{registerControlReplacement:r,getControlReplacements:s}=(0,o.createControlReplacementsRegistry)()},"./packages/packages/core/editor-editing-panel/src/controls-actions.ts":function(e,t,n){n.r(t),n.d(t,{controlActionsMenu:function(){return a}});var o=n("@elementor/menus"),r=n("./packages/packages/core/editor-editing-panel/src/action.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/popover-action.tsx");const a=(0,o.createMenu)({components:{Action:r.default,PopoverAction:s.PopoverAction}})},"./packages/packages/core/editor-editing-panel/src/controls-registry/conditional-field.tsx":function(e,t,n){n.r(t),n.d(t,{ConditionalField:function(){return ConditionalField},getDependencies:function(){return getDependencies}});var o=n("@elementor/editor-controls"),r=n("@elementor/editor-props"),s=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-fields.ts");const ConditionalField=({children:e})=>{const{propType:t}=(0,o.useBoundProp)(),n=getDependencies(t),{values:a}=(0,s.useStylesFields)(n);return!(0,r.isDependencyMet)(t?.dependencies,a)?null:e};function getDependencies(e){return e?.dependencies?.terms.length?e.dependencies.terms.flatMap(e=>(0,r.isDependency)(e)?[]:e.path):[]}},"./packages/packages/core/editor-editing-panel/src/controls-registry/control-type-container.tsx":function(e,t,n){n.r(t),n.d(t,{ControlTypeContainer:function(){return ControlTypeContainer}});var o=n("react"),r=n("@elementor/ui");const ControlTypeContainer=({children:e,layout:t})=>"custom"===t?e:o.createElement(s,{layout:t},e),s=(0,r.styled)(r.Box,{shouldForwardProp:e=>!["layout"].includes(e)})(({layout:e,theme:t})=>({display:"grid",gridGap:t.spacing(1),...getGridLayout(e)})),getGridLayout=e=>({justifyContent:"space-between",gridTemplateColumns:{full:"minmax(0, 1fr)","two-columns":"repeat(2, minmax(0, 1fr))"}[e]})},"./packages/packages/core/editor-editing-panel/src/controls-registry/control.tsx":function(e,t,n){n.r(t),n.d(t,{Control:function(){return Control}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/errors.ts"),a=n("./packages/packages/core/editor-editing-panel/src/controls-registry/controls-registry.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const Control=({props:e,type:t})=>{const n=(0,a.getControl)(t),{element:i}=(0,r.useElement)();if(!n)throw new s.ControlTypeNotFoundError({context:{controlType:t}});return o.createElement(n,_extends({},e,{context:{elementId:i.id}}))}},"./packages/packages/core/editor-editing-panel/src/controls-registry/controls-registry.tsx":function(e,t,n){n.r(t),n.d(t,{getControl:function(){return getControl},getDefaultLayout:function(){return getDefaultLayout},getPropTypeUtil:function(){return getPropTypeUtil}});var o=n("@elementor/editor-controls"),r=n("@elementor/editor-props");const s={image:{component:o.ImageControl,layout:"full",propTypeUtil:r.imagePropTypeUtil},"svg-media":{component:o.SvgMediaControl,layout:"full",propTypeUtil:r.imageSrcPropTypeUtil},text:{component:o.TextControl,layout:"full",propTypeUtil:r.stringPropTypeUtil},textarea:{component:o.TextAreaControl,layout:"full",propTypeUtil:r.stringPropTypeUtil},size:{component:o.SizeControl,layout:"two-columns",propTypeUtil:r.sizePropTypeUtil},select:{component:o.SelectControl,layout:"two-columns",propTypeUtil:r.stringPropTypeUtil},link:{component:o.LinkControl,layout:"custom",propTypeUtil:r.linkPropTypeUtil},url:{component:o.UrlControl,layout:"full",propTypeUtil:r.stringPropTypeUtil},switch:{component:o.SwitchControl,layout:"two-columns",propTypeUtil:r.booleanPropTypeUtil},repeatable:{component:o.RepeatableControl,layout:"full",propTypeUtil:void 0},"key-value":{component:o.KeyValueControl,layout:"full",propTypeUtil:r.keyValuePropTypeUtil}},getControl=e=>s[e]?.component,getDefaultLayout=e=>s[e].layout,getPropTypeUtil=e=>s[e]?.propTypeUtil},"./packages/packages/core/editor-editing-panel/src/controls-registry/create-top-level-object-type.ts":function(e,t,n){n.r(t),n.d(t,{createTopLevelOjectType:function(){return createTopLevelOjectType}});const createTopLevelOjectType=({schema:e})=>({key:"",kind:"object",meta:{},settings:{},default:null,shape:e})},"./packages/packages/core/editor-editing-panel/src/controls-registry/settings-field.tsx":function(e,t,n){n.r(t),n.d(t,{SettingsField:function(){return SettingsField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-documents"),a=n("@elementor/editor-elements"),i=n("@elementor/editor-props"),l=n("@elementor/editor-v1-adapters"),c=n("@wordpress/i18n"),p=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/utils/prop-dependency-utils.ts"),u=n("./packages/packages/core/editor-editing-panel/src/controls-registry/create-top-level-object-type.ts");const SettingsField=({bind:e,children:t,propDisplayName:n})=>{const{element:{id:g},elementType:{propsSchema:m,dependenciesPerTargetMapping:y={}}}=(0,p.useElement)(),f=(0,a.useElementSettings)(g,Object.keys(m)),k={[e]:f?.[e]??null},x=(0,u.createTopLevelOjectType)({schema:m}),v=function useUndoableUpdateElementProp({elementId:e,propDisplayName:t}){return(0,o.useMemo)(()=>(0,l.undoable)({do:t=>{const n=(0,a.getElementSettings)(e,Object.keys(t));return(0,a.updateElementSettings)({id:e,props:t,withHistory:!1}),(0,s.setDocumentModifiedStatus)(!0),n},undo:({},t)=>{(0,a.updateElementSettings)({id:e,props:t,withHistory:!1})}},{title:(0,a.getElementLabel)(e),subtitle:(0,c.__)("%s edited","elementor").replace("%s",t),debounce:{wait:800}}),[e,t])}({elementId:g,propDisplayName:n});return o.createElement(r.PropProvider,{propType:x,value:k,setValue:t=>{const n=(0,d.extractOrderedDependencies)(e,m,f,y),o=(0,d.updateValues)(t,n,m,f);v(o)},isDisabled:e=>!(0,i.isDependencyMet)(e?.dependencies,f)},o.createElement(r.PropKeyProvider,{bind:e},t))}},"./packages/packages/core/editor-editing-panel/src/controls-registry/styles-field.tsx":function(e,t,n){n.r(t),n.d(t,{StylesField:function(){return StylesField}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-styles"),a=n("./packages/packages/core/editor-editing-panel/src/contexts/styles-inheritance-context.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts"),l=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/styles-inheritance-indicator.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/controls-registry/conditional-field.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/controls-registry/create-top-level-object-type.ts");const StylesField=({bind:e,propDisplayName:t,children:n})=>{const d=(0,s.getStylesSchema)(),u=(0,a.useStylesInheritanceChain)([e]),{value:g,canEdit:m,...y}=(0,i.useStylesField)(e,{history:{propDisplayName:t}}),f=(0,p.createTopLevelOjectType)({schema:d}),[k]=u,x={[e]:k?.value};return o.createElement(r.ControlAdornmentsProvider,{items:[{id:"styles-inheritance",Adornment:l.StylesInheritanceIndicator}]},o.createElement(r.PropProvider,{propType:f,value:{[e]:g},setValue:t=>{y.setValue(t[e])},placeholder:x,isDisabled:()=>!m},o.createElement(r.PropKeyProvider,{bind:e},o.createElement(c.ConditionalField,null,n))))}},"./packages/packages/core/editor-editing-panel/src/dynamics/components/background-control-dynamic-tag.tsx":function(e,t,n){n.r(t),n.d(t,{BackgroundControlDynamicTagIcon:function(){return BackgroundControlDynamicTagIcon},BackgroundControlDynamicTagLabel:function(){return BackgroundControlDynamicTagLabel}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-props"),a=n("@elementor/icons"),i=n("./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-dynamic-tag.ts");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const BackgroundControlDynamicTagIcon=()=>o.createElement(a.DatabaseIcon,{fontSize:"tiny"}),BackgroundControlDynamicTagLabel=({value:e})=>{const t=(0,r.useBoundProp)(s.backgroundImageOverlayPropTypeUtil);return o.createElement(r.PropProvider,_extends({},t,{value:e.value}),o.createElement(r.PropKeyProvider,{bind:"image"},o.createElement(Wrapper,{rawValue:e.value})))},Wrapper=({rawValue:e})=>{const{propType:t}=(0,r.useBoundProp)(),n=t.prop_types["background-image-overlay"];return o.createElement(r.PropProvider,{propType:n.shape.image,value:e,setValue:()=>{}},o.createElement(r.PropKeyProvider,{bind:"src"},o.createElement(Content,{rawValue:e.image})))},Content=({rawValue:e})=>{const t=e.value.src,n=(0,i.useDynamicTag)(t.value.name||"");return o.createElement(o.Fragment,null,n?.label)}},"./packages/packages/core/editor-editing-panel/src/dynamics/components/dynamic-selection-control.tsx":function(e,t,n){n.r(t),n.d(t,{DynamicSelectionControl:function(){return DynamicSelectionControl},DynamicSettingsPopover:function(){return DynamicSettingsPopover}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-ui"),a=n("@elementor/icons"),i=n("@elementor/ui"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/components/popover-body.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/controls-registry/control.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/controls-registry/controls-registry.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/hooks/use-persist-dynamic-value.ts"),g=n("./packages/packages/core/editor-editing-panel/src/dynamics/dynamic-control.tsx"),m=n("./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-dynamic-tag.ts"),y=n("./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts"),f=n("./packages/packages/core/editor-editing-panel/src/dynamics/components/dynamic-selection.tsx");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const k="tiny",DynamicSelectionControl=()=>{const{setValue:e}=(0,r.useBoundProp)(),{bind:t,value:n}=(0,r.useBoundProp)(y.dynamicPropTypeUtil),[s]=(0,u.usePersistDynamicValue)(t),p=(0,i.usePopupState)({variant:"popover"}),{name:d=""}=n,g=(0,m.useDynamicTag)(d);if(!g)throw new Error(`Dynamic tag ${d} not found`);return o.createElement(i.Box,null,o.createElement(i.UnstableTag,_extends({fullWidth:!0,showActionsOnHover:!0,label:g.label,startIcon:o.createElement(a.DatabaseIcon,{fontSize:k})},(0,i.bindTrigger)(p),{actions:o.createElement(o.Fragment,null,o.createElement(DynamicSettingsPopover,{dynamicTag:g}),o.createElement(i.IconButton,{size:k,onClick:()=>{e(s??null)},"aria-label":(0,l.__)("Remove dynamic value","elementor")},o.createElement(a.XIcon,{fontSize:k})))})),o.createElement(i.Popover,_extends({disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:1}}},(0,i.bindPopover)(p)),o.createElement(c.PopoverBody,null,o.createElement(f.DynamicSelection,{close:p.close}))))},DynamicSettingsPopover=({dynamicTag:e})=>{const t=(0,i.usePopupState)({variant:"popover"});return!!e.atomic_controls.length?o.createElement(o.Fragment,null,o.createElement(i.IconButton,_extends({size:k},(0,i.bindTrigger)(t),{"aria-label":(0,l.__)("Settings","elementor")}),o.createElement(a.SettingsIcon,{fontSize:k})),o.createElement(i.Popover,_extends({disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:1}}},(0,i.bindPopover)(t)),o.createElement(c.PopoverBody,null,o.createElement(s.PopoverHeader,{title:e.label,onClose:t.close,icon:o.createElement(a.DatabaseIcon,{fontSize:k})}),o.createElement(DynamicSettings,{controls:e.atomic_controls})))):null},DynamicSettings=({controls:e})=>{const t=e.filter(({type:e})=>"section"===e),{getTabsProps:n,getTabProps:r,getTabPanelProps:s}=(0,i.useTabs)(0);return t.length?o.createElement(o.Fragment,null,o.createElement(i.Tabs,_extends({size:"small",variant:"fullWidth"},n()),t.map(({value:e},t)=>o.createElement(i.Tab,_extends({key:t,label:e.label,sx:{px:1,py:.5}},r(t))))),o.createElement(i.Divider,null),t.map(({value:e},t)=>o.createElement(i.TabPanel,_extends({key:t,sx:{flexGrow:1,py:0,overflowY:"auto"}},s(t)),o.createElement(i.Stack,{p:2,gap:2},e.items.map(e=>"control"===e.type?o.createElement(Control,{key:e.value.bind,control:e.value}):null))))):null},Control=({control:e})=>(0,d.getControl)(e.type)?o.createElement(g.DynamicControl,{bind:e.bind},o.createElement(i.Grid,{container:!0,gap:.75},e.label?o.createElement(i.Grid,{item:!0,xs:12},o.createElement(r.ControlFormLabel,null,e.label)):null,o.createElement(i.Grid,{item:!0,xs:12},o.createElement(p.Control,{type:e.type,props:e.props})))):null},"./packages/packages/core/editor-editing-panel/src/dynamics/components/dynamic-selection.tsx":function(e,t,n){n.r(t),n.d(t,{DynamicSelection:function(){return DynamicSelection}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-ui"),a=n("@elementor/icons"),i=n("@elementor/ui"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/components/popover-body.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/hooks/use-persist-dynamic-value.ts"),d=n("./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-prop-dynamic-tags.ts"),u=n("./packages/packages/core/editor-editing-panel/src/dynamics/sync/get-atomic-dynamic-tags.ts"),g=n("./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts");const DynamicSelection=({close:e})=>{const[t,n]=(0,o.useState)(""),{groups:d}=(0,u.getAtomicDynamicTags)()||{},m=(0,i.useTheme)(),{value:y}=(0,r.useBoundProp)(),{bind:f,value:k,setValue:x}=(0,r.useBoundProp)(g.dynamicPropTypeUtil),[,v]=(0,p.usePersistDynamicValue)(f),h=!!k,b=useFilteredOptions(t),E=!b.length&&!t.trim(),S=b.flatMap(([e,t])=>[{type:"category",value:e,label:d?.[e]?.title||e},...t.map(e=>({type:"item",value:e.value,label:e.label}))]);return o.createElement(c.PopoverBody,null,o.createElement(s.PopoverHeader,{title:(0,l.__)("Dynamic tags","elementor"),onClose:e,icon:o.createElement(a.DatabaseIcon,{fontSize:"tiny"})}),E?o.createElement(NoDynamicTags,null):o.createElement(o.Fragment,null,o.createElement(s.PopoverSearch,{value:t,onSearch:e=>{n(e)},placeholder:(0,l.__)("Search dynamic tags…","elementor")}),o.createElement(i.Divider,null),o.createElement(s.PopoverMenuList,{items:S,onSelect:t=>{h||v(y);const n=b.flatMap(([,e])=>e).find(e=>e.value===t);x({name:t,settings:{label:n?.label}}),e()},onClose:e,selectedValue:k?.name,itemStyle:e=>"item"===e.type?{paddingInlineStart:m.spacing(3.5)}:{},noResultsComponent:o.createElement(NoResults,{searchValue:t,onClear:()=>n("")})})))},NoResults=({searchValue:e,onClear:t})=>o.createElement(i.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},o.createElement(a.DatabaseIcon,{fontSize:"large"}),o.createElement(i.Typography,{align:"center",variant:"subtitle2"},(0,l.__)("Sorry, nothing matched","elementor"),o.createElement("br",null),"“",e,"”."),o.createElement(i.Typography,{align:"center",variant:"caption",sx:{display:"flex",flexDirection:"column"}},(0,l.__)("Try something else.","elementor"),o.createElement(i.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,l.__)("Clear & try again","elementor")))),NoDynamicTags=()=>o.createElement(o.Fragment,null,o.createElement(i.Divider,null),o.createElement(i.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},o.createElement(a.DatabaseIcon,{fontSize:"large"}),o.createElement(i.Typography,{align:"center",variant:"subtitle2"},(0,l.__)("Streamline your workflow with dynamic tags","elementor")),o.createElement(i.Typography,{align:"center",variant:"caption"},(0,l.__)("You'll need Elementor Pro to use this feature.","elementor")))),useFilteredOptions=e=>[...(0,d.usePropDynamicTags)().reduce((t,{name:n,label:o,group:r})=>o.toLowerCase().includes(e.trim().toLowerCase())?(t.has(r)||t.set(r,[]),t.get(r)?.push({label:o,value:n}),t):t,new Map)]},"./packages/packages/core/editor-editing-panel/src/dynamics/dynamic-control.tsx":function(e,t,n){n.r(t),n.d(t,{DynamicControl:function(){return DynamicControl}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("./packages/packages/core/editor-editing-panel/src/controls-registry/create-top-level-object-type.ts"),a=n("./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-dynamic-tag.ts"),i=n("./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts");const DynamicControl=({bind:e,children:t})=>{const{value:n,setValue:l}=(0,r.useBoundProp)(i.dynamicPropTypeUtil),{name:c="",settings:p}=n??{},d=(0,a.useDynamicTag)(c);if(!d)throw new Error(`Dynamic tag ${c} not found`);const u=d.props_schema[e],g=u?.default,m=p?.[e]??g,y=(0,s.createTopLevelOjectType)({schema:d.props_schema});return o.createElement(r.PropProvider,{propType:y,setValue:e=>{l({name:c,settings:{...p,...e}})},value:{[e]:m}},o.createElement(r.PropKeyProvider,{bind:e},t))}},"./packages/packages/core/editor-editing-panel/src/dynamics/dynamic-transformer.ts":function(e,t,n){n.r(t),n.d(t,{dynamicTransformer:function(){return a}});var o=n("@elementor/editor-canvas"),r=n("@elementor/editor-props"),s=n("./packages/packages/core/editor-editing-panel/src/dynamics/errors.ts");const a=(0,o.createTransformer)(e=>e.name?function getDynamicValue(e,t){const n=window,{dynamicTags:o}=n.elementor??{};if(!o)throw new s.DynamicTagsManagerNotFoundError;const getTagValue=()=>{const n=o.createTag("v4-dynamic-tag",e,t);return n?o.loadTagDataFromCache(n)??null:null},r=getTagValue();if(null!==r)return r;return new Promise(e=>{o.refreshCacheFromServer(()=>{e(getTagValue())})})}(e.name,function simpleTransform(e){const t=Object.entries(e).map(([e,t])=>[e,(0,r.isTransformable)(t)?t.value:t]);return Object.fromEntries(t)}(e.settings??{})):null)},"./packages/packages/core/editor-editing-panel/src/dynamics/errors.ts":function(e,t,n){n.r(t),n.d(t,{DynamicTagsManagerNotFoundError:function(){return r}});var o=n("@elementor/utils");const r=(0,o.createError)({code:"dynamic_tags_manager_not_found",message:"Dynamic tags manager not found"})},"./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-dynamic-tag.ts":function(e,t,n){n.r(t),n.d(t,{useDynamicTag:function(){return useDynamicTag}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-prop-dynamic-tags.ts");const useDynamicTag=e=>{const t=(0,r.usePropDynamicTags)();return(0,o.useMemo)(()=>t.find(t=>t.name===e)??null,[t,e])}},"./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-prop-dynamic-action.tsx":function(e,t,n){n.r(t),n.d(t,{usePropDynamicAction:function(){return usePropDynamicAction}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/icons"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/dynamics/components/dynamic-selection.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts");const usePropDynamicAction=()=>{const{propType:e}=(0,r.useBoundProp)();return{visible:!!e&&(0,l.supportsDynamic)(e),icon:s.DatabaseIcon,title:(0,a.__)("Dynamic tags","elementor"),content:({close:e})=>o.createElement(i.DynamicSelection,{close:e})}}},"./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-prop-dynamic-tags.ts":function(e,t,n){n.r(t),n.d(t,{usePropDynamicTags:function(){return usePropDynamicTags}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("./packages/packages/core/editor-editing-panel/src/dynamics/sync/get-atomic-dynamic-tags.ts"),a=n("./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts");const usePropDynamicTags=()=>{let e=[];const{propType:t}=(0,r.useBoundProp)();if(t){const n=(0,a.getDynamicPropType)(t);e=n?.settings.categories||[]}return(0,o.useMemo)(()=>getDynamicTagsByCategories(e),[e.join()])},getDynamicTagsByCategories=e=>{const t=(0,s.getAtomicDynamicTags)();if(!e.length||!t?.tags)return[];const n=new Set(e);return Object.values(t.tags).filter(e=>e.categories.some(e=>n.has(e)))}},"./packages/packages/core/editor-editing-panel/src/dynamics/init.ts":function(e,t,n){n.r(t),n.d(t,{init:function(){return init}});var o=n("@elementor/editor-canvas"),r=n("@elementor/editor-controls"),s=n("./packages/packages/core/editor-editing-panel/src/control-replacement.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/controls-actions.ts"),i=n("./packages/packages/core/editor-editing-panel/src/dynamics/components/background-control-dynamic-tag.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/dynamics/components/dynamic-selection-control.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/dynamics/dynamic-transformer.ts"),p=n("./packages/packages/core/editor-editing-panel/src/dynamics/hooks/use-prop-dynamic-action.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts");const{registerPopoverAction:u}=a.controlActionsMenu,init=()=>{(0,s.registerControlReplacement)({component:l.DynamicSelectionControl,condition:({value:e})=>(0,d.isDynamicPropValue)(e)}),(0,r.injectIntoRepeaterItemLabel)({id:"dynamic-background-image",condition:({value:e})=>(0,d.isDynamicPropValue)(e.value?.image?.value?.src),component:i.BackgroundControlDynamicTagLabel}),(0,r.injectIntoRepeaterItemIcon)({id:"dynamic-background-image",condition:({value:e})=>(0,d.isDynamicPropValue)(e.value?.image?.value?.src),component:i.BackgroundControlDynamicTagIcon}),u({id:"dynamic-tags",useProps:p.usePropDynamicAction}),o.styleTransformersRegistry.register("dynamic",c.dynamicTransformer),o.settingsTransformersRegistry.register("dynamic",c.dynamicTransformer)}},"./packages/packages/core/editor-editing-panel/src/dynamics/sync/get-atomic-dynamic-tags.ts":function(e,t,n){n.r(t),n.d(t,{getAtomicDynamicTags:function(){return getAtomicDynamicTags}});var o=n("./packages/packages/core/editor-editing-panel/src/dynamics/sync/get-elementor-config.ts");const getAtomicDynamicTags=()=>{const{atomicDynamicTags:e}=(0,o.getElementorConfig)();return e?{tags:e.tags,groups:e.groups}:null}},"./packages/packages/core/editor-editing-panel/src/dynamics/sync/get-elementor-config.ts":function(e,t,n){n.r(t),n.d(t,{getElementorConfig:function(){return getElementorConfig}});const getElementorConfig=()=>{const e=window;return e.elementor?.config??{}}},"./packages/packages/core/editor-editing-panel/src/dynamics/utils.ts":function(e,t,n){n.r(t),n.d(t,{dynamicPropTypeUtil:function(){return a},getDynamicPropType:function(){return getDynamicPropType},isDynamicPropValue:function(){return isDynamicPropValue},supportsDynamic:function(){return supportsDynamic}});var o=n("@elementor/editor-props"),r=n("@elementor/schema");const s="dynamic",getDynamicPropType=e=>{const t="union"===e.kind&&e.prop_types[s];return t&&t.key===s?t:null},isDynamicPropValue=e=>(0,o.isTransformable)(e)&&e.$$type===s,supportsDynamic=e=>!!getDynamicPropType(e),a=(0,o.createPropUtils)(s,r.z.strictObject({name:r.z.string(),settings:r.z.any().optional()}))},"./packages/packages/core/editor-editing-panel/src/errors.ts":function(e,t,n){n.r(t),n.d(t,{ControlTypeNotFoundError:function(){return r},StyleNotFoundUnderProviderError:function(){return i},StylesProviderCannotUpdatePropsError:function(){return a},StylesProviderNotFoundError:function(){return s}});var o=n("@elementor/utils");const r=(0,o.createError)({code:"control_type_not_found",message:"Control type not found."}),s=(0,o.createError)({code:"provider_not_found",message:"Styles provider not found."}),a=(0,o.createError)({code:"provider_cannot_update_props",message:"Styles provider doesn't support updating props."}),i=(0,o.createError)({code:"style_not_found_under_provider",message:"Style not found under the provider."})},"./packages/packages/core/editor-editing-panel/src/hooks/use-active-style-def-id.ts":function(e,t,n){n.r(t),n.d(t,{useActiveStyleDefId:function(){return useActiveStyleDefId}});var o=n("@elementor/editor-elements"),r=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/hooks/use-state-by-element.ts");function useActiveStyleDefId(e){const[t,n]=(0,s.useStateByElement)("active-style-id",null),a=function useAppliedClassesIds(e){const{element:t}=(0,r.useElement)();return(0,o.useElementSetting)(t.id,e)}(e)?.value||[],i=function useFirstAppliedClass(e){const{element:t}=(0,r.useElement)(),n=(0,o.getElementStyles)(t.id)??{};return Object.values(n).find(t=>e.includes(t.id))}(a),l=function useActiveAndAppliedClassId(e,t){const n=!!e&&t.includes(e);return n?e:null}(t,a);return[l||i?.id||null,n]}},"./packages/packages/core/editor-editing-panel/src/hooks/use-computed-style.ts":function(e,t,n){n.r(t),n.d(t,{useComputedStyle:function(){return useComputedStyle}});var o=n("@elementor/editor-v1-adapters");function useComputedStyle(e){return(0,o.__privateUseListenTo)([(0,o.windowEvent)("elementor/device-mode/change"),(0,o.commandEndEvent)("document/elements/reset-style"),(0,o.commandEndEvent)("document/elements/settings"),(0,o.commandEndEvent)("document/elements/paste-style")],()=>{if(!e)return null;const t=window,n=t.elementor?.getContainer?.(e);if(!n?.view?.el)return null;return window.getComputedStyle(n.view.el)})}},"./packages/packages/core/editor-editing-panel/src/hooks/use-default-panel-settings.ts":function(e,t,n){n.r(t),n.d(t,{useDefaultPanelSettings:function(){return useDefaultPanelSettings}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx");const s={defaultSectionsExpanded:{settings:["Content","Settings"],style:[]},defaultTab:"settings"},a=(0,o.createContext)({"e-div-block":{defaultSectionsExpanded:s.defaultSectionsExpanded,defaultTab:"style"},"e-flexbox":{defaultSectionsExpanded:s.defaultSectionsExpanded,defaultTab:"style"},"e-divider":{defaultSectionsExpanded:s.defaultSectionsExpanded,defaultTab:"style"}}),useDefaultPanelSettings=()=>{const{element:e}=(0,r.useElement)();return(0,o.useContext)(a)[e.type]||s}},"./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts":function(e,t,n){n.r(t),n.d(t,{useDirection:function(){return useDirection}});var o=n("@elementor/ui"),r=n("./packages/packages/core/editor-editing-panel/src/sync/get-elementor-globals.ts");function useDirection(){const e="rtl"===(0,o.useTheme)().direction;return{isSiteRtl:!!(0,r.getElementorFrontendConfig)()?.is_rtl,isUiRtl:e}}},"./packages/packages/core/editor-editing-panel/src/hooks/use-open-editor-panel.ts":function(e,t,n){n.r(t),n.d(t,{useOpenEditorPanel:function(){return useOpenEditorPanel}});var o=n("react"),r=n("@elementor/editor-v1-adapters"),s=n("./packages/packages/core/editor-editing-panel/src/panel.ts"),a=n("./packages/packages/core/editor-editing-panel/src/sync/is-atomic-widget-selected.ts");const useOpenEditorPanel=()=>{const{open:e}=(0,s.usePanelActions)();(0,o.useEffect)(()=>(0,r.__privateListenTo)((0,r.commandStartEvent)("panel/editor/open"),()=>{(0,a.isAtomicWidgetSelected)()&&e()}),[])}},"./packages/packages/core/editor-editing-panel/src/hooks/use-persist-dynamic-value.ts":function(e,t,n){n.r(t),n.d(t,{usePersistDynamicValue:function(){return usePersistDynamicValue}});var o=n("@elementor/session"),r=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx");const usePersistDynamicValue=e=>{const{element:t}=(0,r.useElement)(),n=`dynamic/non-dynamic-values-history/${t.id}/${e}`;return(0,o.useSessionStorage)(n)}},"./packages/packages/core/editor-editing-panel/src/hooks/use-state-by-element.ts":function(e,t,n){n.r(t),n.d(t,{useStateByElement:function(){return useStateByElement}});var o=n("react"),r=n("@elementor/session"),s=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx");const useStateByElement=(e,t)=>{const{element:n}=(0,s.useElement)(),a=`elementor/editor-state/${n.id}/${e}`,i=(0,r.getSessionStorageItem)(a),[l,c]=(0,o.useState)(i??t);return[l,e=>{(0,r.setSessionStorageItem)(a,e),c(e)}]}},"./packages/packages/core/editor-editing-panel/src/hooks/use-styles-field.ts":function(e,t,n){n.r(t),n.d(t,{useStylesField:function(){return useStylesField}});var o=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-fields.ts");function useStylesField(e,t){const{values:n,setValues:r,canEdit:s}=(0,o.useStylesFields)([e]);return{value:n?.[e]??null,setValue:n=>{r({[e]:n},t)},canEdit:s}}},"./packages/packages/core/editor-editing-panel/src/hooks/use-styles-fields.ts":function(e,t,n){n.r(t),n.d(t,{useStylesFields:function(){return useStylesFields}});var o=n("react"),r=n("@elementor/editor-elements"),s=n("@elementor/editor-styles"),a=n("@elementor/editor-styles-repository"),i=n("@elementor/editor-v1-adapters"),l=n("@wordpress/i18n"),c=n("./packages/packages/core/editor-editing-panel/src/contexts/classes-prop-context.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/contexts/element-context.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/errors.ts"),g=n("./packages/packages/core/editor-editing-panel/src/hooks/use-styles-rerender.ts");const m=800;function useStylesFields(e){const{element:{id:t}}=(0,p.useElement)(),{id:n,meta:l,provider:k,canEdit:x}=(0,d.useStyle)(),v=function useUndoableUpdateStyle({elementId:e,meta:{breakpoint:t,state:n}}){const l=(0,c.useClassesProp)();return(0,o.useMemo)(()=>{const o={breakpoint:t,state:n},c={elementId:e,classesProp:l,meta:o,label:a.ELEMENTS_STYLES_RESERVED_LABEL};return(0,i.undoable)({do:e=>shouldCreateNewLocalStyle(e)?createLocalStyle(e):updateStyleProps(e),undo:(e,t)=>shouldCreateNewLocalStyle(e)?undoCreateLocalStyle(e,t):undoUpdateStyleProps(e,t),redo:(e,t)=>shouldCreateNewLocalStyle(e)?createLocalStyle(e,t):updateStyleProps(e)},{title:({provider:t,styleId:n})=>{let o;return o=isLocalStyle(t,n)?f.title({elementId:e}):y.title({provider:t}),o},subtitle:({provider:t,styleId:n,propDisplayName:o})=>{let r;return r=isLocalStyle(t,n)?f.subtitle({propDisplayName:o}):y.subtitle({provider:t,styleId:n,elementId:e,propDisplayName:o}),r},debounce:{wait:m}});function shouldCreateNewLocalStyle(e){return!e.styleId&&!e.provider}function createLocalStyle({props:e},t){return{createdStyleId:(0,r.createElementStyle)({...c,props:e,styleId:t?.createdStyleId})}}function undoCreateLocalStyle(t,{createdStyleId:n}){(0,r.deleteElementStyle)(e,n)}function updateStyleProps({provider:t,styleId:n,props:r}){if(!t.actions.updateProps)throw new u.StylesProviderCannotUpdatePropsError({context:{providerKey:t.getKey()}});const a=function getCurrentProps(e,t){if(!e)return{};const n=(0,s.getVariantByMeta)(e,t),o=n?.props??{};return structuredClone(o)}(t.actions.get(n,{elementId:e}),o);return t.actions.updateProps({id:n,meta:o,props:r},{elementId:e}),{styleId:n,provider:t,prevProps:a}}function undoUpdateStyleProps(t,{styleId:n,provider:r,prevProps:s}){r.actions.updateProps?.({id:n,meta:o,props:s},{elementId:e})}},[e,t,n,l])}({elementId:t,meta:l});(0,g.useStylesRerender)();const h=function getProps({styleId:e,elementId:t,provider:n,meta:o,propNames:r}){if(!n||!e)return null;const a=n.actions.get(e,{elementId:t});if(!a)throw new u.StyleNotFoundUnderProviderError({context:{styleId:e,providerKey:n.getKey()}});const i=(0,s.getVariantByMeta)(a,o);return Object.fromEntries(r.map(e=>[e,i?.props[e]??null]))}({elementId:t,styleId:n,provider:k,meta:l,propNames:e});return{values:h,setValues:(e,{history:{propDisplayName:t}})=>{v(n?{styleId:n,provider:k,props:e,propDisplayName:t}:{styleId:null,provider:null,props:e,propDisplayName:t})},canEdit:x}}const y={title:({provider:e})=>{const t=e.labels?.singular;return t?function capitalize(e){return e.charAt(0).toUpperCase()+e.slice(1)}(t):(0,l.__)("Style","elementor")},subtitle:({provider:e,styleId:t,elementId:n,propDisplayName:o})=>{const r=e.actions.get(t,{elementId:n})?.label;if(!r)throw new Error(`Style ${t} not found`);return(0,l.__)("%s$1 %s$2 edited","elementor").replace("%s$1",r).replace("%s$2",o)}},f={title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:({propDisplayName:e})=>(0,l.__)("%s edited","elementor").replace("%s",e)};const isLocalStyle=(e,t)=>!e||!t||(0,a.isElementsStylesProvider)(e.getKey())},"./packages/packages/core/editor-editing-panel/src/hooks/use-styles-rerender.ts":function(e,t,n){n.r(t),n.d(t,{useStylesRerender:function(){return useStylesRerender}});var o=n("react"),r=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx");const useStylesRerender=()=>{const{provider:e}=(0,r.useStyle)(),[,t]=(0,o.useReducer)(e=>!e,!1);(0,o.useEffect)(()=>e?.subscribe(t),[e])}},"./packages/packages/core/editor-editing-panel/src/init.ts":function(e,t,n){n.r(t),n.d(t,{init:function(){return init}});var o=n("@elementor/editor"),r=n("@elementor/editor-panels"),s=n("@elementor/editor-v1-adapters"),a=n("./packages/packages/core/editor-editing-panel/src/components/editing-panel-hooks.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/dynamics/init.ts"),l=n("./packages/packages/core/editor-editing-panel/src/panel.ts"),c=n("./packages/packages/core/editor-editing-panel/src/reset-style-props.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/init.ts"),d=n("./packages/packages/core/editor-editing-panel/src/sync/is-atomic-widget-selected.ts");function init(){(0,r.__registerPanel)(l.panel),blockV1Panel(),(0,o.injectIntoLogic)({id:"editing-panel-hooks",component:a.EditingPanelHooks}),(0,i.init)(),(0,p.init)(),(0,c.initResetStyleProps)()}const blockV1Panel=()=>{(0,s.blockCommand)({command:"panel/editor/open",condition:d.isAtomicWidgetSelected})}},"./packages/packages/core/editor-editing-panel/src/panel.ts":function(e,t,n){n.r(t),n.d(t,{panel:function(){return s},usePanelActions:function(){return a},usePanelStatus:function(){return i}});var o=n("@elementor/editor-panels"),r=n("./packages/packages/core/editor-editing-panel/src/components/editing-panel.tsx");const{panel:s,usePanelActions:a,usePanelStatus:i}=(0,o.__createPanel)({id:"editing-panel",component:r.EditingPanel})},"./packages/packages/core/editor-editing-panel/src/popover-action.tsx":function(e,t,n){n.r(t),n.d(t,{PopoverAction:function(){return PopoverAction},useFloatingActionsPopover:function(){return useFloatingActionsPopover}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/ui");function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(null,arguments)}const a="tiny";function PopoverAction({title:e,visible:t=!0,icon:n,content:r}){const{popupState:i,triggerProps:l,popoverProps:c}=useFloatingActionsPopover();return t?o.createElement(o.Fragment,null,o.createElement(s.Tooltip,{placement:"top",title:e},o.createElement(s.IconButton,_extends({"aria-label":e,size:a},l),o.createElement(n,{fontSize:a}))),o.createElement(s.Popover,_extends({disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:2.5}}},c),o.createElement(r,{close:i.close}))):null}function useFloatingActionsPopover(){const{setOpen:e}=(0,r.useFloatingActionsBar)(),t=(0,s.usePopupState)({variant:"popover"}),n=(0,s.bindTrigger)(t),o=(0,s.bindPopover)(t);return{popupState:{...t,close:()=>{t.close(),e(!1)}},triggerProps:{...n,onClick:t=>{n.onClick(t),e(!0)}},popoverProps:{...o,onClose:()=>{o.onClose(),e(!1)}}}}},"./packages/packages/core/editor-editing-panel/src/provider-colors-registry.ts":function(e,t,n){n.r(t),n.d(t,{getStyleProviderColors:function(){return getStyleProviderColors},registerStyleProviderToColors:function(){return registerStyleProviderToColors}});const o={name:"default",getThemeColor:null},r=new Map,registerStyleProviderToColors=(e,t)=>{r.set(e,t)},getStyleProviderColors=e=>r.get(e)??o},"./packages/packages/core/editor-editing-panel/src/reset-style-props.tsx":function(e,t,n){n.r(t),n.d(t,{initResetStyleProps:function(){return initResetStyleProps},useResetStyleValueProps:function(){return useResetStyleValueProps}});var o=n("@elementor/editor-controls"),r=n("@elementor/icons"),s=n("@wordpress/i18n"),a=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/controls-actions.ts");const{registerAction:l}=i.controlActionsMenu;function initResetStyleProps(){l({id:"reset-style-value",useProps:useResetStyleValueProps})}function useResetStyleValueProps(){const e=(0,a.useIsStyle)(),{value:t,setValue:n,path:i}=(0,o.useBoundProp)();return{visible:e&&null!=t&&i.length<=2,title:(0,s.__)("Clear","elementor"),icon:r.BrushBigIcon,onClick:()=>n(null)}}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/action-icons.tsx":function(e,t,n){n.r(t),n.d(t,{ActionIcons:function(){return ActionIcons}});var o=n("react"),r=n("@elementor/ui");const ActionIcons=()=>o.createElement(r.Box,{display:"flex",gap:.5,alignItems:"center"})},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/breakpoint-icon.tsx":function(e,t,n){n.r(t),n.d(t,{BreakpointIcon:function(){return BreakpointIcon}});var o=n("react"),r=n("@elementor/editor-responsive"),s=n("@elementor/icons"),a=n("@elementor/ui");const i={widescreen:s.WidescreenIcon,desktop:s.DesktopIcon,laptop:s.LaptopIcon,tablet_extra:s.TabletLandscapeIcon,tablet:s.TabletPortraitIcon,mobile_extra:s.MobileLandscapeIcon,mobile:s.MobilePortraitIcon},BreakpointIcon=({breakpoint:e})=>{const t=(0,r.useBreakpoints)(),n=e||"desktop",s=i[n];if(!s)return null;const l=t.find(e=>e.id===n)?.label;return o.createElement(a.Tooltip,{title:l,placement:"top"},o.createElement(s,{fontSize:"tiny",sx:{mt:"2px"}}))}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/index.ts":function(e,t,n){n.r(t),n.d(t,{ActionIcons:function(){return a.ActionIcons},BreakpointIcon:function(){return o.BreakpointIcon},LabelChip:function(){return r.LabelChip},ValueComponent:function(){return s.ValueComponent}});var o=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/breakpoint-icon.tsx"),r=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/label-chip.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/value-component.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/action-icons.tsx")},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/label-chip.tsx":function(e,t,n){n.r(t),n.d(t,{LabelChip:function(){return LabelChip}});var o=n("react"),r=n("@elementor/editor-styles-repository"),s=n("@elementor/icons"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/utils/get-styles-provider-color.ts");const c="tiny",LabelChip=({displayLabel:e,provider:t})=>{const n=t===r.ELEMENTS_BASE_STYLES_PROVIDER_KEY?o.createElement(a.Tooltip,{title:(0,i.__)("Inherited from base styles","elementor"),placement:"top"},o.createElement(s.InfoCircleIcon,{fontSize:c})):void 0;return o.createElement(a.Chip,{label:e,size:c,color:(0,l.getStylesProviderColorName)(t),variant:"standard",state:"enabled",icon:n,sx:e=>({lineHeight:1,flexWrap:"nowrap",alignItems:"center",borderRadius:.75*e.shape.borderRadius+"px",flexDirection:"row-reverse",".MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}})})}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/value-component.tsx":function(e,t,n){n.r(t),n.d(t,{ValueComponent:function(){return ValueComponent}});var o=n("react"),r=n("@elementor/ui");const ValueComponent=({index:e,value:t})=>o.createElement(r.Typography,{variant:"caption",color:"text.tertiary",sx:{mt:"1px",textDecoration:0===e?"none":"line-through",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},t)},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/styles-inheritance-indicator.tsx":function(e,t,n){n.r(t),n.d(t,{StylesInheritanceIndicator:function(){return StylesInheritanceIndicator}});var o=n("react"),r=n("@elementor/editor-controls"),s=n("@elementor/editor-props"),a=n("@elementor/editor-styles-repository"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/components/style-indicator.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/contexts/styles-inheritance-context.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/utils/get-styles-provider-color.ts"),u=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/utils.ts"),g=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/styles-inheritance-infotip.tsx");const m=["box-shadow","background-overlay","filter","backdrop-filter","transform"],StylesInheritanceIndicator=()=>{const{path:e,propType:t}=(0,r.useBoundProp)(),n=(0,p.useStylesInheritanceChain)(e);if(!e||!n.length)return null;const s=e.some(e=>m.includes(e));return o.createElement(Indicator,{inheritanceChain:n,path:e,propType:t,isDisabled:s})},Indicator=({inheritanceChain:e,path:t,propType:n,isDisabled:r})=>{const{id:i,provider:p,meta:m}=(0,c.useStyle)(),y=i?(0,u.getValueFromInheritanceChain)(e,i,m):null,f=!(0,s.isEmpty)(y?.value),[k]=e;if(k.provider===a.ELEMENTS_BASE_STYLES_PROVIDER_KEY)return null;const x=y===k,v=getLabel({isFinalValue:x,hasValue:f}),h={getColor:x&&p?(0,d.getStylesProviderThemeColor)(p.getKey()):void 0,isOverridden:!(!f||x)||void 0};return o.createElement(g.StylesInheritanceInfotip,{inheritanceChain:e,path:t,propType:n,label:v,isDisabled:r},o.createElement(l.StyleIndicator,h))},getLabel=({isFinalValue:e,hasValue:t})=>e?(0,i.__)("This is the final value","elementor"):t?(0,i.__)("This value is overridden by another style","elementor"):(0,i.__)("This has value from another style","elementor")},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/styles-inheritance-infotip.tsx":function(e,t,n){n.r(t),n.d(t,{StylesInheritanceInfotip:function(){return StylesInheritanceInfotip}});var o=n("react"),r=n("@elementor/editor-canvas"),s=n("@elementor/editor-ui"),a=n("@elementor/ui"),i=n("@wordpress/i18n"),l=n("./packages/packages/core/editor-editing-panel/src/contexts/section-context.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts"),p=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/hooks/use-normalized-inheritance-chain-items.tsx"),d=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/styles-inheritance-transformers-registry.tsx"),u=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/infotip/index.ts");const StylesInheritanceInfotip=({inheritanceChain:e,propType:t,path:n,label:c,children:g,isDisabled:m})=>{const[y,f]=(0,o.useState)(!1),closeInfotip=()=>{m||f(!1)},k=n.join("."),x=(0,l.useSectionWidth)(),v=(0,o.useMemo)(()=>(0,r.createPropsResolver)({transformers:d.stylesInheritanceTransformersRegistry,schema:{[k]:t}}),[k,t]),h=(0,p.useNormalizedInheritanceChainItems)(e,k,v),b=o.createElement(a.ClickAwayListener,{onClickAway:closeInfotip},o.createElement(a.Card,{elevation:0,sx:{width:x-32+"px",maxWidth:496,overflowX:"hidden"}},o.createElement(a.CardContent,{sx:{display:"flex",gap:.5,flexDirection:"column",p:0,"&:last-child":{pb:0}}},o.createElement(s.PopoverHeader,{title:(0,i.__)("Style origin","elementor"),onClose:closeInfotip}),o.createElement(a.Stack,{gap:1.5,sx:{pl:2,pr:1,pb:2,overflowX:"hidden",overflowY:"auto"},role:"list"},h.map((e,t)=>o.createElement(a.Box,{key:e.id,display:"flex",gap:.5,role:"listitem","aria-label":(0,i.__)("Inheritance item: %s","elementor").replace("%s",e.displayLabel)},o.createElement(a.Box,{display:"flex",gap:.5,sx:{flexWrap:"wrap",width:"100%"}},o.createElement(u.BreakpointIcon,{breakpoint:e.breakpoint}),o.createElement(u.LabelChip,{displayLabel:e.displayLabel,provider:e.provider}),o.createElement(u.ValueComponent,{index:t,value:e.value})),o.createElement(u.ActionIcons,null)))))));return m?o.createElement(a.Box,{sx:{display:"inline-flex"}},g):o.createElement(TooltipOrInfotip,{showInfotip:y,onClose:closeInfotip,infotipContent:b,isDisabled:m},o.createElement(a.IconButton,{onClick:()=>{m||f(e=>!e)},"aria-label":c,sx:{my:"-1px"},disabled:m},g))};function TooltipOrInfotip({children:e,showInfotip:t,onClose:n,infotipContent:r,isDisabled:s}){const l=(0,c.useDirection)().isSiteRtl?9999999:-9999999;return s?o.createElement(a.Box,{sx:{display:"inline-flex"}},e):t?o.createElement(o.Fragment,null,o.createElement(a.Backdrop,{open:t,onClick:n,sx:{backgroundColor:"transparent",zIndex:e=>e.zIndex.modal-1}}),o.createElement(a.Infotip,{placement:"top",content:r,open:t,onClose:n,disableHoverListener:!0,componentsProps:{tooltip:{sx:{mx:2}}},slotProps:{popper:{modifiers:[{name:"offset",options:{offset:[l,0]}}]}}},e)):o.createElement(a.Tooltip,{title:(0,i.__)("Style origin","elementor"),placement:"top"},e)}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/styles-inheritance-section-indicators.tsx":function(e,t,n){n.r(t),n.d(t,{StylesInheritanceSectionIndicators:function(){return StylesInheritanceSectionIndicators}});var o=n("react"),r=n("@elementor/editor-styles-repository"),s=n("@elementor/ui"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/components/style-indicator.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/contexts/styles-inheritance-context.tsx"),p=n("./packages/packages/core/editor-editing-panel/src/utils/get-styles-provider-color.ts");const StylesInheritanceSectionIndicators=({fields:e})=>{const{id:t,meta:n,provider:d}=(0,l.useStyle)(),u=(0,c.useStylesInheritanceSnapshot)(),g=Object.fromEntries(Object.entries(u??{}).filter(([t])=>e.includes(t))),{hasValues:m,hasOverrides:y}=function getIndicators(e,t,n){let o=!1,r=!1;return Object.values(e).forEach(e=>{const s=function getCurrentStyleFromChain(e,t,n){return e.find(({style:{id:e},variant:{meta:{breakpoint:o,state:r}}})=>e===t&&o===n.breakpoint&&r===n.state)}(e,t,n);if(!s)return;const[a]=e;s===a?o=!0:r=!0}),{hasValues:o,hasOverrides:r}}(g,t??"",n);if(!m&&!y)return null;const f=(0,a.__)("Has effective styles","elementor"),k=(0,a.__)("Has overridden styles","elementor");return o.createElement(s.Tooltip,{title:(0,a.__)("Has styles","elementor"),placement:"top"},o.createElement(s.Stack,{direction:"row",sx:{"& > *":{marginInlineStart:-.25}},role:"list"},m&&d&&o.createElement(i.StyleIndicator,{getColor:(0,p.getStylesProviderThemeColor)(d.getKey()),"data-variant":(0,r.isElementsStylesProvider)(d.getKey())?"local":"global",role:"listitem","aria-label":f}),y&&o.createElement(i.StyleIndicator,{isOverridden:!0,"data-variant":"overridden",role:"listitem","aria-label":k})))}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/components/ui-providers.tsx":function(e,t,n){n.r(t),n.d(t,{UiProviders:function(){return UiProviders}});var o=n("react"),r=n("@elementor/ui"),s=n("./packages/packages/core/editor-editing-panel/src/hooks/use-direction.ts");const UiProviders=({children:e})=>{const{isSiteRtl:t}=(0,s.useDirection)();return o.createElement(r.DirectionProvider,{rtl:t},o.createElement(r.ThemeProvider,null,e))}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/consts.ts":function(e,t,n){n.r(t),n.d(t,{excludePropTypeTransformers:function(){return o}});const o=new Set(["background-color-overlay","background-image-overlay","background-gradient-overlay","gradient-color-stop","color-stop","background-image-position-offset","background-image-size-scale","image-src","image","background-overlay"])},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/create-snapshots-manager.ts":function(e,t,n){n.r(t),n.d(t,{createSnapshotsManager:function(){return createSnapshotsManager}});var o=n("@elementor/editor-props"),r=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/utils.ts");function createSnapshotsManager(e,t){const n=function makeBreakpointsInheritancePaths(e){const t={},traverse=(e,n)=>{const{id:o,children:r}=e;t[o]=n?[...n]:[],r?.forEach(e=>{traverse(e,[...t[o]??[],o])})};return traverse(e),t}(t),o={};return t=>{const{breakpoint:s,state:a}=t,i=(0,r.getStateKey)(a),l=(0,r.getBreakpointKey)(s);if(o[l]?.[i])return o[l][i].snapshot;const c=[...n[l],s];return c.forEach((t,n)=>{const s=n>0?c[n-1]:null;((t,n,s)=>{const a=(0,r.getBreakpointKey)(t),i=(0,r.getStateKey)(s);o[a]||(o[a]={[r.DEFAULT_STATE]:buildStateSnapshotSlot(e({breakpoint:t,state:null}),n,{},null)}),s&&!o[a][i]&&(o[a][i]=buildStateSnapshotSlot(e({breakpoint:t,state:s}),n,o[a],s))})(t,s?o[s]:void 0,a)}),o[l]?.[i]?.snapshot}}function buildStateSnapshotSlot(e,t,n,s){const a=function buildInitialSnapshotFromStyles(e){const t={};return e.forEach(e=>{const{variant:{props:n}}=e;Object.entries(n).forEach(([n,r])=>{const s=(0,o.filterEmptyValues)(r);if(null===s)return;t[n]||(t[n]=[]);const a={...e,value:s};t[n].push(a)})}),{snapshot:t,stateSpecificSnapshot:t}}(e);return s?{snapshot:mergeSnapshots([a.snapshot,t?.[s]?.stateSpecificSnapshot,n[r.DEFAULT_STATE]?.snapshot]),stateSpecificSnapshot:mergeSnapshots([a.stateSpecificSnapshot,t?.[s]?.stateSpecificSnapshot])}:{snapshot:mergeSnapshots([a.snapshot,t?.[r.DEFAULT_STATE]?.snapshot]),stateSpecificSnapshot:void 0}}function mergeSnapshots(e){const t={};return e.filter(Boolean).forEach(e=>Object.entries(e).forEach(([e,n])=>{t[e]||(t[e]=[]),t[e]=t[e].concat(n)})),t}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/create-styles-inheritance.ts":function(e,t,n){n.r(t),n.d(t,{createStylesInheritance:function(){return createStylesInheritance}});var o=n("@elementor/editor-props"),r=n("./packages/packages/core/editor-editing-panel/src/contexts/style-context.tsx"),s=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/create-snapshots-manager.ts"),a=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/utils.ts");function createStylesInheritance(e,t){const n=function buildStyleVariantsByMetaMapping(e){const t={};return e.forEach(e=>{const n=(0,r.getProviderByStyleId)(e.id)?.getKey()??null;e.variants.forEach(o=>{const{meta:r}=o,{state:s,breakpoint:i}=r,l=(0,a.getBreakpointKey)(i),c=(0,a.getStateKey)(s);t[l]||(t[l]={});const p=t[l];p[c]||(p[c]=[]),p[c].push({style:e,variant:o,provider:n})})}),t}(e);return{getSnapshot:(0,s.createSnapshotsManager)(({breakpoint:e,state:t})=>n?.[(0,a.getBreakpointKey)(e)]?.[(0,a.getStateKey)(t)]??[],t),getInheritanceChain:(e,t,n)=>{const[r,...s]=t;let a=e[r]??[];if(s.length>0){const e=getFilterPropType(n,s);a=a.map(({value:t,...n})=>({...n,value:getValueByPath(t,s,e)})).filter(({value:e})=>!(0,o.isEmpty)(e))}return a}}}function getValueByPath(e,t,n){return e&&"object"==typeof e?function shouldUseOriginalValue(e,t){return!!e&&(0,o.isTransformable)(t)&&e.key!==t.$$type}(n,e)?e:t.reduce((e,t)=>e?(0,o.isTransformable)(e)?e.value?.[t]??null:"object"==typeof e?e[t]??null:null:null,e):null}const getFilterPropType=(e,t)=>e&&"union"===e.kind?Object.values(e.prop_types).find(e=>!!t.reduce((e,t)=>{if("object"!==e?.kind)return null;const{shape:n}=e;return n[t]?n[t]:null},e))??null:null},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/hooks/use-normalized-inheritance-chain-items.tsx":function(e,t,n){n.r(t),n.d(t,{normalizeInheritanceItem:function(){return normalizeInheritanceItem},useNormalizedInheritanceChainItems:function(){return useNormalizedInheritanceChainItems}});var o=n("react"),r=n("@elementor/editor-styles-repository"),s=n("@wordpress/i18n");const useNormalizedInheritanceChainItems=(e,t,n)=>{const[a,i]=(0,o.useState)([]);return(0,o.useEffect)(()=>{(async()=>{const o=(await Promise.all(e.filter(({style:e})=>e).map((e,o)=>normalizeInheritanceItem(e,o,t,n)))).map(e=>({...e,displayLabel:r.ELEMENTS_BASE_STYLES_PROVIDER_KEY!==e.provider?e.displayLabel:(0,s.__)("Base","elementor")})).filter(e=>!e.value||""!==e.displayLabel).slice(0,2);i(o)})()},[e,t,n]),a},normalizeInheritanceItem=async(e,t,n,o)=>{const{variant:{meta:{state:r,breakpoint:s}},style:{label:a,id:i}}=e,l=`${a}${r?":"+r:""}`;return{id:i?i+(r??""):t,provider:e.provider||"",breakpoint:s??"desktop",displayLabel:l,value:await getTransformedValue(e,n,o)}},getTransformedValue=async(e,t,n)=>{try{const r=await n({props:{[t]:e.value}}),s=r?.[t]??r;return(0,o.isValidElement)(s)?s:"object"==typeof s?JSON.stringify(s):String(s)}catch{return""}}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/init-styles-inheritance-transformers.ts":function(e,t,n){n.r(t),n.d(t,{initStylesInheritanceTransformers:function(){return initStylesInheritanceTransformers}});var o=n("@elementor/editor-canvas"),r=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/consts.ts"),s=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/styles-inheritance-transformers-registry.tsx"),a=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-color-overlay-transformer.tsx"),i=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-gradient-overlay-transformer.tsx"),l=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-image-overlay-transformer.tsx"),c=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-overlay-transformer.tsx");function initStylesInheritanceTransformers(){const e=o.styleTransformersRegistry.all();Object.entries(e).forEach(([e,t])=>{r.excludePropTypeTransformers.has(e)||s.stylesInheritanceTransformersRegistry.register(e,t)}),s.stylesInheritanceTransformersRegistry.registerFallback((0,o.createTransformer)(e=>e)),function registerCustomTransformers(){s.stylesInheritanceTransformersRegistry.register("background-color-overlay",a.backgroundColorOverlayTransformer),s.stylesInheritanceTransformersRegistry.register("background-gradient-overlay",i.backgroundGradientOverlayTransformer),s.stylesInheritanceTransformersRegistry.register("background-image-overlay",l.backgroundImageOverlayTransformer),s.stylesInheritanceTransformersRegistry.register("background-overlay",c.backgroundOverlayTransformer)}()}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/init.ts":function(e,t,n){n.r(t),n.d(t,{init:function(){return init}});var o=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/init-styles-inheritance-transformers.ts");const init=()=>{(0,o.initStylesInheritanceTransformers)()}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/styles-inheritance-transformers-registry.tsx":function(e,t,n){n.r(t),n.d(t,{stylesInheritanceTransformersRegistry:function(){return r}});var o=n("@elementor/editor-canvas");const r=(0,o.createTransformersRegistry)()},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-color-overlay-transformer.tsx":function(e,t,n){n.r(t),n.d(t,{StyledUnstableColorIndicator:function(){return i},backgroundColorOverlayTransformer:function(){return a}});var o=n("react"),r=n("@elementor/editor-canvas"),s=n("@elementor/ui");const a=(0,r.createTransformer)(e=>o.createElement(s.Stack,{direction:"row",gap:10},o.createElement(ItemIconColor,{value:e}),o.createElement(ItemLabelColor,{value:e}))),ItemIconColor=({value:e})=>{const{color:t}=e;return o.createElement(i,{size:"inherit",component:"span",value:t})},ItemLabelColor=({value:{color:e}})=>o.createElement("span",null,e),i=(0,s.styled)(s.UnstableColorIndicator)(({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-gradient-overlay-transformer.tsx":function(e,t,n){n.r(t),n.d(t,{backgroundGradientOverlayTransformer:function(){return l}});var o=n("react"),r=n("@elementor/editor-canvas"),s=n("@elementor/ui"),a=n("@wordpress/i18n"),i=n("./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-color-overlay-transformer.tsx");const l=(0,r.createTransformer)(e=>o.createElement(s.Stack,{direction:"row",gap:10},o.createElement(ItemIconGradient,{value:e}),o.createElement(ItemLabelGradient,{value:e}))),ItemIconGradient=({value:e})=>{const t=getGradientValue(e);return o.createElement(i.StyledUnstableColorIndicator,{size:"inherit",component:"span",value:t})},ItemLabelGradient=({value:e})=>"linear"===e.type?o.createElement("span",null,(0,a.__)("Linear Gradient","elementor")):o.createElement("span",null,(0,a.__)("Radial Gradient","elementor")),getGradientValue=e=>{const t=e.stops?.map(({color:e,offset:t})=>`${e} ${t??0}%`)?.join(",");return"linear"===e.type?`linear-gradient(${e.angle}deg, ${t})`:`radial-gradient(circle at ${e.positions}, ${t})`}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-image-overlay-transformer.tsx":function(e,t,n){n.r(t),n.d(t,{backgroundImageOverlayTransformer:function(){return l}});var o=n("react"),r=n("@elementor/editor-canvas"),s=n("@elementor/editor-ui"),a=n("@elementor/ui"),i=n("@elementor/wp-media");const l=(0,r.createTransformer)(e=>o.createElement(a.Stack,{direction:"row",gap:10},o.createElement(ItemIconImage,{value:e}),o.createElement(ItemLabelImage,{value:e}))),ItemIconImage=({value:e})=>{const{imageUrl:t}=useImage(e);return o.createElement(a.CardMedia,{image:t,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},ItemLabelImage=({value:e})=>{const{imageTitle:t}=useImage(e);return o.createElement(s.EllipsisWithTooltip,{title:t},o.createElement("span",null,t))},useImage=e=>{let t,n=null;const o=e?.image.src,{data:r}=(0,i.useWpMediaAttachment)(o.id||null);if(o.id){const e=getFileExtensionFromFilename(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else o.url&&(n=o.url,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},getFileExtensionFromFilename=e=>{if(!e)return"";return`.${e.substring(e.lastIndexOf(".")+1)}`}},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/transformers/background-overlay-transformer.tsx":function(e,t,n){n.r(t),n.d(t,{backgroundOverlayTransformer:function(){return a}});var o=n("react"),r=n("@elementor/editor-canvas"),s=n("@elementor/ui");const a=(0,r.createTransformer)(e=>e&&0!==e.length?o.createElement(s.Stack,{direction:"column"},e.map((e,t)=>o.createElement(s.Stack,{key:t},e))):null)},"./packages/packages/core/editor-editing-panel/src/styles-inheritance/utils.ts":function(e,t,n){n.r(t),n.d(t,{DEFAULT_STATE:function(){return o},getBreakpointKey:function(){return getBreakpointKey},getStateKey:function(){return getStateKey},getValueFromInheritanceChain:function(){return getValueFromInheritanceChain}});const o="normal",getStateKey=e=>e??o,getBreakpointKey=e=>e??"desktop",getValueFromInheritanceChain=(e,t,n)=>e.find(({style:e,variant:{meta:{breakpoint:o,state:r}}})=>e.id===t&&o===n.breakpoint&&r===n.state)},"./packages/packages/core/editor-editing-panel/src/sync/get-elementor-globals.ts":function(e,t,n){n.r(t),n.d(t,{getElementorConfig:function(){return getElementorConfig},getElementorFrontendConfig:function(){return getElementorFrontendConfig}});const getElementorConfig=()=>{const e=window;return e.elementor?.config??{}},getElementorFrontendConfig=()=>{const e=window;return e.elementorFrontend?.config??{}}},"./packages/packages/core/editor-editing-panel/src/sync/is-atomic-widget-selected.ts":function(e,t,n){n.r(t),n.d(t,{isAtomicWidgetSelected:function(){return isAtomicWidgetSelected}});var o=n("@elementor/editor-elements");const isAtomicWidgetSelected=()=>{const e=(0,o.getSelectedElements)(),t=(0,o.getWidgetsCache)();return 1===e.length&&!!t?.[e[0].type]?.atomic_controls}},"./packages/packages/core/editor-editing-panel/src/utils/get-styles-provider-color.ts":function(e,t,n){n.r(t),n.d(t,{getStylesProviderColorName:function(){return getStylesProviderColorName},getStylesProviderThemeColor:function(){return getStylesProviderThemeColor},getTempStylesProviderThemeColor:function(){return getTempStylesProviderThemeColor}});var o=n("@elementor/editor-styles-repository"),r=n("./packages/packages/core/editor-editing-panel/src/provider-colors-registry.ts");const getStylesProviderColorName=e=>e&&e!==o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,o.isElementsStylesProvider)(e)?"accent":(0,r.getStyleProviderColors)(e).name:"default",getStylesProviderThemeColor=e=>e&&e!==o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,o.isElementsStylesProvider)(e)?e=>e.palette.accent.main:(0,r.getStyleProviderColors)(e).getThemeColor:null;function getTempStylesProviderThemeColor(e){return(0,o.isElementsStylesProvider)(e)?e=>e.palette.primary.main:getStylesProviderThemeColor(e)}},"./packages/packages/core/editor-editing-panel/src/utils/prop-dependency-utils.ts":function(e,t,n){n.r(t),n.d(t,{extractOrderedDependencies:function(){return extractOrderedDependencies},updateValues:function(){return updateValues}});var o=n("@elementor/editor-props");function extractOrderedDependencies(e,t,n,o){const r=getPropType(t,n,e.split("."));if(!r)return[];const s=[];"object"===r.kind&&s.push(...Object.keys(r.shape).map(t=>e+"."+t));const a=extractPropOrderedDependencies(e,o);return s.length?s.reduce((e,r)=>[...e,...extractOrderedDependencies(r,t,n,o)],a):a}function extractPropOrderedDependencies(e,t){return t?.[e]?.length?t[e].reduce((e,n)=>[...e,n,...extractPropOrderedDependencies(n,t)],[]):[]}function updateValues(e,t,n,r){return t.length?t.reduce((e,t)=>{const s=t.split("."),a=getPropType(n,r,s),i={...r,...e};return a?(0,o.isDependencyMet)(a?.dependencies,i)?e:{...e,...updateValue(s,null,i)}:e},{...e}):e}function getPropType(e,t,n){if(!n.length)return null;const[r,...s]=n,a=e[r];return a?s.reduce((e,s,a)=>{if(!e?.kind)return null;if("union"===e.kind){const s=(0,o.extractValue)(n.slice(0,a+1),t),i=s?.$$type??null;return getPropType({[r]:e.prop_types?.[i]},t,n.slice(0,a+2))}return"array"===e.kind?e.item_prop_type:"object"===e.kind?e.shape[s]:e[s]},a):null}function updateValue(e,t,n){const o=e[0],r={...n};return e.reduce((n,o,r)=>n?r===e.length-1?(n[o]=null!==t?{...n[o]??{},value:t}:null,n[o]?.value??n.value):n[o]?.value??n.value:null,r),{[o]:r[o]??null}}},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-canvas":function(e){e.exports=window.elementorV2.editorCanvas},"@elementor/editor-controls":function(e){e.exports=window.elementorV2.editorControls},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-panels":function(e){e.exports=window.elementorV2.editorPanels},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-responsive":function(e){e.exports=window.elementorV2.editorResponsive},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-styles-repository":function(e){e.exports=window.elementorV2.editorStylesRepository},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/menus":function(e){e.exports=window.elementorV2.menus},"@elementor/schema":function(e){e.exports=window.elementorV2.schema},"@elementor/session":function(e){e.exports=window.elementorV2.session},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@elementor/wp-media":function(e){e.exports=window.elementorV2.wpMedia},"@wordpress/i18n":function(e){e.exports=window.wp.i18n},react:function(e){e.exports=window.React}},t={};function __webpack_require__(n){var o=t[n];if(void 0!==o)return o.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,__webpack_require__),r.exports}__webpack_require__.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=function(e,t){for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){__webpack_require__.r(n),__webpack_require__.d(n,{PopoverBody:function(){return l.PopoverBody},controlActionsMenu:function(){return a.controlActionsMenu},init:function(){return d.init},injectIntoClassSelectorActions:function(){return r.injectIntoClassSelectorActions},registerControlReplacement:function(){return t.registerControlReplacement},registerStyleProviderToColors:function(){return o.registerStyleProviderToColors},stylesInheritanceTransformersRegistry:function(){return p.stylesInheritanceTransformersRegistry},useBoundProp:function(){return e.useBoundProp},useFontFamilies:function(){return i.useFontFamilies},usePanelActions:function(){return s.usePanelActions},usePanelStatus:function(){return s.usePanelStatus},useSectionWidth:function(){return c.useSectionWidth}});var e=__webpack_require__("@elementor/editor-controls"),t=__webpack_require__("./packages/packages/core/editor-editing-panel/src/control-replacement.tsx"),o=__webpack_require__("./packages/packages/core/editor-editing-panel/src/provider-colors-registry.ts"),r=__webpack_require__("./packages/packages/core/editor-editing-panel/src/components/css-classes/css-class-selector.tsx"),s=__webpack_require__("./packages/packages/core/editor-editing-panel/src/panel.ts"),a=__webpack_require__("./packages/packages/core/editor-editing-panel/src/controls-actions.ts"),i=__webpack_require__("./packages/packages/core/editor-editing-panel/src/components/style-sections/typography-section/hooks/use-font-families.ts"),l=__webpack_require__("./packages/packages/core/editor-editing-panel/src/components/popover-body.tsx"),c=__webpack_require__("./packages/packages/core/editor-editing-panel/src/contexts/section-context.tsx"),p=__webpack_require__("./packages/packages/core/editor-editing-panel/src/styles-inheritance/styles-inheritance-transformers-registry.tsx"),d=__webpack_require__("./packages/packages/core/editor-editing-panel/src/init.ts")}(),(window.elementorV2=window.elementorV2||{}).editorEditingPanel=n}(),window.elementorV2.editorEditingPanel?.init?.();
//# sourceMappingURL=editor-editing-panel.js.map